package com.purchase.purchase_server.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.bo.WatchBoardBO;
import com.purchase.purchase_server.entity.dataObject.*;
import com.purchase.purchase_server.entity.form.InteriorInfoQuery;
import com.purchase.purchase_server.mapper.ReplenishmentVirtualSkuPurchaseMapper;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【cm_replenishment_virtual_sku_purchase】的数据库操作Service实现
 * @createDate 2024-01-17 10:45:51
 */
@Service
public class ReplenishmentVirtualSkuPurchaseRepositoryImpl extends ServiceImpl<ReplenishmentVirtualSkuPurchaseMapper, ReplenishmentVirtualSkuPurchaseDO> {
    public List<WatchBoardBO> getTrialWatchBoardInfoByReplenishmentId(String replenishmentId, String sourceType) {
        return baseMapper.selectJoinList(WatchBoardBO.class, new MPJLambdaWrapper<ReplenishmentVirtualSkuPurchaseDO>()
                .selectAs(ReplenishmentVirtualSkuPurchaseDO::getId, "shippingProjectId")
                .selectAs(ReplenishmentVirtualSkuPurchaseDO::getVirtualSkuId, "destinationSku")
                .select(FactoryFinishedInventoryDO::getContractCode)
                .select(FactoryFinishedInventoryDO::getDeliveryType)
                .select("CASE t1.is_old_status WHEN '1' THEN t3.virtual_data->>'$.oldSku' WHEN '0' THEN t3.virtual_sku END as destination_sku")
                .select(FactoryFinishedInventoryDO::getFactoryFinishedDate)
                .select(TrialShippingInventoryDO::getDestinationWarehouse)
                .select(TrialShippingInventoryDO::getDeliveryDateRange)
                .select(TrialShippingInventoryDO::getRealShippingStartDate)
                .select(TrialShippingInventoryDO::getShippingNum)
                .select(TrialShippingInventoryDO::getExpectedArrivingDate)
                .select(TrialShippingInventoryDO::getRemarks)
                .select(TrialShippingInventoryDO::getPackageNum)
                .select(TrialShippingInventoryDO::getFactoryFinishedId)
                .selectAs(TrialShippingInventoryDO::getId, "shippingInventoryId")
                .select("t3.self_data->>'$.containerLoad' as container_load")
                .select("t3.self_data->>'$.caseWidth' as case_width")
                .select("t3.self_data->>'$.caseLength' as case_length")
                .select("t3.self_data->>'$.caseHeight' as case_height")
                .select("t3.self_data->>'$.singleCaseGrossWeight' as single_case_gross_weight")
                .leftJoin(FactoryFinishedInventoryDO.class, FactoryFinishedInventoryDO::getShippingProjectId, ReplenishmentVirtualSkuPurchaseDO::getId)
                .leftJoin(TrialShippingInventoryDO.class, TrialShippingInventoryDO::getFactoryFinishedId, FactoryFinishedInventoryDO::getId)
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, FactoryFinishedInventoryDO::getProductSnapshotId)
                .ne(FactoryFinishedInventoryDO::getFactoryRemainNum, FactoryFinishedInventoryDO::getFactoryShippingPackageNum)
                .eq(ReplenishmentVirtualSkuPurchaseDO::getId, replenishmentId)
                .eq(StrUtil.isNotBlank(sourceType), FactoryFinishedInventoryDO::getSourceType, sourceType));
    }

    public List<ReplenishmentVirtualSkuPurchaseDO> getAllOverseasInventory() {
        return baseMapper.selectList(Wrappers.lambdaQuery());
    }

    public List<ReplenishmentVirtualSkuPurchaseDO> selectByProjectIds(List<String> replenishmentProjectIds) {
        return this.baseMapper.selectList(Wrappers.<ReplenishmentVirtualSkuPurchaseDO>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(replenishmentProjectIds), ReplenishmentVirtualSkuPurchaseDO::getReplenishmentProjectId, replenishmentProjectIds));
    }

    public List<ReplenishmentVirtualSkuPurchaseDO> selectByRecordId(String replenishmentRecordId) {
        return this.baseMapper.selectJoinList(ReplenishmentVirtualSkuPurchaseDO.class, new MPJLambdaWrapper<ReplenishmentVirtualSkuPurchaseDO>()
                .selectAll(ReplenishmentVirtualSkuPurchaseDO.class)
                .rightJoin(ReplenishmentProjectDO.class, ReplenishmentProjectDO::getId, ReplenishmentVirtualSkuPurchaseDO::getReplenishmentProjectId)
                .rightJoin(ReplenishmentRecordDO.class, ReplenishmentRecordDO::getId, ReplenishmentProjectDO::getReplenishmentRecordId)
                .eq(ReplenishmentRecordDO::getId, replenishmentRecordId));
    }

    public void deleteByIds(List<String> ids) {
        if (CollectionUtil.isNotEmpty(ids)) {
            baseMapper.deleteByIds(ids);
        }
    }

    public Set<String> getSnapIds(InteriorInfoQuery infoQuery) {
        List<ReplenishmentVirtualSkuPurchaseDO> list = baseMapper.selectList(Wrappers.<ReplenishmentVirtualSkuPurchaseDO>lambdaQuery()
                .select(ReplenishmentVirtualSkuPurchaseDO::getProductSnapshotId)
                .between(StrUtil.isNotBlank(infoQuery.getCreateStartDate()) && StrUtil.isNotBlank(infoQuery.getCreateEndDate()),
                        ReplenishmentVirtualSkuPurchaseDO::getCreateDate, infoQuery.getCreateStartDate(), infoQuery.getCreateEndDate()));
        return CollectionUtil.isEmpty(list) ? Collections.emptySet() :
                list.stream()
                        .map(ReplenishmentVirtualSkuPurchaseDO::getProductSnapshotId)
                        .collect(Collectors.toSet());
    }

    public void updateVirtualSkuPurchase(ReplenishmentVirtualSkuPurchaseDO val){
        baseMapper.updateVirtualSkuPurchase(val);
    }

    public List<String> selectProjectIdBySnap(List<String> snapIds) {
        return baseMapper.selectJoinList(String.class, new MPJLambdaWrapper<ReplenishmentVirtualSkuPurchaseDO>()
                .distinct()
                .select(ReplenishmentVirtualSkuPurchaseDO::getReplenishmentProjectId)
                .in(CollectionUtil.isNotEmpty(snapIds), ReplenishmentVirtualSkuPurchaseDO::getProductSnapshotId, snapIds)
        );
    }
}




