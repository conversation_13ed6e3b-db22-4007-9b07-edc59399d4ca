package com.purchase.purchase_server.utils.commonUtils;

import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2024/10/10
 **/
public class WarehouseSortUtil {

    private static Map<String, Integer> warehouseOrderMap;
    private static Comparator<String> warehouseComparator;

    public static void initializeOrderMap(List<SenboWarehouseDto> senboWarehouseList) {
        warehouseOrderMap = new HashMap<>();
        for (int i = 0; i < senboWarehouseList.size(); i++) {
            String senboWarehouse = senboWarehouseList.get(i).getSenboWarehouse();
            warehouseOrderMap.put(senboWarehouse, senboWarehouseList.size() - i);
        }

        warehouseComparator = (w1, w2) -> {
            int order1 = warehouseOrderMap.getOrDefault(w1, 0);
            int order2 = warehouseOrderMap.getOrDefault(w2, 0);
            if (order1 != order2) {
                return Integer.compare(order2, order1);
            }
            return w1.compareTo(w2);
        };
    }

    public static <V> TreeMap<String, V> createSortedTreeMap() {
        if (warehouseComparator == null) {
            throw new IllegalStateException("WarehouseSortUtil has not been initialized. Call initializeOrderMap first.");
        }
        return new TreeMap<>(warehouseComparator);
    }

    public static <K, V, R> Map<K, TreeMap<String, R>> sortAndReplaceWarehouseKeys(
            Map<K, ? extends Map<String, V>> inputMap,
            Map<String, String> idNameMap,
            Function<V, R> valueMapper) {

        if (warehouseComparator == null) {
            throw new IllegalStateException("WarehouseSortUtil has not been initialized. Call initializeOrderMap first.");
        }

        return inputMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().entrySet().stream()
                                .collect(Collectors.toMap(
                                        warehouseEntry -> idNameMap.getOrDefault(warehouseEntry.getKey(), warehouseEntry.getKey()),
                                        warehouseEntry -> valueMapper.apply(warehouseEntry.getValue()),
                                        (e1, e2) -> e1,
                                        WarehouseSortUtil::createSortedTreeMap
                                )),
                        (e1, e2) -> e1,
                        TreeMap::new
                ));
    }

    public static <K, V, R> TreeMap<K, TreeMap<String, R>> sortAndReplaceWarehouseKeys(
            Map<K, ? extends Map<String, V>> inputMap,
            Function<V, R> valueMapper) {

        if (warehouseComparator == null) {
            throw new IllegalStateException("WarehouseSortUtil has not been initialized. Call initializeOrderMap first.");
        }

        return inputMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().entrySet().stream()
                                .collect(Collectors.toMap(
                                        Map.Entry::getKey,
                                        warehouseEntry -> valueMapper.apply(warehouseEntry.getValue()),
                                        (e1, e2) -> e1,
                                        WarehouseSortUtil::createSortedTreeMap
                                )),
                        (e1, e2) -> e1,
                        TreeMap::new
                ));
    }

    public static List<String> getSortedWarehouseList(Map<String, Double> inventorySaleDefRatio, Map<String, Integer> warehouseSort) {
        // 发货的先后顺序
        return inventorySaleDefRatio.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByValue(Comparator.reverseOrder())
                        .thenComparing((e1, e2) -> warehouseSort.get(e2.getKey()).compareTo(warehouseSort.get(e1.getKey()))))
                .map(Map.Entry::getKey)
                .collect(toList());
    }
}
