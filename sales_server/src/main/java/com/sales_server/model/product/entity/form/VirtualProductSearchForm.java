package com.sales_server.model.product.entity.form;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/12 16:28
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VirtualProductSearchForm implements Serializable {
    private String productName;

    private List<String> selfSkuList;

    private List<String> virtualSkuList;

    private String spuName;

    private List<String> spuIdList;

    private String channel;

    private String operator;

    private Boolean needDatePermission;

    /**
     * 品类id
     */
    private String categoryId;
}
