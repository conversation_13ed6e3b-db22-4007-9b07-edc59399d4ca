package com.purchase.purchase_server.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.utils.constant.RedisKeyConstant;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.purchase.purchase_server.aop.PreventReSubmit;
import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentSoldOutDaysDO;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentRecordDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentSaleDestinationDto;
import com.purchase.purchase_server.entity.form.*;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.*;
import com.purchase.purchase_server.enums.PurchaseNumTypeEnum;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.service.replenishment.IReplenishmentProjectService;
import com.purchase.purchase_server.service.replenishment.IReplenishmentTrialPurchaseInventoryService;
import com.purchase.purchase_server.service.replenishment.IReplenishmentVirtualSkuPurchaseService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.crafts_mirror.utils.constant.RedisKeyConstant.IMPORT_REPLENISHMENT_LOCK;

/**
 * <AUTHOR>
 * @Description 补货
 * @date 2024/1/15
 **/
@RestController
@RequestMapping(value = "replenishment")
@Slf4j
public class ReplenishmentController {
    @Resource
    private IReplenishmentProjectService replenishmentProjectService;
    @Resource
    private IReplenishmentTrialPurchaseInventoryService repPurchaseInventoryService;

    @Resource
    private IReplenishmentVirtualSkuPurchaseService repVirtualSkuPurchaseService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @RequiresPermissions("purchase:replenishment:list")
    @PostMapping("/import")
    @PreventReSubmit
    public ResultDTO<String> importDeliveryInfo(@RequestParam("file") MultipartFile file,
                                                @RequestParam("advicePurchaseStartDate") String advicePurchaseStartDate,
                                                @RequestParam("advicePurchaseEndDate") String advicePurchaseEndDate) {
        if (StrUtil.isBlank(advicePurchaseStartDate) || StrUtil.isBlank(advicePurchaseEndDate)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "请填写理论补货日");
        }
        try {
            if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(IMPORT_REPLENISHMENT_LOCK, "1", 7, TimeUnit.HOURS))) {
                log.error("导入补货计划失败，当前有正在计算的补货计划");
                return ResultDTO.error("上一条导入还在分析中，请稍后再试");
            }
            log.warn("导入补货计划-------------准备开始导入日志");
            replenishmentProjectService.importExcelToDeliveryPurchase(advicePurchaseStartDate, advicePurchaseEndDate, file.getInputStream(), file.getOriginalFilename(), file.getBytes());
        } catch (IOException e) {
            log.error("获取输入流异常：", e);
            throw new RuntimeException(e);
        }
        return ResultDTO.success("正在导入");
    }

    //发货计划导入模板
    @PostMapping("/template/export")
    public void exportTemplate(HttpServletResponse response) {
        replenishmentProjectService.exportTemplate(response);
    }

    @RequiresPermissions("purchase:replenishment:list")
    @PostMapping("/recordPageList")
    @ResponseBody
    public ResultDTO<IPage<ReplenishmentRecordPurchaseListVo>> repRecordListData(@RequestBody ReplenishmentProjectForm form) {
        IPage<ReplenishmentRecordPurchaseListVo> replenishmentRecordPurchaseListVo = replenishmentProjectService.recordPageList(form);
        return ResultDTO.success(replenishmentRecordPurchaseListVo);
    }

    @RequiresPermissions("purchase:replenishment:list")
    @PostMapping("/pageList")
    @ResponseBody
    public ResultDTO<ReplenishmentRecordStatusVo> replenishmentListData(@RequestBody ReplenishmentProjectForm form) {
        ReplenishmentRecordStatusVo replenishmentProjectSkuListVo;
        try {
            replenishmentProjectSkuListVo = replenishmentProjectService.pageList(form);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
        return ResultDTO.success(replenishmentProjectSkuListVo);
    }

    // 确认量信息
    @RequiresPermissions("purchase:replenishment:list")
    @PostMapping("/info/confirmEditQuantity")
    public ResultDTO<ReplenishmentTrialConfirmQuantityVo> confirmEditQuantity(@RequestBody ReplenishmentDetailForm form) {
        ReplenishmentTrialConfirmQuantityVo confirmEditQuantity = repPurchaseInventoryService.getConfirmEditQuantity(form);
        return ResultDTO.success(confirmEditQuantity);
    }

    @GetMapping("/info/confirmedReasonList")
    public ResultDTO<ReplenishmentTrialConfirmQuantityVo> confirmEditQuantity() {
        Map<String, String> postMap = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKeyConstant.OPERATOR_CONFIRMED_REASON), new TypeReference<>() {
        });

        return ResultDTO.success(ReplenishmentTrialConfirmQuantityVo.builder().confirmedReasonList(postMap).build());
    }

    //试算失败原因
    @RequiresPermissions("purchase:replenishment:list")
    @GetMapping("/fileInfo")
    @ResponseBody
    public ResultDTO<MissionCenterVo> selectFileInfo(@RequestParam("recordId") String recordId) {
        MissionCenterVo missionCenterVo = replenishmentProjectService.selectFileInfo(recordId);
        missionCenterVo.setFilePath(null);
        return ResultDTO.success(missionCenterVo);
    }

    // 下载源文件
    @RequiresPermissions("purchase:replenishment:sourceFile")
    @GetMapping("/download/sourceFile")
    @ResponseBody
    public ResultDTO<MissionCenterVo> sourceFile(@RequestParam("recordId") String recordId) {
        return ResultDTO.success(replenishmentProjectService.selectFileInfo(recordId));
    }

    @RequiresPermissions("purchase:replenishment:list")
    @PostMapping("/advicePurchase/checkRecordData")
    @ResponseBody
    public ResultDTO checkRecordData(@RequestParam("recordId") String recordId,
                                             @RequestParam("replenishmentStatus") String replenishmentStatus) {
        try {
            repPurchaseInventoryService.checkRecordData(recordId, replenishmentStatus);
            return ResultDTO.success();
        } catch (RuntimeException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR, e.getMessage());
        }
    }

    //保存
    @RequiresPermissions("purchase:replenishment:record:save")
    @PostMapping("/advicePurchase/save")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO<Boolean> saveAdvicePurchase(@RequestParam("recordId") String recordId,
                                                 @RequestParam("replenishmentStatus") String replenishmentStatus) {
        try {
            return ResultDTO.success(repPurchaseInventoryService.saveAdvicePurchase(recordId, replenishmentStatus));
        } catch (RuntimeException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR, e.getMessage());
        }
    }

    //作废
    @RequiresPermissions("purchase:replenishment:record:discard")
    @PostMapping("/advicePurchase/discard")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO<Boolean> discardAdvicePurchase(@RequestParam("recordId") String recordId,
                                                 @RequestParam("replenishmentStatus") String replenishmentStatus) {
        try {
            return ResultDTO.success(repPurchaseInventoryService.saveAdvicePurchase(recordId, replenishmentStatus));
        } catch (RuntimeException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR, e.getMessage());
        }
    }

    /**
     * 理论补货、建议补货补货导入的excel数据
     * @param repVirtualSkuId 补货id
     * @param purchaseType 补货类型
     * @return 补货导入的excel数据
     */
    @RequiresPermissions("purchase:replenishment:list")
    @GetMapping("/detail/replenishmentRules")
    @ResponseBody
    public ResultDTO<ReplenishmentInfoDetailVo> getReplenishmentRules(String repVirtualSkuId, String purchaseType) {
        try {
            ReplenishmentInfoDetailVo repWatchBoard = repVirtualSkuPurchaseService.getReplenishmentInfoDetail(repVirtualSkuId, purchaseType);
            return ResultDTO.success(repWatchBoard);
        } catch (RuntimeException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR, e.getMessage());
        }
    }

    /**
     * 运营确认补货补货导入的excel数据
     * @param repVirtualSkuId 补货id
     * @return 补货导入的excel数据
     */
    @RequiresPermissions("purchase:replenishment:detail:operator")
    @GetMapping("/detail/replenishmentRules/operator")
    @ResponseBody
    public ResultDTO<ReplenishmentInfoDetailVo> getReplenishmentRulesOperator(String repVirtualSkuId) {
        try {
            ReplenishmentInfoDetailVo repWatchBoard = repVirtualSkuPurchaseService.getReplenishmentInfoDetail(repVirtualSkuId, PurchaseNumTypeEnum.OPERATOR.getCode());
            return ResultDTO.success(repWatchBoard);
        } catch (RuntimeException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR, e.getMessage());
        }
    }

    @RequiresPermissions("purchase:replenishment:export")
    @PostMapping("/export")
    @PreventReSubmit
    public void exportRepInfo(@RequestBody ReplenishmentProjectForm form, HttpServletResponse response) {
        repPurchaseInventoryService.exportRepInfo(form, response);
    }

    @RequiresPermissions("purchase:replenishment:export")
    @PostMapping("/export/notRestock")
    @PreventReSubmit
    public void exportNotRestockRepInfo(@RequestBody ReplenishmentProjectForm form, HttpServletResponse response) {
        repPurchaseInventoryService.exportNotRestockRepInfo(form, response);
    }

    @RequiresPermissions("purchase:replenishment:list")
    @PostMapping("detail/watchBoard/shipping")
    public ResultDTO<ReplenishmentDetailWatchBoardShippingVo> getShippingTrialWatchBoardAndTrialCal(@RequestBody ReplenishmentDetailForm form) {
        return ResultDTO.success(repPurchaseInventoryService.getWatchBoardShippingInfoOpertor(form.getReplenishmentId(), form.getPurchaseNumType()));
    }

    @RequiresPermissions("purchase:replenishment:detail:operator")
    @PostMapping("detail/watchBoard/shipping/operator")
    public ResultDTO<ReplenishmentDetailWatchBoardShippingVo> getShippingTrialWatchBoardAndTrialCalOperator(@RequestBody ReplenishmentDetailForm form) {
        return ResultDTO.success(repPurchaseInventoryService.getWatchBoardShippingInfoOpertor(form.getReplenishmentId(), PurchaseNumTypeEnum.OPERATOR.getCode()));
    }

    @RequiresPermissions("purchase:replenishment:list")
    @PostMapping("detail/watchBoard/replenishment")
    public ResultDTO<ReplenishmentDetailWatchBoardReplenishmentVo> getRepTrialWatchBoard(@RequestBody ReplenishmentDetailForm form) {
        return ResultDTO.success(repPurchaseInventoryService.getWatchBoardReplenishmentInfo(form));
    }

    @RequiresPermissions("purchase:replenishment:detail:operator")
    @PostMapping("detail/watchBoard/replenishment/operator")
    public ResultDTO<ReplenishmentDetailWatchBoardReplenishmentVo> getRepTrialWatchBoardCalOperator(@RequestBody ReplenishmentDetailForm form) {
        form.setPurchaseNumType(PurchaseNumTypeEnum.OPERATOR.getCode());
        return ResultDTO.success(repPurchaseInventoryService.getWatchBoardReplenishmentInfo(form));
    }

    @PostMapping("/info/detail/saleDestination")
    public ResultDTO<List<ReplenishmentSaleDestinationDto>> getSaleDestination(@RequestBody ReplenishmentDetailForm form) {
        return ResultDTO.success(repVirtualSkuPurchaseService.getSaleDestination(form));
    }

    /**
     * 修改目标日销
     * @param form
     */
    @RequiresPermissions("sales:targetDaySales:edit")
    @PostMapping("update/detail/saleDestination")
    @PreventReSubmit
    public ResultDTO<Boolean> updateSaleDestination(@RequestBody ReplenishmentDetailForm form) {
        repVirtualSkuPurchaseService.updateSaleDestination(form, new LogTrackNumDto());
        return ResultDTO.success();
    }

    /**
     * 重新试算
     */
    @RequiresPermissions("purchase:replenishment:detail:operator")
    @PostMapping("detail/watchBoard/replenishment/reCalReplenishment")
    public ResultDTO<ReplenishmentDetailWatchBoardReplenishmentVo> reCalReplenishment(@RequestBody ReplenishmentDetailForm form) {
        replenishmentProjectService.reCalReplenishment(form);
        return ResultDTO.success();
    }

    @RequiresPermissions("purchase:replenishment:list")
    @PostMapping("/detail/watchBoard/save")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO<Boolean> saveDetailInfo(@RequestBody RetrialReplenishmentInventoryForm form) {
        try {
            return ResultDTO.success(repPurchaseInventoryService.saveReplenishmentPurchaseNumChange(form));
        } catch (RuntimeException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR, e.getMessage());
        }
    }

    @RequiresPermissions("purchase:replenishment:list")
    @PostMapping("/detail/watchBoard/calDelivery")
    @ResponseBody
    public ResultDTO<TrialMockInventoryAddSoldVo> calDelivery(@RequestBody RetrialReplenishmentInventoryForm form) {
        try {
            return ResultDTO.success(repPurchaseInventoryService.calDelivery(form));
        } catch (RuntimeException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR, e.getMessage());
        }
    }

    @RequiresPermissions("purchase:replenishment:detail:operator")
    @PostMapping("/detail/watchBoard/calDelivery/operator")
    @ResponseBody
    public ResultDTO<TrialMockInventoryAddSoldVo> calDeliveryOperator(@RequestBody RetrialReplenishmentInventoryForm form) {
        try {
            form.setPurchaseNumType(PurchaseNumTypeEnum.OPERATOR.getCode());
            return ResultDTO.success(repPurchaseInventoryService.calDelivery(form));
        } catch (RuntimeException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR, e.getMessage());
        }
    }

    @RequiresPermissions("purchase:replenishment:list")
    @PostMapping("/detail/watchBoard/delete")
    @ResponseBody
    public ResultDTO<Boolean> deleteTrial(@RequestBody RetrialReplenishmentInventoryForm form) {
        try {
            return ResultDTO.success(repPurchaseInventoryService.delete(form));
        } catch (RuntimeException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR, e.getMessage());
        }
    }

    /**
     * 更新运营确认量
     */
    @PostMapping("/advicePurchase/updateOperation")
    @RequiresPermissions("purchase:replenishment:list")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO<Boolean> updateOperation(@RequestBody OperationConfirmedNumForm form) {
        return ResultDTO.success(repPurchaseInventoryService.updateOperation(form, new LogTrackNumDto()));
    }

    /**
     * 更新运营确认量
     */
    @PostMapping("/advicePurchase/batchUpdateOperation")
    @RequiresPermissions("purchase:replenishment:list")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO<Boolean> batchUpdateOperation(@RequestBody OperationConfirmedNumForm form) {
        return ResultDTO.success(repPurchaseInventoryService.batchUpdateOperation(form, new LogTrackNumDto()));
    }

    @PostMapping("/advicePurchase/refreshData")
    @ResponseBody
    public ResultDTO<?> refreshData() {
        repPurchaseInventoryService.refreshData();
        return ResultDTO.success();
    }

    @GetMapping("/list/operator")
    @ResponseBody
    public ResultDTO<List<OperatorSearchVo>> getOperator() {
        return ResultDTO.success(repVirtualSkuPurchaseService.getOperator());
    }

    //删除试算失败或作废数据
    @RequiresPermissions("purchase:replenishment:delete")
    @DeleteMapping("/delete/record/{recordId}")
    @PreventReSubmit
    public ResultDTO<Boolean> deleteRecord(@PathVariable String recordId) {
        return ResultDTO.success(replenishmentProjectService.deleteRecord(recordId));
    }

    // 更新理论补货日
    @RequiresPermissions("purchase:replenishment:advicePurchaseDate")
    @PostMapping("/update/record/advicePurchaseDate")
    @PreventReSubmit
    public ResultDTO<Boolean> updateAdvicePurchaseDate(@RequestBody @Valid ReplenishmentRecordDto val) {
        return ResultDTO.success(replenishmentProjectService.updateAdvicePurchaseDate(val));
    }

    /**
     * 刷新补货理论补货日
     */
    @GetMapping("/update/advicePurchaseDateAll")
    public ResultDTO<Boolean> updateAdvicePurchaseDateAll() {
        return ResultDTO.success(replenishmentProjectService.updateAdvicePurchaseDateAll());
    }


    /**
     * 检查批量确认量-理论补货量是否展示理由
     */
    @PostMapping("/info/checkConfirmedReason")
    public ResultDTO<Boolean> checkConfirmedReason(@RequestBody OperationConfirmedNumForm form) {
        return ResultDTO.success(repPurchaseInventoryService.checkConfirmedReason(form));
    }

    /**
     * 售罄前断货天数
     * @param form 补货信息id
     * @return 断货天数
     */
    @PostMapping("/beforeSoldOutList")
    public ResultDTO<List<ReplenishmentSoldOutDaysDO>> getBeforeSoldOutList(@RequestBody ReplenishmentProjectForm form) {
        return ResultDTO.success(replenishmentProjectService.getBeforeSoldOutList(form));
    }

    /**
     * 刷新目标日销格式
     */
    @GetMapping("/refresh/saleDestination")
    public ResultDTO<Boolean> refreshSaleDestination() {
        replenishmentProjectService.refreshSaleDestination();
        return ResultDTO.success();
    }

}
