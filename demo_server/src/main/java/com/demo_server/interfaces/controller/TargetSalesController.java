package com.demo_server.interfaces.controller;

import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.demo_server.infrastructures.aop.PreventReSubmit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * @Description 目标日销管理
 * <AUTHOR>
 * @Date 2024/11/6 17:29
 **/
@RestController
@RequestMapping(value = "/targetSales")
@Slf4j
public class TargetSalesController {

    @PostMapping("/save/import")
    @PreventReSubmit
    public ResultDTO<String> importDeliveryInfo(@RequestParam("file") MultipartFile file) {
        log.warn("导入目标日销-------------准备开始导入日志");
        try {
            throw new IOException("");
        } catch (IOException e) {
            log.error("导入目标日销失败，异常信息：", e);
        }
        return ResultDTO.success("正在导入");
    }
}
