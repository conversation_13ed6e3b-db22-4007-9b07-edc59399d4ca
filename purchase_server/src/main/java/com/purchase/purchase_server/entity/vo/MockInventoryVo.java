package com.purchase.purchase_server.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Map;
import java.util.TreeMap;

/**
 * @Description 试算看板模拟销售表格
 * <AUTHOR>
 * @Date 2024/1/10 17:17
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MockInventoryVo implements Serializable {
    Map<String, TreeMap<String, Integer>> mockShippingInventoryMap;
    Map<String, TreeMap<String, Double>> mockRemainInventoryMap;
    Map<String, TreeMap<String, Double>> mockDaysSaleInventoryMap;
}