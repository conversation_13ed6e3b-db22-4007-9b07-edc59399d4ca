package com.inventory_server.model.redundancy.service;

import com.inventory_server.model.redundancy.entity.aggregate.InventoryFactoryPlanInfoA;
import com.inventory_server.model.redundancy.entity.dos.InventoryFactoryPlanInfoDO;
import com.inventory_server.model.redundancy.entity.dto.DeliveryCalResultFactoryFinishedDto;
import com.inventory_server.model.redundancy.entity.dto.FactoryFinishedInventoryDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/10
 **/
public interface IInventoryFactoryPlanInfoService {

    List<InventoryFactoryPlanInfoA> getPlanInfoListByInfoIds(List<String> infoIds);

    List<InventoryFactoryPlanInfoA> getPlanInfoListByInfoId(String infoId);

    List<InventoryFactoryPlanInfoDO> getPlanInfoByIdList(List<String> idList);

    /**
     * 此处将入参的哈希值与插入成功的id做映射
     *
     * @param finishedDtoList 发货详情的工厂计划
     * @param inventoryInfoId 冗余库存id
     * @return 入参工厂计划的哈希值与插入成功的id做映射
     */
    List<FactoryFinishedInventoryDto> saveFactoryPlanInfo(List<DeliveryCalResultFactoryFinishedDto> finishedDtoList,
                                             String inventoryInfoId, String productName);

    void deleteByInventoryInfoId(String inventoryInfoId);
}
