package com.purchase.purchase_server.entity.dto.Lcl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 拼柜到仓数据
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclTrialShippingInventoryDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -8950127605910428803L;

    private String id;

    /**
     * 发货装柜时间
     */
    private String shippingStartDate;

    /**
     * 箱数
     */
    private Integer packageNum;

    /**
     * 目标仓
     */
    private String destinationWarehouse;

    /**
     * 工厂交货表格主键（cm_delivery_factory_inventory)
     */
    private String lclFactoryFinishedId;

    /**
     * 原发货数量
     */
    private Integer originalShippingNum;


    /**
     * 发货数量
     */
    private Integer shippingNum;

    /**
     * 整理后发货数量
     */
    private Integer lclShippingNum;

    /**
     * 箱子是否装满
     */
    private String isPackageFull;

    /**
     * 工厂交货时间
     */
    private String factoryFinishedDate;

    /**
     * 工厂交货数量
     */
    private Integer factoryShippingPackageNum;

    /**
     * 虚拟sku
     */
    private String destinationSku;

    /**
     * 自定义skuId
     */
    private String selfSkuId;

    /**
     * 外箱规格长
     */
    private BigDecimal caseLength;

    /**
     * 外箱规格宽
     */
    private BigDecimal caseWidth;

    /**
     * 外箱规格高
     */
    private BigDecimal caseHeight;

    /**
     * 单箱个数
     */
    private Integer containerLoad;

    /**
     * 单箱子体积
     */
    private BigDecimal volume;

    /**
     * 货件号
     */
    private String shipmentCode;

    /**
     * 合同号
     */
    private String contractCode;

    /**
     * 发货比例
     */
    private String shippingRatio;

    private List<LclShippingNumHistoryDTO> shippingNumHistoryDTOS;

    /**
     * 虚拟skuId
     */
    private String virtualSkuId;

    /**
     * sku类型(0-虚拟sku 1-老sku)
     */
    private String isOldStatus;

    /**
     * 快照记录id
     */
    private String productSnapshotId;

    /**
     * 是否变动
     */
    private String isChange;

    /**
     * 发货类型
     */
    private Integer deliveryType;
    /**
     * 备注
     */
    private String remarks;

    /**
     * 备注
     */
    private String trialRemarks;

    /**
     * 是否在途
     */
    private String isForeignFlag;
}