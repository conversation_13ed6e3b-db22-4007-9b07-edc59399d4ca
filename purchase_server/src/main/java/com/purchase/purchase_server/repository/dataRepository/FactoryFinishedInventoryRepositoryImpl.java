package com.purchase.purchase_server.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.bo.FactoryFinishedInventoryBO;
import com.purchase.purchase_server.entity.dataObject.*;
import com.purchase.purchase_server.entity.dto.delivery.DeliveryNumDto;
import com.purchase.purchase_server.entity.form.DeliveryPurchaseForm;
import com.purchase.purchase_server.entity.form.InteriorInfoQuery;
import com.purchase.purchase_server.entity.form.LclConsolidationForm;
import com.purchase.purchase_server.enums.DeliveryTypeEnum;
import com.purchase.purchase_server.mapper.FactoryFinishedInventoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description 工厂交货数量repository层
 * <AUTHOR>
 * @Date 2024/1/11 17:42
 **/
@Service
@Slf4j
public class FactoryFinishedInventoryRepositoryImpl extends ServiceImpl<FactoryFinishedInventoryMapper, FactoryFinishedInventoryDO> {

    public void insertBatch(List<FactoryFinishedInventoryDO> list) {
        super.saveBatch(list);
    }


    public List<FactoryFinishedInventoryDO> getListByShippingProjectId(List<String> shippingProjectIds, String sourceType) {
        return baseMapper.selectList(Wrappers.<FactoryFinishedInventoryDO>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(shippingProjectIds), FactoryFinishedInventoryDO::getShippingProjectId, shippingProjectIds)
                .eq(StrUtil.isNotBlank(sourceType), FactoryFinishedInventoryDO::getSourceType, sourceType));
    }


    public List<ProductSnapshotDO> getSnapshotByShippingProjectId(String shippingProjectId) {
        return baseMapper.selectJoinList(ProductSnapshotDO.class, new MPJLambdaWrapper<FactoryFinishedInventoryDO>()
                .selectAll(ProductSnapshotDO.class)
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, FactoryFinishedInventoryDO::getProductSnapshotId)
                .eq(FactoryFinishedInventoryDO::getShippingProjectId, shippingProjectId));
    }

    public void deleteByShippingIds(List<String> shippingProjectIds, String sourceType) {
        if (CollectionUtil.isNotEmpty(shippingProjectIds)) {
            baseMapper.deleteByShippingProjectIds(shippingProjectIds, sourceType);
        }
    }

    public List<DeliveryNumDto> getDeliveryNumInfo(List<String> projectList, DeliveryPurchaseForm form) {
        if (CollectionUtil.isEmpty(projectList)) {
            return new ArrayList<>();
        }
        return baseMapper.getDeliveryNumInfo(projectList, form.getShippingStartDate(), form.getShippingEndDate());
    }

    public List<DeliveryNumDto> getNoNeedDeliveryInfo(List<String> projectList, DeliveryPurchaseForm form) {
        if (CollectionUtil.isEmpty(projectList)) {
            return new ArrayList<>();
        }
        return baseMapper.getNotDeliveryNumInfo(projectList, form.getShippingEndDate());
    }

    public List<FactoryFinishedInventoryBO> selectNeedToShipList(LclConsolidationForm form) {
        return this.baseMapper.selectJoinList(FactoryFinishedInventoryBO.class, new MPJLambdaWrapper<FactoryFinishedInventoryDO>()
                .selectAll(FactoryFinishedInventoryDO.class)
                .select(PrepareProductsRulesDO::getHeadShippingDays)
                .select(PrepareProductsRulesDO::getShippingRatio)
                .selectCollection(TrialShippingInventoryDO.class, FactoryFinishedInventoryBO::getTrialShippingInventoryList)
                .leftJoin(ShippingProjectDO.class, ShippingProjectDO::getId, FactoryFinishedInventoryDO::getShippingProjectId)
                .leftJoin(ShippingRecordDO.class, ShippingRecordDO::getId, ShippingProjectDO::getShippingRecordId)
                .leftJoin(PrepareProductsRulesDO.class, PrepareProductsRulesDO::getDeliveryProjectId, ShippingProjectDO::getId)
                .leftJoin(TrialShippingInventoryDO.class, TrialShippingInventoryDO::getFactoryFinishedId, FactoryFinishedInventoryDO::getId)
                .eq(ShippingRecordDO::getId, form.getShippingRecordId())
                .ne(FactoryFinishedInventoryDO::getDeliveryType, DeliveryTypeEnum.STOP_DELIVERY.getCode())
                .ge(form.isLimit() && StrUtil.isNotBlank(form.getShippingStartDate()),
                        TrialShippingInventoryDO::getRealShippingStartDate, form.getShippingStartDate())
                .orderByAsc(FactoryFinishedInventoryDO::getFactoryFinishedDate)
                .last(form.isLimit(), "limit 1")
        );
    }

    public Set<String> getSnapIds(InteriorInfoQuery infoQuery) {
        List<FactoryFinishedInventoryDO> list = baseMapper.selectList(Wrappers.<FactoryFinishedInventoryDO>lambdaQuery()
                .select(FactoryFinishedInventoryDO::getProductSnapshotId)
                .between(StrUtil.isNotBlank(infoQuery.getCreateStartDate()) && StrUtil.isNotBlank(infoQuery.getCreateEndDate()),
                        FactoryFinishedInventoryDO::getCreateDate, infoQuery.getCreateStartDate(), infoQuery.getCreateEndDate()));
        return CollectionUtil.isEmpty(list) ? Collections.emptySet() :
                list.stream()
                        .map(FactoryFinishedInventoryDO::getProductSnapshotId)
                        .collect(Collectors.toSet());
    }
}
