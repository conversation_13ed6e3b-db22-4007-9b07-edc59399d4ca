package com.sales_server.model.channel.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 渠道列表页
 * <AUTHOR>
 * @Date 2025/4/30 16:44
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelPageVo implements Serializable {
    private String nicheChannelId;

    private String nicheChannelName;

    private String saleChannelId;

    private String saleChannelName;

    private Integer sort;

    private String createDate;

    private String updateDate;

    private String remarks;
}
