package com.product.product_server.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.product.product_server.entity.dataObject.VirtualShippingRatioDO;
import com.product.product_server.mapper.VirtualShippingRatioMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 虚拟sku相关repository类
 * <AUTHOR>
 * @Date 2023/12/6 14:55
 **/
@Service
public class VirtualShippingRatioRepositoryImpl extends ServiceImpl<VirtualShippingRatioMapper, VirtualShippingRatioDO> {

    public List<VirtualShippingRatioDO> getVirtualShippingRatioList(List<String> virtualSkuId) {
        return baseMapper.selectList(Wrappers.<VirtualShippingRatioDO>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(virtualSkuId), VirtualShippingRatioDO::getVirtualSkuId, virtualSkuId));
    }

    /**
     * 根据条件删除发货比例信息
     *
     * @param virtualSkuId 虚拟skuid
     * @return 结果
     */
    public int deleteVirtualShippingRatio(String virtualSkuId){
        return baseMapper.delete(Wrappers.<VirtualShippingRatioDO>lambdaUpdate()
                .in(VirtualShippingRatioDO::getVirtualSkuId, virtualSkuId));
    }
}
