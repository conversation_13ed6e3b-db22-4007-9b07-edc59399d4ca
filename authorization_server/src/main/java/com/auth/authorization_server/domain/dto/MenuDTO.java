package com.auth.authorization_server.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/22
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MenuDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -4525094738019095634L;

    /**
     * 菜单ID
     */
    private String id;
    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 父菜单ID
     */
    private String parentId;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 路由名称
     */
    private String pathName;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 是否为外链（0是 1否）
     */
    private String isFrame;

    /**
     * 类型（0目录 1菜单 2按钮）
     */
    private String menuType;

    /**
     * 菜单状态（0正常 1停用）
     */
    private String menuStatus;

    /**
     * 权限字符串
     */
    private String perms;

    /**
     * 菜单图标
     */
    private String icon;

    private String url;

    /** 子菜单 */
    @Builder.Default
    private List<MenuDTO> children = new ArrayList<>();
}
