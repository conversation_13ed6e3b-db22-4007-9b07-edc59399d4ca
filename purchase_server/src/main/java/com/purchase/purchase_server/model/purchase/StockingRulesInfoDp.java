package com.purchase.purchase_server.model.purchase;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static com.purchase.purchase_server.enums.StockingRulesImportHeadEnum.*;


/**
 * @Description 发货计划导入-备货规则数据校验
 * <AUTHOR>
 * @Date 2023/12/7 10:56
 **/
public record StockingRulesInfoDp(HashMap<String, String> keyReplacementMap,
                                  Set<String> senboWarehouseSet) {
    public StockingRulesInfoDp(HashMap<String, String> keyReplacementMap, Set<String> senboWarehouseSet) {
        this.keyReplacementMap = keyReplacementMap;
        this.senboWarehouseSet = senboWarehouseSet;
        new ImportVirtualSkuInfoDp(keyReplacementMap.get(VIRTUAL_SKU.getCode()));
        this.checkNum(keyReplacementMap);
    }

    private void checkNum(HashMap<String, String> map) {
        Map<String, String> deliveryDaysMap = new HashMap<>();
        String safeDays = map.get(SAFE_DAYS.getCode());
        String shippingCircle = map.get(SHIPPING_CIRCLE.getCode());
        deliveryDaysMap.put("国内中转天数", map.get(TRANSIT_DAYS.getCode()));
        deliveryDaysMap.put("安全天数", safeDays);
        deliveryDaysMap.put("发货周期", shippingCircle);
        //校验map中的value是否为大于0正整数数字
        deliveryDaysMap.forEach((key, value) -> {
            if (!isValidNumber(key, value)) {
                throw new IllegalArgumentException(key + "-请正确填写数字");
            }
        });
        if (Integer.parseInt(safeDays) == 0) {
            throw new IllegalArgumentException("安全库存为期初库存，安全天数最小值为1");
        }

        if (Integer.parseInt(shippingCircle) == 0) {
            throw new IllegalArgumentException("发货周期最小值为1");
        }
    }

    // 检查值是否为整数数字且大于0的辅助方法
    private static boolean isValidNumber(String key, String value) {
        try {
            if (StrUtil.isNotBlank(value)) {
                return NumberUtil.isInteger(value) && Integer.parseInt(value) >= 0;
            } else {
                return false;
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException(key + "-请正确填写数字");
        }
    }
}
