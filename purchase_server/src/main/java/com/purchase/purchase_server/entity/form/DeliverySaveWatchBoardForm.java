package com.purchase.purchase_server.entity.form;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 发货计划详情保存参数
 * <AUTHOR>
 * @Date 2024/1/11 11:07
 **/
@Data
public class DeliverySaveWatchBoardForm implements Serializable {
    @Serial
    private static final long serialVersionUID = -1L;

    private String trialId;
    private Integer shippingAmount;
    private String warehouse;
    private String shippingStartDate;
    private String arrivalDate;
    private Integer perCaseNum;
    private Integer restAmount;
    private String destinationSku;
    private String factoryFinishedDate;
    private String factoryFinishedId;
}
