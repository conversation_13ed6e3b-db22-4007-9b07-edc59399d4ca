package com.purchase.purchase_server.service.lcl.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.enums.YesOrNoEnum;
import com.purchase.purchase_server.assembler.LclConsolidationAssembler;
import com.purchase.purchase_server.entity.bo.Lcl.LclFinishedInventoryBO;
import com.purchase.purchase_server.entity.dataObject.Lcl.*;
import com.purchase.purchase_server.entity.dto.Lcl.LclConsolidationNonFinishedInventoryDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclEditRemainNumDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclRemainNumDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclTrialShippingInventoryDTO;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.form.*;
import com.purchase.purchase_server.entity.vo.Lcl.LclConsolidationTrialNumVO;
import com.purchase.purchase_server.entity.vo.UserInteriorVO;
import com.purchase.purchase_server.enums.lcl.*;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.repository.dataRepository.Lcl.*;
import com.purchase.purchase_server.repository.interiorRepository.WarehouseRepository;
import com.purchase.purchase_server.service.ISysUserService;
import com.purchase.purchase_server.service.channel.IChannelInfoService;
import com.purchase.purchase_server.service.lcl.ILclFinishedInventoryService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.EDIT_LCL_SHIPPING_NUM_LOCK;

/**
 * <AUTHOR>
 * @description 针对表【cm_lcl_finished_inventory(拼柜装柜计划数据表)】的数据库操作Service实现
 * @createDate 2024-12-04 16:47:07
 */
@Service
@Slf4j
public class LclFinishedInventoryServiceImpl implements ILclFinishedInventoryService {

    @Resource
    private LclFinishedInventoryRepositoryImpl lclFinRepository;

    @Resource
    private LclTrialShippingInventoryRepositoryImpl lclTrialRepository;

    @Resource
    private LclConsolidationFinishedInventoryRepositoryImpl lclConsolidationFinRepository;

    @Resource
    private LclConsolidationTrialShippingInventoryRepositoryImpl lclConsolidationTrialRepository;

    @Resource
    private LclShippingNumHistoryRepositoryImpl lclHistoryRepository;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private LclConsolidationRecordRepositoryImpl lclRecordRepository;

    @Resource
    private WarehouseRepository warehouseRepository;

    @Resource
    private LclConsolidationAssembler lclConsolidationAssembler;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private IChannelInfoService channelInfoService;

    /**
     * 更新拼柜装柜计划的发货数量
     *
     * @param form 包含装柜计划ID和发货数量的表单
     * @return 更新是否成功
     * @throws BusinessException 当计划不存在或未安排数量不足时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLclShippingNum(LclShippingNumEditListNewForm form) {
        try {
            // 获取基础数据
            ShippingPlanInfo planInfo = getShippingPlanInfo(form);

            // 计算新的发货总数
            int newTotalShippingNum = calculateNewTotalShippingNum(form.getLclShippingNumEditNewList());

            if (planInfo.factoryShippingPackageNum() < newTotalShippingNum) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "未安排数量不足");
            }

            if (form.getIsLclConsolidation().equals(LclConsolidationEnum.NOT_CONSOLIDATION.getCode())) {
                handleNormalShipping(form, planInfo);
            } else {
                handleConsolidationShipping(form, planInfo);
            }
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, e.getMessage());
        } finally {
            stringRedisTemplate.delete(EDIT_LCL_SHIPPING_NUM_LOCK + form.getId());
        }
        return true;
    }


    private record ShippingPlanInfo(
            Integer factoryShippingPackageNum,
            Integer factoryRemainNum,
            Integer containerLoad,
            String id,
            String isForeignFlag
    ) {
    }

    /**
     * 获取装柜计划的基本信息
     *
     * @param form 装柜计划表单
     * @return 包含工厂发货数量、装载量和ID的计划信息
     * @throws BusinessException 当装柜计划不存在时抛出
     */
    private ShippingPlanInfo getShippingPlanInfo(LclShippingNumEditListNewForm form) {
        Integer factoryShippingPackageNum;
        Integer factoryRemainNum;

        Integer containerLoad;
        String id = form.getId();
        String recordId;
        String isForeignFlag;

        if (form.getIsLclConsolidation().equals(LclConsolidationEnum.NOT_CONSOLIDATION.getCode())) {
            LclFinishedInventoryDO inventory = lclFinRepository.getById(id);
            if (ObjectUtil.isEmpty(inventory)) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "装柜计划不存在");
            }
            factoryShippingPackageNum = inventory.getFactoryShippingPackageNum();
            factoryRemainNum = inventory.getFactoryRemainNum();

            var loadInfo = lclFinRepository.selectContainerLoad(id);
            containerLoad = loadInfo.getContainerLoad();
            recordId = inventory.getLclRecordId();
            isForeignFlag = inventory.getIsForeignFlag();
        } else {
            LclConsolidationFinishedInventoryDO inventory = lclConsolidationFinRepository.getById(id);
            if (ObjectUtil.isEmpty(inventory)) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "装柜计划不存在");
            }
            factoryShippingPackageNum = inventory.getFactoryShippingPackageNum();
            factoryRemainNum = inventory.getFactoryRemainNum();
            var loadInfo = lclConsolidationFinRepository.selectContainerLoad(id);
            containerLoad = loadInfo.getContainerLoad();
            recordId = inventory.getLclRecordId();
            isForeignFlag = inventory.getIsForeignFlag();

        }

        LclConsolidationRecordDO recordDO = lclRecordRepository.getById(recordId);
        if (ObjectUtil.isEmpty(recordDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "装柜计划不存在");
        }
        if (recordDO.getLclStatus().equals(LclStatusEnum.CANCELLED.getCode())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "装柜计划已取消，无法编辑");
        }

        return new ShippingPlanInfo(factoryShippingPackageNum, factoryRemainNum, containerLoad, id, isForeignFlag);
    }

    /**
     * 计算新的发货总数
     *
     * @param editList 发货数量编辑列表
     * @return 新的发货总数
     */
    private int calculateNewTotalShippingNum(List<LclShippingNumEditListForm> editList) {
        if (editList == null) {
            return 0;
        }
        return editList.stream()
                .filter(Objects::nonNull)
                .map(LclShippingNumEditListForm::getLclShippingNumEditList)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .map(LclShippingNumEditForm::getShippingNum)
                .filter(Objects::nonNull)
                .mapToInt(Integer::intValue)
                .sum();
    }

    /**
     * 验证库存是否足够
     *
     * @param form                装柜计划表单
     * @param planInfo            计划基本信息
     * @param newTotalShippingNum 新的发货总数
     * @throws BusinessException 当未安排数量不足时抛出
     */
    private void validateInventory(LclShippingNumEditListNewForm form, ShippingPlanInfo planInfo, int newTotalShippingNum) {
        int otherDatesShippingNum;

        if (form.getIsLclConsolidation().equals(LclConsolidationEnum.NOT_CONSOLIDATION.getCode())) {
            List<LclTrialShippingInventoryDO> trials = lclTrialRepository.list(
                    LclTrialShippingInventoryDTO.builder()
                            .lclFactoryFinishedId(form.getId())
                            .build());

            otherDatesShippingNum = calculateOtherDatesShippingNum(trials, form.getShippingDate(), form.getShippingStartDate(), form.getShippingEndDate());
        } else {
            List<LclConsolidationTrialShippingInventoryDO> trials =
                    lclConsolidationTrialRepository.list(form.getId(), null);

            otherDatesShippingNum = calculateOtherDatesConsolidationShippingNum(trials, form.getShippingDate(), form.getShippingStartDate(), form.getShippingEndDate());
        }

        if (newTotalShippingNum > planInfo.factoryRemainNum() + otherDatesShippingNum) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "未安排数量不足");
        }
    }

    private void handleNormalShipping(LclShippingNumEditListNewForm form, ShippingPlanInfo planInfo) {
        // 更新试算记录
        updateNormalTrials(form, planInfo);

    }

    private void handleConsolidationShipping(LclShippingNumEditListNewForm form, ShippingPlanInfo planInfo) {

        // 更新试算记录
        updateConsolidationTrials(form, planInfo);
        // 更新计划剩余数量
        updateConsolidationPlanRemaining(form.getId(), planInfo);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteLclFinished(LclFinishedDeleteForm form) {
        String id = form.getId();
        String shippingStartDate = form.getShippingStartDate();
        boolean isNotConsolidation = form.getIsLclConsolidation()
                .equals(LclConsolidationEnum.NOT_CONSOLIDATION.getCode());

        if (isNotConsolidation) {
            // 未整理数据删除逻辑
            lclTrialRepository.dropByIdFactoryFinishedIdAndShippingStartDate(id, shippingStartDate);
            // 如果试算记录为空,则删除计划
            List<LclTrialShippingInventoryDO> lclTrialList = lclTrialRepository.list(LclTrialShippingInventoryDTO.builder().lclFactoryFinishedId(id).build());
            if (lclTrialList.isEmpty()) {
                LclFinishedInventoryDO lclFinDO = lclFinRepository.getById(id);
                if (ObjectUtil.isNotEmpty(lclFinDO) && StrUtil.isBlank(lclFinDO.getLclForeignId())) {
                    lclFinRepository.removeById(id);
                }else if (ObjectUtil.isNotEmpty(lclFinDO) && StrUtil.isNotBlank(lclFinDO.getLclForeignId())){
                    String lclForeignId = lclFinDO.getLclForeignId();
                    // 还给在途数据
                    LclFinishedInventoryDO byId = lclFinRepository.getById(lclForeignId);
                    if (ObjectUtil.isEmpty(byId)) {
                        lclFinRepository.removeById(id);
                    } else {
                        Integer factoryShippingPackageNum = lclFinRepository.getById(id).getFactoryShippingPackageNum();
                        byId.setFactoryShippingPackageNum(byId.getFactoryShippingPackageNum() + factoryShippingPackageNum);
                        List<LclTrialShippingInventoryDO> lclTrialForeignList = lclTrialRepository.list(Wrappers.<LclTrialShippingInventoryDO>lambdaQuery()
                                .eq(LclTrialShippingInventoryDO::getLclFactoryFinishedId, lclForeignId));
                        LclTrialShippingInventoryDO first = lclTrialForeignList.getFirst();
                        var loadInfo = lclFinRepository.selectContainerLoad(id);
                        updateExistingNormalTrial(first, byId.getFactoryShippingPackageNum(), loadInfo.getContainerLoad());
                        lclTrialRepository.updateById(first);
                        lclFinRepository.updateById(byId);

                        lclFinRepository.removeById(id);
                    }
                }
            } else {
                // 更新剩余数量
                int sum = lclTrialList.stream().mapToInt(LclTrialShippingInventoryDO::getShippingNum).sum();
                LclFinishedInventoryDO finishedInventoryDO = lclFinRepository.getById(id);
                lclFinRepository.updateById(LclFinishedInventoryDO.builder()
                        .factoryRemainNum(finishedInventoryDO.getFactoryShippingPackageNum() - sum)
                        .build());
            }
        } else {
            // 拼柜删除逻辑
            var trialList = lclConsolidationTrialRepository.list(id, shippingStartDate);

            // 删除指定日期的试算记录和历史记录
            if (!trialList.isEmpty()) {
                lclConsolidationTrialRepository.delete(id, shippingStartDate);
                var trialIds = trialList.stream()
                        .map(LclConsolidationTrialShippingInventoryDO::getId)
                        .toList();
                lclHistoryRepository.delete(trialIds);
            }

            // 如果所有试算记录为空,则删除计划
            var remainingTrials = lclConsolidationTrialRepository.list(id, null);
            if (remainingTrials.isEmpty()) {
                lclConsolidationFinRepository.delete(id);
            } else {
                // 更新剩余数量
                int sum = remainingTrials.stream().mapToInt(LclConsolidationTrialShippingInventoryDO::getLclShippingNum).sum();
                LclConsolidationFinishedInventoryDO consolidationFinishedDO = lclConsolidationFinRepository.getById(id);
                var finishedInventory = LclConsolidationFinishedInventoryDO.builder()
                        .id(id)
                        .factoryRemainNum(consolidationFinishedDO.getFactoryShippingPackageNum() - sum)
                        .build();
                lclConsolidationFinRepository.updateById(finishedInventory);
            }
        }
        return true;
    }

    @Override
    public List<LclTrialShippingInventoryDTO> detailRemainNum(LclConsolidationForm form) {
        boolean isNotConsolidation = LclConsolidationEnum.NOT_CONSOLIDATION.getCode().equals(form.getIsConsolidation());

        List<LclFinishedInventoryBO> lclFinishedInventoryBOS = isNotConsolidation ? lclFinRepository.selectListById(form) : lclConsolidationFinRepository.selectListById(form);
        List<LclTrialShippingInventoryDTO> lclTrialDOList = lclFinishedInventoryBOS.getFirst().getLclTrialDTOList();
        if (lclTrialDOList.isEmpty()) {
            return new ArrayList<>();
        }
        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> warehouseMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

        String formShippingStartDateStr = form.getShippingStartDate();
        String formShippingEndDateStr = form.getShippingEndDate();

        Date parsedFormShippingStartDate = DateUtil.parse(formShippingStartDateStr);
        Date parsedFormShippingEndDate = DateUtil.parse(formShippingEndDateStr);

        List<LclTrialShippingInventoryDTO> filteredLclTrials = lclTrialDOList.stream()
                .filter(trial -> {
                    Date trialShippingStartDate = DateUtil.parse(trial.getShippingStartDate());
                    if (trialShippingStartDate == null) {
                        return false;
                    }
                    return trialShippingStartDate.before(parsedFormShippingStartDate) ||
                            trialShippingStartDate.after(parsedFormShippingEndDate);
                })
                .peek(trial -> {
                    trial.setDestinationWarehouse(warehouseMap.get(trial.getDestinationWarehouse()));
                    if (!isNotConsolidation){
                        trial.setShippingNum(trial.getLclShippingNum());
                    }
                })
                .sorted(java.util.Comparator.comparing(LclTrialShippingInventoryDTO::getShippingStartDate))
                .collect(Collectors.toList());

        int totalShippingNumOutsideDateRange = lclTrialDOList.stream()
                .mapToInt(trial -> isNotConsolidation ? trial.getShippingNum() : trial.getLclShippingNum())
                .sum();
        int factoryShippingPackageNum = lclFinishedInventoryBOS.getFirst().getFactoryShippingPackageNum() - totalShippingNumOutsideDateRange;
        if (factoryShippingPackageNum > 0) {
            filteredLclTrials.add(LclTrialShippingInventoryDTO.builder()
                    .shippingNum(factoryShippingPackageNum)
                    .lclShippingNum(factoryShippingPackageNum).build());
        }
        return filteredLclTrials;
    }

    /**
     * 未安排数量编辑列表
     *
     * @param form
     * @return
     */
    @Override
    public LclEditRemainNumDTO detailRemainNumForEditList(LclConsolidationForm form) {

        boolean isNotConsolidation = LclConsolidationEnum.NOT_CONSOLIDATION.getCode().equals(form.getIsConsolidation());

        List<LclFinishedInventoryBO> lclFinishedInventoryBOS = isNotConsolidation ? lclFinRepository.selectListById(form) : lclConsolidationFinRepository.selectListById(form);
        LclFinishedInventoryBO lclFinishedInventoryBO = lclFinishedInventoryBOS.getFirst();
        List<LclTrialShippingInventoryDTO> lclTrialDOList = lclFinishedInventoryBO.getLclTrialDTOList();

        Integer containerLoad = isNotConsolidation ? lclFinRepository.selectContainerLoad(lclFinishedInventoryBO.getId()).getContainerLoad() :
                lclConsolidationFinRepository.selectContainerLoad(lclFinishedInventoryBO.getId()).getContainerLoad();
        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> warehouseMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

        // 列表数据
        TreeMap<String, List<LclTrialShippingInventoryDTO>> groupedResult =
                lclTrialDOList.stream()
                        .collect(Collectors.groupingBy(
                                        LclTrialShippingInventoryDTO::getShippingStartDate,
                                        TreeMap::new,  // 使用TreeMap保证日期顺序
                                        Collectors.toList()
                                )
                        );

        LclEditRemainNumDTO editRemainNumDTO = new LclEditRemainNumDTO();
        editRemainNumDTO.setId(lclFinishedInventoryBO.getId());

        List<LclRemainNumDTO> otherDateEntriesDtoList = new ArrayList<>(); // 用于存储其他符合条件日期的数据

        for (var lclTrialInventory : groupedResult.entrySet()) {
            String shippingStartDate = lclTrialInventory.getKey();
            List<LclTrialShippingInventoryDTO> value = lclTrialInventory.getValue(); // 当前日期的所有试算记录
            List<LclConsolidationTrialNumVO> trialNumList = new ArrayList<>();

            for (val lclTrial : value) {
                Integer shippingNum = isNotConsolidation ? lclTrial.getShippingNum() : lclTrial.getLclShippingNum();
                if (shippingNum > 0){
                    trialNumList.add(LclConsolidationTrialNumVO.builder()
                            .shippingNum(shippingNum)
                            .warehouse(warehouseMap.get(lclTrial.getDestinationWarehouse()))
                            .build());
                }
            }

            LclRemainNumDTO currentDto = LclRemainNumDTO.builder()
                    .shippingStartDate(shippingStartDate)
                    .containerLoad(containerLoad)
                    .trialNumList(trialNumList)
                    .build();

            otherDateEntriesDtoList.add(currentDto);


            // 此处操作是为了将数据放在第一条
            //if (trialShippingStartDate.compareTo(shippingDate) == 0) {
            //    targetDateEntryDto = currentDto;
            //} else if (trialShippingStartDate.compareTo(parsedFormShippingStartDate) < 0 ||
            //        trialShippingStartDate.compareTo(parsedFormShippingEndDate) > 0) {
            //    otherDateEntriesDtoList.add(currentDto);
            //}
        }

        editRemainNumDTO.setTotalRemainNum(lclFinishedInventoryBO.getFactoryShippingPackageNum());
        otherDateEntriesDtoList.sort(Comparator.comparing(LclRemainNumDTO::getShippingStartDate));
        editRemainNumDTO.setLclRemainNumList(otherDateEntriesDtoList);
        return editRemainNumDTO;
    }

    /**
     * 计算除指定日期外的其他日期的发货数量总和
     *
     * @param trials 试算记录列表
     * @return 其他日期的发货数量总和
     */
    private int calculateOtherDatesShippingNum(List<LclTrialShippingInventoryDO> trials, String shippingDate, String shippingStartDate, String shippingEndDate) {
        Date trialShippingDate = DateUtil.parse(shippingDate);
        Date parsedFormShippingStartDate = DateUtil.parse(shippingStartDate);
        Date parsedFormShippingEndDate = DateUtil.parse(shippingEndDate);

        return trials.stream()
                .filter(trial -> {
                    Date trialShippingStartDate = trial.getShippingStartDate();
                    return trialShippingStartDate.compareTo(parsedFormShippingStartDate) < 0 ||
                            trialShippingStartDate.compareTo(parsedFormShippingEndDate) > 0;
                })
                .mapToInt(LclTrialShippingInventoryDO::getShippingNum)
                .sum();
    }

    /**
     * 计算除指定日期外的其他日期的拼柜发货数量总和
     *
     * @param trials 拼柜试算记录列表
     * @return 其他日期的拼柜发货数量总和
     */
    private int calculateOtherDatesConsolidationShippingNum(List<LclConsolidationTrialShippingInventoryDO> trials,
                                                            String shippingDate, String shippingStartDate, String shippingEndDate) {
        Date trialShippingDate = DateUtil.parse(shippingDate);
        Date parsedFormShippingStartDate = DateUtil.parse(shippingStartDate);
        Date parsedFormShippingEndDate = DateUtil.parse(shippingEndDate);

        return trials.stream()
                .filter(trial -> {
                    Date trialShippingStartDate = trial.getShippingStartDate();
                    return trialShippingStartDate.compareTo(parsedFormShippingStartDate) < 0 ||
                            trialShippingStartDate.compareTo(parsedFormShippingEndDate) > 0;
                })
                .mapToInt(LclConsolidationTrialShippingInventoryDO::getLclShippingNum)
                .sum();
    }

    /**
     * 处理普通发货的试算记录更新
     *
     * @param form     装柜计划表单
     * @param planInfo 计划基本信息
     */
    private void updateNormalTrials(LclShippingNumEditListNewForm form, ShippingPlanInfo planInfo) {


        List<LclTrialShippingInventoryDO> trialsToSave = new ArrayList<>();
        LclFinishedInventoryDO inventory = lclFinRepository.getById(form.getId());
        String lclForeignId = inventory.getLclForeignId();
        boolean foreignWarehouseFlag = false;

        String foreignSonId = null;
        // 处理每个仓库的发货数量
        if (CollectionUtil.isNotEmpty(form.getLclShippingNumEditNewList())) {
            for (LclShippingNumEditListForm editForm : form.getLclShippingNumEditNewList()) {
                List<LclShippingNumEditForm> lclShippingNumEditList = editForm.getLclShippingNumEditList();

                for (var trialForm : lclShippingNumEditList) {
                    String warehouse = trialForm.getDestinationWarehouse();
                    Integer newShippingNum = trialForm.getShippingNum();

                    if (!inventory.getIsForeignFlag().equals("1")) {
                        // 创建新的试算记录
                        trialsToSave.add(createNewNormalTrial(editForm, planInfo, trialForm));
                    }
                    // 在途
                    else if (StrUtil.isBlank(lclForeignId)) {

                        List<LclTrialShippingInventoryDO> lclTrialForeignList = lclTrialRepository.list(Wrappers.<LclTrialShippingInventoryDO>lambdaQuery()
                                .eq(LclTrialShippingInventoryDO::getLclFactoryFinishedId, inventory.getId()));
                        LclTrialShippingInventoryDO trialForeignDO = lclTrialForeignList.getFirst();
                        String destinationForeignWarehouse = trialForeignDO.getDestinationWarehouse();

                        if (destinationForeignWarehouse.equals(warehouse)) {
                            foreignWarehouseFlag = true;
                            trialForeignDO.setShippingNum(newShippingNum);
                            trialForeignDO.setIsPackageFull(isPackageFull(newShippingNum, planInfo.containerLoad()));
                            trialForeignDO.setPackageNum(calculateContainers(newShippingNum, planInfo.containerLoad()));
                            lclTrialRepository.updateById(trialForeignDO);
                        } else {
                            List<LclFinishedInventoryDO> lclForeignList = lclFinRepository.list(Wrappers.<LclFinishedInventoryDO>lambdaQuery()
                                    .eq(LclFinishedInventoryDO::getLclForeignId, inventory.getId()));
                            if (CollectionUtil.isNotEmpty(lclForeignList)) {
                                LclFinishedInventoryDO lclForeign = lclForeignList.getFirst();
                                var newLclForeignList = lclTrialRepository.list(Wrappers.<LclTrialShippingInventoryDO>lambdaQuery()
                                        .eq(LclTrialShippingInventoryDO::getLclFactoryFinishedId, lclForeign.getId()));
                                Map<String, List<LclTrialShippingInventoryDO>> groupedResult =
                                        newLclForeignList.stream()
                                                .collect(Collectors.groupingBy(LclTrialShippingInventoryDO::getDestinationWarehouse));
                                List<LclTrialShippingInventoryDO> otherList = groupedResult.getOrDefault(warehouse, new ArrayList<>());
                                if (CollectionUtil.isNotEmpty(otherList)) {
                                    LclTrialShippingInventoryDO newTrialsToSave = otherList.getFirst();
                                    Integer shippingNum = newTrialsToSave.getShippingNum();
                                    newTrialsToSave.setShippingNum(newShippingNum + shippingNum);
                                    newTrialsToSave.setIsPackageFull(isPackageFull(newShippingNum + shippingNum, planInfo.containerLoad()));
                                    newTrialsToSave.setPackageNum(calculateContainers(newShippingNum + shippingNum, planInfo.containerLoad()));
                                    lclTrialRepository.updateById(newTrialsToSave);
                                    foreignSonId = lclForeign.getId();
                                } else {
                                    LclTrialShippingInventoryDO newNormalTrial = createNewNormalTrial(editForm, planInfo, trialForm);
                                    newNormalTrial.setLclFactoryFinishedId(lclForeign.getId());
                                    newNormalTrial.setIsPackageFull(isPackageFull(newNormalTrial.getShippingNum(), planInfo.containerLoad()));
                                    lclTrialRepository.save(newNormalTrial);
                                    foreignSonId = lclForeign.getId();
                                }
                            } else {
                                LclFinishedInventoryDO newInventory = lclConsolidationAssembler.lclFinishedInventoryDO(inventory);
                                newInventory.setId(null);
                                newInventory.setShipmentCode(null);
                                newInventory.setContractCode(null);
                                newInventory.setIsForeignFlag("0");
                                newInventory.setFactoryRemainNum(0);
                                newInventory.setFactoryShippingPackageNum(newShippingNum);
                                newInventory.setLclForeignId(inventory.getId());
                                lclFinRepository.save(newInventory);
                                LclTrialShippingInventoryDO newNormalTrial = createNewNormalTrial(editForm, planInfo, trialForm);
                                newNormalTrial.setLclFactoryFinishedId(newInventory.getId());
                                newNormalTrial.setIsPackageFull(isPackageFull(newNormalTrial.getShippingNum(), planInfo.containerLoad()));
                                lclTrialRepository.save(newNormalTrial);
                            }
                        }
                    }
                }
            }
        }
        if (inventory.getIsForeignFlag().equals("1")) {
            if (!foreignWarehouseFlag) {
                lclFinRepository.removeById(inventory.getId());
                lclTrialRepository.removeById(inventory.getId());
            } else {
                updateOrDeleteNormalPlan(inventory.getId(), planInfo);
                if (StrUtil.isNotBlank(foreignSonId)) {
                    LclFinishedInventoryDO byId = lclFinRepository.getById(foreignSonId);
                    List<LclTrialShippingInventoryDO> allTrials = lclTrialRepository.list(
                            LclTrialShippingInventoryDTO.builder()
                                    .lclFactoryFinishedId(foreignSonId)
                                    .build());
                    int otherDatesTotal = allTrials.stream().map(LclTrialShippingInventoryDO::getShippingNum)
                            .mapToInt(Integer::intValue)
                            .sum();
                    byId.setFactoryShippingPackageNum(otherDatesTotal + byId.getFactoryRemainNum());
                    lclFinRepository.updateById(byId);
                }
            }
        } else {
            if (StrUtil.isBlank(lclForeignId)) {
                lclTrialRepository.removeById(inventory.getId());
                if (CollectionUtil.isNotEmpty(trialsToSave)) {
                    lclTrialRepository.saveOrUpdateBatch(trialsToSave);
                }
                updateOrDeleteNormalPlan(inventory.getId(), planInfo);
            } else {
                if (CollectionUtil.isNotEmpty(trialsToSave)) {
                    lclTrialRepository.removeById(inventory.getId());
                    lclTrialRepository.saveOrUpdateBatch(trialsToSave);
                    updateOrDeleteNormalPlan(inventory.getId(), planInfo);
                } else {
                    // 还给在途数据
                    LclFinishedInventoryDO byId = lclFinRepository.getById(lclForeignId);
                    if (ObjectUtil.isEmpty(byId)) {
                        lclFinRepository.removeById(inventory.getId());
                        lclTrialRepository.removeById(inventory.getId());
                    } else {
                        Integer factoryShippingPackageNum = lclFinRepository.getById(inventory.getId()).getFactoryShippingPackageNum();
                        byId.setFactoryShippingPackageNum(byId.getFactoryShippingPackageNum() + factoryShippingPackageNum);
                        List<LclTrialShippingInventoryDO> lclTrialForeignList = lclTrialRepository.list(Wrappers.<LclTrialShippingInventoryDO>lambdaQuery()
                                .eq(LclTrialShippingInventoryDO::getLclFactoryFinishedId, lclForeignId));
                        LclTrialShippingInventoryDO first = lclTrialForeignList.getFirst();
                        updateExistingNormalTrial(first, byId.getFactoryShippingPackageNum(), planInfo.containerLoad);
                        lclTrialRepository.updateById(first);
                        lclFinRepository.updateById(byId);

                        lclFinRepository.removeById(inventory.getId());
                        lclTrialRepository.removeById(inventory.getId());
                    }
                }
            }
        }
    }

    /**
     * 创建新的普通发货试算记录
     *
     * @param form     装柜计划表单
     * @param planInfo 计划基本信息
     * @param editForm 发货数量编辑表单
     * @return 新的试算记录
     */
    private LclTrialShippingInventoryDO createNewNormalTrial(LclShippingNumEditListForm form,
                                                             ShippingPlanInfo planInfo, LclShippingNumEditForm editForm) {
        int containers = calculateContainers(editForm.getShippingNum(), planInfo.containerLoad());
        return LclTrialShippingInventoryDO.builder()
                .shippingStartDate(DateUtil.parse(form.getShippingStartDate()))
                .packageNum(containers)
                .destinationWarehouse(editForm.getDestinationWarehouse())
                .lclFactoryFinishedId(planInfo.id())
                .shippingNum(editForm.getShippingNum())
                .isPackageFull(isPackageFull(editForm.getShippingNum(), planInfo.containerLoad()))
                .build();
    }

    /**
     * 更新现有的普通发货试算记录
     *
     * @param trial          现有试算记录
     * @param newShippingNum 新的发货数量
     * @param containerLoad  装载量
     */
    private void updateExistingNormalTrial(LclTrialShippingInventoryDO trial,
                                           Integer newShippingNum, Integer containerLoad) {
        int containers = calculateContainers(newShippingNum, containerLoad);
        trial.setShippingNum(newShippingNum);
        trial.setPackageNum(containers);
        trial.setIsPackageFull(isPackageFull(newShippingNum, containerLoad));
    }

    /**
     * 处理拼柜发货的试算记录更新
     *
     * @param form              装柜计划表单
     * @param planInfo          计划基本信息
     */
    private void updateConsolidationTrials(
                                           LclShippingNumEditListNewForm form, ShippingPlanInfo planInfo) {

        LclConsolidationFinishedInventoryDO inventory = lclConsolidationFinRepository.getById(form.getId());
        String isForeignFlag = inventory.getIsForeignFlag();
        // 仓库并集
        var conTriallist = lclConsolidationTrialRepository.list(Wrappers.<LclConsolidationTrialShippingInventoryDO>lambdaQuery()
                .eq(LclConsolidationTrialShippingInventoryDO::getConsolidationFactoryFinishedId, inventory.getId()));

        int sum = conTriallist.stream().mapToInt(LclConsolidationTrialShippingInventoryDO::getLclShippingNum).sum();
        int factoryRemainNum = inventory.getFactoryShippingPackageNum() - sum;
        Set<String> warehouseSet = new HashSet<>(conTriallist.stream().map(LclConsolidationTrialShippingInventoryDO::getDestinationWarehouse).toList());
        // 将conTriallist先按shippingStartDate分组，再按destinationWarehouse分组
        TreeMap<String, Map<String, LclConsolidationTrialShippingInventoryDO>> oldGroupedResult = conTriallist.stream()
                .collect(Collectors.groupingBy(
                        trial -> DateUtil.format(trial.getShippingStartDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN),
                        TreeMap::new,
                        Collectors.toMap(
                                LclConsolidationTrialShippingInventoryDO::getDestinationWarehouse,
                                trial -> trial
                        )
                ));


        TreeMap<String, Map<String, LclShippingNumEditForm>> newGroupedResult = new TreeMap<>();
        // 将新的数据也放到集合中
        if (CollectionUtil.isNotEmpty(form.getLclShippingNumEditNewList())) {
            for (LclShippingNumEditListForm editForm : form.getLclShippingNumEditNewList()) {
                String shippingStartDate = editForm.getShippingStartDate();
                List<LclShippingNumEditForm> lclShippingNumEditList = editForm.getLclShippingNumEditList();
                Map<String, LclShippingNumEditForm> warehouseMap = new HashMap<>();
                if (CollectionUtil.isNotEmpty(lclShippingNumEditList)) {
                    lclShippingNumEditList.forEach(i -> {
                        warehouseMap.put(i.getDestinationWarehouse(), i);
                        warehouseSet.add(i.getDestinationWarehouse());
                    });
                    newGroupedResult.put(shippingStartDate, warehouseMap);
                }
            }
        }
        for (var oldStartDateMap : oldGroupedResult.entrySet()) {
            String shippingStartDate = oldStartDateMap.getKey();
            var oldWarehouseMap = oldStartDateMap.getValue();
            Map<String, LclShippingNumEditForm> newWarehouseMap = newGroupedResult.getOrDefault(shippingStartDate, new HashMap<>());
            for (var warehouse : warehouseSet) {
                var oldLclConTrialDO = oldWarehouseMap.get(warehouse);
                LclShippingNumEditForm lclShippingNumEditForm = null;
                if (CollectionUtil.isNotEmpty(newWarehouseMap)) {
                    lclShippingNumEditForm = newWarehouseMap.get(warehouse);
                }
                // 都挪走啦
                if (lclShippingNumEditForm == null) {
                    if (ObjectUtil.isNotEmpty(oldLclConTrialDO)) {
                        if (oldLclConTrialDO.getLclShippingNum() != 0) {
                            if (isForeignFlag.equals("1")) {
                                oldLclConTrialDO.setLclShippingNum(0);
                                oldLclConTrialDO.setPackageNum(0);
                                oldLclConTrialDO.setIsChange(IsChangeEnum.CHANGED.getCode());
                                oldLclConTrialDO.setIsPackageFull(IsPackageFullEnum.FULL.getCode());
                                lclConsolidationTrialRepository.updateById(oldLclConTrialDO);
                            } else {
                                Integer lclShippingNum = oldLclConTrialDO.getLclShippingNum();
                                oldLclConTrialDO.setLclShippingNum(0);
                                oldLclConTrialDO.setPackageNum(0);
                                oldLclConTrialDO.setIsChange(IsChangeEnum.CHANGED.getCode());
                                oldLclConTrialDO.setIsPackageFull(IsPackageFullEnum.FULL.getCode());
                                lclConsolidationTrialRepository.updateById(oldLclConTrialDO);
                                saveShippingHistory(oldLclConTrialDO.getId(), -lclShippingNum, ChangeTypeEnum.MANUAL_EDIT.getDesc());
                            }
                        }
                    }
                }
                // 可能挪了也可能没挪
                else {
                    if (ObjectUtil.isNotEmpty(oldLclConTrialDO)) {
                        if (!Objects.equals(lclShippingNumEditForm.getShippingNum(), oldLclConTrialDO.getLclShippingNum())) {
                            if (isForeignFlag.equals("1")) {
                                Integer lclShippingNum = oldLclConTrialDO.getLclShippingNum();
                                oldLclConTrialDO.setLclShippingNum(lclShippingNumEditForm.getShippingNum());
                                oldLclConTrialDO.setPackageNum(calculateContainers(lclShippingNumEditForm.getShippingNum(), planInfo.containerLoad()));
                                oldLclConTrialDO.setIsChange(IsChangeEnum.CHANGED.getCode());
                                oldLclConTrialDO.setIsPackageFull(isPackageFull(lclShippingNumEditForm.getShippingNum(), planInfo.containerLoad()));
                                lclConsolidationTrialRepository.updateById(oldLclConTrialDO);
                                saveShippingHistory(oldLclConTrialDO.getId(), lclShippingNumEditForm.getShippingNum() - lclShippingNum, ChangeTypeEnum.MANUAL_EDIT.getDesc());
                            } else {
                                Integer lclShippingNum = oldLclConTrialDO.getLclShippingNum();
                                oldLclConTrialDO.setLclShippingNum(lclShippingNumEditForm.getShippingNum());
                                oldLclConTrialDO.setPackageNum(calculateContainers(lclShippingNumEditForm.getShippingNum(), planInfo.containerLoad()));
                                oldLclConTrialDO.setIsChange(IsChangeEnum.CHANGED.getCode());
                                oldLclConTrialDO.setIsPackageFull(isPackageFull(lclShippingNumEditForm.getShippingNum(), planInfo.containerLoad()));
                                lclConsolidationTrialRepository.updateById(oldLclConTrialDO);
                                saveShippingHistory(oldLclConTrialDO.getId(), lclShippingNumEditForm.getShippingNum() - lclShippingNum, ChangeTypeEnum.MANUAL_EDIT.getDesc());
                            }
                        }
                    }
                    // 创建新计划
                    else {
                        if (isForeignFlag.equals("1")) {
                            // 在途已经被拆出的计划
                            var lclConSonList = lclConsolidationFinRepository.list(Wrappers.<LclConsolidationFinishedInventoryDO>lambdaQuery()
                                    .eq(LclConsolidationFinishedInventoryDO::getLclForeignId, inventory.getId()));

                            if (CollectionUtil.isNotEmpty(lclConSonList)) {
                                LclConsolidationFinishedInventoryDO lclConSon = lclConSonList.getFirst();
                                var lclConSonTrialList = lclConsolidationTrialRepository.list(Wrappers.<LclConsolidationTrialShippingInventoryDO>lambdaQuery()
                                        .eq(LclConsolidationTrialShippingInventoryDO::getConsolidationFactoryFinishedId, lclConSon.getId()));
                                if (CollectionUtil.isNotEmpty(lclConSonTrialList)) {
                                    var lclConTrialShippingInventoryDO = lclConSonTrialList.stream().filter(item -> item.getDestinationWarehouse().equals(warehouse)).findFirst().orElse(null);
                                    if (lclConTrialShippingInventoryDO != null) {
                                        int newLclShippingNum = lclConTrialShippingInventoryDO.getLclShippingNum() + lclShippingNumEditForm.getShippingNum();
                                        lclConTrialShippingInventoryDO.setLclShippingNum(newLclShippingNum);
                                        lclConTrialShippingInventoryDO.setPackageNum(calculateContainers(lclShippingNumEditForm.getShippingNum(), planInfo.containerLoad()));
                                        lclConTrialShippingInventoryDO.setIsChange(IsChangeEnum.CHANGED.getCode());
                                        lclConTrialShippingInventoryDO.setIsPackageFull(isPackageFull(lclShippingNumEditForm.getShippingNum(), planInfo.containerLoad()));
                                        lclConsolidationTrialRepository.updateById(lclConTrialShippingInventoryDO);
                                        saveShippingHistory(lclConTrialShippingInventoryDO.getId(), lclShippingNumEditForm.getShippingNum(), inventory.getShipmentCode() + "移出");
                                        lclConSon.setFactoryShippingPackageNum(lclConSon.getFactoryShippingPackageNum() + lclShippingNumEditForm.getShippingNum() + factoryRemainNum);
                                        lclConsolidationFinRepository.updateById(lclConSon);
                                    } else {
                                        var newConsolidationTrial = createNewConsolidationTrial(shippingStartDate, planInfo, lclShippingNumEditForm);
                                        newConsolidationTrial.setConsolidationFactoryFinishedId(lclConSon.getId());
                                        lclConsolidationTrialRepository.save(newConsolidationTrial);
                                        saveShippingHistory(newConsolidationTrial.getId(), lclShippingNumEditForm.getShippingNum(), inventory.getShipmentCode() + "移出");
                                        lclConSon.setFactoryShippingPackageNum(lclConSon.getFactoryShippingPackageNum() + lclShippingNumEditForm.getShippingNum() + factoryRemainNum);
                                        lclConsolidationFinRepository.updateById(lclConSon);
                                    }
                                }
                            } else {
                                // 在途拆出新的计划
                                var lclConFinSonDO = lclConsolidationAssembler.lclConFinishedInventoryDO(inventory);
                                lclConFinSonDO.setId(null);
                                lclConFinSonDO.setContractCode(null);
                                lclConFinSonDO.setShipmentCode(null);
                                lclConFinSonDO.setIsForeignFlag("0");
                                lclConFinSonDO.setOriginalShippingNum(0);
                                lclConFinSonDO.setFactoryRemainNum(0);
                                lclConFinSonDO.setFactoryShippingPackageNum(lclShippingNumEditForm.getShippingNum());
                                lclConFinSonDO.setLclForeignId(inventory.getId());
                                lclConsolidationFinRepository.save(lclConFinSonDO);
                                var newConsolidationTrial = createNewConsolidationTrial(shippingStartDate, planInfo, lclShippingNumEditForm);
                                newConsolidationTrial.setConsolidationFactoryFinishedId(lclConFinSonDO.getId());
                                lclConsolidationTrialRepository.save(newConsolidationTrial);
                                saveShippingHistory(newConsolidationTrial.getId(), newConsolidationTrial.getLclShippingNum(), inventory.getShipmentCode() + "移出");
                            }
                        } else {
                            var newConsolidationTrial = createNewConsolidationTrial(shippingStartDate, planInfo, lclShippingNumEditForm);
                            lclConsolidationTrialRepository.save(newConsolidationTrial);
                            saveShippingHistory(newConsolidationTrial.getId(), newConsolidationTrial.getLclShippingNum(), ChangeTypeEnum.MANUAL_EDIT.getDesc());
                        }
                    }
                }
            }

        }


    }

    /**
     * 创建新的拼柜发货试算记录
     *
     * @param shippingStartDate 装柜装柜时间
     * @param planInfo          计划基本信息
     * @param editForm          发货数量编辑表单
     * @return 新的拼柜试算记录
     */
    private LclConsolidationTrialShippingInventoryDO createNewConsolidationTrial(
            String shippingStartDate, ShippingPlanInfo planInfo, LclShippingNumEditForm editForm) {
        int containers = calculateContainers(editForm.getShippingNum(), planInfo.containerLoad());
        return LclConsolidationTrialShippingInventoryDO.builder()
                .shippingStartDate(DateUtil.parse(shippingStartDate))
                .packageNum(containers)
                .destinationWarehouse(editForm.getDestinationWarehouse())
                .consolidationFactoryFinishedId(planInfo.id())
                .shippingNum(0)
                .lclShippingNum(editForm.getShippingNum())
                .isChange(IsChangeEnum.CHANGED.getCode())
                .isPackageFull(isPackageFull(editForm.getShippingNum(), planInfo.containerLoad()))
                .build();
    }

    /**
     * 更新现有的拼柜发货试算记录
     *
     * @param trial          现有拼柜试算记录
     * @param newShippingNum 新的发货数量
     * @param containerLoad  装载量
     */
    private void updateExistingConsolidationTrial(LclConsolidationTrialShippingInventoryDO trial,
                                                  Integer newShippingNum, Integer containerLoad) {
        int containers = calculateContainers(newShippingNum, containerLoad);
        trial.setLclShippingNum(newShippingNum);
        trial.setPackageNum(containers);
        trial.setIsPackageFull(isPackageFull(newShippingNum, containerLoad));
        trial.setIsChange(IsChangeEnum.CHANGED.getCode());
    }

    /**
     * 更新或删除普通发货计划
     *
     * @param id       计划ID
     * @param planInfo 计划基本信息
     */
    private void updateOrDeleteNormalPlan(String id, ShippingPlanInfo planInfo) {
        List<LclTrialShippingInventoryDO> allTrials = lclTrialRepository.list(
                LclTrialShippingInventoryDTO.builder()
                        .lclFactoryFinishedId(id)
                        .build());
        if (allTrials.isEmpty()) {
            lclFinRepository.removeById(id);
        } else {
            int otherDatesTotal = allTrials.stream().map(LclTrialShippingInventoryDO::getShippingNum)
                    .mapToInt(Integer::intValue)
                    .sum();
            LclFinishedInventoryDO finishedInventory = LclFinishedInventoryDO.builder()
                    .id(id)
                    .build();
            if (!planInfo.isForeignFlag().equals("1")) {
                finishedInventory.setFactoryRemainNum(planInfo.factoryShippingPackageNum() - otherDatesTotal);
            } else {
                finishedInventory.setFactoryShippingPackageNum(otherDatesTotal);
                finishedInventory.setFactoryRemainNum(0);
            }
            lclFinRepository.updateById(finishedInventory);
        }
    }

    /**
     * 更新拼柜计划的剩余数量
     *
     * @param id       计划ID
     * @param planInfo 计划基本信息
     */
    private void updateConsolidationPlanRemaining(String id, ShippingPlanInfo planInfo) {
        List<LclConsolidationTrialShippingInventoryDO> allTrials =
                lclConsolidationTrialRepository.list(id, null);

        int otherDatesTotal = allTrials.stream().map(LclConsolidationTrialShippingInventoryDO::getLclShippingNum)
                .mapToInt(Integer::intValue)
                .sum();
        var finishedInventory = LclConsolidationFinishedInventoryDO.builder()
                .id(id)
                .factoryRemainNum(planInfo.factoryShippingPackageNum() - otherDatesTotal)
                .build();
        LclConsolidationFinishedInventoryDO byId = lclConsolidationFinRepository.getById(id);
        if (byId.getIsForeignFlag().equals("1")){
            finishedInventory.setFactoryShippingPackageNum(otherDatesTotal);
            finishedInventory.setFactoryRemainNum(0);
            lclConsolidationFinRepository.updateById(finishedInventory);
        }

    }

    /**
     * 保存发货数量变更历史
     *
     * @param trialId     试算记录ID
     * @param changeValue 变更数量
     * @param changeType  变更类型
     */
    private void saveShippingHistory(String trialId, Integer changeValue, String changeType) {
        lclHistoryRepository.save(LclShippingNumHistoryDO.builder()
                .lclConsolidationTsiId(trialId)
                .changeValue(changeValue)
                .changeType(changeType)
                .build());
    }

    /**
     * 计算所需集装箱数量
     *
     * @param shippingNum   发货数量
     * @param containerLoad 装载量
     * @return 所需集装箱数量
     */
    private int calculateContainers(Integer shippingNum, Integer containerLoad) {
        return (shippingNum + containerLoad - 1) / containerLoad;
    }

    /**
     * 判断集装箱是否装满
     *
     * @param shippingNum   发货数量
     * @param containerLoad 装载量
     * @return 装满状态码
     */
    private String isPackageFull(Integer shippingNum, Integer containerLoad) {
        return shippingNum % containerLoad == 0 ?
                IsPackageFullEnum.FULL.getCode() :
                IsPackageFullEnum.NOT_FULL.getCode();
    }

    @Override
    public List<LclConsolidationNonFinishedInventoryDTO> listByRecordId(String recordId) {
        var nonFinishedInventoryDTOS = lclFinRepository.listByLclRecordId(recordId);

        if (CollectionUtil.isEmpty(nonFinishedInventoryDTOS)){
            return new ArrayList<>();
        }
        Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();
        List<UserInteriorVO> userList = sysUserService.getUserList();
        Map<String, String> userMap = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        nonFinishedInventoryDTOS.forEach(item -> {
            double volume = (item.getCaseLength() * item.getCaseWidth() * item.getCaseHeight()) / 1000000.0;
            item.setCaseVolume(new BigDecimal(volume).setScale(4, RoundingMode.HALF_UP).doubleValue());
            item.setPurchaser(userMap.getOrDefault(item.getPurchaser(), item.getPurchaser()));
            item.setOrderTracker(userMap.getOrDefault(item.getOrderTracker(), item.getOrderTracker()));
            item.setBuyer(userMap.getOrDefault(item.getBuyer(), item.getBuyer()));
            item.setChannel(channelIdNameMap.getOrDefault(item.getChannel(), item.getChannel()));
            String operator = item.getOperator();
            if (StrUtil.isNotBlank(operator)) {
                String operatorNames = Arrays.stream(operator.split(",", 0))
                        .map(op -> StrUtil.isNotBlank(userMap.get(op)) ? userMap.get(op) : op)
                        .collect(Collectors.joining(","));
                item.setOperator(operatorNames);
            };
            YesOrNoEnum.ofCode(item.getCommodityInspection())
                    .ifPresent(orNoEnum -> item.setCommodityInspection(orNoEnum.getDesc()));
            PCSTypeEnum.ofCode(item.getPcsType())
                    .ifPresent(pCSTypeEnum -> item.setPcsType(pCSTypeEnum.getDesc()));
        });
        return nonFinishedInventoryDTOS;
    }
}




