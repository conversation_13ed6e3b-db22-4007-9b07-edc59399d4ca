package com.purchase.purchase_server.service.impl;

import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.enums.UserPostEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.entity.form.UserParams;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.OperatorSearchVo;
import com.purchase.purchase_server.entity.vo.UserInteriorVO;
import com.purchase.purchase_server.service.ISysUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

import static com.crafts_mirror.utils.constant.SystemConstant.SYS_USER_INFO;
import static com.crafts_mirror.utils.constant.SystemConstant.SYS_USER_LIST_URL;

@Service
@Slf4j
/**
 * <AUTHOR>
 * @date 2024/4/16
 **/
public class SysUserServiceImpl implements ISysUserService {

    @Resource
    protected RestTemplate restTemplate;
    @Override
    public List<OperatorSearchVo> getOperator() {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        UserParams params = new UserParams();
        params.setPostId(UserPostEnum.OPERATIONS_MANAGER.getCode());
        ResultDTO resultDTO = restTemplateUtil.post(params, ResultDTO.class, SYS_USER_INFO);
        List userList = JSON.to(List.class, resultDTO.getData());
        List<OperatorSearchVo> operatorSearchVo = new ArrayList<>();
        userList.forEach(i -> {
            UserInteriorVO userInteriorVO = JSON.to(UserInteriorVO.class, i);
            operatorSearchVo.add(OperatorSearchVo.builder().userName(userInteriorVO.getUserName()).nickName(userInteriorVO.getNickName()).build());
        });
        return operatorSearchVo;
    }

    @Override
    public List<UserInteriorVO> getUserList() {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO resultDTO = restTemplateUtil.get(SYS_USER_LIST_URL, ResultDTO.class);
        List data = JSON.to(List.class, resultDTO.getData());
        List<UserInteriorVO> map = new ArrayList<>();
        data.forEach(i -> {
            UserInteriorVO userInteriorVO = JSON.to(UserInteriorVO.class, i);
            map.add(userInteriorVO);
        });
        return map;
    }
}
