package com.purchase.purchase_server.entity.dto.factory;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 供应商信息
 * <AUTHOR>
 * @Date 2024/6/5 20:39
 **/
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class FactoryContainerDto implements Serializable {


    @Serial
    private static final long serialVersionUID = -4133592473445153814L;
    private String id;
    /**
     * 省份
     */
    @Length(max = 100, message = "省份最多100个字符")
    private String province;
    /**
     * 城市
     */
    @Length(max = 100, message = "城市最多100个字符")
    private String city;
    /**
     * 详情地址
     */
    @Length(max = 255, message = "详情地址最多255个字符")
    private String detailedAddress;
    /**
     * 地址代号
     */
    @Length(max = 20, message = "地址代号最多20个字符")
    private String addressCode;
    /**
     * 供应商id
     */
    private String factoryInfoId;
    /**
     * 默认标识
     * @see com.purchase.purchase_server.enums.FactoryContainerDefaultIdentifierEnum
     */
    private String defaultIdentifier;
    /**
     * 供应商代码
     */
    private String factoryCode;
}
