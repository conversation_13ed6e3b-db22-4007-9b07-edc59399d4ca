<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sales_server.model.targetSales.mapper.TargetSalesMapper">
    <select id="aggregateAllDayTargetSales" resultType="com.sales_server.model.targetSales.entity.dto.TargetSalesAggDto">
        SELECT
            DATE_FORMAT(target_date, '%Y-%m-01') AS targetDate,
            virtual_sku_id,
            SUM(target_sales) AS targetSales
        FROM
            cm_sales_target_sales_info
        <where>
            virtual_sku_id in
            <foreach collection="set" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
        GROUP BY
            targetDate, virtual_sku_id;
    </select>

    <update id="createPartitionByYear">
        ALTER TABLE cm_sales_target_sales_info
            ADD PARTITION (
                PARTITION ${name} VALUES LESS THAN (YEAR(#{localDate}))
                );
    </update>
</mapper>