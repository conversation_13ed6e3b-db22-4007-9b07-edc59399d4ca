package com.auth.authorization_server.model.dataObject;

import com.auth.authorization_server.enums.RoleStatusEnum;
import com.auth.authorization_server.model.PhysicalBaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;

/**
 * 角色表 cm_sys_role
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_sys_role")
public class RoleDO extends PhysicalBaseEntity {

    @Serial
    private static final long serialVersionUID = -1968236696890451733L;
    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 显示顺序
     */
    private Integer roleSort;

    /**
     * 角色状态（0正常 1停用）
     *
     * @see RoleStatusEnum
     */
    private String roleStatus;

    /**
     * 备注
     */
    private String remarks;

    public static boolean isAdmin(String roleId) {
        return roleId != null && roleId.equals("1");
    }


}
