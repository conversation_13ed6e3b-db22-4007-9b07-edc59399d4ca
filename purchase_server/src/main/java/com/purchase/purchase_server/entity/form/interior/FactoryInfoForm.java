package com.purchase.purchase_server.entity.form.interior;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @Date 2024/1/15 17:16
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryInfoForm implements Serializable {
    @Serial
    private static final long serialVersionUID = 2049449738003810928L;
    private String id;
    /**
     * 供应商代码
     */
    private String factoryCode;
    /**
     * 供应商名称
     */
    private String factoryName;
    /**
     * 简称
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String shortName;
    /**
     * 采购人
     */
    private String purchaser;
    /**
     * 跟单人
     */
    private String orderTracker;
    /**
     * 产品经理
     */
    private String buyer;
    /**
     * 币种
     */
    private String currency;
    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remark;
    /**
     * 禁用状态
     */
    private String disableStatus;
}
