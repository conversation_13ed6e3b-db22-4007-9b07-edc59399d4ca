package com.purchase.purchase_server.repository.dataRepository.Lcl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclTrialShippingInventoryDO;
import com.purchase.purchase_server.entity.dto.Lcl.LclTrialShippingInventoryDTO;
import com.purchase.purchase_server.mapper.Lcl.LclTrialShippingInventoryMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class LclTrialShippingInventoryRepositoryImpl extends ServiceImpl<LclTrialShippingInventoryMapper, LclTrialShippingInventoryDO> {

    public int delete(String factoryFinishedId, String shippingStartDate) {
        return baseMapper.delete(factoryFinishedId, shippingStartDate);
    }

    public void dropByIdFactoryFinishedIdAndShippingStartDate(String factoryFinishedId, String shippingStartDate) {
        baseMapper.delete(Wrappers.<LclTrialShippingInventoryDO>lambdaQuery()
                .eq(LclTrialShippingInventoryDO::getLclFactoryFinishedId, factoryFinishedId)
                .eq(LclTrialShippingInventoryDO::getShippingStartDate, shippingStartDate)
        );
    }

    public int deleteById(String id) {
        return baseMapper.deleteById(id);
    }

    public int deleteByFinId(String finId) {
        return baseMapper.deleteByFinId(finId);
    }

    public List<LclTrialShippingInventoryDO> list(LclTrialShippingInventoryDTO dto) {
        return baseMapper.selectList(Wrappers.<LclTrialShippingInventoryDO>lambdaQuery()
                .eq(StrUtil.isNotBlank(dto.getLclFactoryFinishedId()), LclTrialShippingInventoryDO::getLclFactoryFinishedId, dto.getLclFactoryFinishedId())
                .eq(StrUtil.isNotBlank(dto.getShippingStartDate()), LclTrialShippingInventoryDO::getShippingStartDate, dto.getShippingStartDate()));
    }

    public int deleteByLclRecordId(String lclRecordId) {
        return baseMapper.deleteByLclRecordId(lclRecordId);
    }
}
