package com.product.product_server.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import lombok.Getter;

/**
 * @Description excel转化类
 * <AUTHOR>
 * @Date 2023/12/8 13:18
 **/
public class CustomerStringIntegerConverter implements Converter<Integer> {

    @Getter
    enum IsTexesEnum {
        YES("是", 1),
        NO("否", 0)
        ;

        private final String str;

        private final Integer intValue;

        IsTexesEnum(String str, Integer intValue) {
            this.str = str;
            this.intValue = intValue;
        }

    }

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadConverterContext<?> context) {
        String stringValue = context.getReadCellData().getStringValue();
        if(IsTexesEnum.YES.getStr().equals(stringValue)) {
            return IsTexesEnum.YES.intValue;
        } else if(IsTexesEnum.NO.getStr().equals(stringValue)) {
            return IsTexesEnum.NO.intValue;
        } else {
            throw new IllegalArgumentException("含税只允许填“是”或“否”");
        }
    }
}
