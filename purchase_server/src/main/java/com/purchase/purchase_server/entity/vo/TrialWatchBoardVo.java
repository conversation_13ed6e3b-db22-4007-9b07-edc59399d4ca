package com.purchase.purchase_server.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectWatchBoardWarehouseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;

/**
 * @Description 发货计划试算看板
 * <AUTHOR>
 * @Date 2024/1/10 13:52
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrialWatchBoardVo implements Serializable {

    private List<ShippingProjectWatchBoardWarehouseDto> watchBoardWarehouseList;

    private Set<LocalDate> deliveryDateSet;

    @JsonFormat(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, timezone = "GMT+8")
    private LocalDate deliveryStartDate;

    @JsonFormat(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, timezone = "GMT+8")
    private LocalDate deliveryEndDate;

    private Double totalShippingNum;
}
