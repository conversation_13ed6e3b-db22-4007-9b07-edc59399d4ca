package com.purchase.purchase_server.consts;

import java.util.HashSet;
import java.util.Set;
import static com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum.*;

/**
 * @Description 不需要发货的产品
 * <AUTHOR>
 * @Date 2025/3/4 10:24
 **/
public class SpecialDeliveryCollection {

    public static final Set<Integer> NOT_DELIVERY_SET = new HashSet<>();

    public static final Set<Integer> TEST_SAMPLE_DELIVERY_SET = new HashSet<>();

    static {
        NOT_DELIVERY_SET.add(HALT_DISTRIBUTION.getCode());
        NOT_DELIVERY_SET.add(STOP_SELLING.getCode());

        TEST_SAMPLE_DELIVERY_SET.add(NEW_ARRIVAL_TEST_SAMPLE.getCode());
        TEST_SAMPLE_DELIVERY_SET.add(NEW_ARRIVAL_NORMAL_SAMPLE.getCode());
    }
}
