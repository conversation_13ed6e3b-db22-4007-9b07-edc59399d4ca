package com.product.product_server.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 任务中心任务过程、结果返回给前端实体类
 * <AUTHOR>
 * @Date 2023/12/12 14:08
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MissionCenterVo implements Serializable {


    private String missionId;

    private String fileName;

    private String filePath;

    private Date createDate;

    private Date finishDate;

    private String missionType;

    private String missionStatus;

    private String missionResult;

    private List<String> failedResultList;

    private String createBy;

}
