package com.purchase.purchase_server.entity.vo.purchaseOrder;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 易仓采购单vo
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YicangPurchaseOrdersScheduleVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -1777943721677821189L;

    private String id;

    /**
     * 预期交货时间
     */
    private String expectedDeliveryDate;
    /**
     * 预期交货数量
     */
    private Integer expectedDeliveryQuantity;

}