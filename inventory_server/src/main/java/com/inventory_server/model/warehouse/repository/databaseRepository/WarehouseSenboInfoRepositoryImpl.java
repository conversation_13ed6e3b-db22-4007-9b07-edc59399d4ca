package com.inventory_server.model.warehouse.repository.databaseRepository;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.inventory_server.model.warehouse.entity.dos.WarehouseSenboInfoDO;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.entity.form.SenboWarehouseForm;
import com.inventory_server.model.warehouse.mapper.WarehouseSenboInfoMapper;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * @Description 森帛仓库信息数据库交互层
 * <AUTHOR>
 * @Date 2024/7/4 17:12
 **/
@Service
public class WarehouseSenboInfoRepositoryImpl extends ServiceImpl<WarehouseSenboInfoMapper, WarehouseSenboInfoDO> {

    public List<SenboWarehouseDto> getSenboWarehouseList(SenboWarehouseDto dto) {
        return baseMapper.selectJoinList(SenboWarehouseDto.class, new MPJLambdaWrapper<WarehouseSenboInfoDO>()
                .selectAs(WarehouseSenboInfoDO::getId, "senboWarehouseId")
                .selectAs(WarehouseSenboInfoDO::getWarehouse, "senboWarehouse")
                .select(WarehouseSenboInfoDO::getSort)
                .select(WarehouseSenboInfoDO::getHeadShippingDate)
                .select(WarehouseSenboInfoDO::getUrgentHeadShipDate)
                .select(WarehouseSenboInfoDO::getCreateDate)
                .select(WarehouseSenboInfoDO::getRemarks)
                .eq(StrUtil.isNotBlank(dto.getSenboWarehouse()), WarehouseSenboInfoDO::getWarehouse, dto.getSenboWarehouse())
                .eq(ObjectUtil.isNotEmpty(dto.getSort()), WarehouseSenboInfoDO::getSort, dto.getSort())
                .orderByDesc(WarehouseSenboInfoDO::getSort)
        );
    }

    public List<WarehouseSenboInfoDO> getSenboWarehouseListByWarehouseCollection(Collection<String> collection) {
        return baseMapper.selectList(Wrappers.<WarehouseSenboInfoDO>lambdaQuery().in(WarehouseSenboInfoDO::getId, collection));
    }

    public boolean checkSenboWarehouseExists(Integer senboWarehouseId) {
        if (senboWarehouseId == null) {
            return true;
        }
        return baseMapper.exists(Wrappers.<WarehouseSenboInfoDO>lambdaQuery().eq(WarehouseSenboInfoDO::getId, senboWarehouseId));
    }

    public IPage<SenboWarehouseDto> getSenboWarehousePageList(SenboWarehouseForm form) {
        IPage<SenboWarehouseDto> page = new Page<>();
        page.setSize(form.getSize());
        page.setCurrent(form.getCurrent());
        return baseMapper.selectJoinPage(page, SenboWarehouseDto.class, new MPJLambdaWrapper<WarehouseSenboInfoDO>()
                .selectAs(WarehouseSenboInfoDO::getId, "senboWarehouseId")
                .selectAs(WarehouseSenboInfoDO::getWarehouse, "senboWarehouse")
                .select(WarehouseSenboInfoDO::getSort)
                .select(WarehouseSenboInfoDO::getHeadShippingDate)
                .select(WarehouseSenboInfoDO::getUrgentHeadShipDate)
                .select(WarehouseSenboInfoDO::getCreateDate)
                .select(WarehouseSenboInfoDO::getRemarks)
                .orderByDesc(WarehouseSenboInfoDO::getSort));
    }
}
