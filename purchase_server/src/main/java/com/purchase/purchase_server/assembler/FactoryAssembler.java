package com.purchase.purchase_server.assembler;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.purchase.purchase_server.entity.dataObject.FactoryContainerDO;
import com.purchase.purchase_server.entity.dataObject.FactoryFinancialDO;
import com.purchase.purchase_server.entity.dataObject.FactoryInfoDO;
import com.purchase.purchase_server.entity.dto.factory.FactoryContainerDto;
import com.purchase.purchase_server.entity.dto.factory.FactoryFinancialDto;
import com.purchase.purchase_server.entity.dto.factory.FactoryInfoDto;
import com.purchase.purchase_server.entity.excelObject.FactoryContainerExcel;
import com.purchase.purchase_server.entity.excelObject.FactoryInfoAndFinancialExcel;
import com.purchase.purchase_server.enums.FactoryFinancialPaymentTimeEnum;
import com.purchase.purchase_server.enums.FactoryFinancialSettementEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;


/**
 * 数据转换工具类
 *
 * <AUTHOR>
 * @date 2024/5/11
 */

@Mapper(componentModel = "spring", uses = FactoryConvert.class)
public interface FactoryAssembler {
    FactoryInfoDto factoryInfoDoToDto(FactoryInfoDO val);

    List<FactoryContainerDto> factoryContainerDoListToDtoList(List<FactoryContainerDO> val);

    @Mapping(source = "depositRatio", target = "depositRatio", qualifiedByName = "depositRatio")
    FactoryFinancialDto factoryFinancialDoToDto(FactoryFinancialDO val);

    @Mapping(source = "purchaserUserName", target = "purchaser")
    @Mapping(source = "orderTrackerUserName", target = "orderTracker")
    FactoryInfoDO factoryInfoDtoListToDo(FactoryInfoDto val);

    List<FactoryInfoDO> factoryInfoDtoListToDoList(List<FactoryInfoDto> val);

    @Mapping(source = "province", target = "province", qualifiedByName = "stringToNull")
    @Mapping(source = "city", target = "city", qualifiedByName = "stringToNull")
    @Mapping(source = "detailedAddress", target = "detailedAddress", qualifiedByName = "stringToNull")
    @Mapping(source = "remark", target = "remark", qualifiedByName = "stringToNull")
    @Mapping(source = "depositRatio", target = "depositRatio", qualifiedByName = "depositRatioToDouble")
    @Mapping(source = "finalPaymentTerms", target = "finalPaymentTerms", qualifiedByName = "finalPaymentTermsToInt")
    @Mapping(source = "settlementType", target = "settlementType", qualifiedByName = "stringToNull")
    @Mapping(source = "paymentTime", target = "paymentTime", qualifiedByName = "stringToNull")
    FactoryFinancialDO factoryFinancialDtoListToDo(FactoryFinancialDto val);


    List<FactoryFinancialDO> factoryFinancialDtoListToDoList(List<FactoryFinancialDto> val);

    List<FactoryContainerDO> factoryContainerDtoListToDoList(List<FactoryContainerDto> val);


    @Mapping(source = "factoryCode", target = "factoryCode", qualifiedByName = "doTrim")
    @Mapping(source = "factoryName", target = "factoryName", qualifiedByName = "doTrim")
    @Mapping(source = "factoryRemark", target = "remark")
    @Mapping(source = "purchaser", target = "purchaserUserName")
    @Mapping(source = "orderTracker", target = "orderTrackerUserName")
    FactoryInfoDto factoryInfoExcelToDto(FactoryInfoAndFinancialExcel excel);

    FactoryContainerDto factoryContainerExcelToDto(FactoryContainerExcel excel);

    @Mapping(source = "settlementType", target = "settlementType", qualifiedByName = "settlementType")
    @Mapping(source = "paymentTime", target = "paymentTime", qualifiedByName = "paymentTime")
    @Mapping(source = "financialRemark", target = "remark")
    FactoryFinancialDto factoryFinancialExcelToDto(FactoryInfoAndFinancialExcel excel);


    @Named("settlementType")
    default String settlementType (String value){
        if (StrUtil.isBlank(value)) {
            return null;
        }
        return FactoryFinancialSettementEnum.ofDesc(value).getCode();
    }


    @Named("paymentTime")
    default String paymentTime (String value){
        if (StrUtil.isBlank(value)) {
            return null;
        }
        return FactoryFinancialPaymentTimeEnum.ofDesc(value).getCode();
    }

    @Named("depositRatio")
    default String depositRatio (Double value){
        if (value == null) {
            return null;
        }
        return NumberUtil.toStr(value);
    }
    /**
     * 去空格
     *
     * @param str
     * @return
     */
    @Named("doTrim")
    default String doTrim(String str) {
        if (StrUtil.isBlank(str)) {
            return null;
        }
        return str.trim();
    }

    @Named("depositRatioToDouble")
    default Double depositRatioToDouble (String value){
        if (StrUtil.isBlank(value)) {
            return null;
        }
        return Double.parseDouble(value);
    }

    @Named("finalPaymentTermsToInt")
    default Integer finalPaymentTermsToInt (String value){
        if (StrUtil.isBlank(value)) {
            return null;
        }
        return Integer.parseInt(value);
    }

    @Named("stringToNull")
    default String stringToNull (String value){
        if (StrUtil.isBlank(value)) {
            return null;
        }
        return value;
    }

}














