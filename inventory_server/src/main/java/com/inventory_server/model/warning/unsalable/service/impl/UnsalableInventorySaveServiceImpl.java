package com.inventory_server.model.warning.unsalable.service.impl;

import com.inventory_server.model.warning.unsalable.entity.dos.WarningFactoryUnsalableInventoryDO;
import com.inventory_server.model.warning.unsalable.entity.dos.WarningForeignUnsalableInventoryDO;
import com.inventory_server.model.warning.unsalable.entity.dos.WarningUnsalableInventoryDO;
import com.inventory_server.model.warning.unsalable.repository.WarningUnsalableInventoryRepository;
import com.inventory_server.model.warning.unsalable.service.IFactoryUnsalableInventoryService;
import com.inventory_server.model.warning.unsalable.service.IForeignUnsalableInventoryService;
import com.inventory_server.model.warning.unsalable.service.IUnsalableInventorySaveService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * @Description 无计划库存保存类
 * <AUTHOR>
 * @Date 2025/4/22 13:45
 **/
@Service
public class UnsalableInventorySaveServiceImpl implements IUnsalableInventorySaveService {

    @Resource
    private WarningUnsalableInventoryRepository warningUnsalableInventoryRepository;

    @Resource
    private IFactoryUnsalableInventoryService factoryUnsalableInventoryService;

    @Resource
    private IForeignUnsalableInventoryService foreignUnsalableInventoryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUnsalableInventory(String inventoryId, int totalInventory, Map<String, WarningFactoryUnsalableInventoryDO> factoryUnsalableMap,
                                       List<WarningForeignUnsalableInventoryDO> unsalableInventoryList) {
        // 删除上次的海外仓无计划、在途无计划、工厂无计划
        WarningUnsalableInventoryDO unsalableInventoryDO = warningUnsalableInventoryRepository.getByInventoryId(inventoryId);
        if (unsalableInventoryDO != null) {
            factoryUnsalableInventoryService.deleteFactoryUnsalableByUnsalableId(unsalableInventoryDO.getId());
            foreignUnsalableInventoryService.deleteForeignUnsalableByUnsalableId(unsalableInventoryDO.getId());
        }

        WarningUnsalableInventoryDO build = WarningUnsalableInventoryDO.builder()
                .inventoryInfoId(inventoryId)
                .totalUnsalableInventory(totalInventory)
                .build();
        warningUnsalableInventoryRepository.saveUnsalableInventoryByInventoryInfoId(build);
        String unsalableId = unsalableInventoryDO == null ? build.getId() : unsalableInventoryDO.getId();

        // 保存工厂无计划库存
        List<WarningFactoryUnsalableInventoryDO> factoryList = factoryUnsalableMap.values().stream()
                .peek(m -> m.setUnsalableInventoryId(unsalableId))
                .toList();
        factoryUnsalableInventoryService.saveFactoryUnsalableInventory(factoryList);

        // 保存海外仓无计划、在途无计划
        unsalableInventoryList.forEach(m -> m.setUnsalableInventoryId(unsalableId));
        foreignUnsalableInventoryService.saveForeignUnsalableInventory(unsalableInventoryList);
    }

    @Override
    public void deleteUnsalableInventoryByRedundancyInventoryId(String inventoryInfoId) {
        warningUnsalableInventoryRepository.deleteByInventoryId(inventoryInfoId);
    }
}
