package com.inventory_server.model.redundancy.listener.redundant;

import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.inventory_server.applications.vo.response.ResultDTO;
import com.inventory_server.model.redundancy.entity.form.UpgradeProductInteriorForm;
import com.inventory_server.model.redundancy.entity.vo.UpgradeInfoInteriorVo;
import com.inventory_server.model.redundancy.listener.AbstractImportListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.client.RestTemplate;

import java.util.List;

import static com.crafts_mirror.utils.constant.SystemConstant.PRODUCTS_VIRTUAL_UPGRADE_INFO_URL;

/**
 * @Description 导入补货计划抽象类
 * <AUTHOR>
 * @Date 2024/1/15 11:35
 **/
@Slf4j
public abstract class AbstractInventoryImportListener<T> extends AbstractImportListener<T> {

    protected final RestTemplate restTemplate = ApplicationContextProvider.getBean(RestTemplate.class);

    public UpgradeInfoInteriorVo selectUpgradeInfo(String upgradeId){
        var restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        var virtualProductListVo = restTemplateUtil.post(UpgradeProductInteriorForm.builder().id(upgradeId).build(), ResultDTO.class, PRODUCTS_VIRTUAL_UPGRADE_INFO_URL);
        return JSON.to(UpgradeInfoInteriorVo.class, virtualProductListVo.getData());
    }

    public AbstractInventoryImportListener(String fileName, List<String> errorList) {
        super(fileName, errorList);
    }

    protected String getCalculationType() {
        return "冗余库存计划导入";
    }

}
