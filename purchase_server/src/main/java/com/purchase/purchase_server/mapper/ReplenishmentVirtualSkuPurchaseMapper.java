package com.purchase.purchase_server.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentVirtualSkuPurchaseDO;
import com.purchase.purchase_server.entity.form.AdvicePurchaseForm;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【cm_replenishment_virtual_sku_purchase】的数据库操作Mapper
* @createDate 2024-01-17 10:45:51
*/
public interface ReplenishmentVirtualSkuPurchaseMapper extends MPJBaseMapper<ReplenishmentVirtualSkuPurchaseDO> {
    //List<RepVirtualSkuDateBO> selectRepVirtualSkuList(@Param("form") ReplenishmentProjectForm form);

    Boolean updateAcceptedNumVirtualSku(@Param("form") AdvicePurchaseForm advicePurchaseForm);

    void deleteByIds(@Param("ids")List<String> ids);

    void updateVirtualSkuPurchase (@Param("val") ReplenishmentVirtualSkuPurchaseDO val);
}




