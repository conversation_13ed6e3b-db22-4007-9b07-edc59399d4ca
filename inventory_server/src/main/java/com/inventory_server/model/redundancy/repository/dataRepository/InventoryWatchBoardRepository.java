package com.inventory_server.model.redundancy.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import com.inventory_server.model.redundancy.entity.bo.InventoryOnShippingRedundantBO;
import com.inventory_server.model.redundancy.entity.dos.InventoryForeignShipWatchBoardDO;
import com.inventory_server.model.redundancy.entity.dos.InventoryShippingRedundantInfoDO;
import com.inventory_server.model.redundancy.entity.dp.RedundancyInfo;
import com.inventory_server.model.redundancy.mapper.InventoryWatchBoardMapper;
import com.inventory_server.model.warehouse.entity.dos.WarehouseSenboInfoDO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/10 17:12
 **/
@Service
public class InventoryWatchBoardRepository extends ServiceImpl<InventoryWatchBoardMapper, InventoryForeignShipWatchBoardDO> {

    public List<InventoryForeignShipWatchBoardDO> getInventoryWatchBoardByInfoId(String infoId) {
        return baseMapper.selectList(Wrappers.<InventoryForeignShipWatchBoardDO>lambdaQuery()
                .eq(InventoryForeignShipWatchBoardDO::getInventoryInfoId, infoId)
                .orderByAsc(InventoryForeignShipWatchBoardDO::getJudgeDate)
        );
    }

    public List<InventoryForeignShipWatchBoardDO> getInventoryWatchBoardByInfoIds(List<String> infoIds) {
        return baseMapper.selectList(Wrappers.<InventoryForeignShipWatchBoardDO>lambdaQuery()
                .in(InventoryForeignShipWatchBoardDO::getInventoryInfoId, infoIds)
                .orderByAsc(InventoryForeignShipWatchBoardDO::getJudgeDate)
        );
    }

    public void deleteByInventoryId(String inventoryId) {
        List<InventoryForeignShipWatchBoardDO> list = baseMapper.selectList(Wrappers.<InventoryForeignShipWatchBoardDO>lambdaQuery().eq(InventoryForeignShipWatchBoardDO::getInventoryInfoId, inventoryId));
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<String> idList = list.stream().map(PhysicalBaseEntity::getId).toList();
        baseMapper.deleteByIds(idList);
    }

    public String insertWatchBoardInfo(String inventoryId, RedundancyInfo foreignInfo) {
        InventoryForeignShipWatchBoardDO watchBoardDO = InventoryForeignShipWatchBoardDO.builder()
                .inventoryInfoId(inventoryId)
                .arrivingDate(foreignInfo.getArrivingDate())
                .judgeDate(foreignInfo.getJudgeDate())
                .arrivingNum(foreignInfo.getArrivingNum())
                .initialInventory(foreignInfo.getInitInventory())
                .safeInventory(foreignInfo.getSafeInventory())
                .initialTotalRedundancy(foreignInfo.getTotalRedundancy())
                .redundantNum(foreignInfo.getRedundantNum())
                .redundantType(foreignInfo.getRedundantTypeEnum().getCode())
//                .totalRedundantType(foreignInfo.getTotalRedundantTypeEnum().getCode())
                .type(foreignInfo.getType() == null ? null : Integer.parseInt(foreignInfo.getType().getCode()))
                .build();
        baseMapper.insert(watchBoardDO);
        return watchBoardDO.getId();
    }

    public List<InventoryOnShippingRedundantBO> getOnShippingRedundantInfoList(List<String> infoIdList) {
        return baseMapper.selectJoinList(InventoryOnShippingRedundantBO.class, new MPJLambdaWrapper<InventoryForeignShipWatchBoardDO>()
                .select(InventoryForeignShipWatchBoardDO::getInventoryInfoId)
                .select(WarehouseSenboInfoDO::getWarehouse)
                .select(InventoryShippingRedundantInfoDO::getShipmentCode)
                .select(InventoryShippingRedundantInfoDO::getEnableUsingDate)
                .select(InventoryShippingRedundantInfoDO::getRedundantNum)
                .select(InventoryShippingRedundantInfoDO::getStartShippingDate)
                .leftJoin(InventoryShippingRedundantInfoDO.class, InventoryShippingRedundantInfoDO::getOnShippingWatchBoardId, InventoryForeignShipWatchBoardDO::getId)
                .leftJoin(WarehouseSenboInfoDO.class, WarehouseSenboInfoDO::getId, InventoryShippingRedundantInfoDO::getWarehouse)
                .in(InventoryForeignShipWatchBoardDO::getInventoryInfoId, infoIdList)
                .and(i -> {
                    i.eq(InventoryForeignShipWatchBoardDO::getRedundantType, "1")
                            .or()
                            .eq(InventoryForeignShipWatchBoardDO::getRedundantType, "-1");
                })

                .orderByAsc(WarehouseSenboInfoDO::getWarehouse));
    }
}
