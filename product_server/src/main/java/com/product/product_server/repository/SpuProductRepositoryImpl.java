package com.product.product_server.repository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crafts_mirror.common.security.dataPermission.DataPermission;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.product.product_server.entity.dataObject.ProductCategoryDO;
import com.product.product_server.entity.dataObject.SelfProductDO;
import com.product.product_server.entity.dataObject.SpuProductDO;
import com.product.product_server.entity.dataObject.VirtualProductDO;
import com.product.product_server.entity.form.SpuInfoSaveForm;
import com.product.product_server.entity.form.VirtualProductSearchForm;
import com.product.product_server.entity.vo.SpuProductListVo;
import com.product.product_server.enums.UpgradeStatusEnum;
import com.product.product_server.mapper.SpuProductMapper;
import com.product.product_server.model.products.RemarksSaveDp;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.crafts_mirror.utils.constant.SecurityConstants.ROLE_DATA_PER_VIR;

/**
 * @Description spu相关信息
 * <AUTHOR>
 * @Date 2023/12/16 10:05
 **/
@Service
public class SpuProductRepositoryImpl extends ServiceImpl<SpuProductMapper, SpuProductDO> {

    public boolean deleteSpuProduct(String spuId) {
        if (StrUtil.isBlank(spuId)) {
            throw new RuntimeException("删除spu相关信息时spu为空");
        }
        return this.baseMapper.delete(Wrappers.<SpuProductDO>lambdaQuery().eq(SpuProductDO::getId, spuId)) > 0;
    }

    public boolean checkSpuExisted(String spu, String spuId) {
        return this.exists(Wrappers.<SpuProductDO>lambdaQuery().eq(SpuProductDO::getSpu, spu)
                .ne(StrUtil.isNotBlank(spuId), SpuProductDO::getId, spuId));
    }

    public String saveSpuProduct(SpuInfoSaveForm form) {
        SpuProductDO spuProductDO = convertSpuProduct(form);
        // 如果spuId不为空则更新，否则新增
        String spu = form.spu();
        if (StrUtil.isNotBlank(form.spuId())) {
            this.baseMapper.update(spuProductDO,
                    Wrappers.<SpuProductDO>lambdaQuery().eq(SpuProductDO::getSpu, spu));
            return form.spuId();
        } else if (this.baseMapper.exists(Wrappers.<SpuProductDO>lambdaQuery().eq(SpuProductDO::getSpu, spu))) {
            this.baseMapper.update(spuProductDO,
                    Wrappers.<SpuProductDO>lambdaQuery().eq(SpuProductDO::getSpu, spu));

            return this.baseMapper.selectOne(Wrappers.<SpuProductDO>lambdaQuery().eq(SpuProductDO::getSpu, spu)).getId();
        }
        this.baseMapper.insert(spuProductDO);
        return spuProductDO.getId();
    }

    public SpuProductDO productDetail(String spu) {
        return this.baseMapper.selectOne(Wrappers.<SpuProductDO>lambdaQuery().eq(SpuProductDO::getId, spu));
    }

    public IPage<SpuProductListVo> getSpuProductPage(VirtualProductSearchForm form) {
        IPage<SpuProductListVo> iPage = new Page<>(form.getCurrent(), form.getSize());
        String productName = form.getProductName();
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_VIR, "t1");
        return this.baseMapper.selectJoinPage(iPage, SpuProductListVo.class, new MPJLambdaWrapper<SpuProductDO>()
                .selectAll(SpuProductDO.class)
                .distinct()
                .leftJoin(VirtualProductDO.class, VirtualProductDO::getSpuId, SpuProductDO::getId)
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, VirtualProductDO::getSelfProductSkuId)
                .leftJoin(ProductCategoryDO.class, ProductCategoryDO::getId, SelfProductDO::getCategoryId)
                .like(StrUtil.isNotBlank(productName), SelfProductDO::getProductName, StrUtil.isBlank(productName) ? productName : productName.strip())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), SelfProductDO::getSku, form.getSelfSkuList())
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), VirtualProductDO::getVirtualSku, form.getVirtualSkuList())
                .in(CollectionUtil.isNotEmpty(form.getOldSkuList()), VirtualProductDO::getOldSku, form.getOldSkuList())
                .eq(StrUtil.isNotBlank(form.getBuyer()), SelfProductDO::getBuyer, form.getBuyer())
                .eq(StrUtil.isNotBlank(form.getChannel()), VirtualProductDO::getChannel, form.getChannel())
                .eq(NumberUtil.isInteger(form.getSubType()), VirtualProductDO::getSubType, form.getSubType())
                .eq(NumberUtil.isInteger(form.getProductStatus()), VirtualProductDO::getProductStatus, form.getProductStatus())
                .in(CollectionUtil.isNotEmpty(form.getProductStatusList()), VirtualProductDO::getProductStatus, form.getProductStatusList())
                .eq(NumberUtil.isInteger(form.getProductType()), VirtualProductDO::getProductType, form.getProductType())
                .eq(StrUtil.isNotBlank(form.getCategoryId()), ProductCategoryDO::getParentId, form.getCategoryId())
                .like(StrUtil.isNotBlank(form.getSpuName()), SpuProductDO::getSpuProductName, form.getSpuName())
                .isNull(form.getUpgradeStatus() != null && form.getUpgradeStatus().equals(UpgradeStatusEnum.NOT_UPGRADE.getCode()), VirtualProductDO::getUpgradeId)
                .isNotNull(form.getUpgradeStatus() != null && form.getUpgradeStatus().equals(UpgradeStatusEnum.UPGRADE.getCode()), VirtualProductDO::getUpgradeId)
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
                .orderByDesc(SpuProductDO::getCreateDate)
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public int saveRemarks(RemarksSaveDp saveDp) {
        return this.baseMapper.update(new SpuProductDO(),
                Wrappers.<SpuProductDO>lambdaUpdate()
                        .set(SpuProductDO::getRemarks, saveDp.remarks())
                        .eq(SpuProductDO::getId, saveDp.id())
        );
    }

    private SpuProductDO convertSpuProduct(SpuInfoSaveForm form) {
        return SpuProductDO.builder()
                .spuProductName(form.spuProductName())
                .spu(form.spu())
                .build();
    }
}
