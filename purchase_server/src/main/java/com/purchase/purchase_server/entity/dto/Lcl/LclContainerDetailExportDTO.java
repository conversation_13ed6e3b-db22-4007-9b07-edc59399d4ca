package com.purchase.purchase_server.entity.dto.Lcl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 拼柜装柜
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclContainerDetailExportDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -1L;
    /**
     * 柜子ID
     */
    private String containerInfoId;
    /**
     * 源发货计划表id
     */
    private String consolidationFactoryFinishedId;
    /**
     * 合同号
     */
    private String contractCode;
    /**
     * 发货装柜时间
     */
    private Date shippingStartDate;
    /**
     * 发货类型
     */
    private String deliveryType;
    /**
     * 工厂交期
     */
    private String factoryFinishedDate;
    /**
     * 工厂交货数量
     */
    private Integer factoryShippingPackageNum;
    /**
     * 发货数量
     */
    private Integer lclShippingNum;
    /**
     * 未安排数量
     */
    private Integer factoryRemainNum;
    /**
     * 是否装满
     */
    private Integer isPackageFull;
    /**
     * 快照记录id
     */
    private String productSnapshotId;
    /**
     * 货件号
     */
    private String shipmentCode;
    /**
     * 原发货数量
     */
    private Integer originalShippingNum;


    /**
     * 拼柜计划id 
     */
    private String lclRecordId;

    /**
     * 虚拟skuId
     */
    private String virtualSkuId;

    /**
     * sku类型(0-虚拟sku 1-老sku)
     */
    private String isOldStatus;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 是否在途（0-不是，1-是）
     */
    private String isForeignFlag;

    /**
     * 在途拆分id
     */
    private String lclForeignId;

    /**
     * 虚拟sku
     */
    private String destinationSku;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 渠道来源
     */
    private String channel;

    /**
     * 自定义SKU
     */
    private String selfSku;
    /**
     * 单箱个数
     */
    private Integer containerLoad;
    /**
     * 外箱尺寸长
     */
    private Double caseLength;
    /**
     * 外箱尺寸宽
     */
    private Double caseWidth;
    /**
     * 外箱尺寸高
     */
    private Double caseHeight;
    /**
     * 地址代号
     */
    private String addressCode;
    /**
     * 单箱体积
     */
    private Double caseVolume;
    /**
     * 单箱毛重
     */
    private Double singleCaseGrossWeight;
    /**
     * 采购人员
     */
    private String purchaser;
    /**
     * 跟单人员
     */
    private String orderTracker;
    /**
     * 是否商检
     */
    private String commodityInspection;
    /**
     * 产品经理
     */
    private String buyer;
    /**
     * 运营
     */
    private String operator;
    /**
     * 币种
     */
    private String currency;
    /**
     * 采购金额
     */
    private Double  unitOrContractPrice;
    /**
     * 件的类型
     */
    private String  pcsType;
}