package com.inventory_server.model.redundancy.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 * @Description 海外仓库存信息
 * <AUTHOR>
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_inventory_foreign_store")
public class InventoryForeignStoreDO extends PhysicalBaseEntity {

    private String warehouse;
    private Date enableUsingDate;
    private Date startShippingDate;
    private Integer inventoryNum;
    private String inventoryInfoId;
    private String virtualSku;
    private String shipmentCode;
    private String remarks;
}
