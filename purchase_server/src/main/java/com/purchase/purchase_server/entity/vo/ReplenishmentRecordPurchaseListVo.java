package com.purchase.purchase_server.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 补货计划记录列表页展示
 * <AUTHOR>
 * @Date 2024/4/9 14:21
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ReplenishmentRecordPurchaseListVo implements Serializable {
    @Serial
    private static final long serialVersionUID = -3614564221646346636L;
    /**
     * 补货记录Id
     */
    private String recordId;

    /**
     * 状态
     */
    private String replenishmentStatus;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 理论补货日（开始）
     */
    private String advicePurchaseStartDate;

    /**
     * 理论补货日（结束）
     */
    private String advicePurchaseEndDate;
}
