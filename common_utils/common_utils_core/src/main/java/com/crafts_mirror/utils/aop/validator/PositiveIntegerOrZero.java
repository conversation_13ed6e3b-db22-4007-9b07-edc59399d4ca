package com.crafts_mirror.utils.aop.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2024/6/7
 **/
@Target({ ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PositiveIntegerOrZeroValidator.class)
public @interface PositiveIntegerOrZero {
    String message() default "必须是非负整数";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    boolean isRequired() default true;
}
