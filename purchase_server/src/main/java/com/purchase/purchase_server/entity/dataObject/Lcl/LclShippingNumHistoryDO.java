package com.purchase.purchase_server.entity.dataObject.Lcl;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.util.Date;

/**
 * 发货数量变更表
 * @TableName cm_lcl_shipping_num_history
 */
@TableName(value ="cm_lcl_shipping_num_history")
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclShippingNumHistoryDO extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -4318870941228961324L;
    /**
     * 整理后到仓数据id
     */
    private String lclConsolidationTsiId;

    /**
     * 发货装柜时间
     */
    private Date shippingStartDate;

    /**
     * 变更的值
     */
    private Integer changeValue;

    /**
     * 变更类型
     * @see com.purchase.purchase_server.enums.lcl.ChangeTypeEnum
     */
    private String changeType;
}