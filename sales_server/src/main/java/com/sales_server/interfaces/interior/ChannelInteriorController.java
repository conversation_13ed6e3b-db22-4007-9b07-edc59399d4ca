package com.sales_server.interfaces.interior;

import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.sales_server.model.channel.entity.vo.ChannelSearchVo;
import com.sales_server.model.channel.service.IChannelInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
     * @Description 渠道内部controller层
 * <AUTHOR>
 * @Date 2025/5/6 10:42
 **/
@RestController
@RequestMapping(value = "interior/channel")
@Slf4j
public class ChannelInteriorController {

    @Resource
    private IChannelInfoService channelInfoService;

    @GetMapping("/list/niche")
    public ResultDTO<ChannelSearchVo> getAllNicheChannel() {
        ChannelSearchVo channelVo = channelInfoService.getChannelList(1);
        return ResultDTO.success(channelVo);
    }
}
