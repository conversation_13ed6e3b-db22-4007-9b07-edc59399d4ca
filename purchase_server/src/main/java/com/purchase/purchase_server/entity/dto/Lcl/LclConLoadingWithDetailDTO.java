package com.purchase.purchase_server.entity.dto.Lcl;

import com.purchase.purchase_server.entity.bo.Lcl.LclContainerLoadingBO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclContainerInfoDO;
import com.purchase.purchase_server.entity.vo.Lcl.LclConsolidationTrialNumVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclConLoadingWithDetailDTO {

    private LclContainerInfoDO infoDO;

    private List<LclContainerLoadingBO> trialDetailList;
}