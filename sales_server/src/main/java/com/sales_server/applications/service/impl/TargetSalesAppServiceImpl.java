package com.sales_server.applications.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.crafts_mirror.utils.dp.CalculationForm;
import com.sales_server.applications.service.ITargetSalesAppService;
import com.sales_server.infrastructures.entity.LogTrackNumDto;
import com.sales_server.model.file.service.IFileCenterService;
import com.sales_server.model.product.entity.dp.VirtualProductInfoDp;
import com.sales_server.model.product.service.IVirtualProductService;
import com.sales_server.model.system.service.ISysUserInteriorService;
import com.sales_server.model.targetSales.entity.TargetSalesPage;
import com.sales_server.model.targetSales.entity.dto.TargetDaySalesDto;
import com.sales_server.model.targetSales.entity.form.DaySalesEditForm;
import com.sales_server.model.targetSales.entity.form.TargetDaySalesDetailForm;
import com.sales_server.model.targetSales.entity.form.TargetMonthSalesPageForm;
import com.sales_server.model.targetSales.entity.vo.TargetMonthSalesVo;
import com.sales_server.model.targetSales.repository.redisRepository.ITargetSalesRedisRepository;
import com.sales_server.model.targetSales.service.ITargetSalesMonthSalesService;
import com.sales_server.model.targetSales.service.ITargetSalesService;
import com.sales_server.model.user.entity.vo.UserInteriorVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.enums.ImportExcelStatusEnum.PROCESSING;
import static com.crafts_mirror.utils.enums.ImportExcelTypeEnum.TARGET_SALES_IMPORT_RECORD;

/**
 * <AUTHOR>
 * @Date 2024/11/11 17:07
 **/
@Service
@Slf4j
public class TargetSalesAppServiceImpl implements ITargetSalesAppService {

    @Resource
    private IFileCenterService IFileCenterService;

    @Resource
    private ITargetSalesService targetSalesService;

    @Resource
    private ITargetSalesMonthSalesService targetSalesMonthSalesService;

    @Resource
    private IVirtualProductService virtualProductService;

    @Resource
    private ITargetSalesRedisRepository targetSalesRedisRepository;

    @Resource
    private ISysUserInteriorService userInteriorService;

    @Override
    @Async("importExcelThreadPool")
    public void importTargetSalesInfo(InputStream file, byte[] fileBytes, String fileName) {
        // 上传excel
        String fileId = IFileCenterService.uploadFile(fileBytes, fileName, PROCESSING.getMessage(), TARGET_SALES_IMPORT_RECORD.getMessage());

        // 解析excel
        targetSalesService.importTargetSalesInfo(file, fileId);
    }

    @Override
    public List<TargetMonthSalesVo> getTargetMonthSaleList(TargetMonthSalesPageForm form) {
        // 根据搜索项，获取有多少产品会被检索出来
        List<String> virtualSkuIdList = virtualProductService.getVirtualSkuIdListBySearchInfo(form);
        if (form.needSearchSku() && CollectionUtil.isEmpty(virtualSkuIdList)) {
            return new ArrayList<>();
        }

        List<UserInteriorVO> userList = userInteriorService.getUserList();
        Map<String, String> userMap = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        // 根据时间以及虚拟skuId，逻辑分页获取对应虚拟sku月销数据
        List<TargetMonthSalesVo> targetSalesList = targetSalesMonthSalesService.getMonthTargetSalesList(form, virtualSkuIdList, userMap);
        if (CollectionUtil.isEmpty(targetSalesList)) {
            return new ArrayList<>();
        }

        // 对分好页的数据获取相应的虚拟商品信息
        List<String> virtualIdList = targetSalesList.stream().map(TargetMonthSalesVo::getVirtualSkuId).toList();
        List<VirtualProductInfoDp> virtualProductInfoList = virtualProductService.getVirtualProductInfoListByVirtualIdList(virtualIdList);

        // 加入商品信息
        targetSalesMonthSalesService.combineProductInfoAndTargetSalesInfo(virtualProductInfoList, targetSalesList, userMap);
        return targetSalesList.stream().filter(f -> f.getVirtualProductInfoDp() != null).toList();
    }

    @Override
    public TargetSalesPage getTargetMonthSalePageBySpu(TargetMonthSalesPageForm form) {
        List<TargetMonthSalesVo> targetMonthSaleList = getTargetMonthSaleList(form);
        if (CollectionUtil.isEmpty(targetMonthSaleList)) {
            return new TargetSalesPage();
        }

        return targetSalesMonthSalesService.combineTargetSalesPageBySpu(form, targetMonthSaleList);
    }

    @Override
    public boolean editTargetSalesInfo(DaySalesEditForm form) {
        boolean saved = targetSalesService.editTargetSalesInfo(form);

        if (saved) {
            Set<String> strings = new HashSet<>();
            strings.add(form.getVirtualSkuId());
            targetSalesMonthSalesService.saveTargetSalesMonthAggData(strings);

            // 删除缓存
            targetSalesRedisRepository.deleteCachedTargetSales(form.getVirtualSkuId());
            // 重新插入缓存
            prepareCalculationTargetSales(CalculationForm.builder().virtualSkuIdList(Collections.singletonList(form.getVirtualSkuId())).build());
        }
        return true;
    }

    @Override
    public List<TargetDaySalesDto> getTargetDaySalesInfo(TargetDaySalesDetailForm form) {
        return targetSalesService.getTargetDaySalesInfo(form);
    }

    @Override
    public void exportTargetMonthSales(TargetMonthSalesPageForm form, HttpServletResponse response) {
        form.setCurrent(-1);
        form.setSize(-1);
        List<TargetMonthSalesVo> targetMonthSaleList = getTargetMonthSaleList(form);

        targetSalesService.exportTargetMonthSales(targetMonthSaleList, response, form);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTargetSales(String virtualId) {
        LogTrackNumDto logTrackNumDto = new LogTrackNumDto();
        Map<String, String> map = new HashMap<>();
        map.put(virtualId, "删除目标日销");
        logTrackNumDto.setLogMap(map);
        // 删除目标日销
        targetSalesService.deleteTargetDaySales(virtualId, logTrackNumDto);
        // 删除目标月销
        targetSalesMonthSalesService.deleteMonthSalesByVirtualId(virtualId);
    }

    @Override
    public void prepareCalculationTargetSales(CalculationForm form) {
        targetSalesService.prepareCalculationTargetSales(form);
    }
}
