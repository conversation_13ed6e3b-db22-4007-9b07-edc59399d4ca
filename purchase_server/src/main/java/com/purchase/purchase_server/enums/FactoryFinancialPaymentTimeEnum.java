package com.purchase.purchase_server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum FactoryFinancialPaymentTimeEnum {
    AFTER_PLACING_THE_PURCHASE_ORDER("0", "采购下单时间"),
    AFTER_SHIPMENT("1", "发货时间");

    private final String code;
    private final String desc;

    public static FactoryFinancialPaymentTimeEnum ofCode(String code) {
        return Arrays.stream(FactoryFinancialPaymentTimeEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
    public static FactoryFinancialPaymentTimeEnum ofDesc(String desc) {
        return Arrays.stream(FactoryFinancialPaymentTimeEnum.values())
                .filter(it -> it.getDesc().equals(desc))
                .findFirst()
                .orElse(null);
    }
}
