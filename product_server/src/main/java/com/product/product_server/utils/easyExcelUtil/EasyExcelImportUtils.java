package com.product.product_server.utils.easyExcelUtil;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.ReadListener;
import com.product.product_server.entity.excelObject.BaseExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.function.Supplier;

/**
 * @Description EasyExcel导入工具类
 * <AUTHOR>
 * @Date 2023/12/7 19:54
 **/
@Slf4j
public class EasyExcelImportUtils {

    public static <T extends BaseExcel, R extends ReadListener<T>> void read(MultipartFile file, Class<T> clazz,
                                                                             Supplier<R> supplier, Integer headRowNumber) throws IOException {
        EasyExcel.read(file.getInputStream(), clazz, supplier.get()).sheet().autoTrim(true).headRowNumber(headRowNumber).doRead();
    }

    public static <T extends BaseExcel, R extends ReadListener<T>> void read(MultipartFile file, Class<T> clazz,
                                                                             Supplier<R> supplier) throws IOException {
        EasyExcel.read(file.getInputStream(), clazz, supplier.get()).sheet().autoTrim(true).headRowNumber(1).doRead();
    }

    public static <T extends BaseExcel> void read(MultipartFile file,
                                                  Class<T> clazz,
                                                  ReadListener<T> listener) throws IOException {
        EasyExcel.read(file.getInputStream(), clazz, listener).sheet().autoTrim(true).headRowNumber(1).doRead();
    }

    public static <T extends BaseExcel> void read(MultipartFile file, Class<T> clazz,
                                                  ReadListener<T> listener, Integer headRowNumber) throws IOException {
        log.warn("导入自定义商品-------------正在通过EasyExcel导入excel");
        EasyExcel.read(file.getInputStream(), clazz, listener).sheet().autoTrim(true).headRowNumber(headRowNumber).doRead();
    }

    public static <T extends BaseExcel> void read(InputStream file, Class<T> clazz,
                                                  ReadListener<T> listener, Integer headRowNumber) {
        log.warn("导入自定义商品-------------正在通过EasyExcel导入excel");
        EasyExcel.read(file, clazz, listener).sheet().autoTrim(true).headRowNumber(headRowNumber).doRead();
    }

    //动态表头读取
    public static <T> void read(InputStream file, ReadListener<T> listener, Integer headRowNumber) {
        log.warn("导入自定义商品-------------正在通过EasyExcel导入excel");
        EasyExcel.read(file, listener).sheet().autoTrim(true).headRowNumber(headRowNumber).doRead();
    }
}
