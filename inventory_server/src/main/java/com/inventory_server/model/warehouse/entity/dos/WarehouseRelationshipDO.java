package com.inventory_server.model.warehouse.entity.dos;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.inventory_server.infrastructures.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.experimental.SuperBuilder;


/**
 * @Description 森帛仓库与易仓仓库映射表实体类
 * <AUTHOR>
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_warehouse_relationship")
public class WarehouseRelationshipDO extends BaseEntity {
  @TableId(type = IdType.AUTO)
  private String id;
  private String ycWarehouseId;
  private String senboWarehouseId;

}
