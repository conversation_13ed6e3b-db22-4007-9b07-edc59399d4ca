package com.inventory_server.infrastructures.assembler;

import com.inventory_server.model.inventory.entity.dos.InventoryInventoryInfoDO;
import com.inventory_server.model.inventory.entity.dto.YcInventoryInfoDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring", uses = RedundantInventoryConvert.class)
public interface InventoryInfoAssembler {

    @Mapping(source = "productSku", target = "selfSku")
    InventoryInventoryInfoDO convertDtoToDo(YcInventoryInfoDto dto);

    List<InventoryInventoryInfoDO> convertDtoListToDoList(List<YcInventoryInfoDto> dtoList);
}
