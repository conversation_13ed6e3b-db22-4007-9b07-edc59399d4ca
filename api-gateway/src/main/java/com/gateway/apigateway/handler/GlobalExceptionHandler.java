package com.gateway.apigateway.handler;

import com.gateway.apigateway.config.CustomizedException;
import org.springframework.boot.autoconfigure.web.ErrorProperties;
import org.springframework.boot.autoconfigure.web.WebProperties;
import org.springframework.boot.autoconfigure.web.reactive.error.DefaultErrorWebExceptionHandler;
import org.springframework.boot.web.reactive.error.ErrorAttributes;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: 雪竹
 * @description: 全局异常处理类
 * @dateTime: 2023/11/24 16:20
 **/
public class GlobalExceptionHandler extends DefaultErrorWebExceptionHandler {

    public GlobalExceptionHandler(ErrorAttributes errorAttributes, WebProperties.Resources resources, ErrorProperties errorProperties, ApplicationContext applicationContext) {
        super(errorAttributes, resources, errorProperties, applicationContext);
    }

    @Override
    protected Mono<ServerResponse> renderErrorResponse(ServerRequest request) {
        int status;

        Map<String, Object> responseBodyMap = new HashMap<>();
        // 获取DefaultErrorAttributes整理出来的所有异常信息
        Map<String, Object> errorMap = getErrorAttributes(request, getErrorAttributeOptions(request, MediaType.ALL));

        // 原始异常信息
        Throwable throwable = getError(request);
        if(throwable instanceof CustomizedException customizedException) {
            // 封装异常信息
            status = customizedException.getHttpStatus().value();
            responseBodyMap.put("status", customizedException.getStatus());
            responseBodyMap.put("message", customizedException.getMessage());
            responseBodyMap.put("data", null);
        } else {
            // 如果不是咱们定制的异常，就维持和父类一样的逻辑
            // 返回码
            status = getHttpStatus(errorMap);
            // body内容
            responseBodyMap.putAll(errorMap);
        }

        return ServerResponse.status(status)
                .contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromValue(responseBodyMap));
    }
}
