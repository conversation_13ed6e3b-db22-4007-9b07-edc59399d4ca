package com.purchase.purchase_server.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryFactoryRedundantInfoDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -7002010941271187325L;
    private String inventoryInfoId;
    private Double redundantNum;
    private String watchBoardId;
    private String factoryPlanId;
    private String contractCode;
    private String factoryFinishedDate;
    private String destinationSku;
    /**
     * 状态
     * @see com.purchase.purchase_server.enums.FactoryPlanInfoEnum
     */
    private String  isProduct;
    /**
     * 已调拨数量
     */
    private Double addRedundantNum;
    /**
     * 虚拟sku
     */
    private String virtualSku;
}
