package com.product.product_server.model.products;

import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.utils.utils.PatternUtils;

/**
 * @Description 供应商代码
 * <AUTHOR>
 * @Date 2024/1/8 11:11
 **/
public record ImportFactoryInfoDp(String factoryCode) {
    public ImportFactoryInfoDp {
        validFactoryCode(factoryCode);
    }

    private void validFactoryCode(String factoryCode) {
        if(StrUtil.isBlank(factoryCode)) {
            throw new IllegalArgumentException("供应商代码为空");
        } if(factoryCode.length() > 10) {
            throw new IllegalArgumentException("供应商代码长度超出限制");
        } else if (!PatternUtils.UPPERCASE_LOWERCASE_NUMBER_DOT_PATTERN.matcher(factoryCode).matches()) {
            throw new IllegalArgumentException("供应商代码只能英文、数字和符号的组合");
        }
    }
}