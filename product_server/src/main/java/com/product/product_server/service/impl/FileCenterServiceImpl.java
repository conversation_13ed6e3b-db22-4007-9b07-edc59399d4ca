package com.product.product_server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.product.product_server.entity.form.FileMissionPageForm;
import com.product.product_server.entity.response.ResultDTO;
import com.product.product_server.entity.vo.MissionCenterVo;
import com.product.product_server.entity.vo.UserInteriorVO;
import com.product.product_server.exception.BusinessException;
import com.product.product_server.service.IFileCenterService;
import com.product.product_server.service.ISysUserInteriorService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SystemConstant.*;
import static com.product.product_server.consts.FilePathConstant.FILE_PATH_PRODUCT_VIRTUAL;

@Service
@Slf4j
/**
 * <AUTHOR>
 * @date 2024/4/16
 **/
public class FileCenterServiceImpl implements IFileCenterService {

    @Resource
    protected RestTemplate restTemplate;

    @Resource
    private ISysUserInteriorService sysUserIneriorService;

    @Override
    public MultiValueMap<String, Object> putFile(byte[] byteArrayResource, String fileName, String key, String expireTime) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            ByteArrayResource byteFile  = new ByteArrayResource(byteArrayResource) {
                @Override
                public String getFilename() {
                    return fileName;
                }
            };
            params.clear();
            params.add("avatar", byteFile);
            params.add("key", key);
            params.add("expireTime", expireTime);
            return params;
        }catch (Exception e){
            log.error("上传文件异常", e);
            return null;
        }

    }

    @Override
    public IPage<MissionCenterVo> getMissionPage(FileMissionPageForm form) {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO page = restTemplateUtil.post(form, ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_MISSION_PAGE_LIST);
        IPage iPage = JSON.to(IPage.class, page.getData());
        List list = JSON.to(List.class, iPage.getRecords());
        if(CollectionUtil.isEmpty(list)) {
            return new Page<>(form.getCurrent(), form.getSize(), 0);
        }
        IPage<MissionCenterVo> resultPage = new Page<>();
        List<MissionCenterVo> missionCenterVoList = new ArrayList<>();
        List<UserInteriorVO> userList = sysUserIneriorService.getUserList();
        Map<String, String> userMap = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        list.forEach(i -> {
            MissionCenterVo missionCenterVo = JSON.to(MissionCenterVo.class, i);
            missionCenterVo.setCreateBy(userMap.getOrDefault(missionCenterVo.getCreateBy(), missionCenterVo.getCreateBy()));
            missionCenterVo.setFilePath(null);
            missionCenterVoList.add(missionCenterVo);
        });
        resultPage.setRecords(missionCenterVoList);
        resultPage.setTotal(iPage.getTotal());
        resultPage.setCurrent(iPage.getCurrent());
        resultPage.setSize(iPage.getSize());
        return resultPage;
    }

    @Override
    public String selectFileInfo(String missionId) {
        FileMissionPageForm form = new FileMissionPageForm();
        form.setMissionId(missionId);
        form.setCurrent(1);
        form.setSize(1);
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        MissionCenterVo missionCenter = restTemplateUtil.post(form, MissionCenterVo.class, FILE_SYSTEM_MISSION_CENTER_MISSION_LIST);

        if (ObjectUtil.isEmpty(missionCenter) || StrUtil.isBlank(missionCenter.getFilePath())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "源文件不存在");
        }

        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        String format = String.format(FILE_SYSTEM_MISSION_CENTER_MISSION_GET_URL + "?url=%s", FILE_PATH_PRODUCT_VIRTUAL + missionCenter.getFilePath());
        ResultDTO<String> resultDTO = restTemplateUtils.get(format, ResultDTO.class);
        return Optional.ofNullable(resultDTO).orElse(ResultDTO.success("")).getData();
    }
}
