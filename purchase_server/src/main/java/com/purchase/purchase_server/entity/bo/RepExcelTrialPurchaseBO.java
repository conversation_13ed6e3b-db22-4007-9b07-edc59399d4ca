package com.purchase.purchase_server.entity.bo;

import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.util.Date;

/**
 * @Description 补货计划表导出-虚拟SKU&仓库明细
 * <AUTHOR>
 * @Date 2024/1/21 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RepExcelTrialPurchaseBO extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 8877195239936417341L;
    /**
     * 供应商编码
     */
    private String factoryCode;
    /**
     * 采购人
     */
    private String purchaser;
    /**
     * 产品经理
     */
    private String buyer;
    /**
     * skuId
     */
    private String selfSkuId;
    /**
     * sku
     */
    private String selfSku;
    /**
     * 品名
     */
    private String productName;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 虚拟skuId
     */
    private String virtualSkuId;
    /**
     * 虚拟sku
     */
    private String virtualSku;
    /**
     * 运营
     */
    private String operator;
    /**
     * 子体类型
     */
    private Integer subType;
    /**
     * 产品状态
     */
    private Integer productStatus;
    /**
     * 产品类型
     */
    private Integer productType;
    /**
     * 实际日销
     */
    private Double actualDailySalesNum;
    /**
     * 试算当月目标日销
     */
    private Double targetSalesNum;
    /**
     * 子体达成率
     */
    private Double subEntityRate;
    /**
     * 父体达成率
     */
    private Double parentEntityRate;
    /**
     * 单箱数量
     */
    private Integer adviceContainerLoadNum;
    /**
     * 目标仓
     */
    private String destinationWarehouse;
    /**
     * 理论补货量
     */
    private Integer advicePurchaseNum;
    /**
     * 补货量类型
     */
    private String purchaseNumType;
    /**
     * 建议补货量
     */
    private Double actualReplenishmentNum;
    /**
     * 运营确认量
     */
    private Double operationConfirmedNum;
    /**
     * 运营确认理由
     */
    private String confirmedReason;
    /**
     * 运营补充说明
     */
    private String operatorRemark;
    /**
     * 建议采购时间
     */
    private Date advicePurchaseDate;
    /**
     * 预计交货时间
     */
    private Date expectedFactoryFinishedDate;
    /**
     * 预计装柜时间
     */
    private Date expectedShippingStartDate;
    /**
     * 预计到仓时间
     */
    private Date expectedArrivingDate;
    /**
     * 建议原因
     */
    private String reason;

    /**
     * 工厂交期
     */
    private String purchaseDate;

    /**
     * 采购下单天数
     */
    private Integer purchaseDays;

    /**
     * 是否修改过到货日期
     */
    private String isChangedArrivingDate;
    /**
     * 全链路海外仓理论售罄时间
     */
    private Date fullLinkTheoreticalSoldOutDate;

    /**
     * 全链路售罄前断货天数
     */
    private Integer fullLinkDaysBeforeSoldOut;
}
