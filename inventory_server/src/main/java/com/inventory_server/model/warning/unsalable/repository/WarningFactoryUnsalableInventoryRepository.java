package com.inventory_server.model.warning.unsalable.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.inventory_server.model.redundancy.entity.dos.InventoryFactoryPlanInfoDO;
import com.inventory_server.model.warning.unsalable.entity.dos.WarningFactoryUnsalableInventoryDO;
import com.inventory_server.model.warning.unsalable.entity.dto.FactoryUnsalableDto;
import com.inventory_server.model.warning.unsalable.entity.dto.ForeignUnsalableDto;
import com.inventory_server.model.warning.unsalable.mapper.WarningFactoryUnsalableInventoryMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description 工厂无计划库存持久层
 * <AUTHOR>
 * @Date 2025/4/17 11:24
 **/
@Service
public class WarningFactoryUnsalableInventoryRepository extends ServiceImpl<WarningFactoryUnsalableInventoryMapper, WarningFactoryUnsalableInventoryDO> {


    public void deleteFactoryUnsalableByUnsalableId(String unsalableId) {
        baseMapper.delete(Wrappers.<WarningFactoryUnsalableInventoryDO>lambdaQuery().eq(WarningFactoryUnsalableInventoryDO::getUnsalableInventoryId, unsalableId));
    }

    public List<FactoryUnsalableDto> getByUnsalableId(String unsalableId) {
        return getByUnsalableIdList(Collections.singletonList(unsalableId));
    }

    public List<FactoryUnsalableDto> getByUnsalableIdList(List<String> unsalableIdList) {
        if (CollectionUtil.isEmpty(unsalableIdList)) {
            return new ArrayList<>();
        }
        return baseMapper.selectJoinList(FactoryUnsalableDto.class, new MPJLambdaWrapper<WarningFactoryUnsalableInventoryDO>()
                .selectAs(WarningFactoryUnsalableInventoryDO::getUnsalableInventoryId, ForeignUnsalableDto::getUnsalableId)
                .select(WarningFactoryUnsalableInventoryDO::getUnsalableNum)
                .select(InventoryFactoryPlanInfoDO::getFactoryFinishedDate)
                .select(InventoryFactoryPlanInfoDO::getRemarks)
                .select(InventoryFactoryPlanInfoDO::getContractCode)
                .select(InventoryFactoryPlanInfoDO::getVirtualSkuId)
                .select(InventoryFactoryPlanInfoDO::getIsOld)

                .leftJoin(InventoryFactoryPlanInfoDO.class, InventoryFactoryPlanInfoDO::getId, WarningFactoryUnsalableInventoryDO::getFactoryPlanId)
                .in(WarningFactoryUnsalableInventoryDO::getUnsalableInventoryId, unsalableIdList)
        );
    }
}
