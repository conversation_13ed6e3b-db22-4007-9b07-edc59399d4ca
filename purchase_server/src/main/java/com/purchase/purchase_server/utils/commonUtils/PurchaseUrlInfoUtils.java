package com.purchase.purchase_server.utils.commonUtils;

import com.crafts_mirror.utils.utils.UrlInfo;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description 根据url整理得出的业务类型、日志类型等访问记录信息
 * <AUTHOR>
 * @Date 2024/8/10 17:58
 **/
@Getter
public class PurchaseUrlInfoUtils extends UrlInfo {

    private String serviceUrlPrefix;

    private ServiceLogType serviceLogType;

    public PurchaseUrlInfoUtils(String originalUrl) {
        super(originalUrl);
        initServiceUrl();
    }

    private void initServiceUrl() {
        if (originalUrlArray.length < 2) {
            serviceUrlPrefix = "";
        } else {
            serviceUrlPrefix = originalUrlArray[1];
        }
        serviceLogType = ServiceLogType.getServiceType(serviceUrlPrefix);
    }

    @Getter
    public enum ServiceLogType {
        DELIVERY("deliveryPurchase", "发货模块"),
        REPLENISHMENT("replenishment", "补货模块"),
        FACTORY("factory", "供应商模块"),
        UNKNOWN_TYPE( "", "未知类型"),
        ;
        private final String servicePrefix;

        private final String desc;

        ServiceLogType(String servicePrefix, String desc) {
            this.servicePrefix = servicePrefix;
            this.desc = desc;
        }

        public static ServiceLogType getServiceType(String servicePrefix) {
            return Arrays.stream(ServiceLogType.values()).filter(f -> f.servicePrefix.equals(servicePrefix)).findFirst().orElse(UNKNOWN_TYPE);
        }
    }
}
