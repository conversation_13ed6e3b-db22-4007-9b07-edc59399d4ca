package com.purchase.purchase_server.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentRecordDO;
import org.apache.ibatis.annotations.Param;

public interface ReplenishmentRecordMapper extends MPJBaseMapper<ReplenishmentRecordDO> {

    int deleteById(@Param("recordId") String recordId);

    void updateAllRecord(@Param("val") ReplenishmentRecordDO val);
}