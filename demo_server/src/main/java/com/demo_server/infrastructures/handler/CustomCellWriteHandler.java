package com.demo_server.infrastructures.handler;

import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;

/**
 * @Description 异常处理类
 * <AUTHOR>
 * @Date 2023/12/12 15:38
 **/
@Slf4j
public class CustomCellWriteHandler  implements CellWriteHandler {

    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        Cell cell = context.getCell();
        // 这里可以对cell进行任何操作
        log.info("第{}行，第{}列写入完成。",cell.getRowIndex() , cell.getColumnIndex());
        if (cell.getRowIndex() > 1 && cell.getColumnIndex() > 2){
                //将第三列的数据设置为数字类型
                cell.setCellValue(Double.parseDouble(cell.getStringCellValue()));
        }
    }
}
