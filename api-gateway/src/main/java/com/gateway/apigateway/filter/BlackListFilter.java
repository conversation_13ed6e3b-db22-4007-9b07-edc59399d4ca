package com.gateway.apigateway.filter;

import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.utils.constant.RedisKeyConstant;
import com.crafts_mirror.utils.utils.IPUtils;
import com.gateway.apigateway.config.CustomizedException;
import jakarta.annotation.Resource;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * @Description 黑名单过滤器
 * <AUTHOR>
 * @Date 2024/2/1 11:57
 **/
@Component
public class BlackListFilter implements GlobalFilter {
    
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String ipAddress = IPUtils.getIpAddress(request);
        String blackList = stringRedisTemplate.opsForValue().get(RedisKeyConstant.BLACK_LIST_PREFIX + ipAddress);
        if (StrUtil.isNotBlank(blackList)) {
            return Mono.error(new CustomizedException(HttpStatus.UNAUTHORIZED, "401", "您暂时在小黑屋里面无法出来，请联系研发人员处理"));
        }
        return chain.filter(exchange.mutate().request(request).build());
    }

}
