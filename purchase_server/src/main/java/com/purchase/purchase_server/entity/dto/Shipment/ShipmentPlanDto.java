package com.purchase.purchase_server.entity.dto.Shipment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/6/20 16:49
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentPlanDto implements Serializable {

    /**
     * 主键id
     */
    private String id;
    /**
     * 货件计划编号
     */
    private String shipmentCode;

    /**
     * 仓库id
     */
    private String warehouseId;

}
