package com.purchase.purchase_server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum DeliveryImportHeadEnum {
    CONTRACT_CODE("*合同号", "contractCode"),
    VIRTUAL_SKU("*虚拟SKU","virtualSku"),
    DELIVERY_DATE("*工厂交期","deliveryDate"),
    SHIPMENTS("*数量","shipments"),
    REMARK("备注","remark");



    private final String code;
    private final String desc;
    public static DeliveryImportHeadEnum ofCode(String code) {
        return Arrays.stream(DeliveryImportHeadEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
    public static List<String> getCodeList() {
        return Arrays.stream(DeliveryImportHeadEnum.values())
                .map(DeliveryImportHeadEnum::getCode).collect(Collectors.toList());
    }
}
