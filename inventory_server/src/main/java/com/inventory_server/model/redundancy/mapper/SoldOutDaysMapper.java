package com.inventory_server.model.redundancy.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.inventory_server.model.redundancy.entity.dos.InventorySoldOutDaysDO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface SoldOutDaysMapper extends MP<PERSON><PERSON>aseMapper<InventorySoldOutDaysDO> {

    @Select("SELECT SUM(CEIL(lack_num)) FROM `cm_inventory_sold_out_days` WHERE inventory_info_id = #{inventoryId} AND type = #{type};")
    Double sumLackNumByInventoryId(@Param("inventoryId") String inventoryId, @Param("type") String type);

    @MapKey("inventoryInfoId")
    Map<String, Map<String, Object>> sumLackNumByInventoryIdList(List<String> inventoryInfoIdList, String type);
}
