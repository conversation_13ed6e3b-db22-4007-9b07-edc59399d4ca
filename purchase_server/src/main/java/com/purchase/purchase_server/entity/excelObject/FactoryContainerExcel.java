package com.purchase.purchase_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description excel导入供应商-sheet2转换成的实体列
 * <AUTHOR>
 * @Date 2024/6/6 11:39
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryContainerExcel extends BaseExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = 6643311131665434017L;
    @Length(max = 20, message = "供应商代码不超20字符")
    @ExcelProperty("供应商代码")
    private String factoryCode;

    @Length(max = 100, message = "省份不超100字符")
    @ExcelProperty("省份")
    private String province;

    @Length(max = 100, message = "城市不超100字符")
    @ExcelProperty("城市")
    private String city;

    @Length(max = 255, message = "详细地址不超255字符")
    @ExcelProperty("详细地址")
    private String detailedAddress;

    @Length(max = 20, message = "地址代号不超20字符")
    @ExcelProperty("地址代号")
    private String addressCode;
}
