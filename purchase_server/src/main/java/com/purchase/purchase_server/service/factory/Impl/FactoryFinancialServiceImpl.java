package com.purchase.purchase_server.service.factory.Impl;


import com.purchase.purchase_server.assembler.FactoryAssembler;
import com.purchase.purchase_server.entity.dataObject.FactoryFinancialDO;
import com.purchase.purchase_server.entity.dto.factory.FactoryFinancialDto;
import com.purchase.purchase_server.repository.dataRepository.FactoryFinancialRepositoryImpl;
import com.purchase.purchase_server.service.factory.IFactoryFinancialService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 供应商的service层
 * <AUTHOR>
 * @Date 2024/6/6 16:38
 **/
@Service
@Slf4j
public class FactoryFinancialServiceImpl implements IFactoryFinancialService {

    @Resource
    private FactoryFinancialRepositoryImpl factoryFinancialRepository;

    @Resource
    private FactoryAssembler factoryAssembler;

    @Override
    public FactoryFinancialDO getByFactoryInfoId(String factoryInfoId) {
        return factoryFinancialRepository.listByfactoryInfoId(factoryInfoId);
    }

    @Override
    public boolean insertOrUpdateFinancialInfo(List<FactoryFinancialDto> factoryFinancialDtoList) {
        List<FactoryFinancialDO> factoryFinancialDOS = factoryAssembler.factoryFinancialDtoListToDoList(factoryFinancialDtoList);
        return factoryFinancialRepository.saveOrUpdateBatch(factoryFinancialDOS);
    }

    @Override
    public void deleteByFactoryInfoId(String factoryInfoId) {
        factoryFinancialRepository.deleteByFactoryInfoId(factoryInfoId);
    }


}
