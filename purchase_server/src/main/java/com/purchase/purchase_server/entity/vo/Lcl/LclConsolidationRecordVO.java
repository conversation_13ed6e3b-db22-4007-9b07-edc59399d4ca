package com.purchase.purchase_server.entity.vo.Lcl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 易仓采购单vo
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LclConsolidationRecordVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 841791551790392036L;
    private String id;
    /**
     * 计划名称
     */
    private String shippingRecordName;

    /**
     * 发货记录id
     */
    private String shippingRecordId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 状态
     */
    private String lclStatus;

    /**
     * 最早发货装柜时间
     */
    private String shippingStartDate;

    /**
     * 最晚发货装柜时间
     */
    private String shippingEndDate;

    /**
     * 清洗装柜开始时间
     */
    private String lclStartDate;

    /**
     * 清洗装柜结束时间
     */
    private String lclEndDate;
}