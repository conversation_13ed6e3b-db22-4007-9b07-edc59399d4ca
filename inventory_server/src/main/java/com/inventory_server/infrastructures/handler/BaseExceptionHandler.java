package com.inventory_server.infrastructures.handler;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.exception.NotLoginException;
import com.crafts_mirror.utils.exception.NotPermissionException;
import com.inventory_server.applications.vo.response.ResultDTO;
import com.inventory_server.infrastructures.exception.BusinessException;
import com.inventory_server.infrastructures.exception.RequestTooFrequentlyException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MultipartException;

import java.nio.file.AccessDeniedException;
import java.text.MessageFormat;
import java.util.List;

/**
 * @Description 异常处理类
 * <AUTHOR>
 * @Date 2023/12/12 15:38
 **/
@RestControllerAdvice()
@Slf4j
public class BaseExceptionHandler {

    @ExceptionHandler(value = MultipartException.class)
    public ResultDTO<Void> fileUploadExceptionHandler(MultipartException exception) {
        return ResultDTO.error(exception.getMessage() + "导入失败，文件大小超过50M");
    }

    @ExceptionHandler(value = NotPermissionException.class)
    public ResultDTO handleNotPermissionException(NotPermissionException e) {
        log.error("系统未知异常：{}", e.getMessage());
        return ResultDTO.error(ResponseCodeEnum.FORBIDDEN.getCode(), "没有访问权限，请联系管理员授权");
    }

    @ExceptionHandler(value = NotLoginException.class)
    public ResultDTO handleNotLoginException(NotLoginException e) {
        log.error("系统未知异常：{}", e.getMessage());
        return ResultDTO.error(ResponseCodeEnum.FORBIDDEN.getCode(), "发生未知异常: "+e.getMessage());
    }

    @ExceptionHandler(value = RuntimeException.class)
    public ResultDTO<String> runtimeExceptionHandler(RuntimeException exception) {
        log.error("系统未知异常：", exception);
        return ResultDTO.error("系统异常，请联系开发人员");
    }

    @ExceptionHandler(value = IllegalArgumentException.class)
    public ResultDTO<String> illegalArgumentExceptionHandler(IllegalArgumentException exception) {
        log.error("系统异常：", exception);
        return ResultDTO.error("系统异常，异常原因：" + exception.getMessage());
    }

    @ExceptionHandler(value = Exception.class)
    public ResultDTO<Void> exceptionHandler(Exception exception) {
        log.error("系统未知异常：", exception);
        return ResultDTO.error("未知错误，请联系开发人员");
    }

    @ExceptionHandler(value = ExcelAnalysisException.class)
    public ResultDTO<Void> excelAnalysisExceptionHandler(ExcelAnalysisException exception) {
        log.error("文件格式异常：", exception);
        return ResultDTO.error("文件格式异常，请上传excel文件或者excel文件中添加一个sheet页");
    }

    /**
     * 业务异常捕获
     */
    @ExceptionHandler(value = BusinessException.class)
    public ResultDTO<Void> defaultErrorHandler(HttpServletRequest req, BusinessException e) {
        log.error("系统未知异常：{}", e.getMsg());
        return ResultDTO.error(e.getCode(), e.getMsg());
    }

    /**
     * 参数校验异常绑定返回
     *
     * @param exception 参数校验异常
     * @return 返回参数校验失败的描述信息
     */
    //@ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({MethodArgumentNotValidException.class})
    public ResultDTO handleBodyValidException(MethodArgumentNotValidException exception) {
        String message = exception.getMessage();
        String paramEntity = message.substring(message.indexOf("(") + 1, message.lastIndexOf(")"));
        BindingResult br = exception.getBindingResult();
        List<ObjectError> allErrors = br.getAllErrors();
        StringBuilder outErrorMessage = new StringBuilder();
        if (!allErrors.isEmpty()) {
            for (ObjectError error : allErrors) {
                outErrorMessage.append(error.getDefaultMessage()).append(";\n");
            }
        }
        String errorMessage = (br.getFieldError() != null) ? MessageFormat.format("fieldName:[{0}] filedValue:[{1}] error:[{2}]", br.getFieldError().getField(), br.getFieldError().getRejectedValue(), br.getFieldError().getDefaultMessage()) : "";
        log.warn("URI:{} \n Message:{}", paramEntity, errorMessage);
        return ResultDTO.error(ResponseCodeEnum.BAD_REQUEST.getCode(), outErrorMessage.toString());
    }

    @ExceptionHandler({HttpMessageNotReadableException.class})
    public ResultDTO<Void> handleNotReadableException(HttpMessageNotReadableException exception) {
        log.info(exception.getMessage());
        return ResultDTO.error(ResponseCodeEnum.BAD_REQUEST.getCode(), "请求参数不合法");
    }

    /**
     * 处理ConstraintViolationException
     *
     * @param e
     * @return
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResultDTO<Void> handleConstraintViolationException(ConstraintViolationException e) {
        log.error("参数校验错误", e);

        return ResultDTO.error(HttpStatus.BAD_REQUEST.value(), e.getConstraintViolations().iterator().next().getMessage());
    }

    /**
     * 处理MissingServletRequestParameterException
     *
     * @param e 请求参数确实异常
     * @return
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResultDTO<Void> handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        log.error("参数错误", e);

        return ResultDTO.error(HttpStatus.BAD_REQUEST.value(), "参数错误");
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResultDTO<Void> accessDeniedException(AccessDeniedException e) {
        log.error("缺少该功能的权限， 请联系上级管理员获取权限并重新登录", e);

        return ResultDTO.error(HttpStatus.BAD_REQUEST.value(), "缺少该功能的权限， 请联系上级管理员获取权限并重新登录");
    }

    @ResponseBody
    @ExceptionHandler(value = RequestTooFrequentlyException.class)
    public ResultDTO handleRequestTooFrequently(RequestTooFrequentlyException e, HttpServletRequest request)
    {
        log.error(e.getMessage());
        return ResultDTO.error(ResponseCodeEnum.BAD_REQUEST.getCode(), e.getMessage());
    }
}
