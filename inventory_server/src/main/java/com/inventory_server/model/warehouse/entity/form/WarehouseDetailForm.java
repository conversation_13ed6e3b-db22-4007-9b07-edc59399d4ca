package com.inventory_server.model.warehouse.entity.form;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 获取仓库详情请求参数
 * <AUTHOR>
 * @Date 2024/7/3 14:10
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WarehouseDetailForm implements Serializable {
    @JSONField(name = "warehouse_code_list")
    private List<String> warehouseCodeList;
}
