package com.inventory_server.model.inventory.entity.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

public class AsyncInventoryResultDto {

    @Setter
    private int total;

    private final AtomicInteger succeedCount;

    private final AtomicInteger failedCount;

    @Getter
    private final List<String> failedReasonList;

    public AsyncInventoryResultDto(Integer total) {
        this.total = total;
        this.succeedCount = new AtomicInteger(0);
        this.failedCount = new AtomicInteger(0);
        this.failedReasonList = new ArrayList<>();
    }

    public void addFailedReason(String reason) {
        failedReasonList.add(reason);
    }

    public void addSucceedCount(Integer count) {
        succeedCount.addAndGet(count);
    }

    public void addFailedCount(Integer count) {
        failedCount.addAndGet(count);
    }

    public int getSucceedCount() {
        // 异步方法可能会导致最后一次请求没有返回到结果就结束了子线程。而且成功数量会以100为最小单位增加，因此在此处判定，
        // 若总数 - 成功次数 > 100，则肯定是出现了问题，否则视为全部成功
        return total - succeedCount.get() > 100 ? succeedCount.get() : total;
    }

    public int getFailedCount() {
        if (failedCount.get() > 0) {
            return failedCount.get();
        } else {
            int failedCount = total - getSucceedCount();
            return Math.max(failedCount, 0);
        }
    }
}
