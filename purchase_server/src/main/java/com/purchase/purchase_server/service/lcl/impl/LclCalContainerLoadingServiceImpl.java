package com.purchase.purchase_server.service.lcl.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.enums.YesOrNoEnum;
import com.crafts_mirror.utils.utils.DateUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.assembler.LclConsolidationAssembler;
import com.purchase.purchase_server.entity.BaseEntity;
import com.purchase.purchase_server.entity.bo.Lcl.LclContainerBO;
import com.purchase.purchase_server.entity.bo.Lcl.LclContainerLoadingBO;
import com.purchase.purchase_server.entity.dataObject.*;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclConsolidationRecordDO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclContainerDetailDO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclContainerInfoDO;
import com.purchase.purchase_server.entity.dto.Lcl.ContainerLoadingResultDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclConLoadingWithDetailDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclContainerDetailInfoDto;
import com.purchase.purchase_server.entity.dto.Lcl.LclShipmentCategoryDto;
import com.purchase.purchase_server.entity.dto.ProductCategoryDTO;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.form.*;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.Lcl.LclContainerDetailVo;
import com.purchase.purchase_server.entity.vo.Lcl.LclContainerInfoPage;
import com.purchase.purchase_server.entity.vo.Lcl.LclContainerInfoVo;
import com.purchase.purchase_server.entity.vo.Lcl.LclShipmentCategoryAggVo;
import com.purchase.purchase_server.enums.DeliveryTypeEnum;
import com.purchase.purchase_server.enums.lcl.LclDetailSortEnum;
import com.purchase.purchase_server.enums.lcl.PCSTypeEnum;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.repository.dataRepository.Lcl.LclConsolidationFinishedInventoryRepositoryImpl;
import com.purchase.purchase_server.repository.dataRepository.Lcl.LclConsolidationRecordRepositoryImpl;
import com.purchase.purchase_server.repository.dataRepository.Lcl.LclContainerDetailRepositoryImpl;
import com.purchase.purchase_server.repository.dataRepository.Lcl.LclContainerInfoRepositoryImpl;
import com.purchase.purchase_server.repository.dataRepository.ProductSnapRepositoryImpl;
import com.purchase.purchase_server.repository.interiorRepository.WarehouseRepository;
import com.purchase.purchase_server.service.lcl.ILclCalContainerLoadingService;
import com.purchase.purchase_server.service.lcl.ILclContainerLoadingDetailService;
import com.purchase.purchase_server.utils.commonUtils.ShipmentCodeGenerator;
import jakarta.annotation.Resource;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.RedisKeyConstant.EDIT_LCL_DATA_CLEAN_LOCK;
import static com.crafts_mirror.utils.constant.SystemConstant.PRODUCTS_SELF_CATEGORY_LEAF_TREE_URL;
import static com.purchase.purchase_server.enums.lcl.LclStatusEnum.PRETENDING_SAVE;
import static java.math.RoundingMode.UP;

/**
 * <AUTHOR>
 * @date 2025/5/17
 **/
@Service
@Slf4j
public class LclCalContainerLoadingServiceImpl implements ILclCalContainerLoadingService {

    @Resource
    private LclConsolidationFinishedInventoryRepositoryImpl lclConFinishedInventoryRepository;

    @Resource
    private LclConsolidationRecordRepositoryImpl consolidationRecordRepository;

    @Resource
    private LclConsolidationAssembler lclConsolidationAssembler;

    @Resource
    private LclContainerInfoRepositoryImpl lclContainerInfoRepository;

    @Resource
    private LclContainerDetailRepositoryImpl lclContainerDetailRepository;

    @Resource
    private ILclContainerLoadingDetailService lclContainerLoadingDetailService;

    @Resource
    private ProductSnapRepositoryImpl productSnapRepository;

    @Resource
    private WarehouseRepository warehouseRepository;

    @Resource
    protected RestTemplate restTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Async("defaultThreadPool")
    public void lclContainerLoading(LclConsolidationForm form, LclConsolidationRecordDO lclRecordDO) throws InterruptedException {

        String shippingStartDate = form.getShippingStartDate();
        String shippingEndDate = form.getShippingEndDate();

        List<LclContainerLoadingBO> lclContainerLoadingBOS = lclConFinishedInventoryRepository.listWithTrial(form);

        // 没有发货与加急的数据直接跳过
        // 加急怎么装
        Map<Boolean, List<LclContainerLoadingBO>> collect = lclContainerLoadingBOS.stream()
                .collect(Collectors.partitioningBy(i -> {
                    boolean equals = DeliveryTypeEnum.URGENT.getCode().equals(i.getDeliveryType());
                    return StrUtil.isNotBlank(i.getTrialId()) && !equals;
                }));


        List<LclContainerLoadingBO> lclContainerLoadingBOSList = collect.get(true).stream()
                .filter(i -> i.getLclShippingNum() > 0).collect(Collectors.toList());
        lclContainerLoadingBOSList
                .forEach(item -> item.setVolume((item.getCaseLength() / 100) * (item.getCaseWidth() / 100) * (item.getCaseHeight() / 100)));

        Map<String, List<LclContainerLoadingBO>> groupedByWarehouse = lclContainerLoadingBOSList.stream().collect(Collectors.groupingBy(LclContainerLoadingBO::getDestinationWarehouse));

        List<LclContainerBO> containerBOFinishedDate = new ArrayList<>();

        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> warehouseMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

        // 无发货计划的数据
        List<LclContainerLoadingBO> nonShipItemsList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(groupedByWarehouse)) {
            for (var lclByWarehouses : groupedByWarehouse.entrySet()) {
                String warehouse = lclByWarehouses.getKey();
                List<LclContainerLoadingBO> lclLoadingByWarehouses = lclByWarehouses.getValue();
                // 按发货日期排序
                lclLoadingByWarehouses.sort(Comparator.comparing(LclContainerLoadingBO::getShippingStartDate));

                // 处理每个7天批次
                while (!lclLoadingByWarehouses.isEmpty()) {
                    Date currentBatchStartDate = DateUtil.parse(lclLoadingByWarehouses.getFirst().getShippingStartDate());
                    // 计算当前批次的结束日期（+6天）,如果超过了shippingEndDate，则使用shippingEndDate
                    Date currentBatchEndDate = DateUtil.offsetDay(currentBatchStartDate, 6).after(DateUtil.parse(shippingEndDate))
                            ? DateUtil.parse(shippingEndDate)
                            : DateUtil.offsetDay(currentBatchStartDate, 6);
                    // 计算备胎数据的结束日期（+30天）
                    Date spareTireEndDate = DateUtil.offsetDay(currentBatchStartDate, 30);

                    // 分离当前批次和剩余数据
                    Map<Boolean, List<LclContainerLoadingBO>> batchSplit = lclLoadingByWarehouses.stream()
                            .collect(Collectors.partitioningBy(item -> {
                                Date itemDate = DateUtil.parse(item.getShippingStartDate());
                                return !itemDate.after(currentBatchEndDate);
                            }));

                    List<LclContainerLoadingBO> currentBatch = batchSplit.get(true);
                    List<LclContainerLoadingBO> remainingItems = batchSplit.get(false);

                    // 从剩余数据中分离备胎数据
                    Map<Boolean, List<LclContainerLoadingBO>> spareTireSplit = remainingItems.stream()
                            .collect(Collectors.partitioningBy(item -> {
                                Date itemDate = DateUtil.parse(item.getShippingStartDate());
                                return !itemDate.after(spareTireEndDate);
                            }));

                    List<LclContainerLoadingBO> spareTireDate = spareTireSplit.get(true);
                    List<LclContainerLoadingBO> nextBatchItems = spareTireSplit.get(false);

                    if (CollectionUtil.isNotEmpty(currentBatch)) {
                        if (warehouseMap.get(warehouse).contains("FBA")) {
                            // 将FBA仓库数据按照pcsType分类：大号标准一组，其他一组
                            Map<Boolean, List<LclContainerLoadingBO>> groupedByPcsType = currentBatch.stream()
                                    .collect(Collectors.partitioningBy(bo -> {
                                        String pcsType = bo.getPcsType();
                                        return pcsType != null && pcsType.equals(PCSTypeEnum.OVERSIZE_STANDARD.getCode().toString());
                                    }));

                            List<LclContainerLoadingBO> oversizeStandardItems = groupedByPcsType.get(true);
                            List<LclContainerLoadingBO> otherSizeItems = groupedByPcsType.get(false);

                            Map<Boolean, List<LclContainerLoadingBO>> groupedByPcsTypeSpareTireDate = spareTireDate.stream()
                                    .collect(Collectors.partitioningBy(bo -> {
                                        String pcsType = bo.getPcsType();
                                        return pcsType != null && pcsType.equals(PCSTypeEnum.OVERSIZE_STANDARD.getCode().toString());
                                    }));

                            List<LclContainerLoadingBO> oversizeStandardItemsSpareTireDate = groupedByPcsTypeSpareTireDate.get(true);
                            List<LclContainerLoadingBO> otherSizeItemsSpareTireDate = groupedByPcsTypeSpareTireDate.get(false);

                            // 在processContainerLoading执行后，更新lclLoadingByWarehouses
                            // 将修改后的备胎数据和下一批次数据合并
                            List<LclContainerLoadingBO> updatedRemainingItems = new ArrayList<>();

                            if (CollectionUtil.isNotEmpty(oversizeStandardItems)) {
                                ContainerLoadingResultDTO oversizeResult = processContainerLoading(oversizeStandardItems, warehouse, oversizeStandardItemsSpareTireDate,
                                        warehouseMap);
                                containerBOFinishedDate.addAll(oversizeResult.getContainers());
                                oversizeStandardItemsSpareTireDate.clear();
                                oversizeStandardItemsSpareTireDate.addAll(oversizeResult.getUpdatedSpareTireDate());
                                // 添加修改后的大号标准备胎数据（排除数量为0的项）
                                updatedRemainingItems.addAll(oversizeStandardItemsSpareTireDate.stream()
                                        .filter(item -> item.getLclShippingNum() > 0)
                                        .collect(Collectors.toList()));
                            }


                            if (CollectionUtil.isNotEmpty(otherSizeItems)) {
                                ContainerLoadingResultDTO otherResult = processContainerLoading(otherSizeItems, warehouse, otherSizeItemsSpareTireDate, warehouseMap);
                                containerBOFinishedDate.addAll(otherResult.getContainers());
                                otherSizeItemsSpareTireDate.clear();
                                otherSizeItemsSpareTireDate.addAll(otherResult.getUpdatedSpareTireDate());
                                // 添加修改后的其他尺寸备胎数据（排除数量为0的项）
                                updatedRemainingItems.addAll(otherSizeItemsSpareTireDate.stream()
                                        .filter(item -> item.getLclShippingNum() > 0)
                                        .collect(Collectors.toList()));
                            }

                            // 添加下一批次的数据
                            updatedRemainingItems.addAll(nextBatchItems);
                            updatedRemainingItems.sort(Comparator.comparing(LclContainerLoadingBO::getShippingStartDate));
                            lclLoadingByWarehouses = updatedRemainingItems;
                        } else {
                            ContainerLoadingResultDTO oversizeResult = processContainerLoading(currentBatch, warehouse, spareTireDate, warehouseMap);
                            containerBOFinishedDate.addAll(oversizeResult.getContainers());
                            spareTireDate.clear();
                            spareTireDate.addAll(oversizeResult.getUpdatedSpareTireDate());
                            // 在processContainerLoading执行后，更新lclLoadingByWarehouses
                            // 将修改后的备胎数据和下一批次数据合并
                            List<LclContainerLoadingBO> updatedRemainingItems = new ArrayList<>();
                            // 添加修改后的备胎数据（排除数量为0的项）
                            updatedRemainingItems.addAll(spareTireDate.stream()
                                    .filter(item -> item.getLclShippingNum() > 0)
                                    .collect(Collectors.toList()));
                            // 添加下一批次的数据
                            updatedRemainingItems.addAll(nextBatchItems);
                            updatedRemainingItems.sort(Comparator.comparing(LclContainerLoadingBO::getShippingStartDate));
                            lclLoadingByWarehouses = updatedRemainingItems;
                        }
                    } else {
                        break;
                    }
                }
            }
        }
        // 将 加急的数据安排进柜子
        List<LclContainerLoadingBO> urgentAndNonShipItemsList = collect.get(false);
        if (CollectionUtil.isNotEmpty(urgentAndNonShipItemsList)) {
            // 将加急 与 不发货的分开
            Map<Boolean, List<LclContainerLoadingBO>> urgentSplit = urgentAndNonShipItemsList.stream()
                    .collect(Collectors.partitioningBy(item -> {
                        return StrUtil.isBlank(item.getTrialId());
                    }));
            List<LclContainerLoadingBO> nonShipItems = urgentSplit.get(true);
            if (CollectionUtil.isNotEmpty(nonShipItems)) {
                nonShipItemsList.addAll(nonShipItems);
            }
            List<LclContainerLoadingBO> urgentItems = urgentSplit.get(false);

            if (CollectionUtil.isNotEmpty(urgentItems)) {
                // 将加急数据安排进柜子
                List<LclContainerLoadingBO> urgent = urgentItems.stream()
                        .filter(i -> i.getLclShippingNum() > 0).collect(Collectors.toList());
                urgent.forEach(item -> item.setVolume((item.getCaseLength() / 100) * (item.getCaseWidth() / 100) * (item.getCaseHeight() / 100)));
                List<LclContainerBO> lclContainerBOS = processUrgentContainerLoading(urgent, shippingEndDate, warehouseMap);
                if (CollectionUtil.isNotEmpty(lclContainerBOS)) {
                    containerBOFinishedDate.addAll(lclContainerBOS);
                }
            }
        }

        List<LclContainerInfoDO> containerList = new ArrayList<>();

        List<LclContainerLoadingBO> lclContainerLoadingBOList = new ArrayList<>();
        List<LclConLoadingWithDetailDTO> infoWithDetailList = new ArrayList<>();

        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO resultDTO = restTemplateUtil.get(PRODUCTS_SELF_CATEGORY_LEAF_TREE_URL, ResultDTO.class);
        Map<String, ProductCategoryDTO> categoryDTOMap = JSON.to(HashMap.class, resultDTO.getData());


        AtomicInteger i = new AtomicInteger(1);
        containerBOFinishedDate.forEach(container -> {
            List<LclContainerLoadingBO> loadedItems = container.getLoadedItems();
            LclContainerInfoDO containerInfoDO = lclContainerLoadingDetailService.lclContainerInfoByDetail(loadedItems, categoryDTOMap);
            containerInfoDO.setLclRecordId(form.getLclRecordId());
            containerInfoDO.setShipmentCode(container.getShipmentCode());
            containerList.add(containerInfoDO);
            infoWithDetailList.add(LclConLoadingWithDetailDTO.builder()
                    .infoDO(containerInfoDO)
                    .trialDetailList(loadedItems).build());
            lclContainerLoadingBOList.addAll(loadedItems);
            i.getAndIncrement();
        });

        containerList.sort(Comparator.comparing(LclContainerInfoDO::getShippingStartDate)
                .thenComparing(LclContainerInfoDO::getDestinationWarehouse)
                .thenComparing(LclContainerInfoDO::getAddressCode)
                .thenComparing(LclContainerInfoDO::getSourceType));

        int shipmentCodeNum = 1;
        int shipmentCodeLess20Num = 1;
        for (var item : containerList) {
            String shipmentCode = item.getShipmentCode();
            if (StrUtil.isBlank(shipmentCode)) {
                item.setShipmentCode("待生成的货件号" + shipmentCodeNum);
                shipmentCodeNum++;
            } else if (!shipmentCode.contains("加急")) {
                item.setShipmentCode("不到20方汇总" + shipmentCodeLess20Num);
                shipmentCodeLess20Num++;
            }
        }
        ;
        // 计算未安排数量
        //lclContainerLoadingBOList根据id分组
        Map<String, List<LclContainerLoadingBO>> groupedByFinishId = lclContainerLoadingBOList.stream().collect(Collectors.groupingBy(LclContainerLoadingBO::getId));
        for (var lclById : groupedByFinishId.entrySet()) {
            List<LclContainerLoadingBO> value = lclById.getValue();
            Integer factoryShippingPackageNum = value.getFirst().getFactoryShippingPackageNum();
            Integer lclShippingNum = value.stream().mapToInt(LclContainerLoadingBO::getLclShippingNum).sum();
            for (var item : value) {
                item.setDeliveryQuantity(factoryShippingPackageNum - lclShippingNum);
            }
        }
        lclContainerDetailRepository.deleteByLclRecordId(form.getLclRecordId());
        lclContainerInfoRepository.deleteByLclRecordId(form.getLclRecordId());
        for (var infoWithDetail : infoWithDetailList) {
            LclContainerInfoDO infoDO = infoWithDetail.getInfoDO();
            List<LclContainerLoadingBO> trialDetailList = infoWithDetail.getTrialDetailList();
            List<LclContainerDetailDO> lclContainerDetailDOLit = lclConsolidationAssembler.lclContainerListBOToDO(trialDetailList);
            lclContainerInfoRepository.save(infoDO);
            lclContainerDetailDOLit.forEach(item -> {
                item.setContainerInfoId(infoDO.getId());
                item.setId(null);
            });
            lclContainerDetailRepository.saveBatch(lclContainerDetailDOLit);
        }

        lclRecordDO.setLclStatus(PRETENDING_SAVE.getCode());
        consolidationRecordRepository.updateById(lclRecordDO);
    }

    @Override
    public LclContainerInfoPage<LclContainerInfoVo> getLclContainerInfoPage(LclSearchPageForm form) {
        LclContainerInfoPage<LclContainerInfoVo> lclContainerInfoPage = new LclContainerInfoPage<>();
        if (StrUtil.isBlank(form.getRecordId())) {
            return new LclContainerInfoPage<>();
        }

        String lock = stringRedisTemplate.opsForValue().get(EDIT_LCL_DATA_CLEAN_LOCK + form.getRecordId());
        if (StrUtil.isNotBlank(lock)) {
            LclConsolidationRecordDO recordDo = consolidationRecordRepository.getById(form.getRecordId());
            lclContainerInfoPage.setLclStatus(recordDo.getLclStatus());
            lclContainerInfoPage.setShippingStartDate(DateUtil.formatDate(recordDo.getLclContainerStartDate()));
            lclContainerInfoPage.setShippingEndDate(DateUtil.formatDate(recordDo.getLclContainerEndDate()));
            return lclContainerInfoPage;
        }

        LclConsolidationRecordDO recordDo = consolidationRecordRepository.getById(form.getRecordId());
        if (recordDo == null) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "装柜计划不存在");
        }

        if (form.getIsPackageFull() != null && form.getIsPackageFull() == 2) {
            form.setIsPackageFull(null);
        }

        if (!form.needSearchDetail()) {
            // 若无需查询装柜细节的话，直接找大表数据即可
            lclContainerInfoPage = lclContainerInfoRepository.getLclContainerInfoPage(form, new ArrayList<>());
        } else {
            List<String> detailIdList = getContainerDetailSnapshotIdList(form);
            List<String> containerIdList = lclContainerLoadingDetailService.getContainerIdList(detailIdList, form.getRecordId());
            if (CollectionUtil.isNotEmpty(containerIdList)) {
                lclContainerInfoPage = lclContainerInfoRepository.getLclContainerInfoPage(form, containerIdList);
            }
        }

        lclContainerInfoPage.setLclStatus(recordDo.getLclStatus());
        lclContainerInfoPage.setShippingStartDate(DateUtil.formatDate(recordDo.getLclContainerStartDate()));
        lclContainerInfoPage.setShippingEndDate(DateUtil.formatDate(recordDo.getLclContainerEndDate()));

        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));
        lclContainerInfoPage.getRecords().forEach(p -> p.setDestinationWarehouse(idNameMap.getOrDefault(p.getDestinationWarehouse(), p.getDestinationWarehouse())));
        return lclContainerInfoPage;
    }

    @Override
    public LclShipmentCategoryAggVo aggContainerTypeByRecordId(String recordId) {
        List<LclShipmentCategoryDto> shipmentCategoryList = lclContainerInfoRepository.aggShipmentTypeByRecordId(recordId);
        if (CollectionUtil.isEmpty(shipmentCategoryList)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "当前无需生成货件号");
        }
        return LclShipmentCategoryAggVo.builder().lclShipmentCategoryDtoList(shipmentCategoryList).build();
    }

    @Override
    public LclContainerDetailVo getContainerDetailList(LclSearchPageForm form) {
        if (form.getIsPackageFull() != null && form.getIsPackageFull() == 2) {
            form.setIsPackageFull(null);
        }
        List<String> detailIdList = getContainerDetailSnapshotIdList(form);
        if (CollectionUtil.isEmpty(detailIdList)) {
            return new LclContainerDetailVo();
        }

        // 获取装柜详情
        var containerDetailList = lclContainerLoadingDetailService.getContainerDetailInfoList(detailIdList, form.getContainerId());
        List<String> snapIdList = containerDetailList.stream().map(LclContainerDetailDO::getProductSnapshotId).toList();

        // 获取快照数据
        List<ProductSnapshotDO> productSnapshotList = productSnapRepository.selectListByIdList(snapIdList);
        var snapshotDOMap = productSnapshotList.stream().collect(Collectors.toMap(ProductSnapshotDO::getId, c -> c));
        Map<String, SelfProductDO> selfProductMap = new HashMap<>();
        Map<String, VirtualProductDO> virtualProductMap = new HashMap<>();
        Map<String, FactoryInfoDO> factoryInfoMap = new HashMap<>();
        Map<String, SpuProductDO> spuProductMap = new HashMap<>();

        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO resultDTO = restTemplateUtil.get(PRODUCTS_SELF_CATEGORY_LEAF_TREE_URL, ResultDTO.class);
        Map<String, ProductCategoryDTO> categoryDTOMap = JSON.to(HashMap.class, resultDTO.getData());

        List<LclContainerDetailInfoDto> detailList = new ArrayList<>(containerDetailList.size());
        for (var detail : containerDetailList) {
            String snapshotId = detail.getProductSnapshotId();
            ProductSnapshotDO productSnapshot = snapshotDOMap.get(snapshotId);
            // 自定义商品信息
            SelfProductDO selfProduct = selfProductMap.getOrDefault(snapshotId, JSON.parseObject(productSnapshot.getSelfData(), SelfProductDO.class));
            selfProductMap.putIfAbsent(snapshotId, selfProduct);

            // 虚拟商品信息
            VirtualProductDO virtualProduct = virtualProductMap.getOrDefault(snapshotId, JSON.parseObject(productSnapshot.getVirtualData(), VirtualProductDO.class));
            virtualProductMap.putIfAbsent(snapshotId, virtualProduct);

            // 供应商信息
            FactoryInfoDO factoryInfo = factoryInfoMap.getOrDefault(snapshotId, JSON.parseObject(productSnapshot.getFactoryData(), FactoryInfoDO.class));
            factoryInfoMap.putIfAbsent(snapshotId, factoryInfo);

            // spu信息
            SpuProductDO spuProduct = spuProductMap.getOrDefault(snapshotId, JSON.parseObject(productSnapshot.getSpuData(), SpuProductDO.class));
            spuProductMap.putIfAbsent(snapshotId, spuProduct);

            // 计算发货箱数
            BigDecimal shippingCase = BigDecimal.valueOf(detail.getLclShippingNum()).divide(BigDecimal.valueOf(selfProduct.getContainerLoad()), 0, UP);
            // 计算单箱体积
            BigDecimal singleCaseVolume = BigDecimal.valueOf(selfProduct.getCaseHeight() * selfProduct.getCaseLength() * selfProduct.getCaseWidth());
            // 计算发货体积
            double volume = singleCaseVolume.multiply(shippingCase).divide(BigDecimal.valueOf(1000000), 2, UP).doubleValue();
            // 计算发货重量
            double weight = BigDecimal.valueOf(selfProduct.getSingleCaseGrossWeight()).multiply(shippingCase).setScale(2, UP).doubleValue();

            // 计算未发货箱数
            BigDecimal unShippingCase = BigDecimal.valueOf(detail.getFactoryRemainNum()).divide(BigDecimal.valueOf(selfProduct.getContainerLoad()), 0, UP);
            // 计算未发货体积
            double unShippingVolume = singleCaseVolume.multiply(unShippingCase).divide(BigDecimal.valueOf(1000000), 2, UP).doubleValue();

            String shipmentCategory = null;
            if (StrUtil.isNotBlank(selfProduct.getCategoryId())) {
                ProductCategoryDTO productCategoryDTO = JSON.to(ProductCategoryDTO.class, categoryDTOMap.get(selfProduct.getCategoryId()));
                shipmentCategory = productCategoryDTO.getCategoryName();
            }
            detailList.add(LclContainerDetailInfoDto.builder()
                    .detailId(detail.getId())
                    .containerInfoId(form.getContainerId())
                    .factoryCode(factoryInfo.getFactoryCode())
                    .contractCode(detail.getContractCode())
                    .image(selfProduct.getImage())
                    .productName(selfProduct.getProductName())
                    .sku(selfProduct.getSku())
                    .skuId(selfProduct.getId())
                    .spuId(spuProduct.getSpu())
                    .spuName(spuProduct.getSpuProductName())
                    .destinationSku(YesOrNoEnum.YES.getCodeStr().equals(detail.getIsOldStatus()) ? virtualProduct.getOldSku() : virtualProduct.getVirtualSku())
                    .productStatus(virtualProduct.getProductStatus())
                    .shipmentStartDate(detail.getShippingStartDate())
                    .deliveryType(detail.getDeliveryType())
                    .commodityInspection(selfProduct.getCommodityInspection())
                    .pcsType(selfProduct.getPcsType())
                    .factoryFinishedDate(detail.getFactoryFinishedDate())
                    .containerLoad(selfProduct.getContainerLoad())
                    .factoryShippingPackageNum(detail.getFactoryShippingPackageNum())
                    .lclShippingNum(detail.getLclShippingNum())
                    .shippingVolume(volume)
                    .shippingWeight(weight)
                    .unShippingNum(detail.getFactoryRemainNum())
                    .unShippingVolume(unShippingVolume)
                    .factoryPlanRemarks(detail.getRemarks())
                    .isPackageFull(detail.getIsPackageFull().toString())
                    .shipmentCategory(shipmentCategory)
                    .build());
        }

        // 排序
        if (StrUtil.isNotBlank(form.getSort())) {
            LclDetailSortEnum lclDetailSortEnum = Arrays.stream(LclDetailSortEnum.values()).filter(f -> f.getCode().equals(form.getSort())).findFirst().get();
            Comparator<LclContainerDetailInfoDto> column = lclDetailSortEnum.getColumn();
            if (StrUtil.isNotBlank(form.getDirection()) && LclDetailSortEnum.DESC.getCode().equals(form.getDirection())) {
                column = column.reversed();
            }
            detailList.sort(column);
        } else {
            detailList.sort(Comparator.comparing(LclContainerDetailInfoDto::getSpuId)
                    .thenComparing(LclContainerDetailInfoDto::getSku)
                    .thenComparing(LclContainerDetailInfoDto::getDestinationSku)
                    .thenComparing(LclContainerDetailInfoDto::getFactoryFinishedDate, Comparator.nullsFirst(Date::compareTo))
                    .thenComparing(LclContainerDetailInfoDto::getShipmentStartDate)
            );
        }

        return LclContainerDetailVo.builder().detailList(detailList).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createShipmentCode(LclShipmentCodeCreationForm form) {
        // 获取该次排柜下所有待排柜的货柜
        List<LclContainerInfoDO> containerInfoList = lclContainerInfoRepository.getAllContainerInfoByRecordId(form.getLclRecordId());
        if (CollectionUtil.isEmpty(containerInfoList)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "装柜计划不存在");
        }

        // 获取所有排柜计划中的货件号
        Set<String> existedShipmentCodeSet = lclContainerInfoRepository.selectAllShipmentCodeFromLclAndShipmentManagement();

        Integer furnitureCodeSuffix = form.getFurnitureCodeSuffix();
        Integer lampCodeSuffix = form.getLampCodeSuffix();
        Integer mixCodeSuffix = form.getMixCodeSuffix();
        // 货柜号生成器
        ShipmentCodeGenerator generator = new ShipmentCodeGenerator(furnitureCodeSuffix, lampCodeSuffix, mixCodeSuffix, existedShipmentCodeSet);
        for (var info : containerInfoList) {
            String shipmentCode = generator.generateShipmentCode(info.getShipmentCategory());
            info.setShipmentCode(shipmentCode);
        }

        lclContainerInfoRepository.updateBatchById(containerInfoList);
    }

    @Override
    public void updateStartShippingDate(LclUpdateShippingStartDateForm form) {
        if (form.getChangingShippingDate().isAfter(form.getShippingDateEnd())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "装柜时间需介于货件内产品最晚发货时间~搜索截止时间");
        }

        var containerDetailList = lclContainerLoadingDetailService.getContainerDetailInfoList(new ArrayList<>(), form.getContainerId());
        if (CollectionUtil.isEmpty(containerDetailList)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "装柜计划不存在");
        }
        LocalDate latestShippingDate = containerDetailList.stream()
                .map(m -> DateUtils.convertToLocalDate(m.getShippingStartDate()))
                .max(Comparator.comparing(c -> c)).get();

        if (latestShippingDate.isAfter(form.getChangingShippingDate())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "装柜时间需介于货件内产品最晚发货时间~搜索截止时间");
        }

        lclContainerInfoRepository.updateShippingStartDate(form.getChangingShippingDate(), form.getContainerId());
    }

    private List<String> getContainerDetailSnapshotIdList(LclSearchPageForm form) {
        List<LclContainerDetailDO> detailList = lclContainerLoadingDetailService.getDetailProductSnapshotIdListByFactoryFinishedInfo(form);
        List<String> productSnapIdList = detailList.stream().map(LclContainerDetailDO::getProductSnapshotId).toList();

        if (form.needSearchSnapshotInfo()) {
            QuerySnapDp querySnapDp = new QuerySnapDp(form.getSelfSkuList(), form.getVirtualSkuList(), form.getProductName(),
                    form.getOldSkuList(), form.getSpuIdList(), form.getProductStatusList(), form.getSpuProductName(), form.getFactoryCode());
            productSnapIdList = productSnapRepository.selectSnapIds(querySnapDp, productSnapIdList);
        }
        if (CollectionUtil.isEmpty(productSnapIdList)) {
            return new ArrayList<>();
        }
        Set<String> snapIdSet = new HashSet<>(productSnapIdList);

        return detailList.stream().filter(f -> snapIdSet.contains(f.getProductSnapshotId())).map(BaseEntity::getId).toList();
    }

    /**
     * 处理集装箱装载的方法
     *
     * @param items         需要装载的货物列表
     * @param warehouse     仓库
     * @param spareTireDate 备胎
     * @return 装载好的集装箱列表
     */
    private ContainerLoadingResultDTO processContainerLoading(List<LclContainerLoadingBO> items, String warehouse, List<LclContainerLoadingBO> spareTireDate,
                                                              Map<String, String> warehouseMap) {

        if (CollectionUtil.isEmpty(items)) {
            return new ContainerLoadingResultDTO(new ArrayList<>(), spareTireDate);
        }
        double weightThreshold = warehouseMap.get(warehouse).contains("FBA") ? 19 : 0;
        Map<String, List<LclContainerLoadingBO>> lclContainerLoadingBOByFactory = items.stream().collect(Collectors.groupingBy(LclContainerLoadingBO::getFactoryId));
        Map<String, List<LclContainerLoadingBO>> spareTireDateBOByFactory = spareTireDate.stream().collect(Collectors.groupingBy(LclContainerLoadingBO::getFactoryId));
        List<LclContainerLoadingBO> resultSpareTireDate = new ArrayList<>();
        List<LclContainerBO> resultLclList = new ArrayList<>();
        List<LclContainerBO> resultLclPavilionList = new ArrayList<>();
        for (var lclByFactory : lclContainerLoadingBOByFactory.entrySet()) {
            List<LclContainerLoadingBO> value = lclByFactory.getValue();

            String addressCode = value.getFirst().getAddressCode();
            String factoryCode = value.getFirst().getFactoryCode();

            // 体积与重量的阈值
            double volumeThreshold = setVolumeThreshold(value.getFirst().getAddressCode(), value.getFirst().getFactoryCode(), warehouse,
                    warehouseMap);

            Map<String, List<LclContainerLoadingBO>> groupBySpu = value.stream().collect(Collectors.groupingBy(LclContainerLoadingBO::getSpuId));
            List<LclContainerBO> resultLclBySpu = new ArrayList<>();
            List<LclContainerBO> resultLclBySpuPavilion = new ArrayList<>();

            // spu维度
            for (var lclBySpu : groupBySpu.entrySet()) {
                // spu下的柜子
                List<LclContainerLoadingBO> lclBySpuList = lclBySpu.getValue();

                Map<Boolean, List<LclContainerLoadingBO>> groupedByLt = lclBySpuList.stream()
                        .collect(Collectors.partitioningBy(bo -> {
                            String productName = bo.getProductName();
                            return productName != null && (productName.contains("凉亭") || productName.contains("洗手台"));
                        }));
                // 凉台
                List<LclContainerLoadingBO> lclPavilion = groupedByLt.get(true);
                List<LclContainerBO> resultLclByPavilion = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(lclPavilion)) {
                    resultLclByPavilion = processLtItems(lclPavilion, true, volumeThreshold, weightThreshold);
                    resultLclBySpuPavilion.addAll(resultLclByPavilion);
                }
                // 非凉台
                List<LclContainerLoadingBO> noneLclPavilion = groupedByLt.get(false);
                List<LclContainerBO> noneLclContainerBOS = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(noneLclPavilion)) {
                    noneLclContainerBOS = processLtItems(noneLclPavilion, false, volumeThreshold, weightThreshold);
                    resultLclBySpu.addAll(noneLclContainerBOS);
                }
            }
            // 将体积小于5个方的，从备胎尝试往前挪
            calculateContainerMetrics(resultLclBySpuPavilion);
            calculateContainerMetrics(resultLclBySpu);

            Map<Boolean, List<LclContainerBO>> lessFiveResultLclBySpu = resultLclBySpu.stream()
                    .collect(Collectors.partitioningBy(bo -> bo.getUsedVolume() >= volumeThreshold - 5 && bo.getUsedVolume() < volumeThreshold - 1));
            List<LclContainerLoadingBO> spareTireList = spareTireDateBOByFactory.getOrDefault(lclByFactory.getKey(), new ArrayList<>());
            if (CollectionUtil.isNotEmpty(lessFiveResultLclBySpu.get(true)) && CollectionUtil.isNotEmpty(spareTireList)) {
                List<LclContainerBO> lessFiveContainerList = lessFiveResultLclBySpu.get(true);
                List<LclContainerBO> thanFiveContainerList = lessFiveResultLclBySpu.get(false);

                // 临时变量，模拟装载
                List<LclContainerLoadingBO> containerItems = new ArrayList<>();
                // 记录本次模拟过程中每个spareTireList的变化（key: index, value: 本次模拟要减少的数量）
                Map<Integer, Integer> usedFromSpare = new HashMap<>();

                spareTireList.sort(Comparator.comparing(LclContainerLoadingBO::getShippingStartDate));
                // 依次装载到新的柜子中
                for (int i = 0; i < lessFiveContainerList.size(); i++) {
                    LclContainerBO lessFiveContainer = lessFiveContainerList.get(i);
                    double currentVolume = lessFiveContainer.getUsedVolume();
                    double currentWeight = lessFiveContainer.getUsedWeight();
                    // 第一阶段：尽量将完整的货物项装入柜子
                    for (int j = 0; i < spareTireList.size(); i++) {
                        LclContainerLoadingBO item = spareTireList.get(j);
                        // 跳过数量为0的物品
                        if (item.getLclShippingNum() == 0) {
                            continue;
                        }
                        double itemVolume = calculateItemVolume(item);
                        double itemWeight = calculateItemWeight(item);

                        // 检查添加此项后是否超出限制
                        if (currentVolume + itemVolume <= volumeThreshold && (weightThreshold == 0 || currentWeight + itemWeight <= weightThreshold)) {
                            containerItems.add(item);
                            item.setLclShippingNum(0);
                            currentVolume += itemVolume;
                            currentWeight += itemWeight;
                        } else {
                            // 立即尝试拆分当前物品
                            double singleBoxVolume = item.getVolume();
                            double singleBoxWeight = item.getSingleCaseGrossWeight() / 1000;

                            // 计算可以添加多少箱才能接近但不超过68
                            int maxBoxes = (int) Math.floor((volumeThreshold - currentVolume) / singleBoxVolume);
                            int boxesToAdd = maxBoxes;
                            if (weightThreshold != 0) {
                                int maxBoxesByWeight = (int) Math.floor((weightThreshold - currentWeight) / singleBoxWeight);
                                boxesToAdd = Math.min(maxBoxes, Math.min(maxBoxesByWeight, (int) Math.ceil((double) item.getLclShippingNum() / item.getContainerLoad())));
                            }
                            if (boxesToAdd > 0) {
                                LclContainerLoadingBO splitItem = lclConsolidationAssembler.lclContainerLoading(item);
                                int shippingNum = Math.min(boxesToAdd * splitItem.getContainerLoad(), splitItem.getLclShippingNum());
                                splitItem.setLclShippingNum(shippingNum);
                                containerItems.add(splitItem);
                                // 更新体积和重量
                                currentVolume += boxesToAdd * singleBoxVolume;
                                currentWeight += boxesToAdd * singleBoxWeight;
                                usedFromSpare.put(j, shippingNum);
                            }
                            if (currentVolume >= volumeThreshold - 1) {
                                break;
                            }
                        }
                    }
                    // 只有当本次模拟能达到要求才真正更新数据
                    if (currentVolume >= volumeThreshold - 1) {
                        // 真正扣减spareTireList数量
                        for (Map.Entry<Integer, Integer> entry : usedFromSpare.entrySet()) {
                            int idx = entry.getKey();
                            int usedNum = entry.getValue();
                            LclContainerLoadingBO item = spareTireList.get(idx);
                            item.setLclShippingNum(item.getLclShippingNum() - usedNum);
                        }
                        // 移除数量为0的
                        spareTireList.removeIf(item -> item.getLclShippingNum() == 0);
                        lessFiveContainer.getLoadedItems().addAll(containerItems);
                    }
                }

                resultLclBySpu.clear();
                resultLclBySpu.addAll(lessFiveContainerList);
                resultLclBySpu.addAll(thanFiveContainerList);
            }
            resultSpareTireDate.addAll(spareTireList);
            // 跨spu
            // 更新已装柜子的体积与重量
            calculateContainerMetrics(resultLclBySpuPavilion);
            calculateContainerMetrics(resultLclBySpu);


            Map<Boolean, List<LclContainerBO>> groupedByVolume = resultLclBySpu.stream()
                    .collect(Collectors.partitioningBy(bo -> bo.getUsedVolume() < volumeThreshold - 1));
            List<LclContainerBO> lclContainerBOS = groupedByVolume.get(true);
            if (CollectionUtil.isNotEmpty(lclContainerBOS)) {
                List<Double> volumes = new ArrayList<>();
                List<Double> weights = new ArrayList<>();
                lclContainerBOS.sort(Comparator.comparingDouble(LclContainerBO::getUsedVolume));

                lclContainerBOS.forEach(i -> {
                    volumes.add(i.getUsedVolume());
                    weights.add(i.getUsedWeight());
                });
                // 逐步寻找所有可能的最少元素组合，并返回下标
                List<List<Integer>> results = findAllCombinationsWithIndices(volumes, volumeThreshold, weights, weightThreshold);

                if (CollectionUtil.isNotEmpty(results)) {
                    List<LclContainerBO> removeList = new ArrayList<>();
                    results.forEach(i -> {
                        List<LclContainerLoadingBO> loadedItemsResult = new ArrayList<>();
                        for (int j = 0; j < i.size(); j++) {
                            LclContainerBO lclContainerBO = lclContainerBOS.get(i.get(j));
                            List<LclContainerLoadingBO> loadedItems = lclContainerBO.getLoadedItems();
                            loadedItemsResult.addAll(loadedItems);
                            if (j > 0) {
                                removeList.add(lclContainerBO);
                            }
                        }
                        lclContainerBOS.get(i.getFirst()).setLoadedItems(loadedItemsResult);
                    });
                    if (CollectionUtil.isNotEmpty(removeList)) {
                        lclContainerBOS.removeAll(removeList);
                    }
                }
                resultLclBySpu.clear();
                resultLclBySpu.addAll(lclContainerBOS);
                resultLclBySpu.addAll(groupedByVolume.get(false));
            }

            calculateContainerMetrics(resultLclBySpu);
            // 获取依旧不满足的数据，进行到虚拟分
            Map<Boolean, List<LclContainerBO>> groupedByVolumeData = resultLclBySpu.stream()
                    .collect(Collectors.partitioningBy(bo -> bo.getUsedVolume() < volumeThreshold - 1));

            List<LclContainerBO> lclContainerBOSLess = groupedByVolumeData.get(true);
            if (CollectionUtil.isNotEmpty(lclContainerBOSLess)) {
                // 重新装箱逻辑
                List<LclContainerBO> optimizedContainers = new ArrayList<>();
                List<LclContainerLoadingBO> allItems = new ArrayList<>();

                lclContainerBOSLess.sort(Comparator.comparingDouble(LclContainerBO::getUsedVolume));
                // 将柜子中的货物按要求排序并放入结果列表
                List<List<LclContainerLoadingBO>> lastLclDataList = new ArrayList<>();
                // 依次处理每个柜子
                for (LclContainerBO container : lclContainerBOSLess) {
                    // 获取柜子中的所有货物
                    List<LclContainerLoadingBO> lastLclData = container.getLoadedItems();
                    if (CollectionUtil.isNotEmpty(lastLclData)) {
                        // 按selfId、destinationSku和体积排序
                        List<LclContainerLoadingBO> sortedItems = lastLclData.stream()
                                .sorted(Comparator
                                        .comparing(LclContainerLoadingBO::getSelfId, Comparator.nullsFirst(String::compareTo))
                                        .thenComparing(LclContainerLoadingBO::getDestinationSku, Comparator.nullsFirst(String::compareTo))
                                        .thenComparingDouble(this::calculateItemVolume))
                                .collect(Collectors.toList());
                        // 将排序后的货物列表添加到结果中
                        allItems.addAll(sortedItems);
                    }
                }

                // 依次装载到新的柜子中
                while (!allItems.isEmpty()) {
                    LclContainerBO newContainer = new LclContainerBO();
                    List<LclContainerLoadingBO> containerItems = new ArrayList<>();
                    double currentVolume = 0.0;
                    double currentWeight = 0.0;
                    int i = 0;

                    // 第一阶段：尽量将完整的货物项装入柜子
                    while (i < allItems.size()) {
                        LclContainerLoadingBO item = allItems.get(i);
                        if (item.getLclShippingNum() <= 0) {
                            allItems.remove(i);
                            continue;
                        }
                        double itemVolume = calculateItemVolume(item);
                        double itemWeight = calculateItemWeight(item);

                        // 检查添加此项后是否超出限制
                        if (currentVolume + itemVolume <= volumeThreshold && (weightThreshold == 0 || currentWeight + itemWeight <= weightThreshold)) {
                            containerItems.add(item);
                            currentVolume += itemVolume;
                            currentWeight += itemWeight;
                            allItems.remove(i); // 从总列表中移除
                            continue;
                        } else {
                            // 立即尝试拆分当前物品
                            double singleBoxVolume = item.getVolume();
                            double singleBoxWeight = item.getSingleCaseGrossWeight() / 1000;

                            // 计算可以添加多少箱才能接近但不超过68
                            int maxBoxes = (int) Math.floor((volumeThreshold - currentVolume) / singleBoxVolume);
                            int boxesToAdd = maxBoxes;
                            if (weightThreshold != 0) {
                                int maxBoxesByWeight = (int) Math.floor((weightThreshold - currentWeight) / singleBoxWeight);
                                boxesToAdd = Math.min(maxBoxes, Math.min(maxBoxesByWeight, (int) Math.ceil((double) item.getLclShippingNum() / item.getContainerLoad())));
                            }
                            if (boxesToAdd > 0) {
                                // 添加部分箱子
                                LclContainerLoadingBO splitItem = lclConsolidationAssembler.lclContainerLoading(item);
                                int shippingNum = Math.min(boxesToAdd * splitItem.getContainerLoad(), splitItem.getLclShippingNum());

                                splitItem.setLclShippingNum(shippingNum);
                                containerItems.add(splitItem);

                                // 更新体积和重量
                                currentVolume += boxesToAdd * singleBoxVolume;
                                currentWeight += boxesToAdd * singleBoxWeight;

                                // 更新原项目的数量
                                item.setLclShippingNum(item.getLclShippingNum() - shippingNum);
                                // 如果原项目数量变为0，从列表中移除
                                if (item.getLclShippingNum() <= 0) {
                                    allItems.remove(i);
                                    i--;
                                }
                            }
                            i++;
                        }
                        // 如果体积已经在理想范围内，并且尝试了所有货物，则停止
                        if (((currentVolume >= volumeThreshold - 1 && currentVolume <= volumeThreshold) &&
                                (weightThreshold == 0 || currentWeight >= weightThreshold - 0.3 && currentWeight <= weightThreshold)) ||
                                i >= allItems.size()) {
                            break;
                        }
                    }

                    // 设置柜子的属性并添加到结果列表
                    newContainer.setLoadedItems(containerItems);
                    newContainer.setUsedVolume(currentVolume);
                    newContainer.setUsedWeight(currentWeight);
                    newContainer.setAddressCode(addressCode);
                    newContainer.setFactoryCode(factoryCode);
                    optimizedContainers.add(newContainer);
                }
                // 用优化后的柜子替换原来的柜子
                lclContainerBOSLess.clear();
                lclContainerBOSLess.addAll(optimizedContainers);

                resultLclBySpu.clear();
                resultLclBySpu.addAll(lclContainerBOSLess);
                resultLclBySpu.addAll(groupedByVolumeData.get(false));
            }
            calculateContainerMetrics(resultLclBySpu);

            // 非凉台依旧没满，装到凉台的柜子里
            Map<Boolean, List<LclContainerBO>> nonePavilionList = resultLclBySpu.stream()
                    .collect(Collectors.partitioningBy(bo -> bo.getUsedVolume() < volumeThreshold - 1));
            List<LclContainerBO> smallNonePavilionList = nonePavilionList.get(true);

            if (CollectionUtil.isNotEmpty(smallNonePavilionList) && CollectionUtil.isNotEmpty(resultLclBySpuPavilion)) {
                // 凉台从大到小排
                resultLclBySpuPavilion.sort(Comparator.comparing(LclContainerBO::getUsedVolume).reversed());
                // 按理来说不足的应该只有一箱
                LclContainerBO smallNonePavilion = smallNonePavilionList.getFirst();
                List<LclContainerLoadingBO> sortedItems = smallNonePavilion.getLoadedItems().stream()
                        // 先按spuId分组
                        .collect(Collectors.groupingBy(LclContainerLoadingBO::getSpuId))
                        .entrySet().stream()
                        // 对每个分组内的元素按体积从大到小排序
                        .flatMap(entry -> entry.getValue().stream()
                                .sorted(Comparator.comparingDouble(this::calculateItemVolume).reversed()))
                        .collect(Collectors.toList());

                // 更新loadedItems为排序后的列表
                smallNonePavilion.setLoadedItems(sortedItems);

                List<LclContainerLoadingBO> remainingItems = new ArrayList<>(sortedItems);
                remainingItems.sort(Comparator.comparingDouble(this::calculateItemVolume).reversed());

                for (LclContainerBO containerPavilion : resultLclBySpuPavilion) {
                    double remainingVolume = volumeThreshold - containerPavilion.getUsedVolume();
                    double remainingWeight = weightThreshold - containerPavilion.getUsedWeight();

                    if (remainingVolume <= 0) continue; // 容器已满，跳过

                    int i = 0;
                    while (i < remainingItems.size() && remainingVolume > 0) {
                        LclContainerLoadingBO item = remainingItems.get(i);
                        double itemVolume = calculateItemVolume(item);
                        double itemWeight = calculateItemWeight(item);

                        // 检查是否可以完全放入
                        if (itemVolume <= remainingVolume && (weightThreshold == 0 || itemWeight <= remainingWeight)) {
                            // 可以完全放入
                            containerPavilion.getLoadedItems().add(item);
                            remainingVolume -= itemVolume;
                            remainingWeight -= itemWeight;
                            remainingItems.remove(i);
                        } else if (itemVolume > remainingVolume || (weightThreshold != 0 && itemWeight > remainingWeight)) {
                            // 需要拆分
                            // 计算每箱体积和重量
                            double singleBoxVolume = item.getVolume();
                            double singleBoxWeight = item.getSingleCaseGrossWeight() / 1000;

                            // 计算可放入的箱子数量
                            int boxesToAdd = (int) Math.floor(remainingVolume / singleBoxVolume);
                            if (weightThreshold != 0) {
                                int maxBoxesByWeight = (int) Math.floor((remainingWeight) / singleBoxWeight);
                                boxesToAdd = Math.min(boxesToAdd, Math.min(maxBoxesByWeight, (int) Math.ceil((double) item.getLclShippingNum() / item.getContainerLoad())));
                            }
                            if (boxesToAdd > 0) {
                                // 创建新物品并设置拆分数量
                                LclContainerLoadingBO splitItem = lclConsolidationAssembler.lclContainerLoading(item);
                                int shippingNum = Math.min(boxesToAdd * splitItem.getContainerLoad(), splitItem.getLclShippingNum());

                                splitItem.setLclShippingNum(shippingNum);
                                containerPavilion.getLoadedItems().add(splitItem);

                                // 更新剩余空间
                                remainingVolume -= boxesToAdd * singleBoxVolume;
                                remainingWeight -= boxesToAdd * singleBoxWeight;

                                // 更新原物品数量
                                item.setLclShippingNum(item.getLclShippingNum() - shippingNum);

                                // 如果原物品数量变为0，移除它
                                if (item.getLclShippingNum() <= 0) {
                                    remainingItems.remove(i);
                                } else {
                                    i++; // 移动到下一个物品
                                }
                            } else {
                                i++; // 当前物品无法放入，尝试下一个
                            }
                        }

                        // 更新容器的已用体积和重量
                        containerPavilion.setUsedVolume(volumeThreshold - remainingVolume);
                        containerPavilion.setUsedWeight(weightThreshold != 0 ? weightThreshold - remainingWeight : containerPavilion.getUsedWeight());

                        // 如果已经没有剩余空间，退出循环
                        if (remainingVolume <= 0) break;
                    }
                }

                // 将剩余的物品放回smallNonePavilion
                smallNonePavilion.setLoadedItems(remainingItems);

            }
            resultLclBySpu.clear();
            resultLclBySpu.addAll(smallNonePavilionList);
            resultLclBySpu.addAll(nonePavilionList.get(false));
            resultLclList.addAll(resultLclBySpu);
            resultLclPavilionList.addAll(resultLclBySpuPavilion);

        }

        // 跨供应商，同地址代码拼柜
        calculateContainerMetrics(resultLclList);

        Map<String, List<LclContainerBO>> groupByAddressCode = resultLclList.stream().collect(Collectors.groupingBy(LclContainerBO::getAddressCode));
        List<LclContainerBO> resultContainerList = new ArrayList<>();
        for (var lclContainerByAddressCode : groupByAddressCode.entrySet()) {
            LclContainerBO lessThan20Container = new LclContainerBO();
            lessThan20Container.setLoadedItems(new ArrayList<>());
            // 过滤出未达标数据

            String addressCode = lclContainerByAddressCode.getKey();
            // 1. 划分容器为达标和未达标两组
            Map<Boolean, List<LclContainerBO>> containerGroups = splitContainersByThreshold(
                    lclContainerByAddressCode.getValue(), addressCode, weightThreshold);

            List<LclContainerBO> lessContainerList = containerGroups.get(false); // 未达标容器
            List<LclContainerBO> qualifiedContainers = containerGroups.get(true); // 达标容器

            // 如果没有未达标容器，直接添加所有容器到结果并返回
            if (CollectionUtil.isEmpty(lessContainerList)) {
                resultContainerList.addAll(lclContainerByAddressCode.getValue());
                continue;
            }

            // 2. 选择合适的优化策略
            if (weightThreshold == 0) {
                optimizeZeroWeightContainers(lessContainerList, addressCode);
            } else {
                optimizeWithWeightLimits(lessContainerList, addressCode, weightThreshold, warehouse, warehouseMap);
            }

            // 3. 添加所有处理后的容器到结果
            calculateContainerMetrics(lessContainerList);

            // 小于20放到一个柜子里
            List<LclContainerBO> lessThan20ContainerList = lessContainerList.stream()
                    .filter(container -> container.getUsedVolume() <= 20)
                    .collect(Collectors.toList());
            for (var lessThan20 : lessThan20ContainerList) {
                lessThan20Container.getLoadedItems().addAll(lessThan20.getLoadedItems());
            }
            lessContainerList.removeAll(lessThan20ContainerList);
            calculateContainerMetrics(lessContainerList);
            Map<Boolean, List<LclContainerBO>> newContainerGroups = splitContainersByThreshold(
                    lessContainerList, addressCode, weightThreshold);
            List<LclContainerBO> newLclContainerBOS = newContainerGroups.get(false);
            // 根据缺口拼柜
            if (CollectionUtil.isNotEmpty(newLclContainerBOS) && newLclContainerBOS.size() > 1) {
                newLclContainerBOS.sort(Comparator.comparingDouble(LclContainerBO::getUsedVolume));
                // 将柜子中的货物按要求排序并放入结果列表
                List<List<LclContainerLoadingBO>> lastLclDataList = new ArrayList<>();
                // 依次处理每个柜子
                for (LclContainerBO container : newLclContainerBOS) {
                    // 获取柜子中的所有货物
                    List<LclContainerLoadingBO> lastLclData = container.getLoadedItems();
                    if (CollectionUtil.isNotEmpty(lastLclData)) {
                        // 按selfId、destinationSku和体积排序
                        List<LclContainerLoadingBO> sortedItems = lastLclData.stream()
                                .sorted(Comparator
                                        .comparing(LclContainerLoadingBO::getSelfId, Comparator.nullsFirst(String::compareTo))
                                        .thenComparing(LclContainerLoadingBO::getDestinationSku, Comparator.nullsFirst(String::compareTo))
                                        .thenComparingDouble(this::calculateItemVolume))
                                .collect(Collectors.toList());
                        // 将排序后的货物列表添加到结果中
                        lastLclDataList.add(sortedItems);
                    } else {
                        // 如果柜子为空，添加空列表
                        lastLclDataList.add(new ArrayList<>());
                    }
                }

                // 重新装箱逻辑
                List<LclContainerBO> optimizedContainers = new ArrayList<>();
                List<LclContainerLoadingBO> allItems = new ArrayList<>();

                // 将所有货物合并到一个列表中
                for (List<LclContainerLoadingBO> containerItems : lastLclDataList) {
                    allItems.addAll(containerItems);
                }

                // 按照前面相同的规则排序所有货物
                allItems.sort(Comparator
                        .comparing(LclContainerLoadingBO::getFactoryCode, Comparator.nullsFirst(String::compareTo))
                        .thenComparing(LclContainerLoadingBO::getSpuId, Comparator.nullsFirst(String::compareTo))
                        .thenComparing(LclContainerLoadingBO::getSelfId, Comparator.nullsFirst(String::compareTo))
                        .thenComparing(LclContainerLoadingBO::getDestinationSku, Comparator.nullsFirst(String::compareTo))
                        .thenComparingDouble(this::calculateItemVolume));


                // 依次装载到新的柜子中
                while (!allItems.isEmpty()) {
                    LclContainerBO newContainer = new LclContainerBO();
                    List<LclContainerLoadingBO> containerItems = new ArrayList<>();
                    double currentVolume = 0.0;
                    double currentWeight = 0.0;
                    int i = 0;
                    // 供应商数量
                    Set<String> factoryCodeList = new HashSet<>();
                    // 第一阶段：尽量将完整的货物项装入柜子
                    while (i < allItems.size()) {
                        LclContainerLoadingBO item = allItems.get(i);
                        factoryCodeList.add(item.getFactoryCode());
                        double itemVolume = calculateItemVolume(item);
                        double itemWeight = calculateItemWeight(item);
                        int factoryNum = factoryCodeList.size();
                        double volumeThreshold = 68;
                        if (warehouseMap.get(warehouse).contains("FBA")) {
                            if (addressCode.equals("YN")) {
                                if (factoryNum == 1) {
                                    volumeThreshold = 68;
                                } else if (factoryNum == 2) {
                                    break;
                                }
                            } else if (factoryNum <= 3) {
                                volumeThreshold = 68;
                            } else if (factoryNum <= 7) {
                                volumeThreshold = 67;
                            } else {
                                break;
                            }
                        } else if (addressCode.equals("YN")) {
                            if (warehouseMap.get(warehouse).contains("CG")) {
                                if (factoryNum <= 1) {
                                    volumeThreshold = 70;
                                } else {
                                    break;
                                }
                            } else {
                                if (factoryNum <= 1) {
                                    volumeThreshold = 70;
                                } else if (factoryNum == 2) {
                                    volumeThreshold = 65;
                                } else {
                                    break;
                                }
                            }
                        } else {
                            if (factoryNum <= 2) {
                                boolean isSpecialFactoriesOnly = factoryCodeList.stream().noneMatch(code -> !"GDSS001".equals(code) && !"GDWS001".equals(code));
                                if (!isSpecialFactoriesOnly) {
                                    volumeThreshold = 71;
                                } else {
                                    volumeThreshold = 72;
                                }
                            } else if (factoryNum == 3) {
                                volumeThreshold = 69;
                            } else if (factoryNum <= 7) {
                                volumeThreshold = 67;
                            } else {
                                break;
                            }
                        }
                        // 检查添加此项后是否超出限制
                        if (currentVolume + itemVolume <= volumeThreshold && (weightThreshold == 0 || currentWeight + itemWeight <= weightThreshold)) {
                            containerItems.add(item);
                            currentVolume += itemVolume;
                            currentWeight += itemWeight;

                            allItems.remove(i); // 从总列表中移除
                        } else {
                            // 立即尝试拆分当前物品
                            double singleBoxVolume = item.getVolume();
                            double singleBoxWeight = item.getSingleCaseGrossWeight() / 1000;

                            // 计算可以添加多少箱才能接近但不超过68
                            int maxBoxes = (int) Math.floor((volumeThreshold - currentVolume) / singleBoxVolume);
                            int boxesToAdd = maxBoxes;
                            if (weightThreshold != 0) {
                                int maxBoxesByWeight = (int) Math.floor((weightThreshold - currentWeight) / singleBoxWeight);
                                boxesToAdd = Math.min(maxBoxes, Math.min(maxBoxesByWeight, (int) Math.ceil((double) item.getLclShippingNum() / item.getContainerLoad())));
                            }
                            if (boxesToAdd > 0) {
                                // 添加部分箱子
                                LclContainerLoadingBO splitItem = lclConsolidationAssembler.lclContainerLoading(item);
                                int shippingNum = Math.min(boxesToAdd * splitItem.getContainerLoad(), splitItem.getLclShippingNum());

                                splitItem.setLclShippingNum(shippingNum);
                                containerItems.add(splitItem);

                                // 更新体积和重量
                                currentVolume += boxesToAdd * singleBoxVolume;
                                currentWeight += boxesToAdd * singleBoxWeight;

                                // 更新原项目的数量
                                item.setLclShippingNum(item.getLclShippingNum() - shippingNum);

                                // 如果原项目数量变为0，从列表中移除
                                if (item.getLclShippingNum() <= 0) {
                                    allItems.remove(i);
                                    i--;
                                }
                            }
                            i++;
                        }
                        // 如果体积已经在理想范围内，并且尝试了所有货物，则停止
                        if (((currentVolume >= volumeThreshold - 1 && currentVolume <= volumeThreshold) &&
                                (weightThreshold == 0 || currentWeight >= weightThreshold - 0.3 && currentWeight <= weightThreshold)) ||
                                i >= allItems.size()) {
                            // 设置柜子的属性并添加到结果列表
                            newContainer.setLoadedItems(containerItems);
                            newContainer.setUsedVolume(currentVolume);
                            newContainer.setUsedWeight(currentWeight);
                            optimizedContainers.add(newContainer);
                            break;
                        }
                    }


                }
                // 用优化后的柜子替换原来的柜子
                newLclContainerBOS.clear();
                newLclContainerBOS.addAll(optimizedContainers);
            }

            resultContainerList.addAll(newLclContainerBOS);
            resultContainerList.addAll(newContainerGroups.get(true));
            resultContainerList.addAll(qualifiedContainers);
            if (CollectionUtil.isNotEmpty(lessThan20Container.getLoadedItems())) {
                lessThan20Container.setShipmentCode("不足20的柜子");
                resultContainerList.add(lessThan20Container);
            }
        }
        resultContainerList.addAll(resultLclPavilionList);
        calculateContainerMetrics(resultContainerList);
        ContainerLoadingResultDTO result = new ContainerLoadingResultDTO();
        result.setContainers(resultContainerList);
        result.setUpdatedSpareTireDate(resultSpareTireDate);
        return result;
    }

    /**
     * 计算货物体积
     */
    private double calculateItemVolume(LclContainerLoadingBO item) {
        // 这里需要根据实际业务逻辑计算体积
        // 示例：根据长宽高计算
        double length = item.getCaseLength() != null ? item.getCaseLength() / 100 : 0;
        double width = item.getCaseWidth() != null ? item.getCaseWidth() / 100 : 0;
        double height = item.getCaseHeight() != null ? item.getCaseHeight() / 100 : 0;

        // 计算单个箱子体积
        double singleBoxVolume = length * width * height;

        // 计算所需箱子数量（向上取整）
        int boxCount = (int) Math.ceil((double) item.getLclShippingNum() / item.getContainerLoad());

        // 返回总体积
        return singleBoxVolume * boxCount;
    }

    /**
     * 计算货物重量
     */
    private double calculateItemWeight(LclContainerLoadingBO item) {
        // 计算所需箱子数量（向上取整）
        int boxCount = (int) Math.ceil((double) item.getLclShippingNum() / item.getContainerLoad());

        // 返回总体积
        return item.getSingleCaseGrossWeight() * boxCount / 1000;
    }

    /**
     * 计算每个渠道的总体积和总重量
     *
     * @param lcl 按渠道分组的货物列表
     * @return 每个渠道的汇总信息
     */
    private ChannelSummary calculateSummaries(List<LclContainerLoadingBO> lcl) {
        // 初始化总体积和总重量
        double totalVolume = 0.0;
        double totalWeight = 0.0;

        // 计算每个物品的体积和重量并累加
        for (LclContainerLoadingBO item : lcl) {
            // 计算体积
            double itemVolume = calculateItemVolume(item);
            totalVolume += itemVolume;

            // 计算重量
            double itemWeight = calculateItemWeight(item);
            totalWeight += itemWeight;
        }

        // 创建并存储渠道汇总信息
        return new ChannelSummary(null, totalVolume, totalWeight, lcl);
    }

    /**
     * 计算每个渠道的总体积和总重量
     *
     * @param lclByChannel 按渠道分组的货物列表
     * @return 每个渠道的汇总信息
     */
    private Map<String, ChannelSummary> calculateChannelSummaries(Map<String, List<LclContainerLoadingBO>> lclByChannel) {
        Map<String, ChannelSummary> tempSummaries = new HashMap<>();

        for (Map.Entry<String, List<LclContainerLoadingBO>> channelEntry : lclByChannel.entrySet()) {
            String channel = channelEntry.getKey();
            List<LclContainerLoadingBO> channelItems = channelEntry.getValue();

            // 初始化总体积和总重量
            double totalVolume = 0.0;
            double totalWeight = 0.0;

            // 计算每个物品的体积和重量并累加
            for (LclContainerLoadingBO item : channelItems) {
                // 计算体积
                double itemVolume = calculateItemVolume(item);
                totalVolume += itemVolume;

                // 计算重量
                double itemWeight = calculateItemWeight(item);
                totalWeight += itemWeight;
            }

            // 创建并存储渠道汇总信息
            ChannelSummary summary = new ChannelSummary(channel, totalVolume, totalWeight, channelItems);
            tempSummaries.put(channel, summary);

        }

        return sortChannelSummariesByVolume(tempSummaries);
    }

    /**
     * 渠道汇总信息类
     */
    private static class ChannelSummary {
        private String channel;
        @Setter
        private double totalVolume;
        @Setter
        private double totalWeight;
        @Setter
        private List<LclContainerLoadingBO> items;

        public ChannelSummary(String channel, double totalVolume, double totalWeight, List<LclContainerLoadingBO> items) {
            this.channel = channel;
            this.totalVolume = totalVolume;
            this.totalWeight = totalWeight;
            this.items = items;
        }

        // getter和setter方法
        public String getChannel() {
            return channel;
        }

        public double getTotalVolume() {
            return totalVolume;
        }

        public double getTotalWeight() {
            return totalWeight;
        }

        public List<LclContainerLoadingBO> getItems() {
            return items;
        }

    }

    private void findBestCombination(List<String> boxTypes,
                                     Map<String, Double> boxVolumes,
                                     Map<String, Integer> boxCounts,
                                     Map<String, Double> boxWeights,
                                     double remainingSpace,
                                     double remainingWeight,
                                     Map<String, Integer> currentSolution,
                                     int index, double currentVolume, double currentWeight,
                                     Map<String, Integer> bestSolution, double[] bestVolume) {
        // 到达末尾或已超过剩余空间，评估当前解
        if (index == boxTypes.size() || currentVolume > remainingSpace ||
                (remainingWeight >= 0 && currentWeight > remainingWeight)) {
            if (currentVolume <= remainingSpace &&
                    (remainingWeight < 0 || currentWeight <= remainingWeight) &&
                    currentVolume > bestVolume[0]) {
                bestSolution.clear();
                bestSolution.putAll(currentSolution);
                bestVolume[0] = currentVolume;
            }
            return;
        }

        String currentType = boxTypes.get(index);
        double volume = boxVolumes.get(currentType);
        double weight = boxWeights.getOrDefault(currentType, 0.0);
        int maxCount = boxCounts.get(currentType);

        // 尝试放0到maxCount个当前类型箱子
        for (int count = 0; count <= maxCount; count++) {
            double newVolume = currentVolume + (volume * count);
            double newWeight = currentWeight + (weight * count);

            // 检查体积和重量限制
            if (newVolume <= remainingSpace && (remainingWeight < 0 || newWeight <= remainingWeight)) {
                if (count > 0) {
                    currentSolution.put(currentType, count);
                }
                findBestCombination(boxTypes, boxVolumes, boxCounts, boxWeights,
                        remainingSpace, remainingWeight,
                        currentSolution, index + 1, newVolume, newWeight,
                        bestSolution, bestVolume);
                if (count > 0) {
                    currentSolution.remove(currentType);
                }
            } else {
                break; // 超过空间或重量限制就不继续尝试放更多
            }
        }
    }

    /**
     * 将列表中的数据根据trialId合并，相同trialId的lclShippingNum相加，其他字段保留第一条记录的值
     *
     * @param items 需要合并的货物列表
     * @return 合并后的货物列表
     */
    private List<LclContainerLoadingBO> mergeByTrialId(List<LclContainerLoadingBO> items) {
        if (CollectionUtil.isEmpty(items)) {
            return new ArrayList<>();
        }

        Map<String, LclContainerLoadingBO> mergedMap = new LinkedHashMap<>();

        for (LclContainerLoadingBO item : items) {
            String trialId = item.getTrialId();

            if (mergedMap.containsKey(trialId)) {
                // 如果已存在相同trialId的记录，累加lclShippingNum
                LclContainerLoadingBO existingItem = mergedMap.get(trialId);
                existingItem.setLclShippingNum(existingItem.getLclShippingNum() + item.getLclShippingNum());
            } else {
                // 否则，添加一个新记录
                mergedMap.put(trialId, item);
            }
        }

        // 将合并后的结果转回列表
        return new ArrayList<>(mergedMap.values());
    }

    /**
     * 从渠道汇总信息中移除lclShippingNum为0的项，并移除空渠道
     *
     * @param channelsMap 待处理的渠道汇总信息Map
     * @return 处理后的渠道汇总信息Map
     */
    private Map<String, ChannelSummary> removeEmptyItems(Map<String, ChannelSummary> channelsMap) {
        // 使用迭代器安全删除元素
        Iterator<Map.Entry<String, ChannelSummary>> iterator = channelsMap.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, ChannelSummary> entry = iterator.next();
            ChannelSummary summary = entry.getValue();

            // 过滤掉lclShippingNum为0的项
            List<LclContainerLoadingBO> filteredItems = summary.getItems().stream()
                    .filter(item -> item.getLclShippingNum() > 0)
                    .collect(Collectors.toList());

            if (filteredItems.isEmpty()) {
                // 如果过滤后项目为空，删除整个entry
                iterator.remove();
            } else {
                // 更新过滤后的items列表
                summary.setItems(filteredItems);

                // 重新计算总体积和总重量
                double totalVolume = 0.0;
                double totalWeight = 0.0;

                for (LclContainerLoadingBO item : filteredItems) {
                    double itemVolume = calculateItemVolume(item);
                    totalVolume += itemVolume;
                    double itemWeight = calculateItemWeight(item);
                    totalWeight += itemWeight;
                }
                // 更新总量
                summary.setTotalVolume(totalVolume);
                summary.setTotalWeight(totalWeight);
            }
        }
        return channelsMap;
    }

    /**
     * 按照体积从小到大排序渠道汇总信息
     *
     * @param channelSummaries 需要排序的渠道汇总信息Map
     * @return 按体积从小到大排序的渠道汇总信息Map
     */
    private Map<String, ChannelSummary> sortChannelSummariesByVolume(Map<String, ChannelSummary> channelSummaries) {
        // 按体积从小到大排序
        List<Map.Entry<String, ChannelSummary>> sortedEntries = new ArrayList<>(channelSummaries.entrySet());
        sortedEntries.sort(Comparator.comparing(entry -> entry.getValue().getTotalVolume()));

        // 使用LinkedHashMap保持排序顺序
        Map<String, ChannelSummary> sortedSummaries = new LinkedHashMap<>();
        for (Map.Entry<String, ChannelSummary> entry : sortedEntries) {
            sortedSummaries.put(entry.getKey(), entry.getValue());
        }

        return sortedSummaries;
    }

    /**
     * 根据比例装载物品到集装箱并优化填充剩余空间
     *
     * @param itemsList         要装载的物品列表
     * @param ratio             分配比例
     * @param containerCapacity 剩余需装体积
     * @param weightThreshold   重量标准
     * @param remainWeight      剩余可装重量
     * @return 装载后的物品列表
     */
    private List<LclContainerLoadingBO> loadItemsWithRatio(List<LclContainerLoadingBO> itemsList, double ratio, double containerCapacity, double weightThreshold, double remainWeight) {
        List<LclContainerLoadingBO> loadedItems = new ArrayList<>();
        Map<String, Double> boxVolumes = new LinkedHashMap<>();
        Map<String, Double> boxWeights = new LinkedHashMap<>();
        Map<String, Integer> boxCounts = new LinkedHashMap<>();
        Map<String, Integer> itemsPerBoxMap = new HashMap<>();
        // 装进柜子的体积
        double totalLoadedVolume = 0;
        double totalLoadedWeight = 0;

        // 按比例装载
        for (var item : itemsList) {
            if (item.getLclShippingNum() == 0) {
                continue;
            }
            int boxCount = (int) Math.ceil((double) item.getLclShippingNum() / item.getContainerLoad());
            int boxLoadingCount = (int) Math.floor((double) boxCount * ratio);
            if (weightThreshold != 0) {
                int maxBoxesByWeight = (int) Math.floor((remainWeight / item.getSingleCaseGrossWeight() / 1000));
                boxLoadingCount = Math.min(maxBoxesByWeight, boxLoadingCount);
            }
            int lclMinNum = 0;
            if (boxLoadingCount > 0) {
                lclMinNum = Math.min(boxLoadingCount * item.getContainerLoad(), item.getLclShippingNum());

                if (lclMinNum > 0) {
                    totalLoadedWeight += (int) Math.ceil((double) lclMinNum / item.getContainerLoad()) * item.getSingleCaseGrossWeight() / 1000;
                    totalLoadedVolume += (int) Math.ceil((double) lclMinNum / item.getContainerLoad()) * item.getVolume();

                    // 更新原始物品的剩余数量
                    item.setLclShippingNum(item.getLclShippingNum() - lclMinNum);
                    // 创建新物品对象并设置装载数量
                    LclContainerLoadingBO loadedItem = lclConsolidationAssembler.lclContainerLoading(item);
                    loadedItem.setLclShippingNum(lclMinNum);

                    loadedItems.add(loadedItem);
                }
            }
            boxVolumes.put(item.getTrialId(), item.getVolume());
            boxWeights.put(item.getTrialId(), item.getSingleCaseGrossWeight() / 1000);
            boxCounts.put(item.getTrialId(), boxCount - lclMinNum);
            itemsPerBoxMap.put(item.getTrialId(), item.getContainerLoad());
        }
        containerCapacity -= totalLoadedVolume;
        remainWeight -= totalLoadedWeight;
        if (weightThreshold == 0) {
            remainWeight = -1;
        }

        Map<String, Integer> boxIntegerMap = optimizeBoxLoading(boxVolumes, boxCounts, boxWeights, itemsPerBoxMap, containerCapacity, remainWeight);

        // 分配额外物品填充剩余空间
        for (var largePavilion : itemsList) {
            Integer needBox = boxIntegerMap.getOrDefault(largePavilion.getTrialId(), 0);
            if (needBox > 0) {
                // 更新原始物品的剩余数量
                largePavilion.setLclShippingNum(largePavilion.getLclShippingNum() - needBox);
                // 创建新物品对象并设置装载数量
                LclContainerLoadingBO additionalItem = lclConsolidationAssembler.lclContainerLoading(largePavilion);
                additionalItem.setLclShippingNum(needBox);
                loadedItems.add(additionalItem);
            }
        }
        // 合并相同trialId的记录
        return mergeByTrialId(loadedItems);
    }

    /**
     * 计算每个集装箱的总体积和总重量
     *
     * @param containers 集装箱列表
     */
    private void calculateContainerMetrics(List<LclContainerBO> containers) {
        if (CollectionUtil.isEmpty(containers)) {
            return;
        }

        for (LclContainerBO container : containers) {
            List<LclContainerLoadingBO> loadedItems = container.getLoadedItems();
            if (CollectionUtil.isEmpty(loadedItems)) {
                container.setUsedVolume(0.0);
                container.setUsedWeight(0.0);
                continue;
            }

            // 计算总体积和总重量
            double totalVolume = 0.0;
            double totalWeight = 0.0;

            for (LclContainerLoadingBO item : loadedItems) {
                // 计算体积
                double itemVolume = calculateItemVolume(item);
                totalVolume += itemVolume;

                double itemWeight = calculateItemWeight(item);
                totalWeight += itemWeight;
            }

            // 更新集装箱的体积和重量
            container.setUsedVolume(totalVolume);
            container.setUsedWeight(totalWeight); // 转换为吨
        }
        // 按照体积从小到大排序
        containers.sort(Comparator.comparing(LclContainerBO::getUsedVolume));
    }


    /**
     * 处理物品的装柜逻辑
     *
     * @param lclPavilion 物品列表
     * @param isPavilion  是否是凉台
     * @return 装载好的集装箱列表
     */
    private List<LclContainerBO> processLtItems(List<LclContainerLoadingBO> lclPavilion, boolean isPavilion,
                                                double volumeThreshold, double weightThreshold) {
        // spu下的柜子
        List<LclContainerBO> resultLclBySpu = new ArrayList<>();

        ChannelSummary lclPavilionSummary = calculateSummaries(lclPavilion);
        double volumeThresholds = isPavilion ? 60 : volumeThreshold;
        String addressCode = lclPavilion.getFirst().getAddressCode();
        String factoryCode = lclPavilion.getFirst().getFactoryCode();
        if (isPavilion && lclPavilionSummary.getTotalVolume() <= volumeThresholds) {
            resultLclBySpu.add(LclContainerBO.builder()
                    .factoryNum(1)
                    .addressCode(addressCode)
                    .factoryCode(factoryCode)
                    .loadedItems(lclPavilion).build());
        } else {
            Map<String, List<LclContainerLoadingBO>> lclByChannel = lclPavilion.stream()
                    .collect(Collectors.groupingBy(LclContainerLoadingBO::getChannel));

            // 计算每个渠道的体积和重量汇总信息
            Map<String, ChannelSummary> channelSummaries = calculateChannelSummaries(lclByChannel);

            // 过滤体积大于阈值的渠道
            Map<String, ChannelSummary> largeVolumeChannels = channelSummaries.entrySet().stream()
                    .filter(entry -> entry.getValue().getTotalVolume() > volumeThresholds)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            LinkedHashMap::new
                    ));

            if (CollectionUtil.isNotEmpty(largeVolumeChannels)) {
                for (var entry : largeVolumeChannels.entrySet()) {
                    LclContainerBO largeLclContainerBO = new LclContainerBO();
                    ChannelSummary summaryInfo = entry.getValue();
                    double totalVolume = summaryInfo.getTotalVolume();
                    double totalWeight = summaryInfo.getTotalWeight();

                    double ratio = weightThreshold == 0 ? volumeThresholds / totalVolume :
                            Math.min(volumeThresholds / totalVolume, totalWeight / weightThreshold);

                    List<LclContainerLoadingBO> largePavilionList = summaryInfo.getItems();
                    List<LclContainerLoadingBO> loadedlist = new ArrayList<>();
                    Map<String, Double> boxVolumes = new LinkedHashMap<>();
                    Map<String, Double> boxWeights = new LinkedHashMap<>();
                    Map<String, Integer> boxCounts = new LinkedHashMap<>();
                    Map<String, Integer> itemsPerBoxMap = new HashMap<>();

                    // 装进柜子的体积
                    double lclTotalVolume = 0;
                    // 装进柜子的重量
                    double lclTotalWeight = 0;


                    for (var largePavilion : largePavilionList) {
                        if (largePavilion.getLclShippingNum() == 0) {
                            continue;
                        }
                        // 计算所需箱子数量（向上取整）
                        int boxCount = (int) Math.ceil((double) largePavilion.getLclShippingNum() / largePavilion.getContainerLoad());
                        int boxLoadingCount = (int) Math.floor((double) boxCount * ratio);
                        if (weightThreshold != 0) {
                            int maxBoxesByWeight = (int) Math.floor((weightThreshold - lclTotalWeight) / largePavilion.getSingleCaseGrossWeight() / 1000);
                            boxLoadingCount = Math.min(maxBoxesByWeight, boxLoadingCount);
                        }
                        int lclMinNum = 0;
                        if (boxLoadingCount > 0) {
                            lclMinNum = Math.min(boxLoadingCount * largePavilion.getContainerLoad(), largePavilion.getLclShippingNum());
                            if (lclMinNum > 0) {
                                // 重新计算分出几个箱
                                lclTotalVolume += (int) Math.ceil((double) lclMinNum / largePavilion.getContainerLoad()) * largePavilion.getVolume();
                                lclTotalWeight += (int) Math.ceil((double) lclMinNum / largePavilion.getContainerLoad()) * largePavilion.getSingleCaseGrossWeight() / 1000;
                                largePavilion.setLclShippingNum(largePavilion.getLclShippingNum() - lclMinNum);
                                // 挪走的数据
                                LclContainerLoadingBO lclContainerLoadingBO = lclConsolidationAssembler.lclContainerLoading(largePavilion);
                                lclContainerLoadingBO.setLclShippingNum(lclMinNum);
                                loadedlist.add(lclContainerLoadingBO);
                            }
                        }
                        boxVolumes.put(largePavilion.getTrialId(), largePavilion.getVolume());
                        boxWeights.put(largePavilion.getTrialId(), largePavilion.getSingleCaseGrossWeight() / 1000);
                        boxCounts.put(largePavilion.getTrialId(), boxCount - lclMinNum);
                        itemsPerBoxMap.put(largePavilion.getTrialId(), largePavilion.getContainerLoad());
                    }

                    // 由于向下取整，会出现不足的情况，计算还差多少体积
                    double remainingSpace = volumeThresholds - lclTotalVolume;
                    double remainingWeight = weightThreshold - lclTotalWeight;
                    if (weightThreshold == 0) {
                        remainingWeight = -1;
                    }

                    Map<String, Integer> boxIntegerMap = optimizeBoxLoading(boxVolumes, boxCounts, boxWeights, itemsPerBoxMap, remainingSpace, remainingWeight);
                    for (var largePavilion : largePavilionList) {
                        // 再拿出去的数量
                        Integer needBox = boxIntegerMap.getOrDefault(largePavilion.getTrialId(), 0);
                        if (needBox > 0) {
                            largePavilion.setLclShippingNum(largePavilion.getLclShippingNum() - needBox);
                            LclContainerLoadingBO moreLclContainerLoadingBO = lclConsolidationAssembler.lclContainerLoading(largePavilion);
                            moreLclContainerLoadingBO.setLclShippingNum(needBox);
                            loadedlist.add(moreLclContainerLoadingBO);
                        }
                    }
                    // 合并相同trialId的记录
                    loadedlist = mergeByTrialId(loadedlist);
                    largeLclContainerBO.setLoadedItems(loadedlist);
                    largeLclContainerBO.setUsedVolume(remainingSpace);
                    largeLclContainerBO.setUsedWeight(remainingWeight);
                    largeLclContainerBO.setAddressCode(addressCode);
                    largeLclContainerBO.setFactoryCode(factoryCode);
                    resultLclBySpu.add(largeLclContainerBO);
                }
                // 更新剩余发货数量
                removeEmptyItems(largeVolumeChannels);
            }

            // 过滤体积小于60的渠道
            Map<String, ChannelSummary> smallVolumeChannels = channelSummaries.entrySet().stream()
                    .filter(entry -> entry.getValue().getTotalVolume() < volumeThresholds)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            LinkedHashMap::new  // 保持排序
                    ));
            if (CollectionUtil.isNotEmpty(smallVolumeChannels)) {

                if (CollectionUtil.isNotEmpty(largeVolumeChannels)) {
                    smallVolumeChannels.putAll(largeVolumeChannels);
                    smallVolumeChannels = sortChannelSummariesByVolume(smallVolumeChannels);
                }
                double lclTotalVolume = 0;
                double lclTotalWeight = 0;

                // 这里可能有多个柜子，注意创建新柜子
                LclContainerBO largeLclContainerBO = new LclContainerBO();
                List<LclContainerLoadingBO> resultSmallLclData = new ArrayList<>();
                // 接收每次加权后剩下的数据
                List<LclContainerLoadingBO> smallResultLclData = new ArrayList<>();

                for (var smallData : smallVolumeChannels.entrySet()) {
                    ChannelSummary channelSummary = smallData.getValue();
                    List<LclContainerLoadingBO> smallLclData = channelSummary.getItems();
                    // 处理之前剩余的货物
                    if (CollectionUtil.isNotEmpty(smallResultLclData)) {
                        double smallResultVolume = 0;
                        double smallResultWeight = 0;

                        for (var smallResult : smallResultLclData) {
                            if (smallResult.getLclShippingNum() > 0) {
                                smallResultVolume += calculateItemVolume(smallResult);
                                smallResultWeight += calculateItemWeight(smallResult);
                            }
                        }
                        if (smallResultVolume > 0) {
                            lclTotalVolume += smallResultVolume;
                            lclTotalWeight += smallResultWeight;
                            resultSmallLclData.addAll(smallResultLclData);
                        }
                    }
                    if (lclTotalVolume + channelSummary.getTotalVolume() <= volumeThresholds && (weightThreshold == 0 || lclTotalWeight + channelSummary.getTotalWeight() <= weightThreshold)) {
                        lclTotalVolume += channelSummary.getTotalVolume();
                        lclTotalWeight += channelSummary.getTotalWeight();
                        resultSmallLclData.addAll(smallLclData);
                    } else {
                        double ratioByVolume = (volumeThresholds - lclTotalVolume) / channelSummary.getTotalVolume();
                        double ratio = weightThreshold == 0 ? ratioByVolume :
                                Math.min((weightThreshold - lclTotalWeight) / channelSummary.getTotalWeight(), ratioByVolume);
                        List<LclContainerLoadingBO> lclContainerLoadingBOS = loadItemsWithRatio(smallLclData, ratio, volumeThresholds - lclTotalVolume, weightThreshold, weightThreshold - lclTotalWeight);
                        resultSmallLclData.addAll(lclContainerLoadingBOS);
                        largeLclContainerBO.setLoadedItems(new ArrayList<>(resultSmallLclData));
                        largeLclContainerBO.setAddressCode(addressCode);
                        largeLclContainerBO.setFactoryCode(factoryCode);
                        resultLclBySpu.add(largeLclContainerBO);

                        largeLclContainerBO = new LclContainerBO();
                        lclTotalVolume = 0;
                        lclTotalWeight = 0;
                        resultSmallLclData = new ArrayList<>();
                        smallResultLclData = smallLclData;
                    }
                }
                // 如果最后还有剩余
                if (CollectionUtil.isNotEmpty(resultSmallLclData)) {
                    resultLclBySpu.add(LclContainerBO.builder()
                            .factoryNum(1)
                            .addressCode(addressCode)
                            .factoryCode(factoryCode)
                            .loadedItems(resultSmallLclData).build());
                }
            }
        }

        return resultLclBySpu;
    }

    /**
     * 贪心算法寻找所有可能的最少元素组合，并返回原数组中的下标
     *
     * @param originalArray   原始数组
     * @param targetSum       目标和的上限，范围为[targetSum-1, targetSum]
     * @param weights         对应每个元素的重量数组，与originalArray一一对应
     * @param weightThreshold 重量阈值，如果为0则不考虑重量
     * @return 满足条件的组合列表
     */
    public static List<List<Integer>> findAllCombinationsWithIndices(double[] originalArray, double targetSum,
                                                                     double[] weights, double weightThreshold) {
        // 创建可用元素的下标和值
        List<Integer> availableIndices = new ArrayList<>();
        List<Double> availableValues = new ArrayList<>();
        List<Double> availableWeights = new ArrayList<>();
        List<List<Integer>> originalIndicesResult = new ArrayList<>();

        for (int i = 0; i < originalArray.length; i++) {
            availableIndices.add(i);
            availableValues.add(originalArray[i]);

            // 如果提供了权重，则添加权重；否则使用0
            if (weights != null && i < weights.length) {
                availableWeights.add(weights[i]);
            } else {
                availableWeights.add(0.0);
            }
        }

        int round = 1;

        while (!availableIndices.isEmpty()) {
            System.out.println("\n第" + round + "轮查找：");
            System.out.println("当前可用元素下标: " + availableIndices);
            System.out.println("对应的元素值: " + availableValues);

            if (weightThreshold > 0) {
                System.out.println("对应的重量值: " + availableWeights);
            }

            // 寻找当前数组中和为目标值范围内的所有最小元素组合（下标形式）
            List<List<Integer>> combinations = findBestCombinationsByIndices(
                    availableIndices, availableValues, availableWeights, targetSum - 1, targetSum, weightThreshold);

            if (combinations.isEmpty()) {
                System.out.println("没有找到更多满足条件的组合");
                break;
            }

            // 选择第一个组合
            List<Integer> selectedCombination = combinations.getFirst();
            List<Integer> originalIndices = convertToOriginalIndices(selectedCombination, availableIndices);
            originalIndicesResult.add(originalIndices);

            // 从可用元素列表中移除选中组合的元素（从后往前移除，避免索引变化问题）
            selectedCombination.sort(Collections.reverseOrder());
            for (int index : selectedCombination) {
                availableIndices.remove(index);
                availableValues.remove(index);
                availableWeights.remove(index);
            }

            round++;
        }
        return originalIndicesResult;
    }

    /**
     * 将当前可用数组中的下标转换为原始数组中的下标
     *
     * @param currentIndices   当前数组中的下标组合
     * @param availableIndices 可用元素的原始下标
     * @return 原始数组中的下标组合
     */
    private static List<Integer> convertToOriginalIndices(List<Integer> currentIndices, List<Integer> availableIndices) {
        List<Integer> originalIndices = new ArrayList<>();
        for (int index : currentIndices) {
            originalIndices.add(availableIndices.get(index));
        }
        return originalIndices;
    }

    /**
     * 重载方法，不考虑重量约束的情况
     */
    public static List<List<Integer>> findAllCombinationsWithIndices(double[] originalArray, double targetSum) {
        return findAllCombinationsWithIndices(originalArray, targetSum, null, 0);
    }

    /**
     * 重载方法，支持使用List<Double>作为输入
     */
    public static List<List<Integer>> findAllCombinationsWithIndices(List<Double> originalList, double targetSum,
                                                                     List<Double> weights, double weightThreshold) {
        double[] originalArray = new double[originalList.size()];
        for (int i = 0; i < originalList.size(); i++) {
            originalArray[i] = originalList.get(i);
        }

        double[] weightsArray = null;
        if (weights != null) {
            weightsArray = new double[weights.size()];
            for (int i = 0; i < weights.size(); i++) {
                weightsArray[i] = weights.get(i);
            }
        }

        return findAllCombinationsWithIndices(originalArray, targetSum, weightsArray, weightThreshold);
    }

    /**
     * 寻找最佳组合（元素个数最少且和在目标范围内的所有组合），返回下标形式
     *
     * @param availableIndices 可用元素的原始下标
     * @param availableValues  可用元素的值
     * @param availableWeights 可用元素的重量
     * @param minTargetSum     目标和的下限
     * @param maxTargetSum     目标和的上限
     * @param weightThreshold  重量阈值，如果为0则不考虑重量
     * @return 所有满足条件且元素个数最少的组合（下标形式）
     */
    public static List<List<Integer>> findBestCombinationsByIndices(
            List<Integer> availableIndices, List<Double> availableValues, List<Double> availableWeights,
            double minTargetSum, double maxTargetSum, double weightThreshold) {

        List<List<Integer>> result = new ArrayList<>();
        int minSize = Integer.MAX_VALUE;

        // 构建下标到值的映射，用于回溯搜索
        double[] values = new double[availableValues.size()];
        for (int i = 0; i < availableValues.size(); i++) {
            values[i] = availableValues.get(i);
        }

        // 构建下标到重量的映射
        double[] weights = null;
        if (availableWeights != null && weightThreshold > 0) {
            weights = new double[availableWeights.size()];
            for (int i = 0; i < availableWeights.size(); i++) {
                weights[i] = availableWeights.get(i);
            }
        }

        // 对值数组进行排序，并跟踪原始下标的变化
        Integer[] sortedIndices = new Integer[values.length];
        for (int i = 0; i < sortedIndices.length; i++) {
            sortedIndices[i] = i;
        }

        // 使用自定义比较器进行排序，按值排序，相同值则按下标排序
        Arrays.sort(sortedIndices, (a, b) -> {
            int compare = Double.compare(values[a], values[b]);
            if (compare == 0) {
                return Integer.compare(a, b);
            }
            return compare;
        });

        // 创建排序后的值数组
        double[] sortedValues = new double[values.length];
        // 创建排序后的重量数组
        double[] sortedWeights = weights != null ? new double[weights.length] : null;

        for (int i = 0; i < sortedValues.length; i++) {
            sortedValues[i] = values[sortedIndices[i]];
            if (sortedWeights != null) {
                sortedWeights[i] = weights[sortedIndices[i]];
            }
        }

        // 使用回溯法寻找所有可能的组合
        List<Integer> current = new ArrayList<>();
        findCombinationsByIndices(sortedValues, sortedWeights, sortedIndices,
                minTargetSum, maxTargetSum, weightThreshold,
                0, current, result, 0.0, 0.0, minSize);

        // 筛选元素个数最少的组合
        if (!result.isEmpty()) {
            int minElements = Integer.MAX_VALUE;
            for (List<Integer> comb : result) {
                minElements = Math.min(minElements, comb.size());
            }

            // 只保留元素个数等于最小值的组合
            List<List<Integer>> filteredResult = new ArrayList<>();
            for (List<Integer> comb : result) {
                if (comb.size() == minElements) {
                    filteredResult.add(comb);
                }
            }

            return filteredResult;
        }

        return result;
    }

    /**
     * 回溯法寻找所有可能的组合（下标形式），和在目标范围内，且重量不超过阈值
     */
    private static void findCombinationsByIndices(double[] sortedValues, double[] sortedWeights, Integer[] sortedIndices,
                                                  double minTargetSum, double maxTargetSum, double weightThreshold,
                                                  int start, List<Integer> current,
                                                  List<List<Integer>> result,
                                                  double currentSum, double currentWeight, int minSize) {
        // 如果当前已知最小组合大小不是初始值，且当前组合大小已经超过了最小大小，则剪枝
        if (minSize != Integer.MAX_VALUE && current.size() >= minSize) {
            return;
        }

        // 使用一个小的误差范围来处理浮点数比较
        double epsilon = 1e-10;

        // 检查重量约束（如果指定了阈值）
        if (weightThreshold > 0 && currentWeight > weightThreshold + epsilon) {
            return;
        }

        // 找到一个满足条件的组合（在目标范围内）
        if (currentSum >= minTargetSum - epsilon && currentSum <= maxTargetSum + epsilon) {
            // 检查重量约束
            if (weightThreshold <= 0 || currentWeight <= weightThreshold + epsilon) {
                // 如果当前组合比已知的最小组合更小，则清空结果集
                if (current.size() < minSize) {
                    result.clear();
                    minSize = current.size();
                }

                // 添加当前组合到结果集
                result.add(new ArrayList<>(current));
            }
            return;
        }

        // 如果当前和已经超过目标和上限，则剪枝
        if (currentSum > maxTargetSum + epsilon) {
            return;
        }

        // 尝试每个可能的元素
        for (int i = start; i < sortedValues.length; i++) {
            // 避免重复：如果当前元素与前一个元素相同（考虑浮点数误差），且不是第一个位置，则跳过
            if (i > start && Math.abs(sortedValues[i] - sortedValues[i - 1]) < epsilon) {
                continue;
            }

            current.add(sortedIndices[i]);
            // 计算新的重量和
            double newWeight = currentWeight;
            if (sortedWeights != null) {
                newWeight += sortedWeights[i];
            }

            findCombinationsByIndices(sortedValues, sortedWeights, sortedIndices,
                    minTargetSum, maxTargetSum, weightThreshold,
                    i + 1, current, result, currentSum + sortedValues[i],
                    newWeight, minSize);

            current.remove(current.size() - 1);
        }
    }

    private double setVolumeThreshold(String addressCode, String factoryCode, String warehouse, Map<String, String> warehouseMap) {
        if (warehouseMap.get(warehouse).contains("FBA")) {
            return 68;
        }

        if (addressCode.equals("YN")) {
            return 70;
        } else {
            if (factoryCode.equals("GDSS001") || factoryCode.equals("GDWS001")) {
                return 72;
            } else {
                return 71;
            }
        }
    }


    /**
     * 按照容量阈值分离容器
     */
    private Map<Boolean, List<LclContainerBO>> splitContainersByThreshold(
            List<LclContainerBO> containers, String addressCode, double weightThreshold) {

        return containers.stream().collect(Collectors.partitioningBy(container -> {
            if (weightThreshold != 0) {
                return container.getUsedVolume() >= 67;
            }
            if (addressCode.equals("YN")) {
                return container.getUsedVolume() >= 69;
            } else {
                return container.getUsedVolume() >= 70;
            }
        }));
    }

    /**
     * 优化无重量限制的容器
     */
    private void optimizeZeroWeightContainers(List<LclContainerBO> lessContainerList, String addressCode) {
        if (addressCode.equals("YN")) {
            optimizeContainersToTarget(lessContainerList, 65, 0, indices -> indices.size() == 2);
        } else {
            // 第一轮优化 - 尝试组合少于4个容器
            optimizeContainersToTarget(lessContainerList, 68, 0, indices -> indices.size() < 4);

            // 第二轮优化 - 尝试组合4-7个容器
            if (CollectionUtil.isNotEmpty(lessContainerList)) {
                calculateContainerMetrics(lessContainerList);
                optimizeContainersToTarget(lessContainerList, 67, 0,
                        indices -> indices.size() >= 4 && indices.size() <= 7);
            }
        }
    }

    /**
     * 优化有重量限制的容器
     */
    private void optimizeWithWeightLimits(List<LclContainerBO> lessContainerList,
                                          String addressCode,
                                          double weightThreshold,
                                          String warehouse, Map<String, String> warehouseMap) {
        if (addressCode.equals("YN")) {
            if (warehouseMap.get(warehouse).contains("CG")) {
                return; // 跳过特定仓库
            }
            optimizeContainersToTarget(lessContainerList, 65, weightThreshold, indices -> indices.size() == 2);
        } else {
            // 第一轮优化 - 目标71立方
            optimizeContainersToTarget(lessContainerList, 71, weightThreshold, indices -> indices.size() == 2);

            // 第二轮优化 - 目标69立方
            if (CollectionUtil.isNotEmpty(lessContainerList)) {
                calculateContainerMetrics(lessContainerList);
                optimizeContainersToTarget(lessContainerList, 69, weightThreshold, indices -> indices.size() == 3);
            }

            // 第三轮优化 - 目标67立方
            if (CollectionUtil.isNotEmpty(lessContainerList)) {
                calculateContainerMetrics(lessContainerList);
                optimizeContainersToTarget(lessContainerList, 67, weightThreshold, indices -> indices.size() == 3);
            }
        }
    }

    /**
     * 将容器优化到目标容量
     */
    private void optimizeContainersToTarget(List<LclContainerBO> containers,
                                            double targetVolume,
                                            double weightThreshold,
                                            Predicate<List<Integer>> sizeCondition) {
        // 收集体积和重量信息
        List<Double> volumes = new ArrayList<>();
        List<Double> weights = new ArrayList<>();

        containers.forEach(container -> {
            volumes.add(container.getUsedVolume());
            weights.add(container.getUsedWeight());
        });

        // 寻找可能的组合
        List<List<Integer>> results = findAllCombinationsWithIndices(
                volumes, targetVolume, weights, weightThreshold);

        if (CollectionUtil.isNotEmpty(results)) {
            List<LclContainerBO> containersToRemove = new ArrayList<>();

            // 处理符合条件的组合
            for (List<Integer> indices : results) {
                if (sizeCondition.test(indices)) {
                    mergeContainers(containers, indices, containersToRemove);
                }
            }

            // 移除已合并的容器
            containers.removeAll(containersToRemove);
        }
    }

    /**
     * 合并容器
     */
    private void mergeContainers(List<LclContainerBO> containers,
                                 List<Integer> indices,
                                 List<LclContainerBO> containersToRemove) {
        List<LclContainerLoadingBO> mergedItems = new ArrayList<>();

        for (int j = 0; j < indices.size(); j++) {
            LclContainerBO container = containers.get(indices.get(j));
            mergedItems.addAll(container.getLoadedItems());

            // 标记除第一个外的容器，稍后移除
            if (j > 0) {
                containersToRemove.add(container);
            }
        }

        // 更新第一个容器
        LclContainerBO targetContainer = containers.get(indices.getFirst());
        targetContainer.setLoadedItems(mergedItems);
        targetContainer.setFactoryNum(indices.size());
    }

    /**
     * 主调用方法，优化箱子装载。
     *
     * @param boxVolumes       每种货物的单箱体积 (key: 货物类型, value: 单箱体积)
     * @param initialBoxCounts 每种货物的初始可用【物品数量】 (key: 货物类型, value: 可用物品总数)
     * @param boxItemWeights   每种货物的【单箱】重量 (key: 货物类型, value: 单箱重量)
     * @param itemsPerBoxMap   每种货物的每箱可放入的【物品数量】 (key: 货物类型, value: 每箱物品数)
     * @param remainingSpace   柜子的初始剩余空间体积
     * @param remainingWeight  柜子的初始剩余可放入重量 (-1 表示不考虑重量约束)
     * @return 一个Map，key为货物类型，value为该类型货物放入的【物品数量】，以达到最优装载
     */
    public static Map<String, Integer> optimizeBoxLoading(
            Map<String, Double> boxVolumes,
            Map<String, Integer> initialBoxCounts,
            Map<String, Double> boxItemWeights,
            Map<String, Integer> itemsPerBoxMap,
            double remainingSpace,
            double remainingWeight) {

        List<String> boxTypes = new ArrayList<>(boxVolumes.keySet());
        Map<String, Integer> bestSolution = new HashMap<>();
        double[] bestRemainingSpaceHolder = {Double.MAX_VALUE};

        findBestCombination(
                boxTypes,
                boxVolumes,
                new HashMap<>(initialBoxCounts),
                boxItemWeights,
                itemsPerBoxMap,
                remainingSpace,
                remainingWeight,
                new HashMap<>(),
                0,
                remainingSpace,
                remainingWeight,
                bestSolution,
                bestRemainingSpaceHolder
        );
        return bestSolution;
    }

    /**
     * 递归辅助方法，寻找最佳的箱子组合。
     */
    private static void findBestCombination(
            List<String> boxTypes,
            Map<String, Double> boxVolumesPerFullBox,
            Map<String, Integer> currentAvailableItems,
            Map<String, Double> weightPerBox,  // 修改为单箱重量
            Map<String, Integer> itemsPerFullBoxMap,
            double initialTotalSpace,
            double initialTotalWeight,
            Map<String, Integer> currentSolutionItems,
            int currentIndex,
            double currentRemainingSpace,
            double currentRemainingWeight,
            Map<String, Integer> bestSolutionItems,
            double[] bestRemainingSpaceHolder) {

        if (currentIndex == boxTypes.size()) {
            if (currentRemainingSpace >= -1e-9 &&
                    currentRemainingSpace <= bestRemainingSpaceHolder[0] + 1e-9) {
                if (currentRemainingSpace < bestRemainingSpaceHolder[0] - 1e-9 ||
                        (Math.abs(currentRemainingSpace - bestRemainingSpaceHolder[0]) < 1e-9)) {
                    bestRemainingSpaceHolder[0] = currentRemainingSpace;
                    bestSolutionItems.clear();
                    bestSolutionItems.putAll(currentSolutionItems);
                }
            }
            return;
        }

        String currentBoxTypeKey = boxTypes.get(currentIndex);
        double volumePerOneFullBox = boxVolumesPerFullBox.get(currentBoxTypeKey);
        double weightPerOneBox = weightPerBox.get(currentBoxTypeKey);  // 修改为单箱重量
        int itemsPerOneFullBox = itemsPerFullBoxMap.get(currentBoxTypeKey);
        int availableItemsOfCurrentType = currentAvailableItems.getOrDefault(currentBoxTypeKey, 0);

        // 计算最多可以尝试放入多少"逻辑箱"
        int maxLogicalBoxesToTry = (availableItemsOfCurrentType == 0) ? 0 :
                (int) Math.ceil((double) availableItemsOfCurrentType / itemsPerOneFullBox);

        // 尝试放入 0 到 maxLogicalBoxesToTry 个当前类型的"逻辑箱"
        for (int numLogicalBoxesToAttempt = 0; numLogicalBoxesToAttempt <= maxLogicalBoxesToTry; numLogicalBoxesToAttempt++) {
            // 计算这次尝试要放入的物品数量
            int itemsToPlaceInThisAttempt = Math.min(
                    numLogicalBoxesToAttempt * itemsPerOneFullBox,
                    availableItemsOfCurrentType
            );

            // 计算这次尝试的体积和重量消耗
            double addedVolume = numLogicalBoxesToAttempt * volumePerOneFullBox;
            double addedWeight = numLogicalBoxesToAttempt * weightPerOneBox;  // 使用单箱重量

            if (currentRemainingSpace >= addedVolume - 1e-9 &&
                    (initialTotalWeight == -1 || currentRemainingWeight >= addedWeight - 1e-9)) {

                currentSolutionItems.put(currentBoxTypeKey, itemsToPlaceInThisAttempt);

                Map<String, Integer> nextAvailableItems = new HashMap<>(currentAvailableItems);
                nextAvailableItems.put(currentBoxTypeKey, availableItemsOfCurrentType - itemsToPlaceInThisAttempt);

                findBestCombination(
                        boxTypes, boxVolumesPerFullBox, nextAvailableItems, weightPerBox, itemsPerFullBoxMap,
                        initialTotalSpace, initialTotalWeight,
                        currentSolutionItems, currentIndex + 1,
                        currentRemainingSpace - addedVolume,
                        initialTotalWeight == -1 ? -1 : currentRemainingWeight - addedWeight,
                        bestSolutionItems, bestRemainingSpaceHolder
                );

                currentSolutionItems.remove(currentBoxTypeKey);
            } else {
                break;
            }
        }
    }

    //public static void main(String[] args) {
    //    // 示例数据：
    //    Map<String, Double> boxVolumes = new HashMap<>();
    //    boxVolumes.put("a", 2.1); // 体积
    //    boxVolumes.put("b", 0.8);
    //    boxVolumes.put("c", 0.4);
    //
    //    Map<String, Integer> boxCounts = new HashMap<>(); // 可用【物品数量】
    //    boxCounts.put("a", 5);  // a类物品有5个
    //    boxCounts.put("b", 6);  // b类物品有6个
    //    boxCounts.put("c", 2);  // c类物品有2个
    //
    //    Map<String, Double> boxWeights = new HashMap<>(); // 【单个物品】重量
    //    boxWeights.put("a", 0.6);
    //    boxWeights.put("b", 1.1);
    //    boxWeights.put("c", 0.1);
    //
    //    Map<String, Integer> loadNum = new HashMap<>(); // 每箱【物品数量】
    //    loadNum.put("a", 2); // a类每箱2个物品
    //    loadNum.put("b", 3); // b类每箱3个物品
    //    loadNum.put("c", 2); // c类每箱2个物品
    //
    //    double remainingSpace = 2.0;
    //    double remainingWeight = 2; //不考虑重量
    //
    //    System.out.println("Initial data:");
    //    System.out.println("Box Volumes (per box): " + boxVolumes);
    //    System.out.println("Available Items (total): " + boxCounts);
    //    System.out.println("Item Weights (per item): " + boxWeights);
    //    System.out.println("Items per Box: " + loadNum);
    //    System.out.println("Container - Remaining Space: " + remainingSpace + ", Remaining Weight: " + (remainingWeight == -1 ? "N/A" : remainingWeight));
    //    System.out.println("-----------------------------------------");
    //
    //    Map<String, Integer> results = optimizeBoxLoading(boxVolumes, boxCounts, boxWeights, loadNum, remainingSpace, remainingWeight);
    //    System.out.println("Optimized loading (item counts): " + results);
    //
    //    // 计算最终结果的总体积和总重量 (仅用于验证)
    //    double finalVolumeUsed = 0;
    //    double finalWeightUsed = 0;
    //    if (results != null) {
    //        for (Map.Entry<String, Integer> entry : results.entrySet()) {
    //            String type = entry.getKey();
    //            int itemsPlaced = entry.getValue();
    //            if (itemsPlaced > 0) {
    //                int itemsPerFullBox = loadNum.get(type);
    //                double volumePerFullBox = boxVolumes.get(type);
    //                double weightPerSingleItem = boxWeights.get(type);
    //
    //                int boxesForVolume = (int) Math.ceil((double) itemsPlaced / itemsPerFullBox);
    //                finalVolumeUsed += boxesForVolume * volumePerFullBox;
    //                finalWeightUsed += itemsPlaced * weightPerSingleItem;
    //            }
    //        }
    //    }
    //    System.out.println("Final Volume Used by Result: " + finalVolumeUsed);
    //    System.out.println("Final Weight Used by Result: " + finalWeightUsed);
    //    System.out.println("Remaining Space in Container (Target 2.0): " + (remainingSpace - finalVolumeUsed));
    //
    //    System.out.println("-----------------------------------------");
    //    System.out.println("Expected for provided case (remainingSpace=2.0):");
    //    System.out.println("One possible optimal: {b=6 items (2 boxes, 1.6 vol), c=2 items (1 box, 0.4 vol)} -> Total Vol = 2.0, Remaining = 0.0");
    //}

    /**
     * 处理加急数据的装柜逻辑
     *
     * @param urgentItems 加急货物列表
     */
    private List<LclContainerBO> processUrgentContainerLoading(List<LclContainerLoadingBO> urgentItems, String shippingEndDate, Map<String, String> warehouseMap) {
        List<LclContainerLoadingBO> lclContainerLoadingBOSList = urgentItems.stream()
                .filter(i -> i.getLclShippingNum() > 0).collect(Collectors.toList());
        lclContainerLoadingBOSList
                .forEach(item -> item.setVolume((item.getCaseLength() / 100) * (item.getCaseWidth() / 100) * (item.getCaseHeight() / 100)));

        Map<String, List<LclContainerLoadingBO>> groupedByWarehouse = lclContainerLoadingBOSList.stream().collect(Collectors.groupingBy(LclContainerLoadingBO::getDestinationWarehouse));

        List<LclContainerBO> containerBOFinishedDate = new ArrayList<>();

        for (var lclByWarehouses : groupedByWarehouse.entrySet()) {
            String warehouse = lclByWarehouses.getKey();
            List<LclContainerLoadingBO> lclLoadingByWarehouses = lclByWarehouses.getValue();
            // 按发货日期排序
            lclLoadingByWarehouses.sort(Comparator.comparing(LclContainerLoadingBO::getShippingStartDate));

            // 处理每个7天批次
            while (!lclLoadingByWarehouses.isEmpty()) {
                Date currentBatchStartDate = DateUtil.parse(lclLoadingByWarehouses.getFirst().getShippingStartDate());
                // 计算当前批次的结束日期（+6天）,如果超过了shippingEndDate，则使用shippingEndDate
                Date currentBatchEndDate = DateUtil.offsetDay(currentBatchStartDate, 6).after(DateUtil.parse(shippingEndDate))
                        ? DateUtil.parse(shippingEndDate)
                        : DateUtil.offsetDay(currentBatchStartDate, 6);

                // 分离当前批次和剩余数据
                Map<Boolean, List<LclContainerLoadingBO>> batchSplit = lclLoadingByWarehouses.stream()
                        .collect(Collectors.partitioningBy(item -> {
                            Date itemDate = DateUtil.parse(item.getShippingStartDate());
                            return !itemDate.after(currentBatchEndDate);
                        }));

                List<LclContainerLoadingBO> currentBatch = batchSplit.get(true);
                List<LclContainerLoadingBO> remainingItems = batchSplit.get(false);

                if (CollectionUtil.isNotEmpty(currentBatch)) {
                    if (warehouseMap.get(warehouse).contains("FBA")) {
                        // 将FBA仓库数据按照pcsType分类：大号标准一组，其他一组
                        Map<Boolean, List<LclContainerLoadingBO>> groupedByPcsType = currentBatch.stream()
                                .collect(Collectors.partitioningBy(bo -> {
                                    String pcsType = bo.getPcsType();
                                    return pcsType != null && pcsType.equals(PCSTypeEnum.OVERSIZE_STANDARD.getCode().toString());
                                }));

                        List<LclContainerLoadingBO> oversizeStandardItems = groupedByPcsType.get(true);
                        List<LclContainerLoadingBO> otherSizeItems = groupedByPcsType.get(false);

                        if (CollectionUtil.isNotEmpty(oversizeStandardItems)) {
                            containerBOFinishedDate.add(loadItemsIntoContainers(oversizeStandardItems, warehouse));
                        }
                        if (CollectionUtil.isNotEmpty(otherSizeItems)) {
                            containerBOFinishedDate.add(loadItemsIntoContainers(otherSizeItems, warehouse));
                        }

                        lclLoadingByWarehouses = remainingItems;
                    } else {
                        if (CollectionUtil.isNotEmpty(currentBatch)) {
                            containerBOFinishedDate.add(loadItemsIntoContainers(currentBatch, warehouse));
                        }
                        lclLoadingByWarehouses = remainingItems;
                    }
                } else {
                    break;
                }
            }
        }
        if (CollectionUtil.isNotEmpty(containerBOFinishedDate)) {
            int shipmentNum = 1;
            for (var i : containerBOFinishedDate) {
                i.setShipmentCode("加急" + shipmentNum);
                shipmentNum++;
            }
            ;
        }
        return containerBOFinishedDate;
    }

    /**
     * 将数据放入指定的柜子中
     *
     * @param itemsToLoad 需要装载的货物列表
     * @return 装载后的柜子列表
     */
    private LclContainerBO loadItemsIntoContainers(List<LclContainerLoadingBO> itemsToLoad, String warehouse) {

        LclContainerLoadingBO first = itemsToLoad.getFirst();

        Set<String> factorySet = itemsToLoad.stream().map(LclContainerLoadingBO::getFactoryCode).collect(Collectors.toSet());
        ChannelSummary channelSummary = calculateSummaries(itemsToLoad);

        return LclContainerBO.builder()
                .factoryNum(factorySet.size())
                .addressCode(first.getAddressCode())
                .usedVolume(channelSummary.getTotalVolume())
                .usedWeight(channelSummary.getTotalWeight())
                .destinationWarehouse(warehouse)
                .loadedItems(itemsToLoad)
                .build(); // 占位返回
    }
}


