package com.purchase.purchase_server.service.shipment;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.dataObject.ShipmentPlanDO;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.form.PurchaseOrdersForm;
import com.purchase.purchase_server.entity.form.ShipmentPlanForm;
import com.purchase.purchase_server.entity.vo.ShipmentPlan.ShipmentDetailVO;
import com.purchase.purchase_server.entity.vo.ShipmentPlan.ShipmentPlanVO;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrderVirtualVO;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrdersVO;
import jakarta.servlet.http.HttpServletResponse;

import java.io.InputStream;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cm_shipment_plan(货件计划表)】的数据库操作Service
* @createDate 2024-11-18 13:59:01
*/
public interface IShipmentPlanService {

    /**
     * 导入货件计划
     *
     */
    void importShipmentPlanInfo(InputStream file, byte[] fileBytes, String fileName);

    /**
     * 导入货件计划明细
     *
     */
    void importShipmentDetailInfo(InputStream file, byte[] fileBytes, String fileName);


    /**
     * 保存货件数据
     * @param val 货件数据
     */
    void save(ShipmentPlanDO val);

    /**
     * 分页查询
     *
     * @param form 查询条件
     */
    IPage<ShipmentPlanVO> pageList(ShipmentPlanForm form);

    /**
     * 货件计划详情
     *
     * @param form 查询条件
     */
    List<ShipmentDetailVO> getShipmentDetail(ShipmentPlanForm form);


    boolean editShipmentPlan(ShipmentPlanForm form, LogTrackNumDto logTrackNumDto);

    /**
     * 删除货件计划
     */
    boolean deletePlan(String planId,LogTrackNumDto logTrackNumDto);

    void exportTemplate(HttpServletResponse response);

    List<SenboWarehouseDto> getSenboWarehouseVo();
}
