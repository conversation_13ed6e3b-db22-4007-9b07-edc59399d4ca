package com.purchase.purchase_server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum IsOldStatusEnum {

    VIRTUAL_SKU("0", "虚拟sku"),
    OLD_SKU("1", "老sku");



    private final String code;
    private final String desc;
    public static IsOldStatusEnum ofCode(String code) {
        return Arrays.stream(IsOldStatusEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
    public static List<String> getCodeList() {
        return Arrays.stream(IsOldStatusEnum.values())
                .map(IsOldStatusEnum::getCode).collect(Collectors.toList());
    }
}
