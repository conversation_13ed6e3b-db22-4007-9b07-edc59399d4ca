package com.purchase.purchase_server.service.lcl.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.enums.YesOrNoEnum;
import com.purchase.purchase_server.entity.bo.Lcl.LclContainerLoadingBO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclContainerDetailDO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclContainerInfoDO;
import com.purchase.purchase_server.entity.dto.Lcl.LclContainerDetailInfoDto;
import com.purchase.purchase_server.entity.dto.ProductCategoryDTO;
import com.purchase.purchase_server.entity.form.LclSearchPageForm;
import com.purchase.purchase_server.entity.form.LclUpdateShippingNumForm;
import com.purchase.purchase_server.entity.vo.Lcl.LclContainerDetailVo;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.repository.dataRepository.Lcl.LclContainerDetailRepositoryImpl;
import com.purchase.purchase_server.repository.dataRepository.Lcl.LclContainerInfoRepositoryImpl;
import com.purchase.purchase_server.service.lcl.ILclCalContainerLoadingService;
import com.purchase.purchase_server.service.lcl.ILclContainerLoadingDetailService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.RoundingMode.HALF_UP;
import static java.math.RoundingMode.UP;

/**
 * <AUTHOR>
 * @date 2024/12/18
 **/
@Service
public class LclContainerLoadingDetailServiceImpl implements ILclContainerLoadingDetailService {

    @Resource
    private LclContainerDetailRepositoryImpl lclContainerDetailRepository;

    @Resource
    private LclContainerInfoRepositoryImpl lclContainerInfoRepository;

    @Resource
    @Lazy
    private ILclCalContainerLoadingService lclCalContainerLoadingService;

    @Override
    public LclContainerInfoDO lclContainerInfoByDetail(List<LclContainerLoadingBO> loadedItems, Map<String, ProductCategoryDTO> categoryDTOMap) {
        LclContainerInfoDO containerInfoDO = new LclContainerInfoDO();
        boolean isForeignFlag = false;
        Date latestDate = null;
        Set<String> factorySet = new HashSet<>();
        int lclShippingNum = 0;
        BigDecimal shippingVolume = BigDecimal.ZERO, inspectionVolume = BigDecimal.ZERO, shippingWeight = BigDecimal.ZERO;
        String shipmentCategory = null;
        for (LclContainerLoadingBO loadedItem : loadedItems) {
            if (loadedItem.getIsForeignFlag().equals("1")) {
                isForeignFlag = true;
            }
            DateTime shippingStartDate = DateUtil.parse(loadedItem.getShippingStartDate());
            if (latestDate == null || shippingStartDate.isAfter(latestDate)) {
                latestDate = shippingStartDate;
            }
            lclShippingNum += loadedItem.getLclShippingNum();

            // 计算发货箱数
            BigDecimal shippingCase = BigDecimal.valueOf(loadedItem.getLclShippingNum()).divide(BigDecimal.valueOf(loadedItem.getContainerLoad()), 0, UP);
            // 计算单箱体积
            BigDecimal singleCaseVolume = BigDecimal.valueOf(loadedItem.getCaseHeight()).multiply(BigDecimal.valueOf(loadedItem.getCaseLength()).multiply(BigDecimal.valueOf(loadedItem.getCaseWidth())));
            //BigDecimal singleCaseVolume = BigDecimal.valueOf(loadedItem.getCaseHeight() * loadedItem.getCaseLength() * loadedItem.getCaseWidth());
            // 计算发货体积
            BigDecimal volume = singleCaseVolume.multiply(shippingCase).divide(BigDecimal.valueOf(1000000));
            // 计算发货重量
            BigDecimal weight = BigDecimal.valueOf(loadedItem.getSingleCaseGrossWeight()).multiply(shippingCase).setScale(2, HALF_UP);

            factorySet.add(loadedItem.getFactoryCode());
            shippingVolume = shippingVolume.add(volume);
            if (loadedItem.getCommodityInspection().equals(YesOrNoEnum.YES.getCodeStr())) {
                inspectionVolume = inspectionVolume.add(volume);
            }
            shippingWeight = shippingWeight.add(weight);

            if (StrUtil.isNotBlank(loadedItem.getCategoryId())) {
                ProductCategoryDTO productCategoryDTO = JSON.to(ProductCategoryDTO.class, categoryDTOMap.get(loadedItem.getCategoryId()));
                if (StrUtil.isBlank(shipmentCategory)) {
                    shipmentCategory = productCategoryDTO.getCategoryName();
                } else if (!shipmentCategory.equals(productCategoryDTO.getCategoryName())) {
                    shipmentCategory = "家具+灯具";
                }
            }
        }
        BigDecimal totalCaseVolume = BigDecimal.valueOf(72);
        containerInfoDO.setShippingStartDate(latestDate);
        containerInfoDO.setSourceType(isForeignFlag ? "未装满拼柜" : "排柜新生成");
        containerInfoDO.setDestinationWarehouse(loadedItems.getFirst().getDestinationWarehouse());
        containerInfoDO.setAddressCode(loadedItems.getFirst().getAddressCode());
        containerInfoDO.setFactoryCount(factorySet.size());
        containerInfoDO.setShippingQuantity(lclShippingNum);
        shippingVolume = shippingVolume.setScale(2, HALF_UP);
        containerInfoDO.setShippingVolume(shippingVolume.doubleValue());
        double containerLoadRate = shippingVolume.divide(totalCaseVolume, 4, HALF_UP).doubleValue();
        containerInfoDO.setContainerLoadingRate(containerLoadRate == 0 ? 0.01 : containerLoadRate);
        double surplusVolume = totalCaseVolume.subtract(shippingVolume).doubleValue();
        containerInfoDO.setSurplusVolume(surplusVolume < 0 ? 0 : surplusVolume);
        inspectionVolume = inspectionVolume.setScale(2, HALF_UP);
        containerInfoDO.setInspectionVolumeRatio(inspectionVolume.divide(shippingVolume, 4, HALF_UP).doubleValue());
        containerInfoDO.setShippingWeight(shippingWeight.doubleValue());
        containerInfoDO.setShipmentCategory(shipmentCategory);
        return containerInfoDO;
    }

    @Override
    public List<LclContainerDetailDO> getDetailProductSnapshotIdListByFactoryFinishedInfo(LclSearchPageForm form) {
        return lclContainerDetailRepository.getLclContainerDetailProductSnapshotIdByRecordId(form);
    }

    @Override
    public List<String> getContainerIdList(List<String> detailIdList, String recordId) {
        return lclContainerDetailRepository.getContainerIdList(detailIdList, recordId);
    }

    @Override
    public List<LclContainerDetailDO> getContainerDetailInfoList(List<String> detailIdList, String containerId) {
        return lclContainerDetailRepository.getContainerDetailList(detailIdList, containerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShippingNum(LclUpdateShippingNumForm form) {
        // 获取待修改货件详情数据
        LclContainerDetailDO containerDetail = lclContainerDetailRepository.getById(form.getDetailId());

        // 获取该计划其他的装柜详情数据并计算发货数量
        List<LclContainerDetailDO> allDetailList = lclContainerDetailRepository.getContainerDetailList(containerDetail.getConsolidationFactoryFinishedId());
        List<LclContainerDetailDO> otherDetailList = allDetailList.stream()
                .filter(f -> !f.getId().equals(containerDetail.getId()))
                .collect(Collectors.toList());
        int otherShippingNum = otherDetailList.stream().mapToInt(LclContainerDetailDO::getLclShippingNum).sum();
        int totalShippingNum = form.getLclShippingNum() + otherShippingNum;

        // 计算新的未安排数量（工厂交货数量-sum(所有详情数据的发货数量)）
        Integer packageNum = containerDetail.getFactoryShippingPackageNum();
        if (packageNum < totalShippingNum) {
            // 若总发货数量 > 工厂交货数量，报错返回前端
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "发货数量大于未安排数量");
        }
        int unShippingNum = packageNum - totalShippingNum;
        containerDetail.setLclShippingNum(form.getLclShippingNum());
        containerDetail.setFactoryRemainNum(unShippingNum);
        containerDetail.setUpdateBy(null);
        containerDetail.setUpdateDate(null);

        // 修改其他详情数据的未安排数量
        for (var detail : otherDetailList) {
            detail.setFactoryRemainNum(unShippingNum);
            detail.setUpdateBy(null);
            detail.setUpdateDate(null);
        }

        if (form.getLclShippingNum() == 0) {
            // 若发货数量为0，直接删除该条装柜信息
            lclContainerDetailRepository.removeById(containerDetail.getId());
        } else {
            otherDetailList.add(containerDetail);
        }
        lclContainerDetailRepository.updateBatchById(otherDetailList);

        // 计算待修改货件柜子的装货数据（柜子发货数量、体积、盈余体积等）
        String containerInfoId = containerDetail.getContainerInfoId();
        updateContainerInfo(containerInfoId);
    }

    /**
     * 更新柜子信息（发货数量、体积、装载率等）
     * @param containerInfoId 柜子信息ID
     */
    @Override
    public void updateContainerInfo(String containerInfoId) {
        LclContainerInfoDO infoDO = lclContainerInfoRepository.getById(containerInfoId);
        if (ObjectUtil.isEmpty(infoDO)) {
            return;
        }
        LclSearchPageForm lclSearchPageForm = new LclSearchPageForm();
        lclSearchPageForm.setContainerId(containerInfoId);
        LclContainerDetailVo detailVo = lclCalContainerLoadingService.getContainerDetailList(lclSearchPageForm);
        List<LclContainerDetailInfoDto> allDetailOneCaseList = detailVo.getDetailList();

        if (CollectionUtil.isEmpty(allDetailOneCaseList)) {
            // 若当前柜子的发货数量为0，直接删除改货件
            lclContainerInfoRepository.removeById(containerInfoId);
        } else {
            LclContainerInfoDO containerInfo = lclContainerInfoRepository.getById(containerInfoId);
            int shippingNum = 0;
            double shippingVolume = 0;
            double commodityInspectionVolume = 0;
            double shippingWeight = 0;
            String shipmentCategory = null;
            Date latestDate = null;
            Set<String> factorySet = new HashSet<>();
            for (var detail : allDetailOneCaseList) {
                shippingNum += detail.getLclShippingNum();
                shippingVolume += detail.getShippingVolume();
                shippingWeight += detail.getShippingWeight();
                if (YesOrNoEnum.YES.getCodeInt().equals(detail.getCommodityInspection())) {
                    commodityInspectionVolume += detail.getShippingVolume();
                }

                if (StrUtil.isNotBlank(detail.getShipmentCategory())) {
                    if (StrUtil.isBlank(shipmentCategory)) {
                        shipmentCategory = detail.getShipmentCategory();
                    } else if (!shipmentCategory.equals(detail.getShipmentCategory())) {
                        shipmentCategory = "家具+灯具";
                    }
                }
                DateTime shippingStartDate = DateUtil.parse(detail.getShippingStartDate());
                if (latestDate == null || shippingStartDate.isAfter(latestDate)) {
                    latestDate = shippingStartDate;
                }
                factorySet.add(detail.getFactoryCode());
            }
            BigDecimal wholeVolume = BigDecimal.valueOf(72);
            BigDecimal shippingVolumeBig = BigDecimal.valueOf(shippingVolume).setScale(2, HALF_UP);
            double containerLoadingRate = shippingVolumeBig.divide(wholeVolume, 4, HALF_UP).doubleValue();
            double surplusVolume = wholeVolume.subtract(shippingVolumeBig).doubleValue();
            double inspectionVolumeRatio = BigDecimal.valueOf(commodityInspectionVolume).divide(shippingVolumeBig, 4, HALF_UP).doubleValue();

            containerInfo.setShippingQuantity(shippingNum);
            containerInfo.setShippingWeight(BigDecimal.valueOf(shippingWeight).setScale(2, HALF_UP).doubleValue());
            containerInfo.setShippingVolume(shippingVolumeBig.doubleValue());
            containerInfo.setContainerLoadingRate(containerLoadingRate);
            containerInfo.setSurplusVolume(surplusVolume);
            containerInfo.setInspectionVolumeRatio(inspectionVolumeRatio);
            containerInfo.setShipmentCategory(shipmentCategory);
            containerInfo.setShippingStartDate(latestDate);
            containerInfo.setFactoryCount(factorySet.size());
            containerInfo.setUpdateBy(null);
            containerInfo.setUpdateDate(null);
            lclContainerInfoRepository.updateById(containerInfo);
        }
    }
}
