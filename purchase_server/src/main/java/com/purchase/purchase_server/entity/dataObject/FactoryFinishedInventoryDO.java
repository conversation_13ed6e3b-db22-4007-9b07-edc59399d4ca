package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.Objects;

/**
 * @Description 工厂交货实体类
 * <AUTHOR>
 * @Date 2024/1/10 14:30
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_delivery_factory_inventory")
public class FactoryFinishedInventoryDO extends BaseEntity {

    /**
     * 合同号
     */
    private String contractCode;

    /**
     * 工厂交货时间
     */
    private Date factoryFinishedDate;
    /**
     * 剩余数量
     */
    private Integer factoryRemainNum;
    /**
     * 工厂交货数量
     */
    private Integer factoryShippingPackageNum;
    /**
     * 发货计划id
     */
    private String shippingProjectId;
    /**
     * 虚拟skuId
     */
    private String virtualSkuId;
    /**
     * sku类型
     * @see com.purchase.purchase_server.enums.IsOldStatusEnum
     */
    private String isOldStatus;

    /**
     * 快照记录id
     */
    private String productSnapshotId;

    /**
     * 数据来源
     * @see com.purchase.purchase_server.enums.ShippingSourceTypeEnum
     */
    private String sourceType;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 该计划是否需要优先发货
     */
    @TableField(exist = false)
    private Boolean needToPriorDelivery;

    /**
     * 发货类型
     */
    private Integer deliveryType;

    @TableField(exist = false)
    private String virtualSku;

    public int newHashCode() {
        return Objects.hash(contractCode, virtualSku, factoryFinishedDate, remarks);
    }
}
