package com.purchase.purchase_server.entity.form;

import com.purchase.purchase_server.entity.dto.RepProjectDto;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 补货计划-采纳建议
 * @date 2024/1/18
 **/
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
public class AdvicePurchaseForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1190426467232645258L;
    /**
     * 补货计划ID
     */
    private List<RepProjectDto> repVirtualPurchaseIdList;
    /**
     * 补货采纳状态
     */
    private String trialStatus;
    /**
     * 采购日开始时间
     */
    private String advicePurchaseStartDate;
    /**
     * 采购日结束时间
     */
    private String advicePurchaseEndDate;
    /**
     * 修改人
     */
    private String updateBy;
}
