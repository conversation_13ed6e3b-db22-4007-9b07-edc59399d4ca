package com.inventory_server.model.redundancy.entity.dp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * @Description 本地仓各工厂交期冗余库存
 * <AUTHOR>
 * @Date 2024/12/12 14:03
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryFinishedRedundancy {

    private String contractCode;

    private String virtualSku;

    private LocalDate factoryFinishedDate;

    private Integer redundancy;

    private String remarks;
}
