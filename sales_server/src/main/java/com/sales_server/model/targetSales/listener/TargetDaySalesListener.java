package com.sales_server.model.targetSales.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.sales_server.model.targetSales.entity.excel.TargetDaySalesExcelEntity;
import com.sales_server.model.targetSales.repository.dataRepository.TargetSalesRepository;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @Date 2024/11/8 14:35
 **/
public class TargetDaySalesListener extends AbstractTargetSalesListener<TargetDaySalesExcelEntity> {

    private final AtomicInteger succeedTimes;

    private final Set<String> successfulVirtualIdSet;

    public TargetDaySalesListener(List<String> errorList, Map<String, String> virtualSkuIdMap, AtomicInteger succeedTimes
            , TargetSalesRepository targetSalesRepository, Set<String> successfulVirtualIdSet) {
        super(errorList, virtualSkuIdMap, targetSalesRepository);
        this.succeedTimes = succeedTimes;
        this.successfulVirtualIdSet = successfulVirtualIdSet;
    }

    @Override
    public void invoke(TargetDaySalesExcelEntity data, AnalysisContext context) {
        Integer approximateRowNumber = context.readSheetHolder().getApproximateTotalRowNumber();
        validTotalNum(approximateRowNumber);
        String virtualSku = data.getVirtualSku();

        // 判断虚拟sku是否存在，不存在则报错，若存在则转化成id
        String virtualId = validateSku(virtualSku);
        successfulVirtualIdSet.add(virtualId);

        // 校验目标日销是否合规
        BigDecimal targetSales = validateDecimal(data.getTargetSales(), virtualSku);
        LocalDate localDate = validateDate(data.getLocalDate());

        // 获取日销
        addSalesInfo(virtualId, localDate, targetSales);

        if (salesInfoList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            salesInfoList = new ArrayList<>(BATCH_COUNT);
        }
        succeedTimes.incrementAndGet();
    }

    @Override
    protected LocalDate validateDate(String dateStr) {
        LocalDate localDate = super.validateDate(dateStr);
        // 限制暂时放开，后续版本加上
//        if (LocalDate.now().isAfter(localDate)) {
//            throw new IllegalArgumentException("不可编辑历史时间目标日销");
//        }
        return localDate;
    }

    @Override
    protected List<String> getExistedHeadList() {
        return TargetDaySalesHeadEnum.getNameList();
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        String sheetName = context.readSheetHolder().getReadSheet().getSheetName();
        List<String> headList = headMap.values().stream()
                .filter(StrUtil::isNotBlank)
                .toList();

        List<String> existedHeadList = getExistedHeadList();
        if (!headList.equals(existedHeadList)) {
            throw new RuntimeException(sheetName + "表头错误，请检查表头是否正确");
        }
    }

    @Getter
    @AllArgsConstructor
    public enum TargetDaySalesHeadEnum {

        VIRTUAL_SKU("*虚拟SKU"),
        DATE("*日期"),
        TARGET_SALES("*目标日销");

        private final String name;

        public static List<String> getNameList() {
            return Arrays.stream(values()).map(TargetDaySalesHeadEnum::getName).toList();
        }
    }
}
