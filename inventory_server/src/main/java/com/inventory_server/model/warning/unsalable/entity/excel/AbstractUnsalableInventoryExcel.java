package com.inventory_server.model.warning.unsalable.entity.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.inventory_server.model.product.enums.BorrowingStrategyEnum;
import com.inventory_server.model.product.enums.VirtualProductTypeEnum;
import com.inventory_server.model.product.enums.VirtualSubTypeEnum;
import com.inventory_server.model.warning.unsalable.entity.vo.UnsalableWaringVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN;

/**
 * <AUTHOR>
 * @Date 2025/2/25 14:00
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ContentStyle(wrapped = BooleanEnum.TRUE,
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER)
public abstract class AbstractUnsalableInventoryExcel {
    @ExcelProperty(value = "虚拟SKU", order = 1)
    private String virtualSku;

    @ExcelProperty(value = "自定义SKU", order = 2)
    private String selfSku;

    @ExcelProperty(value = "品名", order = 3)
    private String productName;

    @ExcelProperty(value = "大类", order = 4)
    private String category;

    @ExcelProperty(value = "渠道", order = 5)
    private String channel;

    @ExcelProperty(value = "产品状态", order = 6)
    private String productStatus;

    @ExcelProperty(value = "子体类型", order = 7)
    private String subType;

    @ExcelProperty(value = "借货策略", order = 8)
    private String borrowingStrategy;;

    @ExcelProperty(value = "产品类型", order = 9)
    private String productType;

    @ExcelProperty(value = "运营", order = 10)
    private String operator;

    /**
     * 无计划库存
     */
    @ExcelProperty(value = "无计划库存", order = 996)
    private Integer totalUnsalableInventory;

    /**
     * 无计划货值
     */
    @ExcelProperty(value = "无计划货值(CNY)", order = 997)
    private Double unsalablePrices;

    /**
     * 含税单价
     */
    @ExcelProperty(value = "单价(CNY)", order = 998)
    private Double priceWithTaxes;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN)
    @ExcelProperty(value = "更新时间", order = 999)
    private String updateDate;

    public AbstractUnsalableInventoryExcel(UnsalableWaringVo vo) {
        this.virtualSku = vo.getVirtualSku();
        this.selfSku = vo.getSelfSku();
        this.productName = vo.getProductName();
        this.category = vo.getCategory();
        this.channel = vo.getChannel();
        this.productStatus = vo.getProductStatus() == null ? null : VirtualProductStatusEnum.ofCode(vo.getProductStatus()).getDesc();
        this.subType = vo.getSubType() == null ? null : VirtualSubTypeEnum.ofCode(vo.getSubType()).getDesc();
        this.borrowingStrategy = vo.getBorrowingStrategy() == null ? null : BorrowingStrategyEnum.ofCode(vo.getBorrowingStrategy()).getDesc();
        this.productType = vo.getProductType() == null ? null : VirtualProductTypeEnum.ofCode(vo.getProductType()).getDesc();
        this.operator = vo.getOperator();
        this.totalUnsalableInventory = vo.getTotalUnsalableInventory();
        this.priceWithTaxes = vo.getPriceWithTaxes();
        this.unsalablePrices = vo.getUnsalablePrices();
        this.updateDate = vo.getUpdateDate();
    }
}
