package com.purchase.purchase_server.entity.form;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/10
 **/
@Data
public class InteriorInfoQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = -6718927821664917529L;
    /**
     * 创建开始时间
     */
    private String createStartDate;

    /**
     * 创建结束时间
     */
    private String createEndDate;
}
