package com.inventory_server.model.warehouse.service;

import com.inventory_server.infrastructures.entity.LogTrackNumDto;
import com.inventory_server.model.warehouse.entity.dto.WarehouseDetailDto;
import com.inventory_server.model.warehouse.entity.form.ChangeSenboWarehouseForm;
import com.inventory_server.model.warehouse.entity.form.SenboWarehouseForm;

import java.util.List;

public interface IWarehouseSaveService {
    void saveWarehouseInfo(List<WarehouseDetailDto> warehouseDetailList, LogTrackNumDto dto);

    void changeWarehouseSenboInfo(ChangeSenboWarehouseForm form, LogTrackNumDto dto);

    String saveOrUpdateSenBoWarehouseInfo(SenboWarehouseForm form);
}
