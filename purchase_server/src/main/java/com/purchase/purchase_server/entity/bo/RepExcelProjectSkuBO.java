package com.purchase.purchase_server.entity.bo;

import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * @Description 补货计划表导出-建议采购量
 * <AUTHOR>
 * @Date 2024/1/19 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RepExcelProjectSkuBO extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 7927485032668200310L;
    /**
     * skuId
     */
    private String selfSkuId;
    /**
     * sku
     */
    private String selfSku;
    /**
     * 供应商代码
     */
    private String factoryCode;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 虚拟skuId
     */
    private String virtualSkuId;
    /**
     * 虚拟sku
     */
    private String virtualSku;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 子体类型
     */
    private Integer subType;
    /**
     * 产品状态
     */
    private Integer productStatus;
    /**
     * 实际日销
     */
    private Double actualDailySalesNum;
    /**
     * 试算当月目标日销
     */
    private Double targetSalesNum;
    /**
     * 子体达成率
     */
    private Double subEntityRate;
    /**
     * 父体达成率
     */
    private Double parentEntityRate;
    /**
     * 理论补货量
     */
    private Integer advicePurchaseNum;
    /**
     * 建议补货量
     */
    private Double actualReplenishmentNum;
    /**
     * 建议原因
     */
    private String reason;
    /**
     * 运营确认量
     */
    private Double operationConfirmedNum;
    /**
     * 运营确认理由
     */
    private  String confirmedReason;
    /**
     * 头程时间
     */
    private String headShippingDays;
}
