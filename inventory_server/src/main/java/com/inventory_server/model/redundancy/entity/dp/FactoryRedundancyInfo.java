package com.inventory_server.model.redundancy.entity.dp;

import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 工厂冗余信息
 * <AUTHOR>
 * @Date 2024/12/12 13:54
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class FactoryRedundancyInfo extends RedundancyInfo {

    /**
     * 本地未安排数量
     */
    private Integer unShippingNum;

    @Builder.Default
    private List<FactoryFinishedRedundancy> factoryFinishedRedundancyList = new ArrayList<>();

    public void addRedundancy(FactoryFinishedRedundancy redundancy) {
        factoryFinishedRedundancyList.add(redundancy);
    }
}
