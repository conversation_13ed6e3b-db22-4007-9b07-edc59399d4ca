package com.purchase.purchase_server.enums.lcl;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum LclDirectionAndSortEnum {
    SHIPMENT_CODE("shipmentCode","货件号"),
    SHIPPING_START_DATE("shippingStartDate","发货装柜时间"),
    FACTORY_REMAIN_NUM("factoryRemainNum","未安排数量"),

    ASC("asc","升序"),
    DESC("desc","降序");

    private final String code;
    private final String desc;
    public static LclDirectionAndSortEnum ofCode(String code) {
        return Arrays.stream(LclDirectionAndSortEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
    public static List<String> getCodeList() {
        return Arrays.stream(LclDirectionAndSortEnum.values())
                .map(LclDirectionAndSortEnum::getCode).collect(Collectors.toList());
    }
}
