package com.purchase.purchase_server.model.purchase;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.purchase.purchase_server.entity.excelObject.RepStockQuantityInfoExcel;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Map;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_M_D_DATE_FORMAT_SLASH;

/**
 * @Description 补货计划导入-库存数据校验
 * <AUTHOR>
 * @Date 2024/1/16 10:56
 **/
public record RepStockQuantityInfoDp(RepStockQuantityInfoExcel infoExcel, Map<String, String> nameIdMap) {
    public RepStockQuantityInfoDp(RepStockQuantityInfoExcel infoExcel, Map<String, String> nameIdMap) {
        this.infoExcel = infoExcel;
        this.nameIdMap = nameIdMap;

        if (StrUtil.isBlank(infoExcel.getStartShippingTime())) {
            throw new  IllegalArgumentException("出货时间为空");
        }

        if (Optional.ofNullable(infoExcel.getStoreHouse()).isPresent() &&
                Optional.ofNullable(infoExcel.getStockQuantity()).isPresent() &&
                Optional.ofNullable(infoExcel.getVirtualSku()).isPresent() &&
                Optional.ofNullable(infoExcel.getInventoryTime()).isPresent()){
            new ImportVirtualSkuInfoDp(infoExcel.getVirtualSku());
            this.checkDate(infoExcel);
            this.checkNum(infoExcel);
            //检查仓库是否存在
            if (!nameIdMap.containsKey(infoExcel.getStoreHouse())){
                throw new IllegalArgumentException("请填写正确仓库");
            }
        }else{
            throw new IllegalArgumentException("请填写内容");
        }

    }
    private void checkNum(RepStockQuantityInfoExcel stockQuantityInfoExcel){
        if (!(NumberUtil.isInteger(stockQuantityInfoExcel.getStockQuantity()) && Integer.parseInt(stockQuantityInfoExcel.getStockQuantity()) >= 0)){
            throw new IllegalArgumentException("请正确填写数字");
        }
    }
  //判断日期格式是否正确
    private void checkDate(RepStockQuantityInfoExcel stockQuantityInfoExcel){
        String inventoryTime = stockQuantityInfoExcel.getInventoryTime();
        String startShippingTime = stockQuantityInfoExcel.getStartShippingTime();
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(YYYY_M_D_DATE_FORMAT_SLASH);
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);

        try {
            // 尝试解析日期字符串
            LocalDate inventoryDate = LocalDate.parse(inventoryTime, inputFormatter);
            //判断日期是否大于当前日期，使用java8的日期格式
            if (!inventoryDate.isAfter(LocalDate.now())){
                throw new IllegalArgumentException("请填写大于当前日期");
            }
            // 将日期格式化为所需的格式
            inventoryDate.format(outputFormatter);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("请正确填写库存可用日期");
        }

        try {
            LocalDate startShippingDate = LocalDate.parse(startShippingTime, inputFormatter);
            startShippingDate.format(outputFormatter);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("出货时间格式必须是YYYY/MM/DD");
        }
    }
}
