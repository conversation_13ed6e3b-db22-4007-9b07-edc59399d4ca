package com.sales_server.model.targetSales.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description 根据SPU聚合的目标月销
 * <AUTHOR>
 * @Date 2025/4/7 17:50
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpuTargetMonthSalesVo implements Serializable {

    private String spuId;

    private String spuName;

    /**
     * 销售渠道
     */
    private String saleChannel;

    /**
     * 单个虚拟sku待统计月份的总销量
     */
    private Double totalSalesNum;

    /**
     * 动态目标月销数据
     */
    private Map<String, Double> monthTargetSaleMap;

    private List<TargetMonthSalesVo> targetMonthSalesList;

}
