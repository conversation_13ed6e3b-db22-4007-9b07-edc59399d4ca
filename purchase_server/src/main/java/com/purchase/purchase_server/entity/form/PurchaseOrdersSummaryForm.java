package com.purchase.purchase_server.entity.form;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 易仓Api getPurchaseOrders 接口的请求参数
 * <AUTHOR>
 * @Date 2024/6/28 15:09
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrdersSummaryForm implements Serializable {
    @Serial
    private static final long serialVersionUID = 2840797000335683727L;
    private Integer page;

    @JSONField(name = "page_size")
    private Integer pageSize;

    @JSONField(name = "search_date_type")
    private String searchDateType;

    @JSONField(name = "date_for")
    private String dateFor;

    @JSONField(name = "date_to")
    private String dateTo;
}
