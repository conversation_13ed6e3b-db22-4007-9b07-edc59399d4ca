package com.inventory_server.model.warning.unsalable.entity.excel;

import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.inventory_server.model.warning.unsalable.entity.vo.UnsalableWaringVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @Description 无计划库存汇总sheet页
 * <AUTHOR>
 * @Date 2025/4/17 16:47
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@ContentStyle(wrapped = BooleanEnum.TRUE,
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER)
public class GeneralUnsalableInventoryExcel extends AbstractUnsalableInventoryExcel {

    public GeneralUnsalableInventoryExcel(UnsalableWaringVo vo) {
        super(vo);
    }
}
