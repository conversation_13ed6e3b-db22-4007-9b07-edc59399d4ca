package com.purchase.purchase_server.entity.dto.replenishment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 确认量详情页
 * <AUTHOR>
 * @Date 2024/10/10 15:13
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReplenishmentTrialConfirmQuantityDto implements Serializable {
    private String purchaseInventoryId;

    private String warehouse;

    private String trialStatus;

    private String advicePurchaseDate;

    /**
     * 理论补货量
     */
    private Integer advicePurchaseNum;

    /**
     * 补货量区间
     */
    private String replenishmentRange;

    /**
     * 建议补货量
     */
    private Integer actualReplenishmentNum;

    /**
     * 运营确认量
     */
    private Integer operationConfirmedNum;

    private String expectedFactoryFinishedDate;

    private String expectedShippingStartDate;

    private String expectedArrivingDate;

    private Integer containerLoad;

    private Integer packageNum;

    private String purchaseNumType;

    /**
     * 建议补货量Id
     */
    private String adviceId;

    /**
     * 理论补货量Id
     */
    private String theoreticalId;
}
