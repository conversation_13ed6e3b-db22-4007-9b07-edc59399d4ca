package com.inventory_server.model.redundancy.entity.form;

import com.inventory_server.model.redundancy.entity.dto.DeliveryCalRequestDto;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 发货试算表单
 * <AUTHOR>
 * @Date 2024/5/9 16:12
 **/
@Data
@Builder
public class DeliveryCalculationForm implements Serializable {

    /**
     * 发货开始时间
     */
    private String shippingStartDateStr;

    private String calculatedDateStr;

    private List<DeliveryCalRequestDto> deliveryCalRequestDtoList;
}
