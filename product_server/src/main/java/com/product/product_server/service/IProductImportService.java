package com.product.product_server.service;

import com.product.product_server.entity.dataObject.FactoryInfoDO;
import com.product.product_server.entity.excelObject.BaseExcel;
import com.product.product_server.entity.excelObject.SelfProductInfoExcel;
import com.product.product_server.entity.excelObject.VirtualProductInfoExcel;
import com.product.product_server.entity.form.SpuInfoSaveForm;
import com.product.product_server.enums.PCSTypeEnum;
import com.product.product_server.model.products.*;
import com.product.product_server.model.virtualProduct.VirtualProductSkuDp;
import com.product.product_server.utils.easyExcelUtil.listener.AbstractUpdateVirtualProductInfoListener;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;

/**
 * <AUTHOR>
 */
public interface IProductImportService {

    void importExcelToSelfProduct(InputStream file, byte[] byteArrayResource, String fileName) throws IOException;

    void updateSelfProductCommodityInspection(InputStream file, byte[] byteArrayResource, String fileName);

    void importExcelToVirtualProduct(InputStream file, byte[] byteArrayResource, String fileName) throws IOException;

    void updateVirtualProductUpgrade(InputStream file, byte[] byteArrayResource, String fileName) throws IOException;

    void updateVirtualBorrowingStrategy(InputStream file, byte[] byteArrayResource, String fileName) throws IOException;

    <T extends BaseExcel> void updateVirtualProductInfoPartly(InputStream file, byte[] byteArrayResource, String fileName, AbstractUpdateVirtualProductInfoListener.UpdateType updateType) throws IOException, NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException;

    void saveSelfProductInfo(ImportSkuInfoDp importSkuInfo, FactoryInfoDO factoryInfoDO, ImportVirtualSkuInfoDp virtualSkuInfo, SpecificationDp specification,
                             PurchaseInfoDp purchaseInfo, CaseWeightInfoDp caseWeightInfo, SelfProductInfoExcel product,
                             SpuInfoSaveForm spuInfoSaveForm, String imageUrl, PCSTypeEnum pcsTypeEnum);

    void saveVirtualProductInfo(VirtualProductInfoExcel excel, VirtualProductSkuDp virtualProduct, String selfProductId);

    void importProductShippingRatio(InputStream file, byte[] byteArrayResource, String fileName) throws IOException;

}
