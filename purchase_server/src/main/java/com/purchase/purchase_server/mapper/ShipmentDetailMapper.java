package com.purchase.purchase_server.mapper;


import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.dataObject.ShipmentDetailDO;
import org.apache.ibatis.annotations.Param;


/**
* <AUTHOR>
* @description 针对表【cm_shipment_detail(货件明细表)】的数据库操作Mapper
* @createDate 2024-11-18 13:59:01
* @Entity generator.domain.CmShipmentDetail
*/
public interface ShipmentDetailMapper extends MPJBaseMapper<ShipmentDetailDO> {
    int deleteByPlanId(@Param("planId") String planId);

}




