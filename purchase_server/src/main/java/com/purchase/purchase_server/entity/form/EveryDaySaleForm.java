package com.purchase.purchase_server.entity.form;

import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 目标日销更新
 * <AUTHOR>
 * @Date 2024/1/25 11:56
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EveryDaySaleForm implements Serializable {

    private String virtualPurchaseId;

    private String everydaySaleDate;

    @NotNull(message = "目标日销不可以为空")
    @Digits(fraction = 3, message = "目标日销最多3位小数", integer = 10)
    private Double everydaySaleNum;
}
