package com.inventory_server.applications.cqe;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/15
 **/
@Data
public class LeadTimeCommand implements Serializable {

    @Serial
    private static final long serialVersionUID = -962579910424938693L;
    @NotBlank(message = "规则id不能为空")
    private String saleRuleId;

    @Positive(message = "生产交期只能为正整数")
    @NotNull(message = "生产交期只能为正整数")
    private Integer purchaseCircle;

    /**
     * 是否回溯，为空或者为false都不回溯，否则回溯
     */
    private Boolean isBackTracking;
}
