package com.file.system.file_system.service;

import org.springframework.web.multipart.MultipartFile;

public interface IOssFileService {

    String putObject(MultipartFile file, String key, String expireTime);

    /**
     * 根据网络连接上传文件
     * @param form
     * @param networkUrl
     * @return
     */
    String putObject(String key, String networkUrl, String expireTime);

    /**
     * 生成带有时效性的文件访问链接
     *
     * @param key          文件云存储库的唯一地址
     * @return
     */
    String getUrl(String url);

    Boolean doesObjectExist(String filePath);
}
