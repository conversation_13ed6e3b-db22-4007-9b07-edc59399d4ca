package com.demo_server.infrastructures.aop.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2024/6/7
 **/
public class EnumValueValidator implements ConstraintValidator<EnumValue, String> {

    private Class<? extends Enum<?>> enumClass;
    private String methodName;
    @Override
    public void initialize(EnumValue constraintAnnotation) {
        this.enumClass = constraintAnnotation.enumClass();
        this.methodName = constraintAnnotation.methodName();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // 可以根据需求设置是否允许null值
        }
        if (enumClass != null && enumClass.isEnum()) {
            try {
                Method ofDesc = enumClass.getMethod(methodName, String.class);
                Object  result = ofDesc.invoke(null, value);
                return result != null;
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }
}