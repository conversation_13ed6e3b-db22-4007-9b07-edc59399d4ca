package com.inventory_server.model.warning.unsalable.repository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crafts_mirror.common.security.dataPermission.DataPermission;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.inventory_server.model.inventory.entity.dos.InventoryInventoryInfoDO;
import com.inventory_server.model.product.entity.dos.SelfProductDO;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.redundancy.entity.dos.RedundantInventoryDO;
import com.inventory_server.model.warning.unsalable.entity.dos.WarningUnsalableInventoryDO;
import com.inventory_server.model.warning.unsalable.entity.form.UnsalablePageForm;
import com.inventory_server.model.warning.unsalable.entity.vo.UnsalableWaringVo;
import com.inventory_server.model.warning.unsalable.enums.UnsalableWarningSortEnum;
import com.inventory_server.model.warning.unsalable.mapper.WarningUnsalableInventoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.crafts_mirror.utils.constant.SecurityConstants.ROLE_DATA_PER_WARNING;
import static com.inventory_server.model.warning.unsalable.enums.UnsalableWarningSortEnum.UNSALABLE_INVENTORY;
import static com.inventory_server.model.warning.unsalable.enums.UnsalableWarningSortEnum.UNSALABLE_PRICES;

@Service
@Slf4j
public class WarningUnsalableInventoryRepository extends ServiceImpl<WarningUnsalableInventoryMapper, WarningUnsalableInventoryDO> {

    public IPage<UnsalableWaringVo> getUnsalableWaringPage(UnsalablePageForm form) {
        IPage<UnsalableWaringVo> iPage = new Page<>(form.getCurrent(), form.getSize());
        return baseMapper.selectJoinPage(iPage, UnsalableWaringVo.class, buildQueryWrapper(form));
    }

    public List<UnsalableWaringVo> getUnsalableWaringList(UnsalablePageForm form) {
        return baseMapper.selectJoinList(UnsalableWaringVo.class, buildQueryWrapper(form));
    }

    public WarningUnsalableInventoryDO getByInventoryId(String inventoryId) {
        return getOne(Wrappers.<WarningUnsalableInventoryDO>lambdaQuery().eq(WarningUnsalableInventoryDO::getInventoryInfoId, inventoryId));
    }

    public void deleteByInventoryId(String inventoryId) {
        remove(Wrappers.<WarningUnsalableInventoryDO>lambdaQuery().eq(WarningUnsalableInventoryDO::getInventoryInfoId, inventoryId));
    }

    private MPJLambdaWrapper<WarningUnsalableInventoryDO> buildQueryWrapper(UnsalablePageForm form) {
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_WARNING, "t2");

        return new MPJLambdaWrapper<WarningUnsalableInventoryDO>()
                .selectAs(WarningUnsalableInventoryDO::getId, "warningId")
                .select(WarningUnsalableInventoryDO::getTotalUnsalableInventory)
                .select(WarningUnsalableInventoryDO::getUpdateDate)
                .select(WarningUnsalableInventoryDO::getInventoryInfoId)
                .select(SelfProductDO::getImage)
                .select(SelfProductDO::getProductName)
                .select(SelfProductDO::getCurrency)
                .selectAs(SelfProductDO::getCategoryId, "category")
                .select(SelfProductDO::getPriceWithTaxes)
                .selectAs(SelfProductDO::getSku, "selfSku")
                .selectAs(VirtualProductDO::getId, "virtualSkuId")
                .select(VirtualProductDO::getVirtualSku)
                .select(VirtualProductDO::getOldSku)
                .select(VirtualProductDO::getChannel)
                .select(VirtualProductDO::getOperator)
                .select(VirtualProductDO::getProductStatus)
                .select(VirtualProductDO::getProductType)
                .select(VirtualProductDO::getSubType)
                .select(VirtualProductDO::getBorrowingStrategy)


                .select("t3.price_with_taxes * t.total_unsalable_inventory AS unsalablePrices")

                .leftJoin(RedundantInventoryDO.class, RedundantInventoryDO::getId, WarningUnsalableInventoryDO::getInventoryInfoId)
                .leftJoin(VirtualProductDO.class, VirtualProductDO::getId, RedundantInventoryDO::getVirtualSkuId)
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, VirtualProductDO::getSelfProductSkuId)

                // 只展示无计划库存大于0的
                .gt(WarningUnsalableInventoryDO::getTotalUnsalableInventory, 0)
                .like(StrUtil.isNotBlank(form.getProductName()), SelfProductDO::getProductName, form.getProductName())
                .like(StrUtil.isNotBlank(form.getCategoryId()), SelfProductDO::getCategoryId, form.getCategoryId())
                .like(StrUtil.isNotBlank(form.getCurrency()), SelfProductDO::getCurrency, form.getCurrency())
                .eq(StrUtil.isNotBlank(form.getChannel()), VirtualProductDO::getChannel, form.getChannel())
                .eq(form.getProductStatus() != null, VirtualProductDO::getProductStatus, form.getProductStatus())
                .in(CollectionUtil.isNotEmpty(form.getProductStatusList()), VirtualProductDO::getProductStatus, form.getProductStatusList())
                .eq(form.getProductType() != null, VirtualProductDO::getProductType, form.getProductType())
                .eq(form.getSubType() != null, VirtualProductDO::getSubType, form.getSubType())
                .eq(StrUtil.isNotBlank(form.getOperator()), VirtualProductDO::getOperator, form.getOperator())
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), VirtualProductDO::getVirtualSku, form.getVirtualSkuList())
                .in(CollectionUtil.isNotEmpty(form.getOldSkuList()), VirtualProductDO::getOldSku, form.getOldSkuList())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), SelfProductDO::getSku, form.getSelfSkuList())
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
                .func(StrUtil.isNotBlank(form.getSort()) && StrUtil.isNotBlank(form.getDirection()),
                        i -> {
                            if (form.getSort().equals(UNSALABLE_PRICES.getCode())) {
                                i.orderBy(true,
                                        UnsalableWarningSortEnum.ASC.getCode().equals(form.getDirection()),
                                        "unsalablePrices");
                            } else if (form.getSort().equals(UNSALABLE_INVENTORY.getCode())) {
                                i.orderBy(true,
                                        UnsalableWarningSortEnum.ASC.getCode().equals(form.getDirection()),
                                        WarningUnsalableInventoryDO::getTotalUnsalableInventory);
                            }
                        }
                )
                .orderByDesc(InventoryInventoryInfoDO::getUpdateDate)
                .orderByAsc(VirtualProductDO::getVirtualSku);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveUnsalableInventoryByInventoryInfoId(WarningUnsalableInventoryDO unsalableInventoryDO) {
        String inventoryInfoId = unsalableInventoryDO.getInventoryInfoId();
        boolean exists = checkUnsalableInventory(inventoryInfoId);
        if (exists) {
            baseMapper.update(unsalableInventoryDO, Wrappers.<WarningUnsalableInventoryDO>lambdaQuery()
                    .eq(WarningUnsalableInventoryDO::getInventoryInfoId, inventoryInfoId));
        } else {
            baseMapper.insert(unsalableInventoryDO);
        }
    }

    public boolean checkUnsalableInventory(String inventoryInfoId) {
        if (StrUtil.isBlank(inventoryInfoId)) {
            return false;
        }
        return baseMapper.exists(Wrappers.<WarningUnsalableInventoryDO>lambdaQuery()
                .eq(WarningUnsalableInventoryDO::getInventoryInfoId, inventoryInfoId)
        );
    }

}
