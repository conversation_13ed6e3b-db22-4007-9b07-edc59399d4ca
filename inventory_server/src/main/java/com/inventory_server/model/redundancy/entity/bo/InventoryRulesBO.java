package com.inventory_server.model.redundancy.entity.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/11
 **/

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryRulesBO {
    private String inventoryInfoId;

    private Integer safeDays;

    private Integer changeableSafeDays;

    private Integer purchaseDays;

    private Integer purchaseCircle;

    private Integer shippingCircle;

    private Integer transitDays;

    private String purchaseDate;

    private Map<String, Integer> headShippingDays;

    private Map<String, Double> shippingRatio;
}
