<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.purchase.purchase_server.mapper.TrialShippingInventoryMapper">

    <resultMap id="trialShippingInventoryMap" type="com.purchase.purchase_server.entity.bo.TrialShippingInventoryBO">
        <result property="shippingProjectId" column="shippingProjectId" jdbcType="VARCHAR"/>
        <result property="factoryCode" column="factory_code" jdbcType="VARCHAR"/>
        <result property="virtualSku" column="virtual_sku" jdbcType="VARCHAR"/>
        <result property="shippingStartDate" column="shipping_start_date" jdbcType="TIMESTAMP"/>
        <result property="factoryFinishedDate" column="factory_finished_date" jdbcType="TIMESTAMP"/>
        <result property="factoryFinishedId" column="factory_finished_id" jdbcType="VARCHAR"/>
        <result property="factoryShippingNum" column="factory_shipping_num" jdbcType="INTEGER"/>
        <result property="remainNum" column="remain_num" jdbcType="INTEGER"/>
        <result property="selfProductSku" column="self_product_sku" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="channel" column="channel" jdbcType="VARCHAR"/>
        <result property="contractCode" column="contract_code" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <insert id="insertLogInfo">
        INSERT INTO `crafts_mirror`.`cm_shipping_calculation_log` ( `virtual_sku`, `warehouse`, `arriving_date`, `info`, `project_create_date`, create_date)
        VALUES (#{virtualSku}, #{warehouse}, #{arrivingDate}, #{info}, #{projectCreateDate}, now());
    </insert>



    <!--到仓明细导出-->
    <resultMap id="arrivalDetailsMap" type="com.purchase.purchase_server.entity.bo.ArrivalDetailsBO">
        <result property="factoryCode" column="factory_code" jdbcType="VARCHAR"/>
        <result property="destinationSku" column="destination_sku" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="channel" column="channel" jdbcType="VARCHAR"/>
        <result property="selfProductSku" column="self_product_sku" jdbcType="VARCHAR"/>
        <result property="containerLoad" column="container_load" jdbcType="INTEGER"/>
        <result property="caseLength" column="case_length" jdbcType="DOUBLE"/>
        <result property="caseWidth" column="case_width" jdbcType="DOUBLE"/>
        <result property="caseHeight" column="case_height" jdbcType="DOUBLE"/>
        <result property="caseVolume" column="case_volume" jdbcType="DOUBLE"/>
        <result property="singleCaseGrossWeight" column="single_case_gross_weight" jdbcType="DOUBLE"/>
        <result property="buyer" column="buyer" jdbcType="VARCHAR"/>
        <result property="shippingStartDate" column="shipping_start_date" jdbcType="TIMESTAMP"/>
        <result property="realShippingStartDate" column="real_shipping_start_date" jdbcType="TIMESTAMP"/>
        <result property="factoryFinishedDate" column="factory_finished_date" jdbcType="TIMESTAMP"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="destinationWarehouse" column="destination_warehouse" jdbcType="VARCHAR"/>
        <result property="shippingNum" column="shipping_num" jdbcType="INTEGER"/>
        <result property="factoryShippingVolume" column="factory_shipping_volume" jdbcType="DOUBLE"/>
        <result property="factoryGrossWeight" column="factory_gross_weight" jdbcType="DOUBLE"/>
        <result property="contractCode" column="contract_code" jdbcType="VARCHAR"/>
    </resultMap>


    <resultMap id="shipCountMap" type="com.purchase.purchase_server.entity.bo.ShipCountBO">
        <result property="shippingNum" column="shipping_num" jdbcType="DOUBLE"/>
        <result property="factoryRemainNum" column="factory_remain_num" jdbcType="DOUBLE"/>
        <result property="notYetShippingPeriodNum" column="not_yet_shipping_period_num" jdbcType="DOUBLE"/>
    </resultMap>




    <!-- 批量删除 -->
    <delete id="deleteByfactoryFinishedIds">
        DELETE FROM cm_trial_shipping_inventory WHERE factory_finished_id IN
        <foreach collection="factoryFinishedIds" item="factoryFinishedId" open="(" separator="," close=")">
            #{factoryFinishedId}
        </foreach>
    </delete>
</mapper>