package com.inventory_server.model.product.service.impl;

import com.inventory_server.model.product.bo.SelfAndVirtualBO;
import com.inventory_server.model.product.entity.dos.SelfProductDO;
import com.inventory_server.model.product.repository.databaseRepository.SelfProductRepositoryImpl;
import com.inventory_server.model.product.service.ISelfProductService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 自定义展示的service层
 * <AUTHOR>
 * @Date 2024/5/7 16:38
 **/
@Service
@Slf4j
public class SelfProductServiceImpl implements ISelfProductService {

    @Resource
    private SelfProductRepositoryImpl selfProductRepositoryImpl;

    @Override
    public SelfAndVirtualBO getSelfProductByVirtualId(String virtualId) {
        return selfProductRepositoryImpl.getSelfProductByVirtualId(virtualId);
    }

    @Override
    public List<SelfProductDO> getSelfProductByFactoryInfoCode(String val) {
        return selfProductRepositoryImpl.getSelfProductByFactoryInfoCode(val);
    }

    @Override
    public SelfProductDO getSelfProductByVirtualSku(String virtualSku) {
        return selfProductRepositoryImpl.getSelfProductDoByVirtualSku(virtualSku);
    }
}
