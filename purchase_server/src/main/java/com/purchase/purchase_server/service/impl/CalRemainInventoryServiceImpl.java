package com.purchase.purchase_server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.purchase.purchase_server.entity.dto.FactoryFinishedInventoryDto;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.entity.dto.delivery.TrialCalReplenishmentDto;
import com.purchase.purchase_server.service.ICalRemainInventoryService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateField.DAY_OF_YEAR;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.DELIVERY_CAL_REDUNDANT_DATE_NUM;
import static java.math.RoundingMode.HALF_UP;

/**
 * @Description 处理发货补货库存消耗的模块
 * <AUTHOR>
 * @Date 2024/4/7 11:03
 **/
@Service(value = "calRemainInventoryService")
@Slf4j
public class CalRemainInventoryServiceImpl implements ICalRemainInventoryService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Map<String, Map<String, Double>> calRemainInventoryMap(DateTime shippingStartDate,
                                                                  List<FactoryRemainInventoryDto> factoryRemainInventoryList,
                                                                  int maxCalDate, int minShippingDate,
                                                                  Map<String, Double> targetSalesMap,
                                                                  Map<String, Double> inventorySaleDefRatio,
                                                                  Map<String, Map<String, Double>> everyDayWarehouseSaleMap,
                                                                  List<String> sortedWarehouseList) {
        var everyDayRetainMap = initEverydayRemainInventoryMap(factoryRemainInventoryList, targetSalesMap);
        // 处理剩余库存多余需要售出件数的情况
        calRetainInventory(everyDayRetainMap, inventorySaleDefRatio, shippingStartDate, maxCalDate,
                targetSalesMap, everyDayWarehouseSaleMap, sortedWarehouseList);

        return everyDayRetainMap;
    }

    private Map<String, Map<String, Double>> initEverydayRemainInventoryMap(List<FactoryRemainInventoryDto> factoryRemainInventoryList,
                                                                            Map<String, Double> targetSalesMap) {
        // 发货日开始，各仓库的可售库存
        Map<String, Map<String, Double>> everyDayRetainMap = new TreeMap<>();
        DateTime maxSalesDate = targetSalesMap.keySet().stream()
                .map(m -> DateUtil.parse(m, YYYY_MM_DD_DATE_FORMAT_SLASH))
                .max(Comparator.comparing(m -> m))
                .orElse(DateTime.now());
        // 初始化剩余库存数据
        for (FactoryRemainInventoryDto dto : factoryRemainInventoryList) {
            String formatDate = DateUtil.format(dto.getEnableUsingDate(), YYYY_MM_DD_DATE_FORMAT_SLASH);
            Map<String, Double> retainMap = everyDayRetainMap.getOrDefault(formatDate, new HashMap<>(8));
            // 此处只考虑可用库存生效的对应的库存量，之前可用库存的和移步到计算剩余可用库存中去
            if (dto.getEnableUsingDate().compareTo(maxSalesDate) > 0) {
                continue;
            }
            retainMap.put(dto.getWarehouse(), retainMap.getOrDefault(dto.getWarehouse(), 0D) + dto.getStoreNum());
            everyDayRetainMap.put(formatDate, retainMap);
        }
        return everyDayRetainMap;
    }

    @Override
    public BigDecimal consumeZeroRatioWarehouseInventory(Map<String, Double> inventorySaleDefRatio,
                                                         Map<String, Double> warehouseInventoryMap,
                                                         Map<String, Double> warehouseDaySaleMap,
                                                         Map<String, Double> nextDayInventoryMap,
                                                         double everyDayTotalSalNum) {
        BigDecimal everydayTotalSaleNumDec = BigDecimal.valueOf(everyDayTotalSalNum).setScale(3, HALF_UP);

        // 发货比例为0的仓需要优先消耗掉库存，如果有多个仓的发货比例都为0，则这些仓均分日销
        List<String> zeroRatioWarehouseSet = inventorySaleDefRatio.entrySet().stream()
                .filter(entry -> entry.getValue() == 0 && warehouseInventoryMap.getOrDefault(entry.getKey(), 0D) > 0)
                .sorted(Comparator.comparing(entry -> warehouseInventoryMap.getOrDefault(entry.getKey(), 0D)))
                .map(Map.Entry::getKey).toList();
        BigDecimal warehouseSize = BigDecimal.valueOf(zeroRatioWarehouseSet.size());
        for (String warehouse : zeroRatioWarehouseSet) {
            BigDecimal todayWarehouseInventory = BigDecimal.valueOf(warehouseInventoryMap.getOrDefault(warehouse, 0D));
            if (todayWarehouseInventory.equals(BigDecimal.ZERO)) {
                continue;
            }
            BigDecimal needSale = everydayTotalSaleNumDec.divide(warehouseSize, 3, HALF_UP);
            if (todayWarehouseInventory.compareTo(needSale) >= 0) {
                everydayTotalSaleNumDec = everydayTotalSaleNumDec.subtract(needSale);
                nextDayInventoryMap.compute(warehouse, (k, v) -> {
                    BigDecimal subtract = todayWarehouseInventory.subtract(needSale);
                    BigDecimal tomorrowRemainNum = BigDecimal.valueOf(Optional.ofNullable(v).orElse(0D));
                    return tomorrowRemainNum.add(subtract).setScale(3, HALF_UP).doubleValue();
                });
                warehouseDaySaleMap.put(warehouse, needSale.setScale(3, HALF_UP).doubleValue());
            } else {
                everydayTotalSaleNumDec = everydayTotalSaleNumDec.subtract(todayWarehouseInventory);
                warehouseDaySaleMap.put(warehouse, todayWarehouseInventory.setScale(3, HALF_UP).doubleValue());
            }
            warehouseSize = warehouseSize.subtract(BigDecimal.ONE);
        }
        return everydayTotalSaleNumDec;
    }

    @Override
    public int getRedundantDateNum() {
        int redundantDate;
        try {
            redundantDate = Integer.parseInt(Objects.requireNonNull(stringRedisTemplate.opsForValue().get(DELIVERY_CAL_REDUNDANT_DATE_NUM)));
        } catch (Exception e) {
            redundantDate = 7;
            stringRedisTemplate.opsForValue().set(DELIVERY_CAL_REDUNDANT_DATE_NUM, String.valueOf(redundantDate));
        }
        return redundantDate;
    }

    @Override
    public TrialCalReplenishmentDto reCalShippingRemainInventory(Map<String, Double> inventorySaleDefRatio, int shippingFrequency,
                                                                 int safeDate, List<String> sortedWarehouseList,
                                                                 List<FactoryRemainInventoryDto> factoryRemainInventoryList,
                                                                 int containerLoad, Map<String, Integer> headShippingDateMap,
                                                                 Map<String, Double> saleNumPerDayMap, DateTime projectStartDate,
                                                                 List<FactoryFinishedInventoryDto> factoryFinishedInventoryList,
                                                                 List<FactoryRemainInventoryDto> afterShippingEnableUsingInventoryList) {
        DateTime maxCalDate = saleNumPerDayMap.keySet().stream().map(DateUtil::parse).max(DateUtil::compare).orElse(projectStartDate);
        int calCircle = (int) DateUtil.betweenDay(projectStartDate, DateUtil.endOfMonth(maxCalDate), true);
        var mockShippingMap = getMockShippingMap(afterShippingEnableUsingInventoryList, headShippingDateMap, projectStartDate,
                calCircle, factoryRemainInventoryList);

        factoryRemainInventoryList.addAll(afterShippingEnableUsingInventoryList);
        Map<String, Map<String, Double>> everyDayWarehouseSaleMap = new HashMap<>(64);
        int minShippingDate = headShippingDateMap.values().stream().min(Comparator.comparing(h -> h)).orElseThrow(NullPointerException::new);

        // 将所有试算结果重新计算一遍，算出整个表格
        Map<String, Map<String, Double>> everyDayRemainInventoryMap = calRemainInventoryMap(projectStartDate,
                Collections.unmodifiableList(factoryRemainInventoryList),
                calCircle, minShippingDate, saleNumPerDayMap, inventorySaleDefRatio, everyDayWarehouseSaleMap, sortedWarehouseList);

        implementShippingStartDateAndArrivingDate(afterShippingEnableUsingInventoryList, headShippingDateMap, mockShippingMap, everyDayRemainInventoryMap);
        log.info("----------------------------------------------------------");
        log.info(JSON.toJSONString(mockShippingMap));
        log.info(JSON.toJSONString(everyDayRemainInventoryMap));
        log.info(JSON.toJSONString(everyDayWarehouseSaleMap));

        return TrialCalReplenishmentDto.builder()
                .everydayOnShippingInventoryMap(mockShippingMap)
                .everydayRemainInventoryMap(everyDayRemainInventoryMap)
                .everydaySaleProductMap(everyDayWarehouseSaleMap)
                .shippingInventoryList(afterShippingEnableUsingInventoryList)
                .build();
    }

    public Map<String, Map<String, Double>> getMockShippingMap(List<FactoryRemainInventoryDto> inventoryDtos,
                                                               Map<String, Integer> headShippingDateMap,
                                                               DateTime shippingStartDate, int calCircle,
                                                               List<FactoryRemainInventoryDto> remainInventoryListForCal) {
        Set<String> warehouseSet = headShippingDateMap.keySet();
        Map<String, Map<String, Double>> mockShippingInventoryMap = new TreeMap<>();
        // 计算模拟在途表格
        for (DateTime calDate = new DateTime(shippingStartDate);
             calDate.compareTo(shippingStartDate.offsetNew(DAY_OF_YEAR, calCircle)) <= 0;
             calDate.offset(DAY_OF_YEAR, 1)
        ) {
            // 初始化数据
            String calDateString = calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
            Map<String, Double> warehouseShippingMap = new HashMap<>(mockShippingInventoryMap.size());
            for (String warehouse : warehouseSet) {
                warehouseShippingMap.put(warehouse, 0D);
            }
            mockShippingInventoryMap.put(calDateString, warehouseShippingMap);
        }

        // 填充有在途货物的数据
        for (FactoryRemainInventoryDto dto : inventoryDtos) {
            DateTime startDate = DateUtil.offset(dto.getEnableUsingDate(), DAY_OF_YEAR, -headShippingDateMap.get(dto.getWarehouse()));
            startDate = dto.getStartShippingTime() == null ? startDate : DateTime.of(dto.getStartShippingTime());
            DateTime endDate = DateTime.of(dto.getEnableUsingDate());
            calShippingStartDateAndEndDate(startDate, endDate, dto, mockShippingInventoryMap);
        }

        // 填充导入的海外在途库存
        for (FactoryRemainInventoryDto dto : remainInventoryListForCal) {
            DateTime startDate = new DateTime(shippingStartDate);
            DateTime endDate = new DateTime(dto.getEnableUsingDate());
            calShippingStartDateAndEndDate(startDate, endDate, dto, mockShippingInventoryMap);
        }
        return mockShippingInventoryMap;
    }

    private void calShippingStartDateAndEndDate(DateTime startDate, DateTime endDate, FactoryRemainInventoryDto dto,
                                                Map<String, Map<String, Double>> mockShippingInventoryMap) {
        for (DateTime calDate = new DateTime(startDate); calDate.compareTo(endDate) < 0; calDate.offset(DAY_OF_YEAR, 1)) {
            Map<String, Double> warehouseShippingMap = mockShippingInventoryMap.get(calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH));
            if (warehouseShippingMap == null) {
                continue;
            }
            Double storeNum = dto.getStoreNum();
            warehouseShippingMap.compute(dto.getWarehouse(), (k, v) -> Optional.ofNullable(v).orElse(0D) + storeNum);
        }
    }

    @Override
    public void implementShippingStartDateAndArrivingDate(List<FactoryRemainInventoryDto> inventoryDtos,
                                                          Map<String, Integer> headShippingDateMap,
                                                          Map<String, Map<String, Double>> mockShippingMap,
                                                          Map<String, Map<String, Double>> everyDayRemainInventoryMap) {
        for (FactoryRemainInventoryDto dto : inventoryDtos) {
            if (dto.getStoreNum() <= 0) {
                continue;
            }
            Date enableUsingDate = dto.getEnableUsingDate();
            Integer headShippingDays = headShippingDateMap.get(dto.getWarehouse());
            DateTime startShippingTime = dto.getStartShippingTime() == null ? DateUtil.offset(enableUsingDate, DAY_OF_YEAR, -headShippingDays) : DateTime.of(dto.getStartShippingTime());
            // 设置发货时间
            String shippingDate = startShippingTime.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
            Map<String, Double> warehouseShippingMap = mockShippingMap.get(shippingDate);
            if (warehouseShippingMap != null) {
                warehouseShippingMap.put("startShipping", 1.0);
            }

            // 计算各仓到货时间
            Map<String, Double> remainInventoryMap = everyDayRemainInventoryMap.get(DateUtil.format(enableUsingDate, YYYY_MM_DD_DATE_FORMAT_SLASH));
            if (remainInventoryMap == null) {
                continue;
            }
            remainInventoryMap.put("startSale", 1.0);
        }
    }

    private void calRetainInventory(Map<String, Map<String, Double>> everyDayRetainMap, Map<String, Double> inventorySaleDefRatio,
                                    DateTime startDate, int maxCalDate, Map<String, Double> targetSalesMap,
                                    Map<String, Map<String, Double>> everyDayWarehouseSaleMap, List<String> sortedWarehouseList) {
        for (DateTime calDate = new DateTime(startDate); DateUtil.compare(calDate, startDate.offsetNew(DAY_OF_YEAR, maxCalDate)) <= 0;
             calDate = calDate.offset(DAY_OF_YEAR, 1)) {
            String calDateStr = calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
            String nextDateStr = calDate.offsetNew(DAY_OF_YEAR, 1).toString(YYYY_MM_DD_DATE_FORMAT_SLASH);

            Map<String, Double> warehouseInventoryMap = everyDayRetainMap.getOrDefault(calDateStr, new HashMap<>(8));

            // 填充剩余库存为0的仓库情况
            for (String warehouse : inventorySaleDefRatio.keySet()) {
                warehouseInventoryMap.putIfAbsent(warehouse, 0D);
                everyDayRetainMap.putIfAbsent(calDateStr, warehouseInventoryMap);
            }
            // 当天各仓库的消耗
            Map<String, Double> warehouseSaleMap = new HashMap<>(8);

            Map<String, Double> nextDayInventoryMap = everyDayRetainMap.getOrDefault(nextDateStr, new HashMap<>(8));
            double everyDayTotalSaleNum = targetSalesMap.getOrDefault(calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH), 0D);

            BigDecimal everydayTotalSaleNumDec = consumeZeroRatioWarehouseInventory(inventorySaleDefRatio, warehouseInventoryMap,
                    warehouseSaleMap, nextDayInventoryMap, everyDayTotalSaleNum);
            // 如果当天的目标日销消耗完了，则直接进入下一天
            if (everydayTotalSaleNumDec.compareTo(BigDecimal.ZERO) <= 0) {
                for (var entry : inventorySaleDefRatio.entrySet()) {
                    if (entry.getValue() <= 0) {
                        continue;
                    }
                    warehouseSaleMap.putIfAbsent(entry.getKey(), 0D);
                    nextDayInventoryMap.compute(entry.getKey(), (k, v) ->
                            roundUpToThreeDecimal(Optional.ofNullable(v).orElse(0D) + warehouseInventoryMap.getOrDefault(entry.getKey(), 0D)));
                }
                if (DateUtil.compare(calDate, startDate.offsetNew(DAY_OF_YEAR, maxCalDate)) != 0) {
                    everyDayRetainMap.put(nextDateStr, nextDayInventoryMap);
                }
                everyDayWarehouseSaleMap.put(calDateStr, warehouseSaleMap);
                continue;
            }

            BigDecimal tempAfterZeroTargetSales = new BigDecimal(String.valueOf(everydayTotalSaleNumDec));
            Map<String, Double> warehouseRemainMap = everyDayRetainMap.getOrDefault(calDateStr, new HashMap<>());

            Set<String> soldOutSet = new HashSet<>(8);
            BigDecimal sales = BigDecimal.ZERO;
            Set<String> remainInventorySet = warehouseRemainMap.entrySet().stream()
                    .filter(entry -> entry.getValue() > 0 && inventorySaleDefRatio.get(entry.getKey()) > 0)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());

            // 处理剩余库存不够一天消耗的仓库
            boolean needRepeat = CollectionUtil.isNotEmpty(remainInventorySet);
            while (needRepeat) {
                needRepeat = false;
                Iterator<String> iterator = remainInventorySet.iterator();
                while (iterator.hasNext()) {
                    double totalRatio = warehouseRemainMap.entrySet().stream()
                            .filter(entry -> entry.getValue() > 0 && !soldOutSet.contains(entry.getKey()) && inventorySaleDefRatio.get(entry.getKey()) > 0)
                            .mapToDouble(entry -> inventorySaleDefRatio.get(entry.getKey()))
                            .sum();
                    if (totalRatio == 0) {
                        break;
                    }
                    String arrivedWarehouse = iterator.next();
                    BigDecimal remainInventory = BigDecimal.valueOf(warehouseRemainMap.get(arrivedWarehouse));
                    double ratio = inventorySaleDefRatio.get(arrivedWarehouse);
                    BigDecimal realRatio = BigDecimal.valueOf(ratio).divide(BigDecimal.valueOf(totalRatio), 3, HALF_UP);
                    BigDecimal realSales = tempAfterZeroTargetSales.multiply(realRatio);

                    if (remainInventory.compareTo(realSales) < 0) {
                        warehouseSaleMap.put(arrivedWarehouse, remainInventory.setScale(3, HALF_UP).doubleValue());
                        sales = sales.add(remainInventory);
                        needRepeat = true;
                        iterator.remove();
                        soldOutSet.add(arrivedWarehouse);
                        tempAfterZeroTargetSales = tempAfterZeroTargetSales.subtract(remainInventory);
                    }
                }
            }
            double totalRatio = warehouseRemainMap.entrySet().stream()
                    .filter(entry -> entry.getValue() > 0 && !soldOutSet.contains(entry.getKey()) && inventorySaleDefRatio.get(entry.getKey()) > 0)
                    .mapToDouble(entry -> inventorySaleDefRatio.get(entry.getKey()))
                    .sum();

            everydayTotalSaleNumDec = everydayTotalSaleNumDec.subtract(sales);
            for (String warehouse : sortedWarehouseList) {
                BigDecimal destinationRatio = BigDecimal.valueOf(inventorySaleDefRatio.getOrDefault(warehouse, 0D));
                BigDecimal realRatio;
                if (totalRatio == 0 || !remainInventorySet.contains(warehouse)) {
                    realRatio = BigDecimal.ZERO;
                } else {
                    realRatio = destinationRatio.divide(BigDecimal.valueOf(totalRatio), 3, HALF_UP);
                }
                BigDecimal realSales = everydayTotalSaleNumDec.multiply(realRatio).setScale(3, HALF_UP);
                warehouseSaleMap.putIfAbsent(warehouse, realSales.doubleValue());

                nextDayInventoryMap.compute(warehouse, (k, v) -> {
                    if (inventorySaleDefRatio.getOrDefault(k, 0D) == 0 || soldOutSet.contains(k)) {
                        return v;
                    }
                    BigDecimal originalNextDayInventory = BigDecimal.valueOf(Optional.ofNullable(v).orElse(0D));
                    BigDecimal todayInventory = BigDecimal.valueOf(warehouseInventoryMap.getOrDefault(k, 0D));
                    return originalNextDayInventory.add(todayInventory).subtract(realSales).setScale(3, HALF_UP).doubleValue();
                });
            }
            if (DateUtil.compare(calDate, startDate.offsetNew(DAY_OF_YEAR, maxCalDate)) < 0) {
                everyDayRetainMap.put(nextDateStr, nextDayInventoryMap);
            }
            everyDayWarehouseSaleMap.put(calDateStr, warehouseSaleMap);
        }
    }

    /**
     * 保留小数点后三位
     *
     * @param num 入参
     * @return 保留三位小数
     */
    protected double roundUpToThreeDecimal(double num) {
        return BigDecimal.valueOf(num).setScale(3, HALF_UP).doubleValue();
    }

    /**
     * 计算第一次售罄时间
     *
     * @param mockRemainInventoryMap 模拟剩余库存
     * @param destinationWarehouse   目标仓库
     * @return 实际销售比例
     */
    @Override
    public String getFirstSoldOut(Map<String, TreeMap<String, Double>> mockRemainInventoryMap,
                                  String destinationWarehouse, DateTime expectedArrivingDate,
                                  Map<String, TreeMap<String, Double>> destinationSale) {
        // 创建防御性副本避免并发修改
        Map<String, TreeMap<String, Double>> remainInventoryCopy = new TreeMap<>();
        for (var entry : mockRemainInventoryMap.entrySet()) {
            TreeMap<String, Double> innerMapCopy = new TreeMap<>();
            // 对内层Map的每个值也创建副本
            if (entry.getValue() != null) {
                for (Map.Entry<String, Double> innerEntry : entry.getValue().entrySet()) {
                    if (innerEntry.getValue() != null) {
                        innerMapCopy.put(innerEntry.getKey(), innerEntry.getValue());
                    }
                }
            }
            remainInventoryCopy.put(entry.getKey(), innerMapCopy);
        }

        // 使用一个变量来表示和是否大于零
        String firstSoldOutDate = null;
        String beforeDate = null;
        double beforeDateValue = 0;
        // 遍历两个Map，逐一比较日期对应的值之和是否大于零，并找到第一次和等于零的日期
        for (var remain : remainInventoryCopy.entrySet()) {
            Date date = DateUtil.parse(remain.getKey());

            if (date.compareTo(expectedArrivingDate) < 0) {
                beforeDate = remain.getKey();
                beforeDateValue = remain.getValue().get(destinationWarehouse);
                continue;
            }
            TreeMap<String, Double> remainMap = remain.getValue();
            double quantity = remainMap.get(destinationWarehouse);
            if (quantity == 0) {
                //获取month月份的第一天
                Double v = destinationSale.getOrDefault(remain.getKey(), new TreeMap<>()).getOrDefault(destinationWarehouse, 0D);
                if (v != null && v >= 0) {
                    if (beforeDateValue > 0 && beforeDateValue <= v) {
                        firstSoldOutDate = beforeDate;
                    } else {
                        firstSoldOutDate = remain.getKey();
                    }
                    break;
                }
            } else {
                beforeDate = remain.getKey();
                beforeDateValue = remain.getValue().get(destinationWarehouse);
            }
        }
        return firstSoldOutDate;
    }
}
