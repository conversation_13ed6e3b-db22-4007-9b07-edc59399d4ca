package com.demo_server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * @Description 库存管理模块
 * <AUTHOR>
 * @Date 2024/5/7 14:24
 **/
@SpringBootApplication
@ServletComponentScan
@EnableDiscoveryClient
@EnableAsync
@EnableAspectJAutoProxy(proxyTargetClass=true)
public class DemoServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(DemoServerApplication.class, args);
    }
}
