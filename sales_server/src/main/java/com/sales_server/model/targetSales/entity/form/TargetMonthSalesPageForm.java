package com.sales_server.model.targetSales.entity.form;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.utils.dp.BasePageForm;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/12 15:25
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TargetMonthSalesPageForm extends BasePageForm implements Serializable {

    private String productName;

    private List<String> selfSkuList;

    private List<String> virtualSkuList;

    private String spuName;

    private List<String> spuIdList;

    private String saleChannel;

    private String operator;

    private String categoryId;

    @NotBlank(message = "时间筛选不能为空")
    private LocalDate startDate;

    @NotBlank(message = "时间筛选不能为空")
    private LocalDate endDate;

    public boolean needSearchSku() {
        return StrUtil.isNotBlank(productName) || CollectionUtil.isNotEmpty(selfSkuList) || CollectionUtil.isNotEmpty(virtualSkuList) ||
                StrUtil.isNotBlank(spuName) || StrUtil.isNotBlank(saleChannel) || StrUtil.isNotBlank(operator) ||
                CollectionUtil.isNotEmpty(spuIdList);
    }
}
