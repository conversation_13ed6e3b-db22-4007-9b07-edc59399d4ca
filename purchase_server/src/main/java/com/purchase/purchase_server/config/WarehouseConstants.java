package com.purchase.purchase_server.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/21 19:15
 **/
@Component
@RefreshScope
@ConfigurationProperties(prefix = "usa.warehouse") // 配置文件的前缀
public class WarehouseConstants {

    private List<String> name;

    public List<String> getName() {
        return name;
    }

    public void setName(List<String> name) {
        this.name = name;
    }
}