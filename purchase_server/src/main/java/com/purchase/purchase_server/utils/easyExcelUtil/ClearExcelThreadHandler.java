package com.purchase.purchase_server.utils.easyExcelUtil;

import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;

/**
 * <AUTHOR>
 * @date 2024/10/31
 **/
public class ClearExcelThreadHandler implements WorkbookWriteHandler {

    @Override
    public void afterWorkbookDispose(final WriteWorkbookHolder writeWorkbookHolder) {
        ExcelThreadContext.clear();
    }

    /**
     * 数据越小越靠前，默认值为0
     *  此处排在最后，防止其他业务没处理完就将线程数据清理掉了
     * @return
     */
    @Override
    public int order() {
        return Integer.MAX_VALUE;
    }
}