package com.sales_server.model.product.repository.interior;

import com.sales_server.model.product.entity.dp.VirtualProductInfoDp;
import com.sales_server.model.product.entity.dto.VirtualProductListDto;
import com.sales_server.model.product.entity.form.VirtualIdListForm;
import com.sales_server.model.product.entity.form.VirtualProductSearchForm;

import java.util.List;

public interface IVirtualProductInteriorRepository {
    List<VirtualProductListDto> getAllVirtualProductList();

    List<VirtualProductListDto> getVirtualProductList(VirtualProductSearchForm form);

    List<VirtualProductInfoDp> getVirtualProductInfoListByVirtualIdList(VirtualIdListForm form);
}
