package com.auth.authorization_server.domain.vo;

import com.auth.authorization_server.model.BasePageForm;
import com.auth.authorization_server.model.vo.RouterVo;
import com.auth.authorization_server.domain.dto.UserDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.util.List;
import java.util.Set;

/**
 * 角色表 cm_sys_role
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserVO extends BasePageForm {

    @Serial
    private static final long serialVersionUID = -5230627251187887379L;
    /**
     * 角色id
     */
    private String id;

    /**
     * 用户账号
     */
    private String userName;
    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 手机号码
     */
    private String phoneNumber;
    /**
     * 帐号状态（0正常 1停用）
     */
    private String userStatus;

    /**
     * 职位Id
     */
    private String postId;

    private UserDTO sysUser;

    private Set<String> roles;

    private List<String> roleNames;

    private Set<String> permissions;

    private List<RouterVo> routerVo;

    private List<RouterVo> filterRouterVo;

    private String createDate;
}
