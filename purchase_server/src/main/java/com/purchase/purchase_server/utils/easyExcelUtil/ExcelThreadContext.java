package com.purchase.purchase_server.utils.easyExcelUtil;

import cn.hutool.core.convert.Convert;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/31
 **/
public class ExcelThreadContext {
    private static final ThreadLocal<Map<String, Object>> THREAD_LOCAL = ThreadLocal.withInitial(HashMap::new);

    public static void clear() {
        THREAD_LOCAL.remove();
    }

    public static void setData(String key, Object value) {
        Map<String, Object> map = get();
        map.put(key, value);
    }

    public static Integer getInteger(String key) {
        return getInteger(key, 1);
    }

    public static Integer getInteger(String key, Integer defaultValue) {
        Map<String, Object> map = get();
        return Convert.toInt(map.get(key), defaultValue);
    }

    public static <K, V> Map<K, V> getMap(String key) {
        return getMap(key, new HashMap<>());
    }

    public static <K, V> Map<K, V> getMap(String key, Map<K, V> defaultValue) {
        Map<String, Object> map = get();
        try {
            return (Map<K, V>) map.getOrDefault(key, defaultValue);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static <T> T getObject(String key) {
        return getObject(key, null);
    }

    public static <T> T getObject(String key, T defaultValue) {
        Map<String, Object> map = get();
        try {
            return (T) map.getOrDefault(key, defaultValue);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    private static void set(Map<String, Object> map) {
        THREAD_LOCAL.set(map);
    }

    public static Map<String, Object> get() {
        return THREAD_LOCAL.get();
    }
}
