package com.purchase.purchase_server.entity.bo.Lcl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * 拼柜装柜计划数据
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclConsolidationFinishedInventoryBO implements Serializable {
    private String id;
    /**
     * 货件号
     */
    private String shipmentCode;
    /**
     * 合同号
     */
    private String contractCode;

    /**
     * 工厂交货时间
     */
    private Date factoryFinishedDate;

    /**
     * 工厂交货数量
     */
    private Integer factoryShippingPackageNum;

    /**
     * 原发货数量
     */
    private Integer originalShippingNum;

    /**
     * 未安排数量
     */
    private Integer factoryRemainNum;

    /**
     * 拼柜计划id
     */
    private String lclRecordId;

    /**
     * 自定义skuId
     */
    private String selfId;

    /**
     * 虚拟skuId
     */
    private String virtualSkuId;

    /**
     * sku类型(0-虚拟sku 1-老sku)
     */
    private String isOldStatus;

    /**
     * 发货类型
     */
    private Integer deliveryType;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 快照记录id
     */
    private String productSnapshotId;

    /**
     * 是否在途（0-不是，1-是）
     */
    private String isForeignFlag;

    /**
     * spuId
     */
    private String spuId;

    /**
     * 外箱规格长
     */
    private Double caseLength;

    /**
     * 外箱规格宽
     */
    private Double caseWidth;

    /**
     * 外箱规格高
     */
    private Double caseHeight;

    /**
     * 单箱个数
     */
    private Integer containerLoad;

    /**
     * 虚拟sku
     */
    private String destinationSku;

    /**
     * 件的类型
     */
    private String pcsType;

    /**
     * 工厂简称
     */
    private String shortName;

    /**
     * 工厂id
     */
    private String factoryId;

    /**
     * 工厂地址代号
     */
    private String addressCode;

    /**
     * 工厂代码
     */
    private String factoryCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 是否商检
     */
    private String commodityInspection;

    /**
     * 单箱毛重
     */
    private Double singleCaseGrossWeight;

    /**
     * 货件大类
     */
    private String categoryId;




}