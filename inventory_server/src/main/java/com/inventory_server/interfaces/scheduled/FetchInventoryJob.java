package com.inventory_server.interfaces.scheduled;

import com.inventory_server.applications.service.IInventoryAppService;
import com.inventory_server.model.redundancy.entity.dos.InventorySaleRulesDO;
import com.inventory_server.model.redundancy.repository.dataRepository.InventorySaleRulesRepository;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Description 从易仓拉取库存信息
 * <AUTHOR>
 * @Date 2024/7/11 18:56
 **/
@Service
@Slf4j
public class FetchInventoryJob {

    @Resource
    private IInventoryAppService iInventoryAppService;

    @Resource
    private InventorySaleRulesRepository inventorySaleRulesRepository;

    @XxlJob("fetchYcInventoryInfoList")
    public void fetchYcInventoryInfoList() {
        iInventoryAppService.fetchYcInventoryListAndSave();
    }

    @XxlJob("changeWarehouseName")
    @Transactional(rollbackFor = Exception.class)
    public void changeWarehouseName() {
        List<InventorySaleRulesDO> inventorySaleRulesList = inventorySaleRulesRepository.getAllInventorySaleRulesList();
        for (var rules : inventorySaleRulesList) {
            String saleDestination = rules.getSaleDestination();
            // 使用正则表达式替换键名
            saleDestination = saleDestination.replaceAll("美西仓", "1");
            saleDestination = saleDestination.replaceAll("美南仓", "2");
            saleDestination = saleDestination.replaceAll("美东仓", "3");
            saleDestination = saleDestination.replaceAll("FBA仓", "4");
            saleDestination = saleDestination.replaceAll("CG仓", "5");
            saleDestination = saleDestination.replaceAll("美中仓", "6");
            saleDestination = saleDestination.replaceAll("美东南仓", "7");
            rules.setSaleDestination(saleDestination);
        }
        inventorySaleRulesRepository.saveOrUpdateBatch(inventorySaleRulesList);
    }
}
