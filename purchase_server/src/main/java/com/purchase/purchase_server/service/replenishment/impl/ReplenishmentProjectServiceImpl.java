package com.purchase.purchase_server.service.replenishment.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.dp.OperationLogListForm;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.utils.SnowflakeIdWorker;
import com.purchase.purchase_server.entity.BaseEntity;
import com.purchase.purchase_server.entity.bo.RepVirtualSkuDateBO;
import com.purchase.purchase_server.entity.dataObject.*;
import com.purchase.purchase_server.entity.dto.*;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectBaseParamDto;
import com.purchase.purchase_server.entity.dto.delivery.TrialCalReplenishmentDto;
import com.purchase.purchase_server.entity.dto.replenishment.EntityRateDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentProjectSaveDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentRecordDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentStatusReasonDto;
import com.purchase.purchase_server.entity.excelObject.RepDeliveryPurchaseInfoExcel;
import com.purchase.purchase_server.entity.excelObject.RepStockQuantityInfoExcel;
import com.purchase.purchase_server.entity.form.FileMissionForm;
import com.purchase.purchase_server.entity.form.QuerySnapDp;
import com.purchase.purchase_server.entity.form.ReplenishmentDetailForm;
import com.purchase.purchase_server.entity.form.ReplenishmentProjectForm;
import com.purchase.purchase_server.entity.form.interior.SoldOutDateBeforeTheoryDto;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.*;
import com.purchase.purchase_server.enums.*;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.model.purchase.ReplenishDeliveryRangeAndNumDp;
import com.purchase.purchase_server.repository.dataRepository.*;
import com.purchase.purchase_server.repository.interiorRepository.WarehouseRepository;
import com.purchase.purchase_server.repository.redis.IChannelRedisRepository;
import com.purchase.purchase_server.service.FileCenterService;
import com.purchase.purchase_server.service.IProductSnapshotService;
import com.purchase.purchase_server.service.channel.IChannelInfoService;
import com.purchase.purchase_server.service.purchaseOrder.IPurchaseOrdersService;
import com.purchase.purchase_server.service.replenishment.*;
import com.purchase.purchase_server.service.shipping.IShippingSaveCalResultService;
import com.purchase.purchase_server.service.targetSales.ITargetSalesService;
import com.purchase.purchase_server.utils.easyExcelUtil.listener.Replenishment.*;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentManager;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentMapManager;
import com.purchase.purchase_server.utils.easyExcelUtil.template.Replenishment.ExcelTemplateGenerator;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.date.DateField.DAY_OF_YEAR;
import static com.crafts_mirror.utils.constant.Constants.CHANNEL_MAPPING;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.IMPORT_REPLENISHMENT_LOCK;
import static com.crafts_mirror.utils.constant.SystemConstant.*;
import static com.purchase.purchase_server.entity.consts.FilePathConstant.FILE_PATH_PURCHASE_REPLENISHMENT;
import static com.purchase.purchase_server.enums.ReplenishmentStatusReasonEnum.*;
import static com.purchase.purchase_server.enums.ShippingSourceTypeEnum.DISCOUNT_REPLENISH;
import static com.purchase.purchase_server.enums.ShippingSourceTypeEnum.REPLENISH;
import static java.math.RoundingMode.HALF_UP;


/**
 * <AUTHOR>
 * @description 针对表【cm_replenishment_project】的数据库操作Service实现
 * @createDate 2024-01-17 10:45:51
 */
@Service
@Slf4j
public class ReplenishmentProjectServiceImpl implements IReplenishmentProjectService {
    @Resource
    protected RestTemplate restTemplate;
    @Resource
    private ReplenishmentProjectRepositoryImpl replenishmentProjectRepository;

    @Resource
    private MockInventoryRepositoryImpl mockInventoryRepository;

    @Resource
    private ReplenishmentVirtualSkuPurchaseRepositoryImpl replenishmentVirtualSkuPurchaseRepository;

    @Resource
    private ProductSnapRepositoryImpl productSnapRepository;

    @Resource
    private ReplenishmentTrialPurchaseInventoryRepositoryImpl replenishmentTrialPurchaseInventoryRepository;

    @Resource
    private IShippingSaveCalResultService shippingSaveCalResultService;

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepository;

    @Resource
    @Lazy
    private IReplenishmentCalculationService replenishmentCalculationService;

    @Resource
    private OperationLogRepository operationLogRepository;

    @Resource
    private IProductSnapshotService productSnapshotService;

    @Resource
    private ReplenishmentRecordRepositoryImpl replenishmentRecordRepository;

    @Resource
    private FileCenterService fileCenterService;

    @Resource
    private SnowflakeIdWorker snowflakeIdWorker;

    @Resource
    private WarehouseRepository warehouseRepository;

    @Resource
    private ISysUserInteriorService sysUserInteriorService;

    @Resource
    private ReplenishmentForeignInventoryRepositoryImpl replenishmentForeignInventoryRepository;

    private static final BigDecimal achievementRate = new BigDecimal("0.7");

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource(name = "defaultThreadPool")
    private Executor importExcelThreadPool;

    @Resource
    private IChannelRedisRepository channelRedisRepository;

    @Resource
    private ISoldOutDaysService soldOutDaysService;

    @Resource
    private IReplenishmentTrialPurchaseInventoryService replenishmentTrialPurchaseInventoryService;

    @Resource
    private FactoryFinishedInventoryRepositoryImpl factoryFinishedInventoryRepository;

    @Resource
    private TrialShippingInventoryRepositoryImpl trialShippingInventoryRepository;

    @Resource
    private TrialInventorySaleDestinationRepositoryImpl shippingSaleDestinationRepository;

    @Resource
    private IPurchaseOrdersService purchaseOrdersService;

    @Resource
    private ITargetSalesService targetSalesService;

    @Resource
    private ShippingRecordRepositoryImpl shippingRecordRepository;

    @Resource
    private IChannelInfoService channelInfoService;

    @Resource
    private EntityRateDto entityRateDto;

    @Override
    @Async
    public void importExcelToDeliveryPurchase(String advicePurchaseStartDate, String advicePurchaseEndDate,
                                              InputStream file, String fileName, byte[] byteArrayResource) {
        ReplenishmentManager replenishmentManager = ReplenishmentManager.getInstance();
        ReplenishmentMapManager replenishmentMapManager = ReplenishmentMapManager.getInstance();

        // 查询所有的虚拟商品
        List<VirtualProductDO> allVirtualProductList = virtualProductRepository.getAllVirtualProductList();
        Map<String, VirtualProductDO> skuVirtualProductMap = allVirtualProductList.stream().collect(Collectors.toMap(VirtualProductDO::getVirtualSku, m -> m));
        replenishmentMapManager.setSkuVirtualProductMap(skuVirtualProductMap);
        Map<String, VirtualProductDO> oldSkuVirtualProductMap = allVirtualProductList.stream()
                .filter(f -> StrUtil.isNotBlank(f.getOldSku()))
                .collect(Collectors.toMap(VirtualProductDO::getOldSku, m -> m));
        replenishmentMapManager.setOldSkuVirtualProductMap(oldSkuVirtualProductMap);

        // 保存产品导入进度
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ReplenishmentRecordDO repRecordDO = ReplenishmentRecordDO.builder()
                .replenishmentStatus(ReplenishmentStatusEnum.CALCULATING.getCode())
                .dataSourceType(DataSourceTypeEnum.IMPORT.getCode())
                .advicePurchaseStartDate(DateUtil.parseDate(advicePurchaseStartDate))
                .advicePurchaseEndDate(DateUtil.parseDate(advicePurchaseEndDate))
                .build();
        // 只有在导入第一行时插入正在导入的上传状态数据
        if (!StrUtil.isNotBlank(replenishmentManager.getRecordId())) {
            //上传oss
            String snowId = String.valueOf(snowflakeIdWorker.nextId());
            String key = snowId + "." + Objects.requireNonNull(fileName).substring(fileName.lastIndexOf(".") + 1);
            MultiValueMap<String, Object> httpEntity = fileCenterService.putFile(byteArrayResource, fileName, FILE_PATH_PURCHASE_REPLENISHMENT + key, DateUtil.offsetDay(new Date(), 730).toString());
            ResultDTO<String> resultDTO = restTemplateUtil.post(httpEntity, ResultDTO.class, FILE_SYSTEM_PUT_OBJECT_BY_FILE);
            if (!Objects.equals(resultDTO.getStatus(), ResponseCodeEnum.OK.getCode())) {
                log.error("导入发货计划时上传oss失败，异常原因：{}", resultDTO.getMessage());
                throw new RuntimeException("导入发货计划时上传oss失败，异常原因：" + resultDTO.getMessage());
            }
            ResultDTO<String> restResult = restTemplateUtil.post(
                    FileMissionForm.builder().fileName(fileName)
                            .importStatus("导入分析中").type("补货计划导入")
                            .filePath(key)
                            .build(),
                    ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL
            );
            if (!Objects.equals(restResult.getStatus(), ResponseCodeEnum.OK.getCode())) {
                log.error("导入补货计划时插入文件中心失败，异常原因：{}", restResult.getMessage());
                throw new RuntimeException("导入补货计划时插入文件中心失败，异常原因：" + restResult.getMessage());
            } else {
                repRecordDO.setDataSourceId(restResult.getData());
                replenishmentRecordRepository.save(repRecordDO);
                replenishmentManager.setRecordId(repRecordDO.getId());
                replenishmentManager.setCreateTime(DateUtil.beginOfDay(DateTime.now()));
            }
        }

        // 从数据库中获取现有的仓库列表
        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> channelMapping = channelRedisRepository.getChannelMapping();
        Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();
        if (CollectionUtil.isEmpty(channelMapping)) {
            channelMapping = CHANNEL_MAPPING;
        }
        ExcelReader excelReader = null;
        try {
            ReadSheet readSheet1 = EasyExcel.readSheet(0).headRowNumber(1)
                    .registerReadListener(new RepStockingRulesImportListener(senboWarehouseList)).autoTrim(true).build();
            ReadSheet readSheet2 = EasyExcel.readSheet(1).headRowNumber(1)
                    .registerReadListener(new RepTargetSalesImportListener(senboWarehouseList)).autoTrim(true).build();
            ReadSheet readSheet3 = EasyExcel.readSheet(2).headRowNumber(1)
                    .registerReadListener(new AMRepStockQuantityImportListener(senboWarehouseList, channelMapping, channelIdNameMap)).autoTrim(true).build();
            ReadSheet readSheet4 = EasyExcel.readSheet(3).headRowNumber(1)
                    .registerReadListener(new AMRepStockQuantityImportListener(senboWarehouseList, channelMapping, channelIdNameMap)).autoTrim(true).build();
            ReadSheet readSheet5 = EasyExcel.readSheet(4).head(RepStockQuantityInfoExcel.class)
                    .registerReadListener(new RepStockQuantityImportListener(senboWarehouseList)).autoTrim(true).build();
            ReadSheet readSheet6 = EasyExcel.readSheet(5).head(RepDeliveryPurchaseInfoExcel.class)
                    .registerReadListener(new RepDeliveryImportListener(senboWarehouseList)).autoTrim(true).build();
            excelReader = EasyExcel.read(file).build();
            List<ReadSheet> readSheets = excelReader.excelExecutor().sheetList();
            if (excelReader.excelExecutor().sheetList().size() != 6) {
                throw new RuntimeException("sheet页数量不正确");
            } else if (!RepImportSheetNameEnum.STOCKING_RULES.getCode().equals(readSheets.get(0).getSheetName()) ||
                    !RepImportSheetNameEnum.TARGET_SALES.getCode().equals(readSheets.get(1).getSheetName()) ||
                    !RepImportSheetNameEnum.AM_STOCK_QUANTITY.getCode().equals(readSheets.get(2).getSheetName()) ||
                    !RepImportSheetNameEnum.LAMP_STOCK_QUANTITY.getCode().equals(readSheets.get(3).getSheetName()) ||
                    !RepImportSheetNameEnum.STOCK_QUANTITY.getCode().equals(readSheets.get(4).getSheetName()) ||
                    !RepImportSheetNameEnum.DELIVERY.getCode().equals(readSheets.get(5).getSheetName())) {
                throw new RuntimeException("请检查sheet是否正确");
            }
            excelReader.read(readSheet1, readSheet2, readSheet3, readSheet4, readSheet5, readSheet6);

            // 开始试算
            replenishmentCalculationService.calReplenishment(senboWarehouseList);
            repRecordDO.setReplenishmentStatus(ReplenishmentStatusEnum.UNSAVED.getCode());
            repRecordDO.setTaskResult("导入成功");
            replenishmentRecordRepository.updateById(repRecordDO);

        } catch (Exception e) {
            log.error("导入补货计划异常{}", e.getMessage());
            // 保存补货导入进度
            repRecordDO.setReplenishmentStatus(ReplenishmentStatusEnum.CALCULATION_FAILED.getCode());
            repRecordDO.setFailedResult(String.join(";", replenishmentManager.getErrorList().isEmpty() ? Collections.singletonList(e.getMessage()) : replenishmentManager.getErrorList()));
            repRecordDO.setTaskResult("导入失败");
            replenishmentRecordRepository.updateById(repRecordDO);
        } finally {
            replenishmentManager.resetVariables();
            replenishmentMapManager.resetVariables();
            if (excelReader != null) {
                log.info("导入补货计划关闭");
                excelReader.finish();
            }
            stringRedisTemplate.delete(IMPORT_REPLENISHMENT_LOCK);
        }
    }

    @Override
    public void exportTemplate(HttpServletResponse response) {
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("补货计划模板", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();

            List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
            ExcelTemplateGenerator excelTemplateGenerator = new ExcelTemplateGenerator();
            excelTemplateGenerator.generateTemplate(excelWriter, senboWarehouseList);

        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    @Override
    public ReplenishmentRecordDO getRecordCalculating() {
        return replenishmentRecordRepository.getOne(Wrappers.<ReplenishmentRecordDO>lambdaQuery().eq(ReplenishmentRecordDO::getReplenishmentStatus, ReplenishmentStatusEnum.CALCULATING.getCode()));
    }

    @Override
    public IPage<ReplenishmentRecordPurchaseListVo> recordPageList(ReplenishmentProjectForm form) {
        List<String> recordIds;
        if (StrUtil.isNotBlank(form.getProductName()) || CollectionUtil.isNotEmpty(form.getSelfSkuList()) ||
                CollectionUtil.isNotEmpty(form.getVirtualSkuList()) || CollectionUtil.isNotEmpty(form.getOldSkuList())) {
            List<String> snapIds = productSnapRepository.selectSnapIds(new QuerySnapDp(form.getSelfSkuList(), form.getVirtualSkuList(),
                    form.getProductName(), form.getOldSkuList(), new ArrayList<>(), new ArrayList<>(), null, null));
            if (CollectionUtil.isEmpty(snapIds)) {
                return new Page<>(form.getCurrent(), form.getSize(), 0);
            }
            List<String> projectIds = replenishmentVirtualSkuPurchaseRepository.selectProjectIdBySnap(snapIds);
            List<ReplenishmentProjectDO> replenishmentProjectDOS = replenishmentProjectRepository.listByIds(projectIds);
            if (CollectionUtil.isEmpty(replenishmentProjectDOS)) {
                return new Page<>(form.getCurrent(), form.getSize(), 0);
            }
            form.setRecordIds(replenishmentProjectDOS.stream().map(ReplenishmentProjectDO::getReplenishmentRecordId).collect(Collectors.toList()));
        }

        IPage<ReplenishmentRecordPurchaseListVo> page = replenishmentRecordRepository.getRecordPageList(form);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return new Page<>(form.getCurrent(), form.getSize(), page.getTotal());
        }

        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));
        for (ReplenishmentRecordPurchaseListVo s : page.getRecords()) {
            String createBy = collect.get(s.getCreateBy());
            if (StrUtil.isBlank(createBy)) {
                createBy = s.getCreateBy();
            }
            s.setCreateBy(createBy);
        }
        return page;
    }

    @Override
    public ReplenishmentRecordStatusVo pageList(ReplenishmentProjectForm form) {
        // 默认时间
        ReplenishmentRecordDO recordByIdDO = replenishmentRecordRepository.getById(form.getId());
        if (ObjectUtil.isEmpty(recordByIdDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "补货计划不存在");
        }
        String advicePurchaseStartDate = form.getAdvicePurchaseStartDate();
        String advicePurchaseEndDate = form.getAdvicePurchaseEndDate();
        String defaultAdvicePurchaseStartDate = DateUtil.format(recordByIdDO.getAdvicePurchaseStartDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN);
        String defaultAdvicePurchaseEndDate = DateUtil.format(recordByIdDO.getAdvicePurchaseEndDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN);

        if (StrUtil.isBlank(advicePurchaseStartDate) || StrUtil.isBlank(advicePurchaseEndDate)) {
            advicePurchaseStartDate = defaultAdvicePurchaseStartDate;
            form.setAdvicePurchaseStartDate(advicePurchaseStartDate);
            advicePurchaseEndDate = defaultAdvicePurchaseEndDate;
            form.setAdvicePurchaseEndDate(advicePurchaseEndDate);
        } else {
            Date startDate = DateUtil.parse(form.getAdvicePurchaseStartDate());
            advicePurchaseStartDate = DateUtil.format(startDate, YYYY_MM_DD_DATE_FORMAT_HYPHEN);
            Date endDate = DateUtil.parse(form.getAdvicePurchaseEndDate());
            advicePurchaseEndDate = DateUtil.format(endDate, YYYY_MM_DD_DATE_FORMAT_HYPHEN);
        }
        CompletableFuture<IPage<RepVirtualSkuDateBO>> replenishmentQuantityFuture = CompletableFuture.supplyAsync(() -> replenishmentProjectRepository.pageList(form), importExcelThreadPool);

        // 需补货-无数据权限
        var restockQuantityNoDataPermissionFuture = CompletableFuture.supplyAsync(() -> {
            ReplenishmentProjectForm replenishment = ReplenishmentProjectForm.builder()
                    .id(form.getId())
                    .advicePurchaseStartDate(form.getAdvicePurchaseStartDate())
                    .advicePurchaseEndDate(form.getAdvicePurchaseEndDate())
                    .isNotReplenishment(ViReplenishmentStatusEnum.RESTOCK_REQUIRED.getCode())
                    .build();
            return replenishmentProjectRepository.selectCountList(replenishment);

        }, importExcelThreadPool);

        // 无需-无数据权限
        var noReplenishmentQuantityNoDataPermissionFuture = CompletableFuture.supplyAsync(() -> {
            ReplenishmentProjectForm replenishment = ReplenishmentProjectForm.builder()
                    .id(form.getId())
                    .advicePurchaseStartDate(form.getAdvicePurchaseStartDate())
                    .advicePurchaseEndDate(form.getAdvicePurchaseEndDate())
                    .isNotReplenishment(ViReplenishmentStatusEnum.NOT_RESTOCK_REQUIRED.getCode())
                    .build();
            return replenishmentProjectRepository.selectCountList(replenishment);
        }, importExcelThreadPool);

        String finalAdvicePurchaseStartDate = advicePurchaseStartDate;
        String finalAdvicePurchaseEndDate = advicePurchaseEndDate;

        ReplenishmentRecordStatusVo replenishmentRecordStatusVo;
        try {
            replenishmentRecordStatusVo = CompletableFuture.allOf(replenishmentQuantityFuture, restockQuantityNoDataPermissionFuture, noReplenishmentQuantityNoDataPermissionFuture)
                    .thenApply(v -> {
                        IPage<RepVirtualSkuDateBO> replenishmentQuantity = replenishmentQuantityFuture.join();

                        List<RepVirtualSkuDateBO> restockQuantityNoDataPermission = restockQuantityNoDataPermissionFuture.join();
                        List<RepVirtualSkuDateBO> noReplenishmentQuantityNoDataPermission = noReplenishmentQuantityNoDataPermissionFuture.join();
                        List<RepVirtualSkuDateBO> resultList = replenishmentQuantity.getRecords();
                        IPage<ReplenishmentVirtualSkuListVo> pageVO = new Page<>(form.getCurrent(), form.getSize());
                        pageVO.setTotal(replenishmentQuantity.getTotal());
                        if (CollectionUtil.isEmpty(resultList)) {
                            return ReplenishmentRecordStatusVo.builder()
                                    .advicePurchaseStartDate(finalAdvicePurchaseStartDate)
                                    .advicePurchaseEndDate(finalAdvicePurchaseEndDate)
                                    .defaultAdvicePurchaseStartDate(defaultAdvicePurchaseStartDate)
                                    .defaultAdvicePurchaseEndDate(defaultAdvicePurchaseEndDate)
                                    .replenishmentStatus(recordByIdDO.getReplenishmentStatus())
                                    .replenishmentVirtualSkuList(pageVO)
                                    .replenishmentQuantity(Math.toIntExact(restockQuantityNoDataPermission.size()))
                                    .noReplenishmentQuantity(Math.toIntExact(noReplenishmentQuantityNoDataPermission.size()))
                                    .build();
                        }
                        //运营
                        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
                        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

                        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
                        Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

                        Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();

                        List<String> purchaseIdList = resultList.stream().map(RepVirtualSkuDateBO::getId).toList();
                        var foreignInventoryMap = replenishmentForeignInventoryRepository.getMapByReplenishmentVirtualPurchaseIdList(purchaseIdList);
                        for (RepVirtualSkuDateBO bo : resultList) {
                            // 获取海外仓库存和海外仓在途
                            List<ReplenishmentForeignInventoryDO> foreignInventoryList = foreignInventoryMap.getOrDefault(bo.getId(), new ArrayList<>());
                            Map<String, Integer> overseasInventory = new HashMap<>(foreignInventoryList.size());
                            Map<String, Integer> overseasShipping = new HashMap<>(foreignInventoryList.size());
                            overseasShipping.putIfAbsent("合计", 0);
                            overseasInventory.putIfAbsent("合计", 0);

                            for (var foreignInventory : foreignInventoryList) {
                                Date enableUsingDate = foreignInventory.getEnableUsingDate();
                                String warehouse = idNameMap.get(foreignInventory.getWarehouseId());
                                Map<String, Integer> targetMap = enableUsingDate.after(recordByIdDO.getCreateDate()) ? overseasShipping : overseasInventory;

                                targetMap.merge(warehouse, foreignInventory.getStoreNum(), Integer::sum);
                                targetMap.merge("合计", foreignInventory.getStoreNum(), Integer::sum);
                            }

                            bo.setOverseasInventory(JSON.toJSONString(overseasInventory));
                            bo.setOverseasShipping(JSON.toJSONString(overseasShipping));

                            String operator = bo.getOperator();
                            String operatorNames = "";
                            if (StrUtil.isNotBlank(operator)) {
                                // 使用String.split()方法的新重载,直接返回Stream
                                operatorNames = Arrays.stream(operator.split(",", 0))
                                        .map(op -> StrUtil.isNotBlank(collect.get(op)) ? collect.get(op) : op)
                                        .collect(Collectors.joining(","));
                            }
                            bo.setOperator(operatorNames);
                            String buyer = bo.getBuyer();
                            if (StrUtil.isNotBlank(buyer)) {
                                bo.setBuyer(collect.getOrDefault(buyer, buyer));
                            }
                            String purchaser = bo.getPurchaser();
                            if (StrUtil.isNotBlank(purchaser)) {
                                bo.setPurchaser(collect.getOrDefault(purchaser, purchaser));
                            }
                            if (ViReplenishmentStatusEnum.NOT_RESTOCK_REQUIRED.getCode().equals(form.getIsNotReplenishment())) {
                                bo.setAdvicePurchaseNum(0);
                            }

                            if (StrUtil.isNotBlank(bo.getReason()) && ViReplenishmentStatusEnum.NOT_RESTOCK_REQUIRED.getCode().equals(form.getIsNotReplenishment())) {
                                bo.setReason("该时间段内无需补货");
                            }

                            bo.setChannel(channelIdNameMap.getOrDefault(bo.getChannel(), bo.getChannel()));
                        }
                        List<ReplenishmentVirtualSkuListVo> list = ReplenishmentVirtualSkuListVo.convertRepVirtualSkuList(resultList);
                        pageVO.setRecords(list);

                        return ReplenishmentRecordStatusVo.builder()
                                .advicePurchaseStartDate(finalAdvicePurchaseStartDate)
                                .advicePurchaseEndDate(finalAdvicePurchaseEndDate)
                                .defaultAdvicePurchaseStartDate(defaultAdvicePurchaseStartDate)
                                .defaultAdvicePurchaseEndDate(defaultAdvicePurchaseEndDate)
                                .replenishmentStatus(recordByIdDO.getReplenishmentStatus())
                                .replenishmentVirtualSkuList(pageVO)
                                .replenishmentQuantity(Math.toIntExact(restockQuantityNoDataPermission.size()))
                                .noReplenishmentQuantity(Math.toIntExact(noReplenishmentQuantityNoDataPermission.size()))
                                .build();
                    }).get(5, TimeUnit.MINUTES);

        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), e.getMessage());
        }
        return replenishmentRecordStatusVo;
    }

    @Override
    public MissionCenterVo selectFileInfo(String recordId) {
        FileMissionCenterDO fileMissionCenterDO = replenishmentRecordRepository.selectFailResultList(recordId);
        List<String> failResultList = null;
        if (StrUtil.isNotBlank(fileMissionCenterDO.getFailedResult())) {
            failResultList = Arrays.asList(fileMissionCenterDO.getFailedResult().split(";"));
        }
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        String format = String.format(FILE_SYSTEM_MISSION_CENTER_MISSION_GET_URL + "?url=%s", FILE_PATH_PURCHASE_REPLENISHMENT + fileMissionCenterDO.getFilePath());
        ResultDTO<String> resultDTO = restTemplateUtils.get(format, ResultDTO.class);
        return MissionCenterVo.builder()
                .missionId(fileMissionCenterDO.getId())
                .fileName(fileMissionCenterDO.getFileName())
                .filePath(Optional.ofNullable(resultDTO).orElse(ResultDTO.success("")).getData())
                .createDate(fileMissionCenterDO.getCreateDate())
                .finishDate(fileMissionCenterDO.getFinishDate())
                .missionType(fileMissionCenterDO.getType())
                .missionStatus(fileMissionCenterDO.getImportStatus())
                .missionResult(fileMissionCenterDO.getImportResult())
                .failedResultList(failResultList)
                .build();
    }

    @Override
    public void saveReplenishmentProjectInfo(ReplenishmentProjectSaveDto projectSaveDto, VirtualProductDO virtualProductDO,
                                             AchievementRateDto achievementRateDto) {
        String virtualSku = projectSaveDto.getVirtualSku();
        ReplenishmentMapManager instance = ReplenishmentMapManager.getInstance();
        // 获取本地仓库情况
        var factoryPlanList = instance.getFactoryPlanMap().getOrDefault(virtualSku, new ArrayList<>());
        Map<String, Double> localWarehouseMap = factoryPlanList.stream()
                .collect(Collectors.toMap(d -> DateTime.of(d.getFactoryFinishedDate()).compareTo(new Date()) <= 0 ? "已生产好" : "生产中",
                        v -> Optional.ofNullable(v.getShippingNum()).orElse(0D), Double::sum, TreeMap::new));
        localWarehouseMap.putIfAbsent("已生产好", 0D);
        localWarehouseMap.putIfAbsent("生产中", 0D);
        // 保存补货计划
        List<FactoryRemainInventoryDto> remainInventoryList = instance.getStockQuantityMap().getOrDefault(virtualSku, new ArrayList<>());

        // 获取海外仓库存
        Map<String, Double> remainInventoryMap = remainInventoryList.stream()
                .filter(dto -> DateTime.of(dto.getEnableUsingDate()).compareTo(new Date()) <= 0)
                .collect(Collectors.toMap(FactoryRemainInventoryDto::getWarehouse,
                        FactoryRemainInventoryDto::getStoreNum,
                        Double::sum));
        double remainSum = remainInventoryMap.values().stream().mapToDouble(r -> r).sum();
        remainInventoryMap.put("合计", remainSum);
        // 获取海外仓在途
        Map<String, Double> onShippingMap = remainInventoryList.stream()
                .filter(dto -> DateTime.of(dto.getEnableUsingDate()).compareTo(new Date()) > 0)
                .collect(Collectors.toMap(FactoryRemainInventoryDto::getWarehouse,
                        FactoryRemainInventoryDto::getStoreNum,
                        Double::sum));
        double shippingSum = onShippingMap.values().stream().mapToDouble(r -> r).sum();
        onShippingMap.put("合计", shippingSum);

        // 获取建议采购量
        var everydayReplenishmentMap = projectSaveDto.getEverydayReplenishmentMap();

        // 获取补货原因
        ReplenishmentStatusReasonDto replenishmentStatusReason = getReplenishmentStatusReason(achievementRateDto, virtualProductDO,
                everydayReplenishmentMap, projectSaveDto.getShippingRules(), projectSaveDto.getIsTest());

        int totalAdvicePurchaseNum = 0;
        if (CollectionUtil.isNotEmpty(everydayReplenishmentMap)) {
            totalAdvicePurchaseNum = everydayReplenishmentMap.values().stream()
                    .mapToInt(reV -> reV.values().stream().mapToInt(m -> m.getNum().intValue()).sum())
                    .sum();
        }

        //子父体达成率 achievementRateMap
        BigDecimal subEntityRate = achievementRateDto.getSubEntityRate();
        BigDecimal parentEntityRate = achievementRateDto.getParentEntityRate();

        // 保存补货计划
        String projectId = saveReplenishmentProject(projectSaveDto.getReplenishmentTrial());

        // 保存售罄时间
        soldOutDaysService.saveSoldOutDays(projectSaveDto, projectId);

        // 保存快照
        String virtualId = virtualProductDO.getId();
        String productSnapshotId = productSnapshotService.selecetAndSaveSnapList(virtualId, "徐波");

        // 保存补货计划跟虚拟sku有关的部分，默认展示8个月的日销，前两个月+后6个月
        LocalDate localDate = LocalDate.now().minusMonths(2).withDayOfMonth(1);
        LocalDate endDate = LocalDate.now().plusMonths(7).withDayOfMonth(1);
        Map<String, Double> targetSalesMap = targetSalesService.getTargetSales(virtualId, localDate, endDate);

        //实际日销，当月目标日销
        ActualDailySalesDto actualDailySalesDto = instance.getActualDailySalesMap().getOrDefault(virtualSku, new ActualDailySalesDto());

        Map<String, Double> updatedTargetSalesMap = new HashMap<>();

        if (projectSaveDto.isNeedMultiply()) {
            for (Map.Entry<String, Double> entry : targetSalesMap.entrySet()) {
                updatedTargetSalesMap.put(entry.getKey(), BigDecimal.valueOf(entry.getValue()).multiply(subEntityRate).setScale(3, HALF_UP).doubleValue());
            }
        } else {
            updatedTargetSalesMap = targetSalesMap;
        }

        ReplenishmentVirtualSkuPurchaseDO virtualSkuPurchaseDO = ReplenishmentVirtualSkuPurchaseDO.builder()
                .virtualSkuId(virtualId)
                .replenishmentProjectId(projectId)
                .replenishmentStatus(totalAdvicePurchaseNum == 0 ? "0" : "1")
                .reason(replenishmentStatusReason.getReason())
                .saleDestination(JSON.toJSONString(targetSalesMap))
                .discountSaleDestination(JSON.toJSONString(updatedTargetSalesMap))
                .operatorSaleDestination(JSON.toJSONString(targetSalesMap))
                .rulesId(projectSaveDto.getShippingRules().getRuleId())
                .localInventory(JSON.toJSONString(localWarehouseMap))
                .productSnapshotId(productSnapshotId)
                .actualDailySalesNum(actualDailySalesDto.getActualDailySalesNum())
                .targetSalesNum(actualDailySalesDto.getTargetSalesNum())
                .subEntityRate(subEntityRate.doubleValue())
                .parentEntityRate(parentEntityRate.doubleValue())
                .build();
        replenishmentVirtualSkuPurchaseRepository.save(virtualSkuPurchaseDO);

        // 海外仓库存原始数据
        String skuPurchaseDOId = virtualSkuPurchaseDO.getId();
        replenishmentForeignInventoryRepository.batchSaveDeliveryForeignInventory(skuPurchaseDOId, remainInventoryList);

        // 保存操作日志
        OperationLogDO operationLogDO = OperationLogDO.builder()
                .operator(SecurityUtils.getUsername())
                .content("生成")
                .operationType("补货计划操作")
                .trackNumber(skuPurchaseDOId)
                .build();
        operationLogRepository.save(operationLogDO);

        // 保存试算看板补货数据
        ShippingProjectBaseParamDto projectRules = projectSaveDto.getShippingRules();
        int containerLoad = projectSaveDto.getSelfProductInfo().getContainerLoad();

        replenishmentTrialPurchaseInventoryService.saveTrialPurchaseInventoryInfo(projectSaveDto, virtualProductDO, skuPurchaseDOId);

        // 保存发货试算数据以及试算模拟表格
        TrialCalReplenishmentDto shippingTrial = projectSaveDto.getShippingTrial();
        List<FactoryRemainInventoryDto> shippingInventoryList = shippingTrial.getShippingInventoryList();
        var factoryFinishedInventoryList = shippingSaveCalResultService.insertFactoryFinishedInventory(shippingInventoryList, skuPurchaseDOId,
                projectSaveDto.getVirtualSkuAndOldSkuMap(), factoryPlanList, virtualProductDO, REPLENISH.getCode());

        TrialCalReplenishmentDto discountShippingTrial = projectSaveDto.getDiscountShippingTrial();
        List<FactoryRemainInventoryDto> discountShippingInventoryList = discountShippingTrial.getShippingInventoryList();

        List<FactoryFinishedInventoryDO> discountFactoryFinishedInventoryList = shippingSaveCalResultService.insertFactoryFinishedInventory(
                discountShippingInventoryList, skuPurchaseDOId, projectSaveDto.getVirtualSkuAndOldSkuMap(), factoryPlanList,
                virtualProductDO, DISCOUNT_REPLENISH.getCode());

        // 保存发到仓库的发货数量信息
        Map<String, Integer> headShippingDateMap = projectRules.getHeadShippingDays();
        shippingSaveCalResultService.insertWarehouseInventoryInfo(shippingInventoryList, containerLoad, headShippingDateMap,
                factoryFinishedInventoryList, shippingTrial.getPriorDeliveryList());

        shippingSaveCalResultService.insertWarehouseInventoryInfo(discountShippingInventoryList, containerLoad,
                    headShippingDateMap, discountFactoryFinishedInventoryList, discountShippingTrial.getPriorDeliveryList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateReCalReplenishmentProjectInfo(ReplenishmentProjectSaveDto projectSaveDto, VirtualProductDO virtualProduct,
                                                    AchievementRateDto achievementRateDto, ReplenishmentVirtualSkuPurchaseDO virRepDO) {
        List<String> projectIdList = Collections.singletonList(virRepDO.getReplenishmentProjectId());

        // 更新全链路售罄时间、全链路断货天数
        updateReplenishmentProjectInfo(projectSaveDto.getReplenishmentTrial(), virRepDO.getReplenishmentProjectId());

        // 删除并重新插入售罄时间
        soldOutDaysService.deleteByReplenishmentProjectId(projectIdList);
        soldOutDaysService.saveSoldOutDays(projectSaveDto, virRepDO.getReplenishmentProjectId());

        // 获取建议采购量
        var everydayReplenishmentMap = projectSaveDto.getEverydayReplenishmentMap();

        // 获取补货原因
        ReplenishmentStatusReasonDto replenishmentStatusReason = getReplenishmentStatusReason(achievementRateDto, virtualProduct,
                everydayReplenishmentMap, projectSaveDto.getShippingRules(), projectSaveDto.getIsTest());

        int totalAdvicePurchaseNum = 0;
        if (CollectionUtil.isNotEmpty(everydayReplenishmentMap)) {
            totalAdvicePurchaseNum = everydayReplenishmentMap.values().stream()
                    .mapToInt(reV -> reV.values().stream().mapToInt(m -> m.getNum().intValue()).sum())
                    .sum();
        }

        // 更新cm_replenishment_virtual_sku_purchase
        ReplenishmentVirtualSkuPurchaseDO virtualSkuPurchaseDO = ReplenishmentVirtualSkuPurchaseDO.builder()
                .id(virRepDO.getId())
                .replenishmentStatus(totalAdvicePurchaseNum == 0 ? "0" : "1")
                .reason(replenishmentStatusReason.getReason())
                .saleDestination(JSON.toJSONString(projectSaveDto.getTargetSalesMap()))
                .discountSaleDestination(JSON.toJSONString(projectSaveDto.getUpdatedTargetSalesMap()))
                .operatorSaleDestination(JSON.toJSONString(projectSaveDto.getTargetSalesMap()))
                .soldOutDate("")
                .expectedSoldOutDate(null)
                .confirmedReason("")
                .operatorRemark("")
                .build();
        replenishmentVirtualSkuPurchaseRepository.updateById(virtualSkuPurchaseDO);

        String skuPurchaseId = virtualSkuPurchaseDO.getId();
        // 删除后重新插入cm_replenishment_trial_purchase_inventory
        replenishmentTrialPurchaseInventoryRepository.deleteByVirtualSkuPurchaseIds(Collections.singletonList(skuPurchaseId));
        replenishmentTrialPurchaseInventoryService.saveTrialPurchaseInventoryInfo(projectSaveDto, virtualProduct, skuPurchaseId);

        // 删除后并重新插入cm_delivery_factory_inventory表
        List<FactoryFinishedInventoryDO> finishedInfoList = factoryFinishedInventoryRepository.getListByShippingProjectId(projectIdList, REPLENISH.getCode());

        ReplenishmentMapManager instance = ReplenishmentMapManager.getInstance();

        // 删除后保存发到仓库的发货数量信息
        shippingSaveCalResultService.deleteFactoryFinishedByShippingIds(Collections.singletonList(skuPurchaseId), REPLENISH.getCode());
        shippingSaveCalResultService.deleteFactoryFinishedByShippingIds(Collections.singletonList(skuPurchaseId), DISCOUNT_REPLENISH.getCode());

        TrialCalReplenishmentDto shippingTrial = projectSaveDto.getShippingTrial();
        List<FactoryRemainInventoryDto> shippingInventoryList = shippingTrial.getShippingInventoryList();
        var factoryPlanList = instance.getFactoryPlanMap().getOrDefault(virtualProduct.getVirtualSku(), new ArrayList<>());

        List<FactoryFinishedInventoryDO> factoryFinishedInventoryList = shippingSaveCalResultService.insertFactoryFinishedInventory(
                shippingInventoryList, skuPurchaseId, projectSaveDto.getVirtualSkuAndOldSkuMap(), factoryPlanList, virtualProduct, REPLENISH.getCode());

        TrialCalReplenishmentDto discountShippingTrial = projectSaveDto.getDiscountShippingTrial();
        List<FactoryRemainInventoryDto> discountShippingInventoryList = discountShippingTrial.getShippingInventoryList();
        List<FactoryFinishedInventoryDO> discountFactoryFinishedInventoryList = shippingSaveCalResultService.insertFactoryFinishedInventory(discountShippingInventoryList,
                skuPurchaseId, projectSaveDto.getVirtualSkuAndOldSkuMap(), factoryPlanList, virtualProduct, DISCOUNT_REPLENISH.getCode());

        List<String> finishedIdList = finishedInfoList.stream().map(BaseEntity::getId).toList();
        trialShippingInventoryRepository.deleteByFactoryFinishedIds(finishedIdList);

        ShippingProjectBaseParamDto projectRules = projectSaveDto.getShippingRules();
        int containerLoad = projectSaveDto.getSelfProductInfo().getContainerLoad();
        Map<String, Integer> headShippingDateMap = projectRules.getHeadShippingDays();
        shippingSaveCalResultService.insertWarehouseInventoryInfo(shippingInventoryList, containerLoad, headShippingDateMap, factoryFinishedInventoryList, shippingTrial.getPriorDeliveryList());

        shippingSaveCalResultService.insertWarehouseInventoryInfo(discountShippingInventoryList, containerLoad, headShippingDateMap, discountFactoryFinishedInventoryList, discountShippingTrial.getPriorDeliveryList());
    }

    @Override
    public ReplenishmentStatusReasonDto getReplenishmentStatusReason(AchievementRateDto achievementRateDto, VirtualProductDO virtualSkuDO,
                                                                     Map<String, Map<String, ReplenishDeliveryRangeAndNumDp>> everydayReplenishmentMap,
                                                                     ShippingProjectBaseParamDto shippingRules, boolean isTest) {
        if (isTest) {
            // 在既无计划，也无非撤销状态采购单的情况下，新品测款产品才需要补货
            String virtualSkuId = virtualSkuDO.getId();
            List<YicangPurchaseOrdersDO> notAbandonedList = purchaseOrdersService.getNotAbandonedDetailList(virtualSkuId);

            if (CollectionUtil.isNotEmpty(notAbandonedList)) {
                String reason = ReplenishmentStatusReasonEnum.NOT_ABANDONED.getDesc() + ",参考号：" + notAbandonedList.stream()
                        .map(order -> String.valueOf(order.getRefNo()))
                        .collect(Collectors.joining(","));
                return ReplenishmentStatusReasonDto.builder().reasonEnum(ReplenishmentStatusReasonEnum.NOT_ABANDONED).reason(reason).build();
            }

            ReplenishmentMapManager instance = ReplenishmentMapManager.getInstance();
            ReplenishmentManager replenishmentManager = ReplenishmentManager.getInstance();

            DateTime replenishmentStartDate = replenishmentManager.getCreateTime();
            // 计划
            var factoryPlanMap = instance.getFactoryPlanMap();
            var skuTargetSalesMap = instance.getTargetSalesMap();

            String virtualSku = virtualSkuDO.getVirtualSku();
            List<FactoryFinishedInventoryDto> finishedInventoryList = factoryPlanMap.getOrDefault(virtualSku, new ArrayList<>());
            factoryPlanMap.putIfAbsent(virtualSku, finishedInventoryList);
            HashMap<String, Double> targetSalesMap = skuTargetSalesMap.get(virtualSku);
            // 获取计算周期：直到目标日销的最后一天的剩余库存依然在安全库存周围浮动
            DateTime maxSaleDate = targetSalesMap.keySet().stream()
                    .map(DateUtil::parse)
                    .max(Comparator.comparing(k -> k))
                    .orElse(DateUtil.endOfMonth(replenishmentStartDate));
            int transitDays = shippingRules.getTransitDays();
            List<FactoryFinishedInventoryDto> maybeShippingFinishedInventoryList = finishedInventoryList.stream()
                    .filter(m -> maxSaleDate.offsetNew(DAY_OF_YEAR, -transitDays).compareTo(m.getFactoryFinishedDate()) >= 0)
                    .map(ObjectUtil::clone)
                    .toList();
            if (CollectionUtil.isNotEmpty(maybeShippingFinishedInventoryList)) {
                return ReplenishmentStatusReasonDto.builder()
                        .reasonEnum(ReplenishmentStatusReasonEnum.SHIPPING_FINISHED)
                        .reason(ReplenishmentStatusReasonEnum.SHIPPING_FINISHED.getDesc()).build();
            }
            return ReplenishmentStatusReasonDto.builder()
                    .reasonEnum(ReplenishmentStatusReasonEnum.NORMAL)
                    .reason(ReplenishmentStatusReasonEnum.NORMAL.getDesc()).build();
        }
        // 获取建议采购量
        int totalAdvicePurchaseNum = 0;
        if (CollectionUtil.isNotEmpty(everydayReplenishmentMap)) {
            totalAdvicePurchaseNum = everydayReplenishmentMap.values().stream()
                    .mapToInt(reV -> reV.values().stream().mapToInt(m -> m.getNum().intValue()).sum())
                    .sum();
        }

        //子父体达成率 achievementRateMap
        BigDecimal subEntityRate = achievementRateDto.getSubEntityRate();
        BigDecimal parentEntityRate = achievementRateDto.getParentEntityRate();

        // 通过redis设置达成率标准
        BigDecimal subAchievementRate = StrUtil.isBlank(entityRateDto.getSubAchievementRate()) ? achievementRate : new BigDecimal(entityRateDto.getSubAchievementRate());
        BigDecimal parentAchievementRate = StrUtil.isBlank(entityRateDto.getParentAchievementRate()) ? achievementRate : new BigDecimal(entityRateDto.getParentAchievementRate());
        ReplenishmentStatusReasonEnum replenishmentStatusReason;
        Integer productStatus = virtualSkuDO.getProductStatus();
        Integer subType = virtualSkuDO.getSubType();
        if (totalAdvicePurchaseNum == 0) {
            if (VirtualProductStatusEnum.HALT_DISTRIBUTION.getCode().equals(productStatus)) {
                replenishmentStatusReason = STOP_SEND;
            } else if (VirtualProductStatusEnum.SOLD_OUT_STOP_SELLING.getCode().equals(productStatus)) {
                replenishmentStatusReason = STOP_SELL_OUT;
            } else if (VirtualProductStatusEnum.STOP_SELLING.getCode().equals(productStatus)) {
                replenishmentStatusReason = STOP_SELL;
            } else {
                replenishmentStatusReason = NO_NEED_REPLENISHMENT;
            }
        } else {
            if (parentEntityRate.compareTo(parentAchievementRate) >= 0) {
                if (subEntityRate.compareTo(subAchievementRate) >= 0) {
                    replenishmentStatusReason = NORMAL;
                } else {
                    replenishmentStatusReason = PARENT_STANDARD_CHILD_NOT;
                }
            } else {
                if (!VirtualSubTypeEnum.TRAFFIC.getCode().equals(subType)) {
                    replenishmentStatusReason = PARENT_NOT;
                } else if (subEntityRate.compareTo(subAchievementRate) >= 0) {
                    replenishmentStatusReason = PARENT_NOT_CHILD_STANDARD;
                } else {
                    replenishmentStatusReason = PARENT_CHILD_NOT;
                }
            }
        }
        return ReplenishmentStatusReasonDto.builder()
                .reasonEnum(replenishmentStatusReason)
                .reason(replenishmentStatusReason.getDesc()).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteRecord(String recordId) {
        ReplenishmentRecordDO recordDO = replenishmentRecordRepository.getById(recordId);
        if (ObjectUtil.isEmpty(recordDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "删除失败，该计划不存在");
        }
        if (!ReplenishmentStatusEnum.CALCULATION_FAILED.getCode().equals(recordDO.getReplenishmentStatus()) &&
                !ReplenishmentStatusEnum.ABROGATE.getCode().equals(recordDO.getReplenishmentStatus())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "删除失败，该计划不是试算失败或作废状态");
        }
        List<ReplenishmentProjectDO> replenishmentProjectDOS = replenishmentProjectRepository.selectByRecordId(recordId);
        List<String> virtualSkuPurchaseIds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(replenishmentProjectDOS)) {
            List<String> replenishmentProjectIds = replenishmentProjectDOS.stream().map(ReplenishmentProjectDO::getId).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(replenishmentProjectIds)) {
                List<ReplenishmentVirtualSkuPurchaseDO> virtualSkuPurchaseDOList = replenishmentVirtualSkuPurchaseRepository.selectByProjectIds(replenishmentProjectIds);

                virtualSkuPurchaseIds = virtualSkuPurchaseDOList.stream().map(ReplenishmentVirtualSkuPurchaseDO::getId).collect(Collectors.toList());
                mockInventoryRepository.deleteByShippingIds(replenishmentProjectIds);
                if (CollectionUtil.isNotEmpty(virtualSkuPurchaseIds)) {

                    List<FactoryFinishedInventoryDO> factoryFinishedList = shippingSaveCalResultService.selectFactoryFinishedByRepProjectIds(virtualSkuPurchaseIds, REPLENISH.getCode());
                    List<FactoryFinishedInventoryDO> discountFactoryFinishedList = shippingSaveCalResultService.selectFactoryFinishedByRepProjectIds(virtualSkuPurchaseIds, DISCOUNT_REPLENISH.getCode());

                    List<String> factoryFinishedIds = Stream.of(
                                    Optional.ofNullable(factoryFinishedList).orElse(Collections.emptyList()),
                                    Optional.ofNullable(discountFactoryFinishedList).orElse(Collections.emptyList())
                            )
                            .flatMap(List::stream)
                            .map(FactoryFinishedInventoryDO::getId)
                            .toList();

                    shippingSaveCalResultService.deleteTrialByFinishedIds(factoryFinishedIds);

                    shippingSaveCalResultService.deleteFactoryFinishedByShippingIds(virtualSkuPurchaseIds, REPLENISH.getCode());
                    shippingSaveCalResultService.deleteFactoryFinishedByShippingIds(virtualSkuPurchaseIds, DISCOUNT_REPLENISH.getCode());


                    replenishmentTrialPurchaseInventoryRepository.deleteByVirtualSkuPurchaseIds(virtualSkuPurchaseIds);
                    replenishmentVirtualSkuPurchaseRepository.deleteByIds(virtualSkuPurchaseIds);
                    soldOutDaysService.deleteByReplenishmentProjectId(replenishmentProjectIds);
                }
                replenishmentProjectRepository.deleteByIds(replenishmentProjectIds);
            }
        }

        String dataSourceId = recordDO.getDataSourceId();
        replenishmentRecordRepository.deleteById(recordId);

        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        if (CollectionUtil.isNotEmpty(virtualSkuPurchaseIds)) {
            Map<String, String> track = new HashMap<>();

            for (String purchaseId : virtualSkuPurchaseIds) {
                track.put(purchaseId, "生成");
            }
            // 保存操作日志
            OperationLogListForm form = new OperationLogListForm(null, track, "补货计划操作",
                    null, null);
            restTemplateUtils.post(form, ResultDTO.class, OPERATION_LOGS_SYSTEM_DELETE_LOG_URL);
        }
        restTemplateUtils.post(
                FileMissionForm.builder().missionId(dataSourceId)
                        .build(),
                ResultDTO.class, FILE_SYSTEM_DELETE_MISSION_CENTER_URL
        );
        return true;
    }

    @Override
    public Boolean updateAdvicePurchaseDate(ReplenishmentRecordDto val) {
        ReplenishmentRecordDO recordDO = replenishmentRecordRepository.getById(val.getRecordId());
        if (recordDO.getReplenishmentStatus().equals(ReplenishmentStatusEnum.SAVED.getCode())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "补货记录已确认无法修改");
        }
        ReplenishmentRecordDO result = new ReplenishmentRecordDO();
        result.setId(val.getRecordId());
        result.setAdvicePurchaseEndDate(DateUtil.parse(val.getAdvicePurchaseEndDate()));
        return replenishmentRecordRepository.updateById(result);
    }

    private void updateReplenishmentProjectInfo(TrialCalReplenishmentDto replenishmentTrial, String replenishmentProjectId) {
        LocalDate fullLinkTheoreticalSoldOutDate = replenishmentTrial.getFullLinkTheoreticalSoldOutDate();
        List<SoldOutDateBeforeTheoryDto> soldOutDateBeforeTheoryList = replenishmentTrial.getSoldOutDateBeforeTheoryList();
        int sum = 0;
        if (CollectionUtil.isNotEmpty(soldOutDateBeforeTheoryList)) {
            sum = soldOutDateBeforeTheoryList.stream()
                    .mapToInt(dto -> dto.getSoldOutDays() != null ? dto.getSoldOutDays() : 0)
                    .sum();
        }

        ReplenishmentProjectDO replenishmentProject = ReplenishmentProjectDO.builder()
                .id(replenishmentProjectId)
                .fullLinkTheoreticalSoldOutDate(fullLinkTheoreticalSoldOutDate)
                .fullLinkDaysBeforeSoldOut(sum)
                .build();
        replenishmentProjectRepository.updateById(replenishmentProject);
    }

    /**
     * 由于需求更改后，该表已无实际作用，暂留id作为关联关系表
     */
    private String saveReplenishmentProject(TrialCalReplenishmentDto replenishmentTrial) {
        LocalDate fullLinkTheoreticalSoldOutDate = replenishmentTrial.getFullLinkTheoreticalSoldOutDate();
        List<SoldOutDateBeforeTheoryDto> soldOutDateBeforeTheoryList = replenishmentTrial.getSoldOutDateBeforeTheoryList();
        int sum = 0;
        if (CollectionUtil.isNotEmpty(soldOutDateBeforeTheoryList)) {
            sum = soldOutDateBeforeTheoryList.stream()
                    .mapToInt(dto -> dto.getSoldOutDays() != null ? dto.getSoldOutDays() : 0)
                    .sum();
        }

        ReplenishmentManager replenishmentManager = ReplenishmentManager.getInstance();
        ReplenishmentProjectDO replenishmentProject = ReplenishmentProjectDO.builder()
                .replenishmentRecordId(replenishmentManager.getRecordId())
                .fullLinkTheoreticalSoldOutDate(fullLinkTheoreticalSoldOutDate)
                .fullLinkDaysBeforeSoldOut(sum)
                .build();
        replenishmentProjectRepository.save(replenishmentProject);
        return replenishmentProject.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAdvicePurchaseDateAll() {
        List<ReplenishmentRecordDO> recordDOList = replenishmentRecordRepository.list();
        var virList = new ArrayList<ReplenishmentVirtualSkuPurchaseDO>();
        recordDOList.forEach(i -> {

            if (ObjectUtil.isEmpty(i.getAdvicePurchaseStartDate()) && ObjectUtil.isEmpty(i.getAdvicePurchaseEndDate())) {
                Date createDate = i.getCreateDate();
                replenishmentRecordRepository.updateAllRecord(ReplenishmentRecordDO.builder()
                        .id(i.getId())
                        .advicePurchaseStartDate(DateUtil.beginOfDay(createDate))
                        .advicePurchaseEndDate(DateUtil.offsetDay(createDate, 6))
                        .build());
            }
            var virtualSkuPurchaseDOS = replenishmentVirtualSkuPurchaseRepository.selectByRecordId(i.getId());

            if (CollectionUtil.isNotEmpty(virtualSkuPurchaseDOS)) {
                virtualSkuPurchaseDOS.forEach(v -> {
                    if (StrUtil.isNotBlank(v.getReason()) && NumberUtil.isInteger(v.getReason())) {
                        virList.add(ReplenishmentVirtualSkuPurchaseDO.builder()
                                .id(v.getId())
                                .reason(ReplenishmentStatusReasonEnum.ofCode(Integer.valueOf(v.getReason())).getDesc()).build());
                    }
                });
            }
        });
        if (CollectionUtil.isNotEmpty(virList)) {
            replenishmentVirtualSkuPurchaseRepository.updateBatchById(virList);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<ReplenishmentSoldOutDaysDO> getBeforeSoldOutList(ReplenishmentProjectForm form) {
        return soldOutDaysService.getBeforeSoldOutList(form.getReplenishmentProjectId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reCalReplenishment(ReplenishmentDetailForm form) {
        try {
            replenishmentCalculationService.reCalReplenishment(form);
        } finally {
            ReplenishmentMapManager.getInstance().resetVariables();
            ReplenishmentManager.getInstance().resetVariables();
        }
    }

    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void refreshSaleDestination() {

        CompletableFuture<Boolean> task1 = CompletableFuture.supplyAsync(() -> {
            int count = 0;
            List<ReplenishmentRecordDO> recordDOList = replenishmentRecordRepository.list();
            List<ReplenishmentVirtualSkuPurchaseDO> resultList = new ArrayList<>();
            for (var recordDO : recordDOList) {
                var records = replenishmentVirtualSkuPurchaseRepository.selectByRecordId(recordDO.getId());
                if (CollectionUtil.isNotEmpty(records)) {
                    for (var repVirDO : records) {
                        resultList.add(ReplenishmentVirtualSkuPurchaseDO.builder()
                                .id(repVirDO.getId())
                                .saleDestination(formatSaleDestination(repVirDO.getSaleDestination()))
                                .discountSaleDestination(formatSaleDestination(repVirDO.getDiscountSaleDestination()))
                                .operatorSaleDestination(formatSaleDestination(repVirDO.getOperatorSaleDestination()))
                                .build());
                    }
                    replenishmentVirtualSkuPurchaseRepository.updateBatchById(resultList);
                    log.info("补货已更新：{}", count);
                    count += resultList.size();
                    resultList.clear();
                }
            }
            return Boolean.TRUE;
        }, importExcelThreadPool);

        CompletableFuture<Boolean> task2 = CompletableFuture.supplyAsync(() -> {
            int count = 0;
            List<ShippingRecordDO> recordDOList = shippingRecordRepository.list();
            List<TrialInventorySaleDestinationDO> resultList = new ArrayList<>();
            for (var recordDO : recordDOList) {
                var records = shippingSaleDestinationRepository.selectByRecordId(recordDO.getId());
                if (CollectionUtil.isNotEmpty(records)) {
                    for (var repVirDO : records) {
                        resultList.add(TrialInventorySaleDestinationDO.builder()
                                .id(repVirDO.getId())
                                .destinationEverydaySale(formatSaleDestination(repVirDO.getDestinationEverydaySale()))
                                .build());
                    }
                    shippingSaleDestinationRepository.updateBatchById(resultList);
                    log.info("发货已更新：{}", count);
                    count += resultList.size();
                    resultList.clear();
                }
            }
            return Boolean.TRUE;
        }, importExcelThreadPool);

        CompletableFuture.allOf(task1, task2).join();
    }

    public String formatSaleDestination(String saleDestination) {
        if (StrUtil.isBlank(saleDestination)) {
            return null;
        }
        Map<String, Map<String, BigDecimal>> saleDestinationMap = JSONObject.parseObject(saleDestination, new TypeReference<>() {
        });
        TreeMap<String, BigDecimal> newSaleDestinationMap = new TreeMap<>();
        if (CollectionUtil.isNotEmpty(saleDestinationMap)) {
            // 遍历每个月份
            for (Map.Entry<String, Map<String, BigDecimal>> monthEntry : saleDestinationMap.entrySet()) {
                String monthKey = monthEntry.getKey(); // 例如 "2024/01/01"
                Map<String, BigDecimal> monthData = monthEntry.getValue();

                // 获取该月的合计值
                BigDecimal monthTotal = monthData.getOrDefault("合计", BigDecimal.ZERO);

                // 解析月份，获取年和月
                String[] dateParts = monthKey.split("/");
                if (dateParts.length >= 3) {
                    int year = Integer.parseInt(dateParts[0]);
                    int month = Integer.parseInt(dateParts[1]);

                    // 获取该月的天数
                    YearMonth yearMonth = YearMonth.of(year, month);
                    int daysInMonth = yearMonth.lengthOfMonth();

                    // 为该月的每一天设置相同的合计值
                    for (int day = 1; day <= daysInMonth; day++) {
                        String dayKey = String.format("%d/%02d/%02d", year, month, day);
                        newSaleDestinationMap.put(dayKey, monthTotal);
                    }
                }
            }
        }
        return JSON.toJSONString(newSaleDestinationMap);
    }
}




