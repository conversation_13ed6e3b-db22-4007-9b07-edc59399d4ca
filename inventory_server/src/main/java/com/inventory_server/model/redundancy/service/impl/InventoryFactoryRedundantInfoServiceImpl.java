package com.inventory_server.model.redundancy.service.impl;

import com.inventory_server.applications.dto.InventoryFactoryRedundantInfoDto;
import com.inventory_server.model.redundancy.entity.dos.InventoryFactoryRedundantInfoDO;
import com.inventory_server.model.redundancy.repository.dataRepository.InventoryFactoryRedundantInfoRepository;
import com.inventory_server.model.redundancy.service.IInventoryFactoryRedundantInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 冗余库存信息获取、初步处理、数据储存、展示的service层
 * <AUTHOR>
 * @Date 2024/5/7 16:38
 **/
@Service
@Slf4j
public class InventoryFactoryRedundantInfoServiceImpl implements IInventoryFactoryRedundantInfoService {
    @Resource
    private InventoryFactoryRedundantInfoRepository inventoryFactoryRedundantInfoRepository;

    @Override
    public List<InventoryFactoryRedundantInfoDO> getFactoryRedundantList(String infoId) {
        return inventoryFactoryRedundantInfoRepository.getFactoryRedundantList(infoId);
    }

    @Override
    public List<InventoryFactoryRedundantInfoDto> getFactoryRedundantInfoList(List<String> infoIdList) {
        return inventoryFactoryRedundantInfoRepository.getFactoryRedundantInfoList(infoIdList);
    }

    @Override
    public void batchSaveFactoryRedundantInfo(List<InventoryFactoryRedundantInfoDO> list) {
        inventoryFactoryRedundantInfoRepository.saveBatch(list);
    }

    @Override
    public void deleteByWatchBoardIds(List<String> watchBoardIds) {
        inventoryFactoryRedundantInfoRepository.deleteByWatchBoardIds(watchBoardIds);
    }
}
