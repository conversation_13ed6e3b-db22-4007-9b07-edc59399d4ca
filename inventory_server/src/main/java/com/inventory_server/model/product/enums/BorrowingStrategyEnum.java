package com.inventory_server.model.product.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum BorrowingStrategyEnum {

    DIRECT_BORROWING(0,"直接借调"),

    MANUAL_CONFIRMATION(1,"人工确认");

    private final Integer code;
    private final String desc;

    public static BorrowingStrategyEnum ofDesc(String value) {
        return Arrays.stream(BorrowingStrategyEnum.values())
                .filter(it -> it.getDesc().equals(value))
                .findFirst()
                .orElse(null);
    }
    public static BorrowingStrategyEnum ofCode(Integer code) {
        if (code == null){
            return null;
        }
        return Arrays.stream(BorrowingStrategyEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
