package com.auth.authorization_server.controller;

/**
 * <AUTHOR>
 * @date 2024/7/24
 **/

import cn.hutool.core.util.StrUtil;
import com.auth.authorization_server.aop.PreventReSubmit;
import com.auth.authorization_server.domain.dto.DeptDTO;
import com.auth.authorization_server.domain.vo.UserVO;
import com.auth.authorization_server.exception.BaseException;
import com.auth.authorization_server.model.vo.TreeSelect;
import com.auth.authorization_server.req.UserParams;
import com.auth.authorization_server.service.ISysDeptService;
import com.auth.authorization_server.service.ISysRoleService;
import com.auth.authorization_server.service.ISysUserService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 部门信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dept")
@Slf4j
public class SysDeptController {

    @Resource
    private ISysDeptService deptService;

    @Resource
    private ISysUserService userService;

    @Resource
    private ISysRoleService roleService;

    @RequiresPermissions("system:dept:list")
    @PostMapping("/list")
    public ResultDTO<List<DeptDTO>> list() {
        return ResultDTO.success(deptService.selectDeptTree());
    }


    /**
     * 根据部门编号获取详细信息
     */
    @RequiresPermissions("system:dept:list")
    @GetMapping(value = {"/detail/", "/detail/{deptId}"})
    public ResultDTO<Map<String, Object>> excludeChild(@PathVariable(value = "deptId", required = false) String deptId) {
        Map<String, Object> ajax = new HashMap<>();
        DeptDTO deptDTO = new DeptDTO();
        List<TreeSelect> treeSelects = new ArrayList<>();
        if (StrUtil.isNotBlank(deptId)) {
            deptDTO = deptService.selectDeptById(deptId);
            treeSelects = deptService.selectDeptExcludeTreeList(deptId);
        } else {
            treeSelects = deptService.selectDeptTreeList();
        }
        ajax.put("sysDept", deptDTO);
        ajax.put("depts", treeSelects);
        return ResultDTO.success(ajax);
    }

    /**
     * 新增部门
     */
    @RequiresPermissions("system:dept:list")
    @PostMapping("/save")
    @PreventReSubmit
    public ResultDTO<Boolean> add(@Validated @RequestBody DeptDTO dept) {
        if (!deptService.checkDeptNameUnique(dept)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        return ResultDTO.success(deptService.insertDept(dept));
    }

    /**
     * 修改部门
     */
    @RequiresPermissions("system:dept:list")
    @PutMapping("/update")
    @PreventReSubmit
    public ResultDTO<Boolean> edit(@Validated @RequestBody DeptDTO dept) {
        String deptId = dept.getId();
        if (!deptService.checkDeptNameUnique(dept)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        } else if (dept.getParentId().equals(deptId)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
        }
        return ResultDTO.success(deptService.updateDept(dept));
    }

    /**
     * 删除部门
     */
    @RequiresPermissions("system:dept:list")
    @DeleteMapping("/delete/{deptId}")
    public ResultDTO<Boolean> remove(@PathVariable String deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "请先删除下级部门！");
        }
        if (deptService.checkDeptExistUser(deptId)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "该部门下还有用户,请先将用户移除后再删除");
        }
        return ResultDTO.success(deptService.deleteDeptById(deptId));
    }

    /**
     * 获取部门下拉树列表-角色管理用
     */
    //@GetMapping("/info/treeselect")
    //public ResultDTO<List<TreeSelect>> treeselect() {
    //    return ResultDTO.success(deptService.selectDeptTreeList());
    //}

    /**
     * 获取对应角色部门树列表
     */
    //@RequiresPermissions("system:dept:list")
    //@GetMapping(value = "/info/deptTree/{roleId}")
    //public ResultDTO<Map<String, Object>> deptTree(@PathVariable("roleId") String roleId) {
    //    Map<String, Object> ajax = new HashMap<>();
    //    ajax.put("checkedKeys", deptService.selectDeptListByRoleId(roleId));
    //    ajax.put("depts", deptService.selectDeptTreeList());
    //    return ResultDTO.success(ajax);
    //}

    /**
     * 查询已分配用户部门列表
     */
//    @RequiresPermissions("system:dept:list")
    @RequiresPermissions("system:dept:list")
    @PostMapping("/list/authUser/allocatedList")
    public ResultDTO<IPage<UserVO>> allocatedList(@RequestBody UserParams user) {
        return ResultDTO.success(userService.selectAllocatedDeptList(user));
    }

    /**
     * 查询未分配用户角色列表
     */
//    @RequiresPermissions("system:dept:list")
    @RequiresPermissions("system:dept:list")
    @PostMapping("/list/authUser/unallocatedList")
    public ResultDTO<List<UserVO>> unallocatedList(@RequestBody UserParams user) {
        return ResultDTO.success(userService.selectUnallocatedDeptList(user));
    }

    /**
     * 批量取消授权用户
     */
//    @RequiresPermissions("system:dept:edit")
    @RequiresPermissions("system:dept:list")
    @PutMapping("/update/authUser/cancel")
    @PreventReSubmit
    public ResultDTO cancelAuthUserAll(@RequestBody DeptDTO dept) {
        deptService.deleteAuthUsers(dept.getId(), dept.getUserIds());
        return ResultDTO.success();
    }

    /**
     * 批量选择用户授权
     */
//    @RequiresPermissions("system:dept:edit")
    @RequiresPermissions("system:dept:list")
    @PutMapping("/update/authUser/selectAll")
    @PreventReSubmit
    public ResultDTO<Boolean> selectAuthUserAll(@RequestBody DeptDTO dept) {
        return ResultDTO.success(deptService.insertAuthUsers(dept.getId(), dept.getUserIds()));
    }
}
