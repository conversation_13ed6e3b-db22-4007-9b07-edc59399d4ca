package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;



/**
 * @Description 目标日销表
 * <AUTHOR>
 * @Date 2024/1/6 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_trial_inventory_sale_destination")
public class TrialInventorySaleDestinationDO extends BaseEntity {
    /**
     * 海外仓状态（根据当前时间与库存可用时间去判断是已经到货还是在途）
     */
    private String foreignInventory;
    /**
     * 目标日销
     */
    private String destinationEverydaySale;
    /**
     * 试算id
     */
    private String shippingProjectId;
}
