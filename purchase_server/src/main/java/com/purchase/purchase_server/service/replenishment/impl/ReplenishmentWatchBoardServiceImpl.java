package com.purchase.purchase_server.service.replenishment.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.crafts_mirror.utils.dp.ProduceDaysDp;
import com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum;
import com.crafts_mirror.utils.utils.DateUtils;
import com.purchase.purchase_server.service.replenishment.IReplenishmentWatchBoardService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;


/**
 * @Description 补货试算看板服务类
 * <AUTHOR>
 * @Date 2025/1/16 14:55
 **/
@Service
public class ReplenishmentWatchBoardServiceImpl implements IReplenishmentWatchBoardService {
    /**
     * 判断下补货单以后，货能否在工厂交货时间之前货好
     * @param purchaseDate 采购下单时间
     * @param factoryFinishedDate 工厂交货时间
     * @param produceDays 生产交期
     * @param purchaseDays 采购下单天数
     */
    private boolean checkEnableArrivingInTime(LocalDate purchaseDate, LocalDate factoryFinishedDate, String produceDays, int purchaseDays) {
        purchaseDate = purchaseDate.plusDays(purchaseDays);
        if (factoryFinishedDate.isBefore(purchaseDate)) {
            return false;
        }

        ProduceDaysDp produceDaysDp = ProduceDaysDp.initProduceDaysDp(produceDays);

        // 通过下单时间计算得来的工厂交货日期
        LocalDate produceEndDate = produceDaysDp.getProduceEndDate(purchaseDate);
        return !produceEndDate.isAfter(factoryFinishedDate);
    }

    @Override
    public boolean checkEnableArrivingInTime(String advicePurchaseDate, String expectedFactoryDate, Integer purchaseDays, String produceDays) {
        DateTime parse = DateUtil.parse(advicePurchaseDate);
        DateTime parse1 = DateUtil.parse(expectedFactoryDate);
        return checkEnableArrivingInTime(DateUtils.convertToLocalDate(parse), DateUtils.convertToLocalDate(parse1), produceDays, purchaseDays);
    }

    @Override
    public boolean checkEnableArrivingInTime(Date advicePurchaseDate, Date expectedFactoryDate, Integer purchaseDays, String produceDays) {
        DateTime parse = new DateTime(advicePurchaseDate);
        DateTime parse1 = new DateTime(expectedFactoryDate);
        return checkEnableArrivingInTime(DateUtils.convertToLocalDate(parse), DateUtils.convertToLocalDate(parse1), produceDays, purchaseDays);
    }

    @Override
    public String getRemarks(Date advicePurchaseDate, Date expectedFactoryDate, Integer purchaseDays, String produceDays,
            String isChangedArrivingDate, Integer productStatus) {
        if ("-1".equals(isChangedArrivingDate)) {
            return null;
        }
        String productStatusDesc = VirtualProductStatusEnum.ofCode(productStatus).getDesc();
        if ("1".equals(isChangedArrivingDate)) {
            return productStatusDesc + "，日销顺延需调整";
        }
        LocalDate purchaseDate = DateUtils.convertToLocalDate(new DateTime(advicePurchaseDate));
        LocalDate factoryFinishedDate = DateUtils.convertToLocalDate(new DateTime(expectedFactoryDate));

        ProduceDaysDp produceDaysDp = ProduceDaysDp.initProduceDaysDp(produceDays);

        // 通过下单时间计算得来的工厂交货日期
        purchaseDate = purchaseDate.plusDays(purchaseDays);
        LocalDate produceEndDate = produceDaysDp.getProduceEndDate(purchaseDate);
        long until = produceEndDate.until(factoryFinishedDate, ChronoUnit.DAYS);
        if (until > 30) {
            return null;
        } else if (until >= 0) {
            return productStatusDesc + "，到仓时间在一个月内";
        } else {
            return productStatusDesc + "，到仓时间无法满足";
        }
    }
}
