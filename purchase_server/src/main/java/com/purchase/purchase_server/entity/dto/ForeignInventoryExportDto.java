package com.purchase.purchase_server.entity.dto;

import cn.hutool.core.date.DateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @Description 补货导出-海外库存冗余数据
 * <AUTHOR>
 * @Date 2024/6/26 13:53
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForeignInventoryExportDto {

    private String virtualSkuId;

    private String virtualSku;

    private String channel;
    //海外冗余总量
    private Double foreignRedundantSum;

    private DateTime enableUsingDate;

    private Map<String, Double> foreignRedundant;
    private Map<String, Double> addForeignRedundant;
}
