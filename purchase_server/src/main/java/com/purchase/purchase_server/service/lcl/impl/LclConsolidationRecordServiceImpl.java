package com.purchase.purchase_server.service.lcl.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.purchase.purchase_server.assembler.LclConsolidationAssembler;
import com.purchase.purchase_server.entity.bo.DeliveryForeignInventoryBO;
import com.purchase.purchase_server.entity.bo.FactoryFinishedInventoryBO;
import com.purchase.purchase_server.entity.bo.Lcl.LclFinishedInventoryBO;
import com.purchase.purchase_server.entity.dataObject.Lcl.*;
import com.purchase.purchase_server.entity.dto.Lcl.*;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.form.DeliveryPurchaseForm;
import com.purchase.purchase_server.entity.form.DeliveryRecordPurchaseForm;
import com.purchase.purchase_server.entity.form.LclConsolidationForm;
import com.purchase.purchase_server.entity.form.LclConsolidationRecordForm;
import com.purchase.purchase_server.entity.vo.DeliveryRecordPurchaseListVo;
import com.purchase.purchase_server.entity.vo.Lcl.LclConsolidationRecordVO;
import com.purchase.purchase_server.entity.vo.UserInteriorVO;
import com.purchase.purchase_server.enums.DeliveryTypeEnum;
import com.purchase.purchase_server.enums.ShippingTrialStatusEnum;
import com.purchase.purchase_server.enums.lcl.*;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.repository.dataRepository.DeliveryForeignInventoryRepositoryImpl;
import com.purchase.purchase_server.repository.dataRepository.FactoryFinishedInventoryRepositoryImpl;
import com.purchase.purchase_server.repository.dataRepository.Lcl.*;
import com.purchase.purchase_server.repository.interiorRepository.WarehouseRepository;
import com.purchase.purchase_server.service.lcl.ILclConsolidationRecordService;
import com.purchase.purchase_server.service.replenishment.ISysUserInteriorService;
import com.purchase.purchase_server.service.shipping.IDeliveryPurchaseService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.EDIT_LCL_DATA_CLEAN_LOCK;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.EXPORT_DELIVERY_LOCK;
import static com.purchase.purchase_server.enums.lcl.LclStatusEnum.PRETENDING_SAVE;

/**
 * <AUTHOR>
 * @description 针对表【cm_lcl_consolidation_record(拼柜装柜记录表)】的数据库操作Service实现
 * @createDate 2024-12-04 16:47:06
 */
@Service
@Slf4j
public class LclConsolidationRecordServiceImpl implements ILclConsolidationRecordService {

    @Resource
    private IDeliveryPurchaseService deliveryPurchaseService;

    @Resource
    private FactoryFinishedInventoryRepositoryImpl factoryFinishedInventoryRepository;

    @Resource
    private DeliveryForeignInventoryRepositoryImpl deliveryForeignInventoryRepository;

    @Resource
    private LclConsolidationRecordRepositoryImpl lclConsolidationRecordRepository;

    @Resource
    private LclFinishedInventoryRepositoryImpl lclFinishedInventoryRepository;

    @Resource
    private LclTrialShippingInventoryRepositoryImpl lclTrialShippingInventoryRepository;

    @Resource
    private LclConsolidationFinishedInventoryRepositoryImpl lclConsolidationFinishedInventoryRepository;

    @Resource
    private LclConsolidationTrialShippingInventoryRepositoryImpl lclConsolidationTrialShippingInventoryRepository;

    @Resource
    private LclShippingNumHistoryRepositoryImpl lclShippingNumHistoryRepository;

    @Resource
    private LclConsolidationAssembler lclConsolidationAssembler;

    @Resource
    private ISysUserInteriorService sysUserInteriorService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private WarehouseRepository warehouseRepository;

    @Resource
    private LclContainerDetailRepositoryImpl lclContainerDetailRepository;

    @Resource
    private LclContainerInfoRepositoryImpl containerInfoRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LclConsolidationRecordVO createLclConsolidation(LclConsolidationForm form) {

        var lclRecordList = lclConsolidationRecordRepository.list(LclConsolidationRecordDTO.builder().shippingRecordId(form.getShippingRecordId()).build());
        if (CollectionUtil.isNotEmpty(lclRecordList)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "装柜计划已存在!");
        }

        DeliveryRecordPurchaseForm recordForm = DeliveryRecordPurchaseForm.builder()
                .recordId(form.getShippingRecordId())
                .build();
        recordForm.setCurrent(1);
        recordForm.setSize(-1);// 使用 Optional 处理空值情况
        var recordVo = deliveryPurchaseService.recordPageList(recordForm)
                .getRecords()
                .stream()
                .findFirst()
                .orElseThrow(() -> new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "发货计划不存在"));

        if (recordVo.getTrialStatus().equals(ShippingTrialStatusEnum.ABROGATE.getCode())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "该计划已作废，无法装柜");
        }
        form.setLimit(true);
        if (CollectionUtil.isEmpty(factoryFinishedInventoryRepository.selectNeedToShipList(form))) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "该装柜时间无发货数据");
        }
        form.setLimit(false);
        String shippingStartDate = form.getShippingStartDate();
        String shippingEndDate = form.getShippingEndDate();

        // 获取计划数据
        List<FactoryFinishedInventoryBO> factoryFinishedInventoryList = factoryFinishedInventoryRepository.selectNeedToShipList(form);
        List<FactoryFinishedInventoryBO> needToShipList = factoryFinishedInventoryList.stream().filter(val -> !val.getTrialShippingInventoryList().isEmpty()).collect(Collectors.toList());

        // 在途数据
        List<DeliveryForeignInventoryBO> deliveryForeignInventoryList = deliveryForeignInventoryRepository.selectNeedToShipList(form);

        if (CollectionUtil.isNotEmpty(deliveryForeignInventoryList)) {
            var foreignList = deliveryForeignInventoryList.stream().filter(item -> !item.getStartShippingDate().before(DateUtil.parse(shippingStartDate)) &&
                    item.getDeliveryType().equals(DeliveryTypeEnum.UNDER_LOAD.getCode())).collect(Collectors.toList());
            deliveryForeignInventoryList.clear();
            deliveryForeignInventoryList.addAll(foreignList);
        }

        if (CollectionUtil.isEmpty(needToShipList)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "该装柜时间无发货数据");
        }
        LclConsolidationRecordDO lclRecordDO = LclConsolidationRecordDO.builder()
                .shippingRecordId(recordVo.getRecordId())
                .shippingRecordName(recordVo.getFileName())
                .shippingStartDate(DateUtil.parse(shippingStartDate))
                .shippingEndDate(DateUtil.parse(shippingEndDate))
                .lclStatus(LclStatusEnum.DRAFT.getCode())
                .isLclConsolidation(LclConsolidationEnum.NOT_CONSOLIDATION.getCode())
                .headShippingDays(factoryFinishedInventoryList.getFirst().getHeadShippingDays())
                .build();
        lclConsolidationRecordRepository.save(lclRecordDO);
        String lclRecordId = lclRecordDO.getId();
        // 1. 转换并保存对应关系
        List<LclFinishedInventoryDO> lclFinishedInventoryDOList = new ArrayList<>();
        List<LclFinishedInventoryDO> lclFinishedInventoryDONotList = new ArrayList<>();

        List<LclFinishedInventoryDO> lcldeliveryForeignDOList = new ArrayList<>();
        Map<String, List<LclTrialShippingInventoryDO>> relationMap = new HashMap<>();

        if (CollectionUtil.isNotEmpty(needToShipList)) {
            needToShipList.forEach(bo -> {
                LclFinishedInventoryDO factoryDo = lclConsolidationAssembler.factoryFinishedBoToLclDO(bo);
                factoryDo.setId(null);
                factoryDo.setLclRecordId(lclRecordId);
                factoryDo.setIsForeignFlag("0");
                lclFinishedInventoryDOList.add(factoryDo);
            });
        }
        List<FactoryFinishedInventoryBO> notNeedToShipList = factoryFinishedInventoryList.stream().filter(val -> val.getTrialShippingInventoryList().isEmpty()).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(notNeedToShipList)) {
            notNeedToShipList.forEach(bo -> {
                LclFinishedInventoryDO factoryDo = lclConsolidationAssembler.factoryFinishedBoToLclDO(bo);
                factoryDo.setId(null);
                factoryDo.setLclRecordId(lclRecordId);
                factoryDo.setIsForeignFlag("0");
                lclFinishedInventoryDONotList.add(factoryDo);
            });
        }
        if (CollectionUtil.isNotEmpty(deliveryForeignInventoryList)) {
            deliveryForeignInventoryList.forEach(bo -> {
                lcldeliveryForeignDOList.add(LclFinishedInventoryDO.builder()
                        .shipmentCode(bo.getShipmentCode())
                        .factoryRemainNum(0)
                        .factoryShippingPackageNum(bo.getStoreNum())
                        .lclRecordId(lclRecordId)
                        .virtualSkuId(bo.getVirtualSkuId())
                        .isOldStatus(bo.getIsOldStatus())
                        .deliveryType(bo.getDeliveryType())
                        .remarks(bo.getRemarks())
                        .productSnapshotId(bo.getProductSnapshotId())
                        .isForeignFlag("1")
                        .shippingRatio(bo.getShippingRatio())
                        .build());
            });
        }

        lclFinishedInventoryRepository.saveBatch(lclFinishedInventoryDOList);
        lclFinishedInventoryRepository.saveBatch(lclFinishedInventoryDONotList);
        lclFinishedInventoryRepository.saveBatch(lcldeliveryForeignDOList);


        // 现在 lclFinishedInventoryDOList 中的对象已经有了 ID
        for (int i = 0; i < needToShipList.size(); i++) {
            List<LclTrialShippingInventoryDO> trialList = lclConsolidationAssembler.trialShippingDoListToLclDO(
                    needToShipList.get(i).getTrialShippingInventoryList()
            );
            relationMap.put(lclFinishedInventoryDOList.get(i).getId(), trialList);
        }
        // 处理在途数据
        for (int i = 0; i < deliveryForeignInventoryList.size(); i++) {
            LclFinishedInventoryDO lclFinishedInventoryDO = lcldeliveryForeignDOList.get(i);
            DeliveryForeignInventoryBO deliveryForeignInventoryBO = deliveryForeignInventoryList.get(i);
            double packageNum = Math.ceil((double) deliveryForeignInventoryBO.getStoreNum() / deliveryForeignInventoryBO.getContainerLoad());
            LclTrialShippingInventoryDO trialShippingInventoryDO = LclTrialShippingInventoryDO.builder()
                    .shippingStartDate(deliveryForeignInventoryBO.getStartShippingDate())
                    .packageNum((int) packageNum)
                    .isPackageFull(packageNum != (double) deliveryForeignInventoryBO.getStoreNum() / deliveryForeignInventoryBO.getContainerLoad() ? "0" : "1")
                    .destinationWarehouse(deliveryForeignInventoryBO.getWarehouseId())
                    .lclFactoryFinishedId(lclFinishedInventoryDO.getId())
                    .shippingNum(deliveryForeignInventoryBO.getStoreNum())
                    .remarks(deliveryForeignInventoryBO.getRemarks())
                    .build();
            relationMap.put(lclFinishedInventoryDO.getId(), Collections.singletonList(trialShippingInventoryDO));
        }

        List<LclTrialShippingInventoryDO> allTrialList = relationMap.entrySet().stream()
                .flatMap(entry -> entry.getValue().stream()
                        .peek(trial -> trial.setLclFactoryFinishedId(entry.getKey())))
                .toList();
        if (!allTrialList.isEmpty()) {
            lclTrialShippingInventoryRepository.saveBatch(allTrialList);
        }
        LclConsolidationRecordVO recordVO = new LclConsolidationRecordVO();
        recordVO.setId(lclRecordId);
        return recordVO;
    }

    @Override
    public IPage<LclConsolidationRecordVO> pageList(LclConsolidationRecordForm form) {
        IPage<LclConsolidationRecordDO> lclConsolidationRecordDOIPage = lclConsolidationRecordRepository.pageList(form);
        List<LclConsolidationRecordDO> doRecords = lclConsolidationRecordDOIPage.getRecords();
        if (CollectionUtil.isEmpty(doRecords)) {
            return new Page<>(form.getCurrent(), form.getSize(), lclConsolidationRecordDOIPage.getTotal());
        }

        List<LclConsolidationRecordVO> resultVO = lclConsolidationAssembler.lclRecordDOlToVOList(doRecords);
        // 用户
        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        for (var result : resultVO) {
            String createBy = result.getCreateBy();
            result.setCreateBy(StrUtil.isNotBlank(collect.get(createBy)) ? collect.get(createBy) : createBy);
        }
        IPage<LclConsolidationRecordVO> returnPage = new Page<>(form.getCurrent(), form.getSize());
        returnPage.setTotal(lclConsolidationRecordDOIPage.getTotal());
        returnPage.setRecords(resultVO);
        return returnPage;
    }

    @Override
    @Async("defaultThreadPool")
    @Transactional(rollbackFor = Exception.class)
    public void dataClean(LclConsolidationForm form, LclConsolidationRecordDO lclRecordDO) {
        String shippingStartDate = form.getShippingStartDate();
        String shippingEndDate = form.getShippingEndDate();

        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> warehouseMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

        // 最小体积
        BigDecimal minVolume = lclRecordDO.getMinVolume();
        List<LclFinishedInventoryBO> lclFinishedInventoryBOS = lclFinishedInventoryRepository.listWithTrial(form);
        // 头程时间
        String headShippingDays = lclFinishedInventoryBOS.getFirst().getHeadShippingDays();
        var headShippingDaysMap = JSON.parseObject(headShippingDays, new TypeReference<TreeMap<String, Integer>>() {
        });
        boolean hasEarlierDate = lclFinishedInventoryBOS.stream()
                .anyMatch(inventory -> {
                    List<LclTrialShippingInventoryDTO> lclTrialDTOList = inventory.getLclTrialDTOList();

                    // 处理空值情况
                    if (CollectionUtil.isEmpty(lclTrialDTOList)) {
                        return false;
                    }

                    // 过滤出交期在指定日期范围内的数据
                    return lclTrialDTOList.stream()
                            .anyMatch(trial -> {
                                var date = DateUtil.parse(trial.getShippingStartDate());
                                return !date.after(DateUtil.parse(shippingEndDate));
                            });
                });
        if (CollectionUtil.isEmpty(lclFinishedInventoryBOS) || !hasEarlierDate) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "该时间范围无装柜数据");
        }

        // 没有发货与加急的数据直接跳过
        Map<Boolean, List<LclFinishedInventoryBO>> batchSplit = lclFinishedInventoryBOS.stream()
                .collect(Collectors.partitioningBy(item -> {
                    List<LclTrialShippingInventoryDTO> lclTrialDTOList = item.getLclTrialDTOList();
                    boolean equals = DeliveryTypeEnum.URGENT.getCode().equals(item.getDeliveryType());
                    return CollectionUtil.isNotEmpty(lclTrialDTOList) && !equals;
                }));


        lclFinishedInventoryBOS.clear();
        lclFinishedInventoryBOS.addAll(batchSplit.get(true));
        // 按照自定义分组
        Map<String, List<LclFinishedInventoryBO>> lclFinishedInventoryBySelf = lclFinishedInventoryBOS
                .stream()
                .collect(Collectors.groupingBy(LclFinishedInventoryBO::getSelfSkuId));

        List<LclTrialShippingInventoryDTO> newResultLclTrialList = new ArrayList<>();

        log.info("开始整理");
        for (var lclFinishedInventoryEntry : lclFinishedInventoryBySelf.entrySet()) {
            List<LclTrialShippingInventoryDTO> resultLclTrialList = new ArrayList<>();

            String key = lclFinishedInventoryEntry.getKey();

            var lclFinishedInventoryList = lclFinishedInventoryEntry.getValue();
            // 提取出到仓数据，并将交期复制到每条数据上
            List<LclTrialShippingInventoryDTO> lclTrialList = lclFinishedInventoryList.stream()
                    .flatMap(bo -> bo.getLclTrialDTOList().stream()
                            .peek(trial -> {
                                trial.setFactoryFinishedDate(bo.getFactoryFinishedDate());
                                trial.setFactoryShippingPackageNum(bo.getFactoryShippingPackageNum());
                                trial.setDestinationSku(bo.getDestinationSku());
                                trial.setSelfSkuId(bo.getSelfSkuId());
                                trial.setLclShippingNum(trial.getShippingNum());
                                trial.setCaseLength(bo.getCaseLength() == null ? null : BigDecimal.valueOf(bo.getCaseLength()).divide(BigDecimal.valueOf(100), 3, RoundingMode.HALF_UP));
                                trial.setCaseWidth(bo.getCaseWidth() == null ? null : BigDecimal.valueOf(bo.getCaseWidth()).divide(BigDecimal.valueOf(100), 3, RoundingMode.HALF_UP));
                                trial.setCaseHeight(bo.getCaseHeight() == null ? null : BigDecimal.valueOf(bo.getCaseHeight()).divide(BigDecimal.valueOf(100), 3, RoundingMode.HALF_UP));
                                trial.setVolume(trial.getCaseLength()
                                        .multiply(trial.getCaseWidth())
                                        .multiply(trial.getCaseHeight()));
                                trial.setShipmentCode(bo.getShipmentCode());
                                trial.setContractCode(bo.getContractCode());
                                trial.setContainerLoad(bo.getContainerLoad());
                                trial.setShippingRatio(bo.getShippingRatio());
                                trial.setShippingNumHistoryDTOS(new ArrayList<>());
                                trial.setVirtualSkuId(bo.getVirtualSkuId());
                                trial.setIsOldStatus(bo.getIsOldStatus());
                                trial.setProductSnapshotId(bo.getProductSnapshotId());
                                trial.setIsChange(IsChangeEnum.UNCHANGED.getCode());
                                trial.setDeliveryType(bo.getDeliveryType());
                                trial.setTrialRemarks(trial.getRemarks());
                                trial.setRemarks(bo.getRemarks());
                                trial.setIsForeignFlag(bo.getIsForeignFlag());
                                trial.setOriginalShippingNum(bo.getFactoryShippingPackageNum() - bo.getFactoryRemainNum());
                            }))
                    .toList();

            if (lclTrialList.isEmpty()) {
                continue;
            }


            Map<String, List<LclTrialShippingInventoryDTO>> lclTrialListByWarehouse = lclTrialList.stream()
                    .collect(Collectors.groupingBy(LclTrialShippingInventoryDTO::getDestinationWarehouse));

            // 对每个仓库的列表按照发货开始日期排序
            for (Map.Entry<String, List<LclTrialShippingInventoryDTO>> entry : lclTrialListByWarehouse.entrySet()) {
                List<LclTrialShippingInventoryDTO> list = entry.getValue();
                list.sort(Comparator.comparing(LclTrialShippingInventoryDTO::getShippingStartDate));

                BigDecimal volume = list.getFirst().getVolume();
                Integer containerLoad = list.getFirst().getContainerLoad();
                Date nowEndDate = DateUtil.parse(shippingStartDate);
                while (!nowEndDate.after(DateUtil.parse(shippingEndDate))) {

                    Date finalNowEndDate = nowEndDate;
                    Date firstDate = list.stream()
                            .filter(trial -> {
                                var date = DateUtil.parse(trial.getShippingStartDate());
                                return !date.before(finalNowEndDate) && !date.after(DateUtil.parse(shippingEndDate)) &&
                                        trial.getLclShippingNum() > 0;
                            })
                            .findFirst()
                            .map(trial -> DateUtil.parse(trial.getShippingStartDate()))
                            .orElse(null);

                    // 如果没有找到有效的发货日期，跳过当前循环
                    if (firstDate == null) {
                        break;
                    }
                    var firstEndDate = DateUtil.offsetDay(firstDate, 6);
                    firstEndDate = firstEndDate.after(DateUtil.parse(shippingEndDate)) ?
                            DateUtil.parse(shippingEndDate) : firstEndDate;
                    var secondStartDate = DateUtil.offsetDay(firstEndDate, 1);
                    nowEndDate = secondStartDate;


                    var firstList = list.stream()
                            .filter(trial -> {
                                var date = DateUtil.parse(trial.getShippingStartDate());
                                return !date.before(firstDate) && date.before(secondStartDate);
                            })
                            .collect(Collectors.toList());


                    // 备胎+30天,并且交期小于等于shippingEndDate
                    var boxableItems = list.stream()
                            .filter(trial -> {
                                var date = DateUtil.parse(trial.getShippingStartDate());
                                var factoryDate = DateUtil.parse(trial.getFactoryFinishedDate());
                                return !date.before(secondStartDate) && !date.after(DateUtil.offsetDay(secondStartDate, 29)) &&
                                        factoryDate.before(secondStartDate);
                            })
                            .collect(Collectors.toList());

                    if (CollectionUtil.isEmpty(boxableItems)) {
                        continue;
                    }
                    firstList.forEach(toBeBox -> {
                        var lclShippingNum = toBeBox.getLclShippingNum();
                        // 判断是否整箱
                        if (lclShippingNum % containerLoad != 0) {
                            // 计算还差多少够整箱
                            var remainingToFull = containerLoad - (lclShippingNum % containerLoad);
                            for (var boxable : boxableItems) {
                                if (boxable.getLclFactoryFinishedId().equals(toBeBox.getLclFactoryFinishedId()) &&
                                        boxable.getLclShippingNum() > 0) {

                                    // 如果备胎数量大于等于差值,则可以凑整箱
                                    if (boxable.getLclShippingNum() >= remainingToFull) {
                                        // 更新备胎数量
                                        updateShippingNumAndHistory(boxable, -remainingToFull, ChangeTypeEnum.FULL_CONTAINER.getDesc(), toBeBox.getShippingStartDate());

                                        // 更新原始数量
                                        updateShippingNumAndHistory(toBeBox, remainingToFull, ChangeTypeEnum.FULL_CONTAINER.getDesc(), boxable.getShippingStartDate());
                                        break;
                                    } else {
                                        var availableNum = boxable.getLclShippingNum();
                                        // 更新原始数量
                                        updateShippingNumAndHistory(toBeBox, availableNum, ChangeTypeEnum.FULL_CONTAINER.getDesc(), boxable.getShippingStartDate());
                                        remainingToFull -= availableNum;
                                        // 更新备胎数量
                                        updateShippingNumAndHistory(boxable, -availableNum, ChangeTypeEnum.FULL_CONTAINER.getDesc(), toBeBox.getShippingStartDate());
                                    }
                                }
                            }
                        }
                    });


                    // 计算第一个时间段(7天)内的总体积
                    BigDecimal firstListTotalVolume = firstList.stream()
                            // 只计算有效数量(>0)的记录
                            .filter(trial -> trial.getLclShippingNum() > 0)
                            // 计算每条记录所需箱数和对应体积:
                            // 1. 根据装载量计算需要的箱数(向上取整)
                            // 2. 每箱乘以标准体积
                            .map(trial -> {
                                double boxCount = Math.ceil((double) trial.getLclShippingNum() / trial.getContainerLoad());
                                return volume.multiply(BigDecimal.valueOf(boxCount));
                            })
                            // 累加所有记录的体积
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            // 保留4位小数,四舍五入
                            .setScale(4, RoundingMode.HALF_UP);

                    if (firstListTotalVolume.compareTo(minVolume) < 0) {
                        String lastShippingStartDate = firstList.getLast().getShippingStartDate();
                        // 计算需要多少箱
                        BigDecimal volumeDifference = minVolume.subtract(firstListTotalVolume);
                        int requiredCount = volumeDifference.divide(volume, 0, RoundingMode.CEILING).intValue();

                        boolean checkFlag = false;
                        if (requiredCount > 0) {

                            while (requiredCount > 0) {
                                // 计算每个SKU的可用总数
                                Map<String, Integer> skuTotalMap = boxableItems.stream()
                                        .filter(item -> item.getLclShippingNum() > 0)
                                        .collect(Collectors.groupingBy(
                                                LclTrialShippingInventoryDTO::getDestinationSku,
                                                Collectors.summingInt(LclTrialShippingInventoryDTO::getLclShippingNum)
                                        ));

                                // 如果没有可用的SKU了，退出循环
                                if (skuTotalMap.isEmpty()) {
                                    break;
                                }
                                // 获取数量最多的SKU
                                String targetSku = skuTotalMap.entrySet().stream()
                                        .max(Map.Entry.comparingByValue())
                                        .map(Map.Entry::getKey)
                                        .orElse(null);


                                // 获取该SKU的所有可用记录
                                List<LclTrialShippingInventoryDTO> availableTrials = boxableItems.stream()
                                        .filter(t -> t.getDestinationSku().equals(targetSku) && t.getLclShippingNum() > 0)
                                        .toList();


                                int remainingContainer = containerLoad;
                                // 为了后面获取交期
                                TreeMap<String, List<LclTrialShippingInventoryDTO>> lclTrialMap = firstList.stream()
                                        .collect(Collectors.groupingBy(LclTrialShippingInventoryDTO::getShippingStartDate,
                                                TreeMap::new,
                                                Collectors.toList()));
                                List<LclTrialShippingInventoryDTO> lclTrialLast = lclTrialMap.getOrDefault(
                                        lastShippingStartDate,
                                        new ArrayList<>()
                                );

                                if (!checkFlag) {
                                    checkFlag = true;
                                    // 判断备胎中的数量时候能凑够最小体积
                                    Set<String> lclTrialLastFactoryIds = lclTrialLast.stream().map(LclTrialShippingInventoryDTO::getLclFactoryFinishedId).collect(Collectors.toSet());
                                    List<LclTrialShippingInventoryDTO> boxableList = boxableItems.stream()
                                            .filter(value -> lclTrialLastFactoryIds.contains(value.getLclFactoryFinishedId()))
                                            .collect(Collectors.toList());

                                    // 计算备胎的体积
                                    BigDecimal boxableTotalVolume = boxableList.stream()
                                            .filter(trial -> trial.getLclShippingNum() > 0)
                                            .map(trial -> {
                                                double boxCount = Math.ceil((double) trial.getLclShippingNum() / trial.getContainerLoad());
                                                return volume.multiply(BigDecimal.valueOf(boxCount));
                                            })
                                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                                            .setScale(4, RoundingMode.HALF_UP);

                                    // 比较volumeDifference与boxableTotalVolume的大小
                                    if (boxableTotalVolume.compareTo(volumeDifference) < 0) {
                                        break;
                                    }
                                }

                                for (LclTrialShippingInventoryDTO trial : availableTrials) {
                                    Integer availableNum = trial.getLclShippingNum();
                                    Integer transferNum = Math.min(availableNum, remainingContainer);
                                    boolean flag = false;
                                    // 更新原记录
                                    // 尝试找到对应的目标记录并更新
                                    for (LclTrialShippingInventoryDTO targetTrial : lclTrialLast) {
                                        if (targetTrial.getLclFactoryFinishedId().equals(trial.getLclFactoryFinishedId())) {
                                            if (transferNum > 0) {
                                                updateShippingNumAndHistory(trial, -transferNum, ChangeTypeEnum.VOLUME_FIRST.getDesc(), lastShippingStartDate);
                                                updateShippingNumAndHistory(targetTrial, transferNum, ChangeTypeEnum.VOLUME_FIRST.getDesc(), trial.getShippingStartDate());
                                                remainingContainer -= transferNum;
                                                flag = true;
                                                break;
                                            }
                                        }
                                    }

                                    // 如果没有找到对应的记录，创建新记录
                                    if (!flag) {
                                        if (transferNum > 0) {
                                            LclTrialShippingInventoryDTO newTrialShipping = createNewTrialShipping(trial, lastShippingStartDate, 0);
                                            updateShippingNumAndHistory(newTrialShipping, transferNum, ChangeTypeEnum.VOLUME_FIRST.getDesc(), trial.getShippingStartDate());
                                            updateShippingNumAndHistory(trial, -transferNum, ChangeTypeEnum.VOLUME_FIRST.getDesc(), lastShippingStartDate);
                                            firstList.add(newTrialShipping);
                                            list.add(newTrialShipping);
                                        }
                                    }

                                    if (remainingContainer == 0) {
                                        break;
                                    }
                                }

                                // 完成一个集装箱的处理
                                requiredCount--;
                            }
                        }
                    }
                }
                newResultLclTrialList.addAll(list);
            }
        }
        log.info("整理结束");
        // 按照计划分组
        Map<String, List<LclTrialShippingInventoryDTO>> lclTrialListByPlan = newResultLclTrialList.stream()
                .filter(val -> val.getLclShippingNum() > 0)
                .collect(Collectors.groupingBy(LclTrialShippingInventoryDTO::getLclFactoryFinishedId,
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> {
                                    list.sort(Comparator.comparing(LclTrialShippingInventoryDTO::getShippingStartDate));
                                    return list;
                                })));
        log.info("第二阶段：开始整理");
        for (var trialListByPlanEntry : lclTrialListByPlan.entrySet()) {
            var trialListByPlan = trialListByPlanEntry.getValue();
            Integer containerLoad = trialListByPlan.getFirst().getContainerLoad();
            List<LclTrialShippingInventoryDTO> inRangeList = trialListByPlan.stream().filter(val -> {
                DateTime startDate = DateUtil.parse(val.getShippingStartDate());
                return !startDate.before(DateUtil.parse(shippingStartDate)) && !startDate.after(DateUtil.parse(shippingEndDate));
            }).collect(Collectors.toList());

            if (CollectionUtil.isEmpty(inRangeList)) {
                continue;
            }

            List<LclTrialShippingInventoryDTO> outRangeList = trialListByPlan.stream().filter(val -> {
                DateTime startDate = DateUtil.parse(val.getShippingStartDate());
                DateTime factoryFinishedDate = DateUtil.parse(val.getFactoryFinishedDate());
                return startDate.after(DateUtil.parse(shippingEndDate)) && !factoryFinishedDate.after(DateUtil.parse(shippingEndDate)) &&
                        val.getLclShippingNum() > 0;
            }).collect(Collectors.toList());

            Integer factoryShippingPackageNum = trialListByPlan.getFirst().getFactoryShippingPackageNum();


            int smallDiffTotal = inRangeList.stream()
                    .mapToInt(LclTrialShippingInventoryDTO::getLclShippingNum)
                    .sum();

            int diff = factoryShippingPackageNum - smallDiffTotal;


            if (diff > 0 && diff <= 5) {

                // 备胎区间剩余发货数量
                Map<String, List<LclTrialShippingInventoryDTO>> outRangeLclTrialMap = outRangeList.stream()
                        .collect(Collectors.groupingBy(LclTrialShippingInventoryDTO::getDestinationWarehouse,
                                Collectors.toList()));

                int outRangeLclTrialTotal = outRangeList.stream()
                        .mapToInt(LclTrialShippingInventoryDTO::getLclShippingNum)
                        .sum();

                // 排除备胎发货-未安排数量
                int diffTotal = diff - outRangeLclTrialTotal;

                TreeMap<String, List<LclTrialShippingInventoryDTO>> inRangeLclTrialMap = inRangeList.stream()
                        .collect(Collectors.groupingBy(LclTrialShippingInventoryDTO::getShippingStartDate,
                                TreeMap::new,
                                Collectors.toList()));
                var inRangeLast = inRangeLclTrialMap.lastEntry().getValue();

                if (diffTotal > 0 && outRangeLclTrialTotal > 0) {
                    for (Map.Entry<String, List<LclTrialShippingInventoryDTO>> map : outRangeLclTrialMap.entrySet()) {
                        var lclShippingNum = map.getValue().stream().mapToInt(LclTrialShippingInventoryDTO::getLclShippingNum)
                                .sum();
                        // 判断是否整箱
                        if (lclShippingNum % containerLoad != 0 && diffTotal > 0) {
                            // 计算还差多少够整箱
                            var remainingToFull = containerLoad - (lclShippingNum % containerLoad);
                            var firstValue = map.getValue().getFirst();
                            if (diffTotal >= remainingToFull) {
                                firstValue.setLclShippingNum(firstValue.getLclShippingNum() + remainingToFull);
                                diffTotal -= remainingToFull;
                            } else {
                                firstValue.setLclShippingNum(firstValue.getLclShippingNum() + diffTotal);
                                diffTotal = 0;
                            }
                        }
                    }
                }


                if (outRangeLclTrialTotal > 0) {
                    for (Map.Entry<String, List<LclTrialShippingInventoryDTO>> map : outRangeLclTrialMap.entrySet()) {
                        String warehouse = map.getKey();
                        List<LclTrialShippingInventoryDTO> list = map.getValue();

                        boolean flag = false;
                        for (var inRangeDTO : inRangeLast) {
                            if (inRangeDTO.getDestinationWarehouse().equals(warehouse)) {
                                flag = true;
                                for (var value : list) {
                                    if (value.getLclShippingNum() > 0) {
                                        updateShippingNumAndHistory(inRangeDTO, value.getLclShippingNum(), ChangeTypeEnum.AVERAGE_SPLIT.getDesc(), value.getShippingStartDate());
                                        updateShippingNumAndHistory(value, -value.getLclShippingNum(), ChangeTypeEnum.AVERAGE_SPLIT.getDesc(), inRangeDTO.getShippingStartDate());
                                    }
                                }
                            }
                        }
                        if (!flag) {
                            LclTrialShippingInventoryDTO first = list.getFirst();
                            String lastShippingDate = inRangeLast.getFirst().getShippingStartDate();
                            LclTrialShippingInventoryDTO newTrialShipping = createNewTrialShipping(first, lastShippingDate, 0);
                            for (var newDTO : list) {
                                if (newDTO.getLclShippingNum() > 0) {
                                    updateShippingNumAndHistory(newTrialShipping, newDTO.getLclShippingNum(), ChangeTypeEnum.AVERAGE_SPLIT.getDesc(), newDTO.getShippingStartDate());
                                    updateShippingNumAndHistory(newDTO, -newDTO.getLclShippingNum(), ChangeTypeEnum.AVERAGE_SPLIT.getDesc(), newTrialShipping.getShippingStartDate());
                                }
                            }
                            inRangeLast.add(newTrialShipping);
                            inRangeList.add(newTrialShipping);
                            newResultLclTrialList.add(newTrialShipping);
                        }
                    }
                }

                if (diffTotal > 0) {
                    // 还剩几箱
                    BigDecimal remainingBoxes = BigDecimal.valueOf(diffTotal)
                            .divide(BigDecimal.valueOf(containerLoad), 0, RoundingMode.CEILING);


                    int remainingToAllocate = remainingBoxes.intValue();
                    // 计算总的 lclShippingNum
                    double totalLclShippingNum = inRangeLast.stream()
                            .mapToInt(LclTrialShippingInventoryDTO::getLclShippingNum)
                            .sum();

                    if (totalLclShippingNum == 0.0) {
                        updateShippingNumAndHistory(inRangeLast.getLast(), diffTotal, ChangeTypeEnum.AVERAGE_SPLIT.getDesc(), null);
                    } else {
                        // 计算每个箱子应得的理论比例
                        List<AllocationItem> allocations = inRangeLast.stream()
                                .map(item -> new AllocationItem(
                                        item,
                                        item.getLclShippingNum() / totalLclShippingNum * remainingToAllocate
                                ))
                                .sorted(Comparator.comparing(AllocationItem::getRatio).reversed())
                                .toList();

                        // 分配整箱
                        for (AllocationItem item : allocations) {
                            // 计算当前仓库应分配的箱子数
                            int fullContainers = remainingBoxes.multiply(BigDecimal.valueOf(item.ratio))
                                    .setScale(0, RoundingMode.UP).intValue();
                            int min = Math.min(fullContainers * containerLoad, diffTotal);
                            if (fullContainers > 0) {
                                updateShippingNumAndHistory(item.dto, min, ChangeTypeEnum.AVERAGE_SPLIT.getDesc(), null);
                                diffTotal -= min;
                                if (diffTotal == 0) {
                                    break;
                                }
                            }
                        }
                    }
                }

            }
        }
        log.info("第二阶段：整理结束");

        // 按照自定义分组
        Map<String, List<LclTrialShippingInventoryDTO>> newLclFinishedInventoryBySelf = newResultLclTrialList
                .stream()
                .collect(Collectors.groupingBy(LclTrialShippingInventoryDTO::getSelfSkuId));

        List<LclTrialShippingInventoryDTO> newResultLclTrialList2 = new ArrayList<>();

        log.info("开始整理");
        for (var lclFinishedInventoryEntry : newLclFinishedInventoryBySelf.entrySet()) {
            var lclFinishedInventoryList = lclFinishedInventoryEntry.getValue();
            lclFinishedInventoryList.sort(Comparator.comparing(LclTrialShippingInventoryDTO::getShippingStartDate));
            // 7天的二维数组
            List<List<LclTrialShippingInventoryDTO>> sevenDaysList = groupDataByTimeRange(lclFinishedInventoryList, shippingEndDate, 7);

            // 时间区间
            for (List<LclTrialShippingInventoryDTO> sevenDays : sevenDaysList) {
                Map<String, List<LclTrialShippingInventoryDTO>> sevenDaysByWarehouseMap = sevenDays.stream()
                        .collect(Collectors.groupingBy(LclTrialShippingInventoryDTO::getDestinationWarehouse));

                // 1. 获取sevenDaysByWarehouseMap的entries并排序
                List<Map.Entry<String, List<LclTrialShippingInventoryDTO>>> entryList =
                        new ArrayList<>(sevenDaysByWarehouseMap.entrySet());

                // 3. 创建新的LinkedHashMap保持排序结果
                Map<String, LclDataSevenDayDTO> sortedSevenMap = new LinkedHashMap<>();
                for (Map.Entry<String, List<LclTrialShippingInventoryDTO>> entry : entryList) {
                    List<LclTrialShippingInventoryDTO> sortedSevenList = entry.getValue();
                    // 计算总体积z
                    BigDecimal totalVolume = BigDecimal.ZERO;
                    Map<String, Map<String, Double>> virtualRatio = new HashMap<>();
                    Map<String, BigDecimal> virtualVolume = new HashMap<>();

                    for (LclTrialShippingInventoryDTO item : sortedSevenList) {
                        Integer lclShippingNum = item.getLclShippingNum();
                        if (lclShippingNum <= 0) {
                            continue;
                        }
                        Integer containerLoad = item.getContainerLoad();
                        BigDecimal volume = item.getVolume();
                        // 计算需要的箱子数量（向上取整）
                        int boxCount = (int) Math.ceil((double) lclShippingNum / containerLoad);
                        // 计算该项的总体积并累加
                        BigDecimal itemVolume = volume.multiply(BigDecimal.valueOf(boxCount));
                        BigDecimal virtualSumVolume = virtualVolume.getOrDefault(item.getDestinationSku(), BigDecimal.valueOf(0));
                        virtualVolume.put(item.getDestinationSku(), virtualSumVolume.add(itemVolume));

                        totalVolume = totalVolume.add(itemVolume);

                        var shippingRatio = JSON.parseObject(item.getShippingRatio(), new TypeReference<TreeMap<String, Double>>() {
                        });
                        String virtualSku = item.getDestinationSku();
                        virtualRatio.put(virtualSku, shippingRatio);

                    }
                    sortedSevenMap.put(entry.getKey(), LclDataSevenDayDTO.builder()
                            .totalVolume(totalVolume)
                            .sortedSevenList(sortedSevenList)
                            .virtualRatio(virtualRatio)
                            .virtualVolume(virtualVolume).build());
                }
                Map<String, LclDataSevenDayDTO> resultLclDataSevenDayDTOMap = reorderMapByVolumeAndHeadDays(sortedSevenMap, minVolume, headShippingDaysMap);
                // 过滤体积小于minVolume的数据，并按totalVolume从小到大排序，结果存储key
                List<String> smallVolumeDateRanges = resultLclDataSevenDayDTOMap.entrySet().stream()
                        // 过滤出体积小于minVolume的数据
                        .filter(entry -> entry.getValue().getTotalVolume().compareTo(minVolume) < 0)
                        // 按照totalVolume从小到大排序
                        .sorted(Comparator.comparing(entry -> entry.getValue().getTotalVolume()))
                        // 只获取key值(日期范围)
                        .map(Map.Entry::getKey)
                        // 收集到List中
                        .collect(Collectors.toList());
                // 记录结果中还未装满的仓
                List<String> copySmallVolumeDateRanges = new ArrayList<>(smallVolumeDateRanges);
                // 已经被分过的仓库
                List<String> warehouseList = new ArrayList<>();
                for (var warehouseId : smallVolumeDateRanges) {
                    warehouseList.add(warehouseId);
                    copySmallVolumeDateRanges.removeFirst();
                    LclDataSevenDayDTO lclDataSevenDayDTO = resultLclDataSevenDayDTOMap.get(warehouseId);
                    BigDecimal totalVolume = lclDataSevenDayDTO.getTotalVolume();
                    if (totalVolume.compareTo(minVolume) >= 0) {
                        continue;
                    }
                    // 这些数据要分出去-到虚拟sku
                    List<LclTrialShippingInventoryDTO> sortedSevenList = lclDataSevenDayDTO.getSortedSevenList();
                    for (var lclTrialShippingData : sortedSevenList) {
                        Integer lclShippingNum = lclTrialShippingData.getLclShippingNum();
                        if (lclShippingNum <= 0) {
                            continue;
                        }
                        String virtualSku = lclTrialShippingData.getDestinationSku();
                        String destinationWarehouse = lclTrialShippingData.getDestinationWarehouse();
                        // 未装满的先装
                        for (var notFullData : copySmallVolumeDateRanges) {
                            LclDataSevenDayDTO resultSevenDayData = resultLclDataSevenDayDTOMap.get(notFullData);
                            if (lclTrialShippingData.getLclShippingNum() <= 0) {
                                //分完了
                                break;
                            }
                            if (resultSevenDayData.getTotalVolume().compareTo(minVolume) >= 0) {
                                continue;
                            }
                            // 判断是否有发货比例并需要发货
                            Map<String, Map<String, Double>> virtualRatio = resultSevenDayData.getVirtualRatio();
                            Map<String, Double> stringIntegerMap = virtualRatio.get(virtualSku);
                            if (CollectionUtil.isEmpty(stringIntegerMap)) {
                                continue;
                            } else {
                                Double ratio = stringIntegerMap.getOrDefault(destinationWarehouse, 0.0);
                                if (ratio == 0 || resultSevenDayData.getTotalVolume().compareTo(BigDecimal.ZERO) <= 0) {
                                    continue;
                                }
                                ;
                            }
                            // 判断当前体积还差多少够minVolume
                            BigDecimal notFullTotalVolume = resultSevenDayData.getTotalVolume();

                            // 计算需要多少箱
                            BigDecimal volumeDifference = minVolume.subtract(notFullTotalVolume);
                            int requiredCount = volumeDifference.divide(lclTrialShippingData.getVolume(), 0, RoundingMode.CEILING).intValue();

                            Integer containerLoad = lclTrialShippingData.getContainerLoad();
                            int countShippingNum = requiredCount * containerLoad;


                            Integer resultNum = lclShippingNum >= countShippingNum ? countShippingNum : lclShippingNum;
                            lclShippingNum = lclShippingNum >= countShippingNum ? lclShippingNum - countShippingNum : 0;
                            boolean isExist = false;
                            for (var resultData : resultSevenDayData.getSortedSevenList()) {
                                if (resultData.getDestinationSku().equals(virtualSku) && resultData.getLclFactoryFinishedId().equals(lclTrialShippingData.getLclFactoryFinishedId()) &&
                                        resultData.getShippingStartDate().equals(lclTrialShippingData.getShippingStartDate())) {
                                    if (resultNum > 0) {
                                        isExist = true;
                                        updateShippingNumAndHistory(lclTrialShippingData, -resultNum, ChangeTypeEnum.ORIGINAL_SHIPMENT2.getDesc() + warehouseMap.get(resultData.getDestinationWarehouse()), resultData.getShippingStartDate());
                                        updateShippingNumAndHistory(resultData, resultNum, "从" + warehouseMap.get(lclTrialShippingData.getDestinationWarehouse()) + "分出", lclTrialShippingData.getShippingStartDate());
                                        break;
                                    }
                                }
                            }
                            ;
                            if (!isExist) {
                                if (resultNum > 0) {
                                    LclTrialShippingInventoryDTO newTrialShipping = createNewTrialShipping(lclTrialShippingData, lclTrialShippingData.getShippingStartDate(), 0);
                                    newTrialShipping.setDestinationWarehouse(notFullData);
                                    updateShippingNumAndHistory(lclTrialShippingData, -resultNum, ChangeTypeEnum.ORIGINAL_SHIPMENT2.getDesc() + warehouseMap.get(newTrialShipping.getDestinationWarehouse()), lclTrialShippingData.getShippingStartDate());
                                    updateShippingNumAndHistory(newTrialShipping, resultNum, "从" + warehouseMap.get(lclTrialShippingData.getDestinationWarehouse()) + "分出", lclTrialShippingData.getShippingStartDate());
                                    resultSevenDayData.getSortedSevenList().add(newTrialShipping);
                                }
                            }
                            resultSevenDayData.setTotalVolume(resultSevenDayData.getTotalVolume().add(
                                    new BigDecimal(resultNum).divide(new BigDecimal(lclTrialShippingData.getContainerLoad()), 0, RoundingMode.CEILING).multiply(lclTrialShippingData.getVolume())));
                        }

                        // 没有缺口量，根据比例给到当前还有发货的仓
                        if (lclShippingNum > 0) {
                            Map<String, LclDataSevenDayDTO> collect = resultLclDataSevenDayDTOMap.entrySet().stream()
                                    .filter(entry -> {
                                        // 判断是否有发货比例并需要发货
                                        Map<String, Map<String, Double>> virtualRatio = entry.getValue().getVirtualRatio();
                                        Map<String, Double> stringIntegerMap = virtualRatio.get(virtualSku);
                                        if (CollectionUtil.isEmpty(stringIntegerMap)) {
                                            return false;
                                        } else {
                                            Double ratio = stringIntegerMap.getOrDefault(destinationWarehouse, 0.0);
                                            if (ratio == 0 || entry.getValue().getTotalVolume().compareTo(BigDecimal.ZERO) <= 0) {
                                                return false;
                                            }
                                            ;
                                        }
                                        return entry.getValue().getTotalVolume().compareTo(minVolume) >= 0 &&
                                                !entry.getKey().equals(warehouseId);
                                    })
                                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                            Map<String, Double> virtualVolumeMap = new HashMap<>();
                            for (var virtualVolume : collect.entrySet()) {
                                LclDataSevenDayDTO value = virtualVolume.getValue();
                                Map<String, Double> virtualRatio = value.getVirtualRatio().getOrDefault(virtualSku, new HashMap<>());
                                Double warehouseRatio = virtualRatio.getOrDefault(virtualVolume.getKey(), 0.0);
                                if (warehouseRatio > 0){
                                    virtualVolumeMap.put(virtualVolume.getKey(), warehouseRatio);
                                }
                            }
                            // 计算每个仓分到的数量
                            Map<String, Integer> warehouseNumMap = distributeByVolumeRatio(virtualVolumeMap, lclShippingNum, lclTrialShippingData.getContainerLoad());

                            for (var warehouseData : warehouseNumMap.entrySet()) {
                                Integer value = warehouseData.getValue();
                                if (value > 0) {
                                    LclDataSevenDayDTO newLclDataSevenDayDTO = resultLclDataSevenDayDTOMap.get(warehouseData.getKey());
                                    boolean isExist = false;
                                    for (var resultData : newLclDataSevenDayDTO.getSortedSevenList()) {
                                        if (resultData.getDestinationSku().equals(virtualSku) && resultData.getLclFactoryFinishedId().equals(lclTrialShippingData.getLclFactoryFinishedId()) &&
                                                resultData.getShippingStartDate().equals(lclTrialShippingData.getShippingStartDate())) {
                                            isExist = true;
                                            updateShippingNumAndHistory(lclTrialShippingData, -value, ChangeTypeEnum.ORIGINAL_SHIPMENT2.getDesc() + warehouseMap.get(resultData.getDestinationWarehouse()), resultData.getShippingStartDate());
                                            updateShippingNumAndHistory(resultData, value, "从" + warehouseMap.get(lclTrialShippingData.getDestinationWarehouse()) + "分出", lclTrialShippingData.getShippingStartDate());
                                            break;
                                        }
                                    }
                                    ;
                                    if (!isExist) {
                                        LclTrialShippingInventoryDTO newTrialShipping = createNewTrialShipping(lclTrialShippingData, lclTrialShippingData.getShippingStartDate(), 0);
                                        newTrialShipping.setDestinationWarehouse(warehouseData.getKey());
                                        updateShippingNumAndHistory(lclTrialShippingData, -value, ChangeTypeEnum.ORIGINAL_SHIPMENT2.getDesc() + warehouseMap.get(newTrialShipping.getDestinationWarehouse()), lclTrialShippingData.getShippingStartDate());
                                        updateShippingNumAndHistory(newTrialShipping, value, "从" + warehouseMap.get(lclTrialShippingData.getDestinationWarehouse()) + "分出", lclTrialShippingData.getShippingStartDate());
                                        newLclDataSevenDayDTO.getSortedSevenList().add(newTrialShipping);
                                    }
                                    newLclDataSevenDayDTO.setTotalVolume(newLclDataSevenDayDTO.getTotalVolume().add(
                                            new BigDecimal(value).divide(new BigDecimal(lclTrialShippingData.getContainerLoad()), 0, RoundingMode.CEILING).multiply(lclTrialShippingData.getVolume())));

                                }
                            }
                        }
                    }

                }
                for (var value : resultLclDataSevenDayDTOMap.entrySet()) {
                    newResultLclTrialList2.addAll(value.getValue().getSortedSevenList());
                }

            }

            var otherDataList = lclFinishedInventoryList.stream().filter(i ->
                            DateUtil.parse(i.getShippingStartDate()).after(DateUtil.parse(shippingEndDate)))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(otherDataList)) {
                newResultLclTrialList2.addAll(otherDataList);
            }

        }
        // 过滤不发货数据，汇总历史记录
        var filteredList = newResultLclTrialList2.stream()
                .filter(dto -> dto.getLclShippingNum() > 0)
                .map(dto -> {
                    var groupedHistory = dto.getShippingNumHistoryDTOS().stream()
                            .collect(Collectors.groupingBy(
                                    history -> "%s_%s".formatted(history.getShippingStartDate(), history.getChangeType()),
                                    Collectors.summingInt(LclShippingNumHistoryDTO::getChangeValue)
                            ));

                    dto.setShippingNumHistoryDTOS(
                            groupedHistory.entrySet().stream()
                                    .map(entry -> {
                                        var keys = entry.getKey().split("_");
                                        return new LclShippingNumHistoryDTO(
                                                null,
                                                keys[0],
                                                entry.getValue(),
                                                keys[1]

                                        );
                                    })
                                    .toList()
                    );
                    return dto;
                })
                .toList();


        Map<String, List<LclTrialShippingInventoryDTO>> groupedByFactory = filteredList.stream()
                .collect(Collectors.groupingBy(
                        LclTrialShippingInventoryDTO::getLclFactoryFinishedId
                ));

        // 删除原整理数据
        lclConsolidationTrialShippingInventoryRepository.deleteByLclRecordId(form.getLclRecordId());
        lclConsolidationFinishedInventoryRepository.deleteByLclRecordId(form.getLclRecordId());
        log.info("第三阶段：插入数据");
        for (Map.Entry<String, List<LclTrialShippingInventoryDTO>> val : groupedByFactory.entrySet()) {
            List<LclTrialShippingInventoryDTO> lclTrialShippingList = val.getValue();
            int sum = lclTrialShippingList.stream()
                    .filter(dto -> {
                        Date startDate = DateUtil.parse(dto.getShippingStartDate());
                        Date rangeStart = DateUtil.parse(shippingStartDate);
                        Date rangeEnd = DateUtil.parse(shippingEndDate);
                        return dto.getLclShippingNum() > 0 &&
                                !startDate.before(rangeStart) &&
                                !startDate.after(rangeEnd);
                    }).mapToInt(LclTrialShippingInventoryDTO::getLclShippingNum).sum();
            LclTrialShippingInventoryDTO lclFirst = lclTrialShippingList.getFirst();
            var lclConsolidationFinishedInventoryDO = LclConsolidationFinishedInventoryDO.builder()
                    .shipmentCode(lclFirst.getShipmentCode())
                    .contractCode(lclFirst.getContractCode())
                    .factoryFinishedDate(DateUtil.parse(lclFirst.getFactoryFinishedDate()))
                    .lclRecordId(form.getLclRecordId())
                    .virtualSkuId(lclFirst.getVirtualSkuId())
                    .isOldStatus(lclFirst.getIsOldStatus())
                    .productSnapshotId(lclFirst.getProductSnapshotId())
                    .factoryShippingPackageNum(lclFirst.getFactoryShippingPackageNum())
                    .factoryRemainNum(lclFirst.getFactoryShippingPackageNum() - sum)
                    .originalShippingNum(lclFirst.getOriginalShippingNum())
                    .deliveryType(lclFirst.getDeliveryType())
                    .remarks(lclFirst.getRemarks())
                    .isForeignFlag(lclFirst.getIsForeignFlag())
                    .build();

            lclConsolidationFinishedInventoryRepository.save(lclConsolidationFinishedInventoryDO);
            String lclConsolidationFinId = lclConsolidationFinishedInventoryDO.getId();

            for (var lclTrial : lclTrialShippingList) {
                var LclConsolidationTrial = LclConsolidationTrialShippingInventoryDO.builder()
                        .shippingStartDate(DateUtil.parse(lclTrial.getShippingStartDate()))
                        .packageNum(lclTrial.getPackageNum())
                        .destinationWarehouse(lclTrial.getDestinationWarehouse())
                        .consolidationFactoryFinishedId(lclConsolidationFinId)
                        .shippingNum(lclTrial.getShippingNum())
                        .lclShippingNum(lclTrial.getLclShippingNum())
                        .isChange(lclTrial.getIsChange())
                        .isPackageFull(lclTrial.getLclShippingNum() % lclTrial.getContainerLoad() == 0 ? IsPackageFullEnum.FULL.getCode() : IsPackageFullEnum.NOT_FULL.getCode())
                        .remarks(lclTrial.getTrialRemarks())
                        .build();
                lclConsolidationTrialShippingInventoryRepository.save(LclConsolidationTrial);
                String lclConsolidationTrialId = LclConsolidationTrial.getId();
                var shippingNumHistoryDTOS = lclTrial.getShippingNumHistoryDTOS();

                if (CollectionUtil.isNotEmpty(shippingNumHistoryDTOS)) {
                    // 先按changeType排序
                    // 再按shippingStartDate排序
                    List<LclShippingNumHistoryDTO> sortedList = new ArrayList<>(shippingNumHistoryDTOS);
                    sortedList.sort(
                            Comparator.comparing(LclShippingNumHistoryDTO::getChangeType, Comparator.nullsLast(String::compareTo))
                                    .thenComparing(LclShippingNumHistoryDTO::getShippingStartDate, Comparator.nullsLast(String::compareTo)));
                    shippingNumHistoryDTOS = sortedList;
                    for (var history : shippingNumHistoryDTOS) {
                        lclShippingNumHistoryRepository.save(LclShippingNumHistoryDO.builder()
                                .lclConsolidationTsiId(lclConsolidationTrialId)
                                .shippingStartDate(DateUtil.parse(history.getShippingStartDate().equals("null") ? null : history.getShippingStartDate()))
                                .changeValue(history.getChangeValue())
                                .changeType(history.getChangeType())
                                .build());
                    }
                }
            }
        }
        log.info("第三阶段：插入数据结束");

        // 加急与不发货数据
        List<LclFinishedInventoryBO> splitList = batchSplit.get(false);
        if (CollectionUtil.isNotEmpty(splitList)) {
            for (var split : splitList) {
                var lclConsolidationFinishedInventoryDO = LclConsolidationFinishedInventoryDO.builder()
                        .shipmentCode(split.getShipmentCode())
                        .contractCode(split.getContractCode())
                        .factoryFinishedDate(DateUtil.parse(split.getFactoryFinishedDate()))
                        .lclRecordId(form.getLclRecordId())
                        .virtualSkuId(split.getVirtualSkuId())
                        .isOldStatus(split.getIsOldStatus())
                        .productSnapshotId(split.getProductSnapshotId())
                        .factoryShippingPackageNum(split.getFactoryShippingPackageNum())
                        .factoryRemainNum(split.getFactoryRemainNum())
                        .originalShippingNum(split.getFactoryShippingPackageNum() - split.getFactoryRemainNum())
                        .deliveryType(split.getDeliveryType())
                        .remarks(split.getRemarks())
                        .isForeignFlag(split.getIsForeignFlag())
                        .build();

                lclConsolidationFinishedInventoryRepository.save(lclConsolidationFinishedInventoryDO);
                List<LclTrialShippingInventoryDTO> lclTrialDTOList = split.getLclTrialDTOList();
                if (CollectionUtil.isNotEmpty(lclTrialDTOList)) {
                    for (var lclTrial : lclTrialDTOList) {
                        var LclConsolidationTrial = LclConsolidationTrialShippingInventoryDO.builder()
                                .shippingStartDate(DateUtil.parse(lclTrial.getShippingStartDate()))
                                .packageNum(lclTrial.getPackageNum())
                                .destinationWarehouse(lclTrial.getDestinationWarehouse())
                                .consolidationFactoryFinishedId(lclConsolidationFinishedInventoryDO.getId())
                                .shippingNum(lclTrial.getShippingNum())
                                .lclShippingNum(lclTrial.getShippingNum())
                                .isChange(IsChangeEnum.UNCHANGED.getCode())
                                .isPackageFull(lclTrial.getShippingNum() % split.getContainerLoad() == 0 ? IsPackageFullEnum.FULL.getCode() : IsPackageFullEnum.NOT_FULL.getCode())
                                .build();
                        lclConsolidationTrialShippingInventoryRepository.save(LclConsolidationTrial);
                    }
                }
            }
        }
        lclConsolidationRecordRepository.updateById(LclConsolidationRecordDO.builder()
                .id(form.getLclRecordId())
                .lclStatus(LclStatusEnum.PENDING_CONTAINER.getCode())
                .build());
        stringRedisTemplate.delete(EDIT_LCL_DATA_CLEAN_LOCK + form.getLclRecordId());
    }


    private void updateShippingNumAndHistory(LclTrialShippingInventoryDTO inventory,
                                             Integer changeValue,
                                             String changeType,
                                             String shippingStartDate) {
        inventory.setLclShippingNum(inventory.getLclShippingNum() + changeValue);
        inventory.setIsChange(IsChangeEnum.CHANGED.getCode());
        LclShippingNumHistoryDTO historyDTO = LclShippingNumHistoryDTO.builder()
                .changeType(changeType)
                .changeValue(changeValue)
                .shippingStartDate(shippingStartDate)
                .build();

        List<LclShippingNumHistoryDTO> historyList = inventory.getShippingNumHistoryDTOS();

        historyList.add(historyDTO);
        inventory.setShippingNumHistoryDTOS(historyList);
    }

    private LclTrialShippingInventoryDTO createNewTrialShipping(LclTrialShippingInventoryDTO source,
                                                                String shippingStartDate,
                                                                Integer lclShippingNum) {
        return LclTrialShippingInventoryDTO.builder()
                .shippingStartDate(shippingStartDate)
                .packageNum(source.getPackageNum())
                .destinationWarehouse(source.getDestinationWarehouse())
                .factoryShippingPackageNum(source.getFactoryShippingPackageNum())
                .lclFactoryFinishedId(source.getLclFactoryFinishedId())
                .originalShippingNum(source.getOriginalShippingNum())
                .containerLoad(source.getContainerLoad())
                .virtualSkuId(source.getVirtualSkuId())
                .isOldStatus(source.getIsOldStatus())
                .productSnapshotId(source.getProductSnapshotId())
                .shippingNum(0)
                .lclShippingNum(lclShippingNum)
                .isPackageFull(source.getIsPackageFull())
                .factoryFinishedDate(source.getFactoryFinishedDate())
                .destinationSku(source.getDestinationSku())
                .selfSkuId(source.getSelfSkuId())
                .caseLength(source.getCaseLength())
                .caseWidth(source.getCaseWidth())
                .caseHeight(source.getCaseHeight())
                .contractCode(source.getContractCode())
                .volume(source.getVolume())
                .shippingNumHistoryDTOS(new ArrayList<>())
                .shippingRatio(source.getShippingRatio())
                .shipmentCode(source.getShipmentCode())
                .deliveryType(source.getDeliveryType())
                .remarks(source.getRemarks())
                .shippingRatio(source.getShippingRatio())
                .build();
    }

    // 辅助类来存储分配信息
    private static class AllocationItem {
        LclTrialShippingInventoryDTO dto;
        double ratio;

        AllocationItem(LclTrialShippingInventoryDTO dto, double ratio) {
            this.dto = dto;
            this.ratio = ratio;
        }

        double getRatio() {
            return ratio;
        }
    }

    @Override
    public List<UserInteriorVO> getUserList() {
        return sysUserInteriorService.getUserList();
    }

    @Override
    public void exportDeliveryPlan(LclConsolidationForm form) {
        try {
            var lclRecordDO = lclConsolidationRecordRepository.getById(form.getLclRecordId());
            if (ObjectUtil.isEmpty(lclRecordDO)) {
                stringRedisTemplate.delete(EXPORT_DELIVERY_LOCK + form.getShippingRecordId());
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "装柜记录不存在");
            }
            deliveryPurchaseService.exportDeliveryInfo(DeliveryPurchaseForm.builder()
                    .recordId(form.getShippingRecordId())
                    .shippingStartDate(DateUtil.format(lclRecordDO.getShippingStartDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN))
                    .shippingEndDate(DateUtil.format(lclRecordDO.getShippingEndDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN)).build());
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRecordStatus(String lclRecordId, LclStatusEnum lclStatus) {
        // 获取当前记录
        LclConsolidationRecordDO currentRecord = lclConsolidationRecordRepository.getById(lclRecordId);
        if (currentRecord == null) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "装柜记录不存在");
        }
        // 状态校验
        String currentStatus = currentRecord.getLclStatus();

        switch (lclStatus) {
            // 取消操作校验
            case CANCELLED -> lclConsolidationRecordRepository.updateById(LclConsolidationRecordDO.builder()
                    .id(lclRecordId)
                    .lclStatus(lclStatus.getCode())
                    .build());
            case DELETE -> {
                // 删除操作校验
                boolean canDelete = LclStatusEnum.INVALID.getCode().equals(currentStatus)
                        || LclStatusEnum.CANCELLED.getCode().equals(currentStatus);
                if (!canDelete) {
                    throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "只有作废或已取消状态才能删除");
                }
                lclContainerDetailRepository.deleteByLclRecordId(lclRecordId);
                containerInfoRepository.deleteByLclRecordId(lclRecordId);
                lclTrialShippingInventoryRepository.deleteByLclRecordId(lclRecordId);
                lclFinishedInventoryRepository.deleteByLclRecordId(lclRecordId);
                lclConsolidationTrialShippingInventoryRepository.deleteByLclRecordId(lclRecordId);
                lclConsolidationFinishedInventoryRepository.deleteByLclRecordId(lclRecordId);
                lclConsolidationRecordRepository.delete(lclRecordId);
            }
            case SAVED -> {
                if (!PRETENDING_SAVE.getCode().equals(currentStatus)) {
                    throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "只有待保存状态才能保存");
                }
                List<LclShipmentCategoryDto> shipmentCategoryList = containerInfoRepository.aggShipmentTypeByRecordId(lclRecordId);
                if(CollectionUtil.isNotEmpty(shipmentCategoryList)) {
                    throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "还有未生成货件号的柜子");
                }

                lclConsolidationRecordRepository.updateById(LclConsolidationRecordDO.builder()
                        .id(lclRecordId)
                        .lclStatus(lclStatus.getCode())
                        .build());
            }
            default -> {}
        }
    }

    @Override
    public DeliveryRecordPurchaseListVo downloadExport(LclConsolidationForm form) {
        return deliveryPurchaseService.downloadExport(DeliveryPurchaseForm.builder().recordId(form.getShippingRecordId()).build());
    }

    /**
     * 将数据按照指定天数间隔分组到二维数组
     *
     * @param dataList     要分组的数据列表
     * @param endDateStr   结束日期 (格式: yyyy-MM-dd)
     * @param intervalDays 每组的天数间隔，默认为7天
     * @return 按时间范围分组的二维数组
     */
    private List<List<LclTrialShippingInventoryDTO>> groupDataByTimeRange(
            List<LclTrialShippingInventoryDTO> dataList,
            String endDateStr,
            int intervalDays) {

        // 确保数据列表不为空
        if (CollectionUtil.isEmpty(dataList)) {
            return new ArrayList<>();
        }

        // 获取开始和结束时间
        String startDateStr = dataList.getFirst().getShippingStartDate();
        Date startDate = DateUtil.parse(startDateStr);
        Date endDate = DateUtil.parse(endDateStr);

        // 创建二维数组存储结果
        List<List<LclTrialShippingInventoryDTO>> result = new ArrayList<>();

        // 按指定天数分区间
        while (!startDate.after(endDate)) {
            Date rangeEnd = DateUtil.offsetDay(startDate, intervalDays - 1);
            if (rangeEnd.after(endDate)) {
                rangeEnd = endDate;
            }

            // 过滤在当前时间范围内的数据
            final Date finalStartDate = startDate;
            final Date finalRangeEnd = rangeEnd;

            List<LclTrialShippingInventoryDTO> rangeData = dataList.stream()
                    .filter(dto -> {
                        Date dtoDate = DateUtil.parse(dto.getShippingStartDate());
                        return !dtoDate.before(finalStartDate) && !dtoDate.after(finalRangeEnd);
                    })
                    .collect(Collectors.toList());

            // 将当前区间的数据添加到结果集
            if (CollectionUtil.isNotEmpty(rangeData)) {
                result.add(rangeData);

            }
            // 移动到下一个区间
            startDate = DateUtil.offsetDay(rangeEnd, 1);

        }

        return result;
    }

    /**
     * 重新排序Map，按照体积大小和头程时间进行排序
     * 首先将数据分为两组：小于最小体积的和大于等于最小体积的
     * 然后分别按照头程时间从小到大排序
     * 最后先放入小于最小体积的数据，再放入大于等于最小体积的数据
     *
     * @param originalMap         原始Map，key为仓库ID，value为七天内的数据
     * @param minVolume           最小体积阈值
     * @param headShippingDaysMap 头程时间Map，key为仓库ID，value为头程天数
     * @return 重新排序后的LinkedHashMap
     */
    private Map<String, LclDataSevenDayDTO> reorderMapByVolumeAndHeadDays(
            Map<String, LclDataSevenDayDTO> originalMap,
            BigDecimal minVolume,
            TreeMap<String, Integer> headShippingDaysMap) {

        Map<String, LclDataSevenDayDTO> reorderedMap = new LinkedHashMap<>();

        // 1. 将条目分为两组：小于minVolume和大于等于minVolume
        List<Map.Entry<String, LclDataSevenDayDTO>> entriesUnderMinVolume = new ArrayList<>();
        List<Map.Entry<String, LclDataSevenDayDTO>> entriesAboveMinVolume = new ArrayList<>();

        for (Map.Entry<String, LclDataSevenDayDTO> entry : originalMap.entrySet()) {
            if (entry.getValue().getTotalVolume().compareTo(minVolume) < 0) {
                entriesUnderMinVolume.add(entry);
            } else {
                entriesAboveMinVolume.add(entry);
            }
        }

        // 2. 分别对两组按照头程时间从小到大排序
        Comparator<Map.Entry<String, LclDataSevenDayDTO>> headShippingDaysComparator =
                (entry1, entry2) -> {
                    String warehouseId1 = entry1.getKey();
                    String warehouseId2 = entry2.getKey();

                    Integer days1 = headShippingDaysMap.getOrDefault(warehouseId1, Integer.MAX_VALUE);
                    Integer days2 = headShippingDaysMap.getOrDefault(warehouseId2, Integer.MAX_VALUE);

                    return days1.compareTo(days2);
                };

        entriesUnderMinVolume.sort(headShippingDaysComparator);
        entriesAboveMinVolume.sort(headShippingDaysComparator);

        // 3. 先添加小于minVolume的数据，然后添加大于等于minVolume的数据
        for (Map.Entry<String, LclDataSevenDayDTO> entry : entriesUnderMinVolume) {
            reorderedMap.put(entry.getKey(), entry.getValue());
        }

        for (Map.Entry<String, LclDataSevenDayDTO> entry : entriesAboveMinVolume) {
            reorderedMap.put(entry.getKey(), entry.getValue());
        }

        return reorderedMap;
    }

    /**
     * 根据各仓库的体积比例分配发货数量
     *
     * @param virtualRatioMap 各仓库的体积，key为仓库ID，value为体积
     * @param lclShippingNum   需要分配的总发货数量
     * @param containerLoad    单箱容量
     * @return 分配结果，key为仓库ID，value为分配的数量
     */
    private Map<String, Integer> distributeByVolumeRatio(
            Map<String, Double> virtualRatioMap,
            int lclShippingNum,
            int containerLoad) {

        // 存储分配结果
        Map<String, Integer> result = new HashMap<>();

        // 计算总比例
        double totalRatio = virtualRatioMap.values().stream()
                .mapToDouble(Double::doubleValue)
                .sum();

        if (totalRatio <= 0) {
            return result; // 总比例为0，无法分配
        }
        // 计算各仓库的比例并排序（从小到大）
        List<Map.Entry<String, Double>> entries = virtualRatioMap.entrySet().stream()
                .map(entry -> {
                    String warehouseId = entry.getKey();
                    double ratio = entry.getValue() / totalRatio; // 归一化比例
                    return Map.entry(warehouseId, ratio);
                })
                .sorted(Map.Entry.comparingByValue())
                .collect(Collectors.toList());

        // 初始化结果Map
        for (Map.Entry<String, Double> entry : entries) {
            result.put(entry.getKey(), 0);
        }

        // 计算需要分配的箱数
        int boxesToDistribute = (int) Math.ceil((double) lclShippingNum / containerLoad);

        // 按照理论比例计算每个仓库应得的箱数
        Map<String, Double> theoreticalBoxes = new HashMap<>();
        for (Map.Entry<String, Double> entry : entries) {
            double theoreticalBoxCount = entry.getValue() * boxesToDistribute;
            theoreticalBoxes.put(entry.getKey(), theoreticalBoxCount);
        }

        // 按照箱子分配
        int remainingBoxes = boxesToDistribute;

        // 先将整箱分配给各个仓库
        for (Map.Entry<String, Double> entry : theoreticalBoxes.entrySet()) {
            int fullBoxes = (int) Math.floor(entry.getValue());
            result.put(entry.getKey(), fullBoxes * containerLoad);
            remainingBoxes -= fullBoxes;
        }

        // 计算各仓库的小数部分
        List<Map.Entry<String, Double>> sortedByFraction = theoreticalBoxes.entrySet().stream()
                .map(entry -> {
                    String warehouseId = entry.getKey();
                    double value = entry.getValue();
                    double fraction = value - Math.floor(value); // 获取小数部分
                    return Map.entry(warehouseId, fraction);
                })
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .collect(Collectors.toList());

        // 如果还有剩余箱子，按照小数部分从大到小分配
        if (remainingBoxes > 0) {
            for (int i = 0; i < remainingBoxes && i < sortedByFraction.size(); i++) {
                String warehouseId = sortedByFraction.get(i).getKey();
                result.put(warehouseId, result.get(warehouseId) + containerLoad);
            }
        }

        // 处理最后一箱（可能不满）
        int totalAllocated = result.values().stream().mapToInt(Integer::intValue).sum();
        if (totalAllocated > lclShippingNum && remainingBoxes > 0 && !sortedByFraction.isEmpty()) {
            // 最后一个分配的仓库需要调整
            String lastWarehouse = sortedByFraction.get(Math.min(remainingBoxes - 1, sortedByFraction.size() - 1)).getKey();
            result.put(lastWarehouse, result.get(lastWarehouse) - (totalAllocated - lclShippingNum));
        }

        return result;
    }
}




