package com.sales_server.model.file.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.utils.common.entity.MissionCenterVo;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.sales_server.infrastructures.entity.form.FileMissionForm;
import com.sales_server.infrastructures.entity.form.FileMissionPageForm;
import org.springframework.util.MultiValueMap;

/**
 * @Description 文件中心服务
 * <AUTHOR>
 * @Date 2024/5/9 10:36
 **/
public interface IFileCenterService {

    MultiValueMap<String, Object> putFile(byte[] byteArrayResource, String fileName, String key, String expireTime);

    void uploadFile(MultiValueMap<String, Object> httpEntity, RestTemplateUtils restTemplateUtil);

    String uploadFile(byte[] fileBytes, String fileName, String importStatus, String type);

    String uploadImportInfo(FileMissionForm form);

    String getSourceFilePath(String filePath);

    IPage<MissionCenterVo> getMissionPage(FileMissionPageForm form);

}
