package com.crafts_mirror.common.security.aspect;

import com.crafts_mirror.common.security.annotation.Retryable;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/10 16:39
 **/
@Aspect
@Component
public class RetryAspect {

    @Pointcut("@annotation(com.crafts_mirror.common.security.annotation.Retryable)")
    private void retryMethodCall(){}

    @Around("retryMethodCall()")
    public Object retry(ProceedingJoinPoint joinPoint) throws InterruptedException {
        // 获取重试次数和重试间隔
        Retryable retry = ((MethodSignature)joinPoint.getSignature()).getMethod().getAnnotation(Retryable.class);
        int maxRetryTimes = retry.retryTimes();
        int retryInterval = retry.retryInterval();
        TimeUnit retryTimeUnit = retry.retryTimeUnit();

        Throwable error = new RuntimeException();
        for (int retryTimes = 1; retryTimes <= maxRetryTimes; retryTimes++){
            try {
                return joinPoint.proceed();
            } catch (Throwable throwable) {
                error = throwable;
            }
            Thread.sleep(retryTimeUnit.toMillis(retryInterval));
        }
        throw new IllegalArgumentException("重试次数耗尽", error);
    }
}

