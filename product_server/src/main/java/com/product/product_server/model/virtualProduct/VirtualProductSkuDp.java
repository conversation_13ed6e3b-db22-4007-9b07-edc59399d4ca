package com.product.product_server.model.virtualProduct;

import com.crafts_mirror.utils.utils.PatternUtils;

import java.util.Objects;

public record VirtualProductSkuDp(String channel, String spu, String spuProductName, String virtualSku, String operator,
                                  VirtualProductSkuStatusDp virtualProductSkuStatusDp) {

    public VirtualProductSkuDp {
        Objects.requireNonNull(channel, "渠道不能为空");
        Objects.requireNonNull(spu, "父spu id不能为空");
        if(spu.length() > 50) {
            throw new IllegalArgumentException("父spu id长度超出限制");
        }
        Objects.requireNonNull(spuProductName, "父spu品名不能为空");
        Objects.requireNonNull(operator, "运营不能为空");
        Objects.requireNonNull(virtualSku, "虚拟sku不能为空");
        if(!PatternUtils.UPPERCASE_LOWERCASE_NUMBER_DOT_PATTERN.matcher(virtualSku).matches()) {
            throw new IllegalArgumentException("虚拟sku只能英文、数字和符号的组合");
        }
    }
}
