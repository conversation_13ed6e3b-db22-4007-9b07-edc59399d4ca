package com.purchase.purchase_server.entity.dto.factory;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/15
 **/
@Data
public class FactoryInfoQuery implements Serializable {


    @Serial
    private static final long serialVersionUID = 5556955446480805113L;
    @NotNull
    private String factoryInfoId;
    /**
     * 供应商代码
     */
    private String factoryCode;
    /**
     * 供应商名称
     */
    private String factoryName;
}
