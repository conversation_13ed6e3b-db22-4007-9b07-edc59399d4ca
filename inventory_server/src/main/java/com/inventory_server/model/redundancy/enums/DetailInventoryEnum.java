package com.inventory_server.model.redundancy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/23
 **/
@AllArgsConstructor
@Getter
public enum DetailInventoryEnum {
    FBA("FBA仓","fba"),
    WESTERN_AMERICA("美西仓","westernAmerica"),
    SOUTHEASTERN_UNITED_STATES("美东南仓","southeasternUnitedStates"),
    CENTRAL_AMERICA("美中仓","centralAmerica"),
    EASTERN_UNITED_STATES("美东仓","easternUnitedStates"),
    SOUTHERN_UNITED_STATES("美南仓","southernUnitedStates"),
    CG("CG仓","cg");

    private final String code;
    private final String desc;

    public static List<String> getCodeList() {
        return Arrays.stream(DetailInventoryEnum.values())
                .map(DetailInventoryEnum::getCode).collect(Collectors.toList());
    }
}
