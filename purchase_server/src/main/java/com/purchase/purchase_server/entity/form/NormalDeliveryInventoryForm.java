package com.purchase.purchase_server.entity.form;

import com.purchase.purchase_server.entity.dto.NormalDeliveryInventorySaveDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/23 11:53
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NormalDeliveryInventoryForm implements Serializable {
    private List<NormalDeliveryInventorySaveDto> list;
}
