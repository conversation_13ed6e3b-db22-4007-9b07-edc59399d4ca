package com.purchase.purchase_server.service.replenishment.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.*;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum;
import com.crafts_mirror.utils.utils.DateUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.google.common.base.Stopwatch;
import com.purchase.purchase_server.consts.SpecialDeliveryCollection;
import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.bo.*;
import com.purchase.purchase_server.entity.dataObject.*;
import com.purchase.purchase_server.entity.dto.*;
import com.purchase.purchase_server.entity.dto.delivery.MockTableCalDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectBaseParamDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectWatchBoardWarehouseDto;
import com.purchase.purchase_server.entity.dto.delivery.TrialCalReplenishmentDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishWarehouseInventoryDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentProjectSaveDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentTrialConfirmQuantityDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentTrialWatchBoardDto;
import com.purchase.purchase_server.entity.excelObject.RepExportNotRestockExcel;
import com.purchase.purchase_server.entity.excelObject.RepExportsheet1InfoExcel;
import com.purchase.purchase_server.entity.form.*;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.*;
import com.purchase.purchase_server.enums.*;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.model.purchase.ReplenishDeliveryRangeAndNumDp;
import com.purchase.purchase_server.repository.dataRepository.*;
import com.purchase.purchase_server.repository.interiorRepository.WarehouseRepository;
import com.purchase.purchase_server.service.ICalRemainInventoryService;
import com.purchase.purchase_server.service.ICommonConsumptionService;
import com.purchase.purchase_server.service.channel.IChannelInfoService;
import com.purchase.purchase_server.service.replenishment.*;
import com.purchase.purchase_server.service.shipping.IShippingCalculationService;
import com.purchase.purchase_server.utils.MockResultUtils;
import com.purchase.purchase_server.utils.commonUtils.WarehouseSortUtil;
import com.purchase.purchase_server.utils.easyExcelUtil.ExcelWidthStyleStrategy;
import com.purchase.purchase_server.utils.easyExcelUtil.RepSimpleCommonExcelMergeUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.date.DateField.DAY_OF_YEAR;
import static com.crafts_mirror.utils.constant.DateFormatConstant.*;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.OPERATOR_CONFIRMED_REASON;
import static com.crafts_mirror.utils.constant.SystemConstant.INVENTORY_SYSTEM_FOREIGN_AND_FACTORY;
import static com.purchase.purchase_server.consts.SpecialDeliveryCollection.TEST_SAMPLE_DELIVERY_SET;
import static com.purchase.purchase_server.enums.PurchaseNumTypeEnum.*;
import static com.purchase.purchase_server.enums.ShippingSourceTypeEnum.DISCOUNT_REPLENISH;
import static com.purchase.purchase_server.enums.ShippingSourceTypeEnum.REPLENISH;
import static java.math.RoundingMode.HALF_UP;

/**
 * <AUTHOR>
 * @description 针对表【cm_replenishment_trial_purchase_inventory】的数据库操作Service实现
 * @createDate 2024-01-17 10:45:51
 */
@Service
@Slf4j
public class ReplenishmentTrialPurchaseInventoryServiceImpl implements IReplenishmentTrialPurchaseInventoryService {
    @Resource
    private ReplenishmentTrialPurchaseInventoryRepositoryImpl repPurchaseInventoryRepository;
    @Resource
    private ReplenishmentVirtualSkuPurchaseRepositoryImpl repVirtualSkuPurchaseRepository;

    @Resource
    private ReplenishmentProjectRepositoryImpl replenishmentProjectRepository;

    @Resource(name = "replenishmentRulesService")
    private IReplenishmentOrShippingRulesService replenishmentRulesService;

    @Resource
    @Lazy
    private IReplenishmentProjectService replenishmentProjectService;

    @Resource(name = "noRepeatableCalculationServiceImpl")
    private IShippingCalculationService shippingCalculationService;

    @Resource
    private FactoryFinishedInventoryRepositoryImpl factoryFinishedInventoryRepository;
    @Resource
    private OperationLogRepository operationLogRepository;

    @Resource
    private ProductSnapshotRepositoryImpl productSnapshotRepository;

    @Resource
    private ReplenishmentRecordRepositoryImpl replenishmentRecordRepositoryImpl;

    @Resource
    private ICalRemainInventoryService calRemainInventoryService;

    @Resource
    protected RestTemplate restTemplate;

    @Resource
    private WarehouseRepository warehouseRepository;

    @Resource
    private ReplenishmentForeignInventoryRepositoryImpl replenishmentForeignInventoryRepository;

    @Resource
    private ReplenishmentTrialPurchaseInventoryCopyRepositoryImpl reCopy;

    @Resource
    private TrialShippingInventoryRepositoryImpl trialShippingInventoryRepository;

    @Resource
    private ICommonConsumptionService commonConsumptionService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IReplenishmentWatchBoardService replenishmentWatchBoardService;

    @Resource
    private IChannelInfoService channelInfoService;

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);

    DateTimeFormatter hyphenFormatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_HYPHEN);

    @Resource
    private ISysUserInteriorService sysUserInteriorService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveAdvicePurchase(String recordId, String replenishmentStatus) {
        if (!ReplenishmentStatusEnum.contains(replenishmentStatus)) {
            throw new RuntimeException("状态码错误");
        }
        if (ReplenishmentStatusEnum.SAVED.getCode().equals(replenishmentStatus)) {
            ReplenishmentRecordDO recordDO = replenishmentRecordRepositoryImpl.getById(recordId);
            if (ReplenishmentStatusEnum.ABROGATE.getCode().equals(recordDO.getReplenishmentStatus())) {
                throw new RuntimeException("保存失败，该计划已被作废");
            }
            var trialDOList = repPurchaseInventoryRepository.checkConfirmedReason(ReplenishmentProjectForm.builder()
                    .id(recordId)
                    .advicePurchaseStartDate(DateUtil.format(recordDO.getAdvicePurchaseStartDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN))
                    .advicePurchaseEndDate(DateUtil.format(recordDO.getAdvicePurchaseEndDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN)).build());

            if (CollectionUtil.isNotEmpty(trialDOList)) {
                throw new RuntimeException("还有运营未确认");
            }
        }

        return replenishmentRecordRepositoryImpl.update(new ReplenishmentRecordDO(), new UpdateWrapper<ReplenishmentRecordDO>().lambda()
                .set(ReplenishmentRecordDO::getReplenishmentStatus, replenishmentStatus)
                .eq(ReplenishmentRecordDO::getId, recordId));
    }

    @Override
    public void checkRecordData(String recordId, String replenishmentStatus) {
        if (!ReplenishmentStatusEnum.contains(replenishmentStatus)) {
            throw new RuntimeException("状态码错误");
        }
        if (ReplenishmentStatusEnum.SAVED.getCode().equals(replenishmentStatus)) {
            ReplenishmentRecordDO byId = replenishmentRecordRepositoryImpl.getById(recordId);
            if (ReplenishmentStatusEnum.ABROGATE.getCode().equals(byId.getReplenishmentStatus())) {
                throw new RuntimeException("保存失败，该计划已被作废");
            }
        }
    }

    // 导出老版本
    //@Override
    //public void exportRepInfo(ReplenishmentProjectForm form, HttpServletResponse response) {
    //    List<RepExcelProjectSkuBO> excelRepProjectList = replenishmentProjectRepository.selectExcelRepProjectData(form);
    //    List<RepExportsheet1InfoExcel> sheetInfoExcelList = new ArrayList<>();
    //    if (CollectionUtil.isNotEmpty(excelRepProjectList)) {
    //        Map<String, RepExcelProjectSkuBO> repProjectMap = excelRepProjectList.stream()
    //                .collect(Collectors.toMap(RepExcelProjectSkuBO::getVirtualSku, repExcelProjectSkuBO -> repExcelProjectSkuBO));
    //        List<String> virtualSkuIds = excelRepProjectList.stream().map(RepExcelProjectSkuBO::getVirtualSkuId).collect(Collectors.toList());
    //        var excelRepPurchaseInventoryList = replenishmentProjectRepository.selectExcelRepTrialPurchaseData(form, virtualSkuIds);
    //        // 理论数据
    //        var adviceList = excelRepPurchaseInventoryList.stream().filter(i -> i.getPurchaseNumType().equals(THEORETICAL.getCode())).toList();
    //        // 建议数据
    //        var actualList = excelRepPurchaseInventoryList.stream().filter(i -> i.getPurchaseNumType().equals(ADVICE.getCode())).toList();
    //        // 运营确认数据
    //        var operatorList = excelRepPurchaseInventoryList.stream().filter(i -> i.getPurchaseNumType().equals(OPERATOR.getCode())).toList();
    //
    //        // 将actualList转换为Map,以DestinationWarehouse和AdvicePurchaseDate为key
    //        Map<String, RepExcelTrialPurchaseBO> actualMap = actualList.stream()
    //                .collect(Collectors.toMap(
    //                        bo -> bo.getVirtualSkuId() + "_" + bo.getDestinationWarehouse() + "_" + bo.getAdvicePurchaseDate(),
    //                        bo -> bo,
    //                        (existing, replacement) -> existing  // 如果有重复,保留第一个
    //                ));
    //
    //        // 将operatorList转换为Map,以DestinationWarehouse和AdvicePurchaseDate为key
    //        Map<String, RepExcelTrialPurchaseBO> adviceMap = adviceList.stream()
    //                .collect(Collectors.toMap(
    //                        bo -> bo.getVirtualSkuId() + "_" + bo.getDestinationWarehouse() + "_" + bo.getAdvicePurchaseDate(),
    //                        bo -> bo,
    //                        (existing, replacement) -> existing  // 如果有重复,保留第一个
    //                ));
    //        // 合并列表
    //        List<RepExcelTrialPurchaseBO> exportInfoList = new ArrayList<>();
    //
    //        for (RepExcelTrialPurchaseBO operatorBO : operatorList) {
    //            String key = operatorBO.getVirtualSkuId() + "_" + operatorBO.getDestinationWarehouse() + "_" + operatorBO.getAdvicePurchaseDate();
    //            operatorBO.setOperationConfirmedNum(Double.valueOf(operatorBO.getAdvicePurchaseNum()));
    //            // 使用Optional来简化空值处理
    //            Optional.ofNullable(adviceMap.get(key))
    //                    .ifPresentOrElse(
    //                            advice -> operatorBO.setAdvicePurchaseNum(advice.getAdvicePurchaseNum()),
    //                            () -> operatorBO.setAdvicePurchaseNum(0)
    //                    );
    //
    //            Optional.ofNullable(actualMap.get(key))
    //                    .ifPresentOrElse(
    //                            actual -> operatorBO.setActualReplenishmentNum(Double.valueOf(actual.getAdvicePurchaseNum())),
    //                            () -> operatorBO.setActualReplenishmentNum(0.0)
    //                    );
    //            exportInfoList.add(operatorBO);
    //        }
    //
    //        var purchaseInventoryMap = exportInfoList.stream()
    //                .collect(Collectors.groupingBy(
    //                        RepExcelTrialPurchaseBO::getSelfSkuId, // 第一个分组依据：selfSkuId
    //                        Collectors.groupingBy(
    //                                RepExcelTrialPurchaseBO::getVirtualSku, // 第二个分组依据：virtualSku
    //                                TreeMap::new,
    //                                Collectors.toList()
    //                        )
    //                ));
    //
    //        List<String> selfSkuIdList = excelRepProjectList.stream().map(RepExcelProjectSkuBO::getSelfSkuId).toList();
    //        InventoryInfoQuery inventoryInfoQuery = new InventoryInfoQuery();
    //        inventoryInfoQuery.setSelfSkuIdList(selfSkuIdList);
    //
    //        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
    //        ResultDTO<InventoryFactoryAndForeignInfoDto> restResult = restTemplateUtil.post(
    //                inventoryInfoQuery,
    //                ResultDTO.class, INVENTORY_SYSTEM_FOREIGN_AND_FACTORY
    //        );
    //        String jsonObject = JSON.toJSONString(restResult.getData());
    //        InventoryFactoryAndForeignInfoDto inventoryFactoryAndForeignInfoDto = JSON.to(InventoryFactoryAndForeignInfoDto.class, jsonObject);
    //        Map<String, FactoryAndForeignInfoMapDto> factoryAndForeignInfoMap;
    //        if (inventoryFactoryAndForeignInfoDto != null) {
    //            factoryAndForeignInfoMap = inventoryFactoryAndForeignInfoDto.getFactoryAndForeignInfoMap();
    //        } else {
    //            factoryAndForeignInfoMap = new HashMap<>();
    //        }
    //
    //        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
    //        Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));
    //        Map<String, String> nameIdMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));
    //
    //        Map<String, String> confirmedReasonMap = JSON.parseObject(stringRedisTemplate.opsForValue().get(OPERATOR_CONFIRMED_REASON), new TypeReference<>() {
    //        });
    //        //运营
    //        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
    //        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));
    //
    //        for (var mapEntry : purchaseInventoryMap.entrySet()) {
    //            TreeMap<String, List<RepExcelTrialPurchaseBO>> virtualSkuMap = mapEntry.getValue();
    //            var factoryAndForeignInfoMapDto = factoryAndForeignInfoMap.get(mapEntry.getKey());
    //            if (factoryAndForeignInfoMapDto != null) {
    //                Map<String, String> virtualSkuByInventoryInfoId = factoryAndForeignInfoMapDto.getVirtualSkuByInventoryInfoId();
    //                Map<String, String> channelByInventoryInfoId = factoryAndForeignInfoMapDto.getChannelByInventoryInfoId();
    //                List<InventoryForeignRedundantInfoDto> foreignRedundantInfo = factoryAndForeignInfoMapDto.getForeignRedundantInfo();
    //                List<InventoryOnShippingRedundantBO> onShippingRedundantInfo = factoryAndForeignInfoMapDto.getOnShippingRedundantInfo();
    //                List<InventoryFactoryRedundantInfoDto> factoryRedundantInfo = factoryAndForeignInfoMapDto.getFactoryRedundantInfo();
    //
    //                Map<String, List<InventoryForeignRedundantInfoDto>> sortedForeignRedundant = foreignRedundantInfo.stream()
    //                        .peek(m -> m.setWarehouse(nameIdMap.get(m.getWarehouse())))
    //                        .collect(Collectors.groupingBy(InventoryForeignRedundantInfoDto::getInventoryInfoId));
    //                List<ForeignInventoryExportDto> foreignInventoryExportList = new ArrayList<>();
    //
    //                sortedForeignRedundant.forEach((inventoryInfoId, foreignRedundantList) -> {
    //                    ForeignInventoryExportDto dto = new ForeignInventoryExportDto();
    //                    String virtualSku = virtualSkuByInventoryInfoId.get(inventoryInfoId);
    //                    dto.setVirtualSku(virtualSku);
    //                    double foreignRedundantSum = 0.0;
    //                    Map<String, Double> foreignRedundant = new HashMap<>();
    //                    Map<String, Double> addForeignRedundant = new HashMap<>();
    //                    for (InventoryForeignRedundantInfoDto i : foreignRedundantList) {
    //                        foreignRedundantSum += i.getRedundantNum();
    //                        foreignRedundant.put(i.getWarehouse(), i.getRedundantNum());
    //                        addForeignRedundant.put(i.getWarehouse(), i.getRedundantNum());
    //                    }
    //                    dto.setChannel(channelByInventoryInfoId.get(inventoryInfoId));
    //                    dto.setForeignRedundantSum(foreignRedundantSum);
    //                    dto.setForeignRedundant(foreignRedundant);
    //                    dto.setAddForeignRedundant(addForeignRedundant);
    //                    foreignInventoryExportList.add(dto);
    //                });
    //
    //                onShippingRedundantInfo.forEach(i -> {
    //                    i.setVirtualSku(virtualSkuByInventoryInfoId.get(i.getInventoryInfoId()));
    //                    i.setAddRedundantNum(i.getRedundantNum());
    //                });
    //
    //                factoryRedundantInfo.forEach(i -> {
    //                    i.setVirtualSku(virtualSkuByInventoryInfoId.get(i.getInventoryInfoId()));
    //                    i.setAddRedundantNum(i.getRedundantNum());
    //                });
    //                // 使用流和lambda表达式排序
    //                List<InventoryFactoryRedundantInfoDto> sortedFactoryRedundant = factoryRedundantInfo.stream()
    //                        .sorted(Comparator.comparing((InventoryFactoryRedundantInfoDto o) -> {
    //                                    try {
    //                                        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_DATE_FORMAT_HYPHEN);
    //                                        return sdf.parse(o.getFactoryFinishedDate());
    //                                    } catch (Exception e) {
    //                                        throw new IllegalArgumentException(e);
    //                                    }
    //                                }).reversed()
    //                                .thenComparing(o -> o.getIsProduct().equals(FactoryPlanInfoEnum.PRODUCT.getCode()) ? 0 : 1))
    //                        .toList();
    //
    //                for (Map.Entry<String, List<RepExcelTrialPurchaseBO>> e : virtualSkuMap.entrySet()) {
    //                    String virtualSku = e.getKey();
    //                    List<RepExcelTrialPurchaseBO> purchaseInventoryList = e.getValue();
    //                    //头程时间
    //                    Map<String, Integer> headShippingDays = JSON.parseObject(repProjectMap.get(virtualSku).getHeadShippingDays(), new TypeReference<HashMap<String, Integer>>() {
    //                    });
    //                    //按照从小打到排序
    //                    LinkedHashMap<String, Integer> sortedMap = headShippingDays.entrySet()
    //                            .stream()
    //                            .sorted(Map.Entry.comparingByValue())
    //                            .collect(Collectors.toMap(
    //                                    Map.Entry::getKey,
    //                                    Map.Entry::getValue,
    //                                    (e1, e2) -> e1,
    //                                    LinkedHashMap::new
    //                            ));
    //                    List<RepExcelTrialPurchaseBO> sortedTrialPurchaseList = purchaseInventoryList.stream()
    //                            .sorted(Comparator.comparingInt(bo -> sortedMap.get(bo.getDestinationWarehouse())))
    //                            .toList();
    //                    Map<String, List<ForeignInventoryExportDto>> collectByChannel = new HashMap<>();
    //                    if (CollectionUtil.isNotEmpty(foreignInventoryExportList)) {
    //                        collectByChannel = foreignInventoryExportList.stream().collect(Collectors.groupingBy(ForeignInventoryExportDto::getChannel));
    //                    }
    //
    //                    for (RepExcelTrialPurchaseBO repBO : sortedTrialPurchaseList) {
    //                        RepExportsheet1InfoExcel sheet1InfoExcel = new RepExportsheet1InfoExcel();
    //                        StringBuilder foreignRedundantDetails = new StringBuilder();
    //                        StringBuilder onShippingRedundantDetails = new StringBuilder();
    //
    //                        StringBuilder productDetails = new StringBuilder();
    //                        StringBuilder inProductDetails = new StringBuilder();
    //
    //                        String channel = repBO.getChannel();
    //                        String destinationWarehouse = repBO.getDestinationWarehouse();
    //                        int advicePurchaseNum = repBO.getAdvicePurchaseNum();
    //                        if (CollectionUtil.isNotEmpty(collectByChannel) && advicePurchaseNum > 0) {
    //                            List<ForeignInventoryExportDto> lists = new ArrayList<>();
    //                            if (channelList.contains(channel)) {
    //                                for (String val : channelList) {
    //                                    List<ForeignInventoryExportDto> foreignInventoryExportDtos = collectByChannel.get(val);
    //                                    if (CollectionUtil.isNotEmpty(foreignInventoryExportDtos)) {
    //                                        lists.addAll(foreignInventoryExportDtos);
    //                                    }
    //                                }
    //
    //                                List<ForeignInventoryExportDto> sortedForeignInventoryList = lists.stream()
    //                                        .sorted((dto1, dto2) -> {
    //                                            Double value1 = dto1.getForeignRedundant() != null ? dto1.getForeignRedundant().get(destinationWarehouse) : null;
    //                                            Double value2 = dto1.getForeignRedundant() != null ? dto2.getForeignRedundant().get(destinationWarehouse) : null;
    //                                            return Comparator.nullsLast(Double::compareTo).compare(value2, value1);
    //                                        })
    //                                        .toList();
    //
    //                                for (ForeignInventoryExportDto dto : sortedForeignInventoryList) {
    //                                    if (!dto.getVirtualSku().equals(virtualSku)) {
    //                                        String foreignVirtualSku = dto.getVirtualSku();
    //                                        Map<String, Double> addForeignRedundant = dto.getAddForeignRedundant();
    //                                        Double foreignRedundantNum = dto.getAddForeignRedundant().getOrDefault(destinationWarehouse, 0.0);
    //                                        if (foreignRedundantNum != 0.0 && advicePurchaseNum != 0) {
    //                                            if (advicePurchaseNum >= foreignRedundantNum) {
    //                                                advicePurchaseNum -= foreignRedundantNum;
    //                                                addForeignRedundant.put(destinationWarehouse, 0.0);
    //                                                if (!foreignRedundantDetails.isEmpty()) {
    //                                                    foreignRedundantDetails.append(",");
    //                                                }
    //                                                foreignRedundantDetails.append(foreignRedundantNum.intValue()).append("(").append(foreignVirtualSku).append(")");
    //                                            } else {
    //                                                foreignRedundantNum -= advicePurchaseNum;
    //                                                addForeignRedundant.put(destinationWarehouse, foreignRedundantNum);
    //                                                if (!foreignRedundantDetails.isEmpty()) {
    //                                                    foreignRedundantDetails.append(",");
    //                                                }
    //                                                foreignRedundantDetails.append(advicePurchaseNum).append("(").append(foreignVirtualSku).append(")");
    //                                                advicePurchaseNum = 0;
    //                                            }
    //                                            dto.setAddForeignRedundant(addForeignRedundant);
    //                                        }
    //                                    }
    //                                }
    //
    //                                // 定义需要排除的键
    //                                List<ForeignInventoryExportDto> resultList = collectByChannel.entrySet().stream()
    //                                        .filter(entry -> !channelList.contains(entry.getKey()))
    //                                        .flatMap(entry -> entry.getValue().stream())
    //                                        .toList();
    //                                if (advicePurchaseNum > 0) {
    //                                    for (ForeignInventoryExportDto dto : resultList) {
    //                                        String foreignVirtualSku = dto.getVirtualSku();
    //                                        if (!dto.getVirtualSku().equals(virtualSku)) {
    //                                            Map<String, Double> addForeignRedundant = dto.getAddForeignRedundant();
    //                                            Double foreignRedundantNum = addForeignRedundant.getOrDefault(destinationWarehouse, 0.0);
    //                                            if (foreignRedundantNum != 0.0 && advicePurchaseNum != 0) {
    //                                                if (advicePurchaseNum >= foreignRedundantNum) {
    //                                                    advicePurchaseNum -= foreignRedundantNum;
    //                                                    addForeignRedundant.put(destinationWarehouse, 0.0);
    //                                                    if (!foreignRedundantDetails.isEmpty()) {
    //                                                        foreignRedundantDetails.append(",");
    //                                                    }
    //                                                    foreignRedundantDetails.append(foreignRedundantNum.intValue()).append("(").append(foreignVirtualSku).append(")");
    //
    //                                                } else {
    //                                                    foreignRedundantNum -= advicePurchaseNum;
    //                                                    if (!foreignRedundantDetails.isEmpty()) {
    //                                                        foreignRedundantDetails.append(",");
    //                                                    }
    //                                                    foreignRedundantDetails.append(advicePurchaseNum).append("(").append(foreignVirtualSku).append(")");
    //                                                    advicePurchaseNum = 0;
    //                                                    addForeignRedundant.put(destinationWarehouse, foreignRedundantNum);
    //                                                }
    //                                            }
    //                                            dto.setAddForeignRedundant(addForeignRedundant);
    //                                        }
    //                                    }
    //                                }
    //                            } else {
    //                                List<ForeignInventoryExportDto> sortedForeignInventoryList = foreignInventoryExportList.stream()
    //                                        .sorted((dto1, dto2) -> {
    //                                            Double value1 = dto1.getForeignRedundant() != null ? dto1.getForeignRedundant().get(destinationWarehouse) : null;
    //                                            Double value2 = dto1.getForeignRedundant() != null ? dto2.getForeignRedundant().get(destinationWarehouse) : null;
    //                                            return Comparator.nullsLast(Double::compareTo).compare(value2, value1);
    //                                        })
    //                                        .toList();
    //                                for (ForeignInventoryExportDto dto : sortedForeignInventoryList) {
    //                                    String foreignVirtualSku = dto.getVirtualSku();
    //                                    if (dto.getVirtualSku().equals(virtualSku)) {
    //                                        continue;
    //                                    }
    //
    //                                    Map<String, Double> addForeignRedundant = dto.getAddForeignRedundant();
    //                                    Double foreignRedundantNum = dto.getAddForeignRedundant().getOrDefault(destinationWarehouse, 0.0);
    //                                    if (foreignRedundantNum != 0.0 && advicePurchaseNum != 0) {
    //                                        if (advicePurchaseNum >= foreignRedundantNum) {
    //                                            advicePurchaseNum -= foreignRedundantNum;
    //                                            addForeignRedundant.put(destinationWarehouse, 0.0);
    //                                            if (!foreignRedundantDetails.isEmpty()) {
    //                                                foreignRedundantDetails.append(",");
    //                                            }
    //                                            foreignRedundantDetails.append(foreignRedundantNum.intValue()).append("(").append(foreignVirtualSku).append(")");
    //
    //                                        } else {
    //                                            foreignRedundantNum -= advicePurchaseNum;
    //                                            if (!foreignRedundantDetails.isEmpty()) {
    //                                                foreignRedundantDetails.append(",");
    //                                            }
    //                                            foreignRedundantDetails.append(advicePurchaseNum).append("(").append(foreignVirtualSku).append(")");
    //                                            advicePurchaseNum = 0;
    //                                            addForeignRedundant.put(destinationWarehouse, foreignRedundantNum);
    //
    //                                        }
    //                                        dto.setAddForeignRedundant(addForeignRedundant);
    //                                    }
    //                                }
    //                            }
    //                        }
    //                        if (advicePurchaseNum > 0) {
    //                            for (InventoryOnShippingRedundantBO onShipping : onShippingRedundantInfo) {
    //                                if (onShipping.getVirtualSku().equals(virtualSku)) {
    //                                    continue;
    //                                }
    //
    //                                Integer onShippingRedundantNum = onShipping.getAddRedundantNum();
    //                                if (onShippingRedundantNum == 0 || advicePurchaseNum == 0) {
    //                                    continue;
    //                                }
    //
    //                                String onShippingVirtualSku = onShipping.getVirtualSku();
    //                                String shipmentCode = onShipping.getShipmentCode();
    //                                Integer redundantNum = onShipping.getRedundantNum();
    //                                LocalDate enableUsingDate = onShipping.getEnableUsingDate();
    //                                StringBuilder factoryDetails = new StringBuilder();
    //                                if (advicePurchaseNum >= onShippingRedundantNum) {
    //                                    advicePurchaseNum -= onShippingRedundantNum;
    //                                    onShipping.setAddRedundantNum(0);
    //                                    factoryDetails.append(onShippingRedundantNum)
    //                                            .append("(").append(shipmentCode).append(",")
    //                                            .append(onShippingVirtualSku).append(",")
    //                                            .append(redundantNum).append(",")
    //                                            .append(enableUsingDate).append(")");
    //                                } else {
    //                                    onShippingRedundantNum -= advicePurchaseNum;
    //                                    factoryDetails.append(advicePurchaseNum)
    //                                            .append("(").append(shipmentCode).append(",")
    //                                            .append(onShippingVirtualSku).append(",")
    //                                            .append(redundantNum).append(",")
    //                                            .append(enableUsingDate).append(")");
    //                                    advicePurchaseNum = 0;
    //                                    onShipping.setAddRedundantNum(onShippingRedundantNum);
    //                                }
    //                                if (!onShippingRedundantDetails.isEmpty()) {
    //                                    onShippingRedundantDetails.append(",");
    //                                }
    //                                onShippingRedundantDetails.append(factoryDetails);
    //                            }
    //                        }
    //                        if (advicePurchaseNum > 0) {
    //                            for (InventoryFactoryRedundantInfoDto factory : sortedFactoryRedundant) {
    //                                String factoryVirtualSku = factory.getVirtualSku();
    //                                StringBuilder factoryDetails = new StringBuilder();
    //                                if (factory.getVirtualSku().equals(virtualSku)) {
    //                                    continue;
    //                                }
    //                                Double factoryRedundantNum = factory.getAddRedundantNum();
    //                                if (factoryRedundantNum != 0.0 && advicePurchaseNum != 0) {
    //                                    if (advicePurchaseNum >= factoryRedundantNum) {
    //                                        advicePurchaseNum -= factoryRedundantNum;
    //                                        factory.setAddRedundantNum(0.0);
    //                                        factoryDetails.append(factoryRedundantNum.intValue()).append("(").append(factoryVirtualSku).append(")");
    //                                    } else {
    //                                        factoryRedundantNum -= advicePurchaseNum;
    //                                        factoryDetails.append(advicePurchaseNum).append("(").append(factoryVirtualSku).append(")");
    //
    //                                        advicePurchaseNum = 0;
    //                                        factory.setAddRedundantNum(factoryRedundantNum);
    //                                    }
    //                                    if (FactoryPlanInfoEnum.PRODUCT.getCode().equals(factory.getIsProduct())) {
    //                                        if (!productDetails.isEmpty()) {
    //                                            productDetails.append(",");
    //                                        }
    //                                        productDetails.append(factoryDetails);
    //                                    } else {
    //                                        if (!inProductDetails.isEmpty()) {
    //                                            inProductDetails.append(",");
    //                                        }
    //                                        inProductDetails.append(factoryDetails);
    //                                    }
    //                                }
    //                            }
    //                        }
    //                        sheet1InfoExcel.setSelfSku(repBO.getSelfSku());
    //                        sheet1InfoExcel.setFactoryCode(repBO.getFactoryCode());
    //                        sheet1InfoExcel.setProductName(repBO.getProductName());
    //                        sheet1InfoExcel.setChannel(repBO.getChannel());
    //                        sheet1InfoExcel.setVirtualSku(virtualSku);
    //                        String operator = repBO.getOperator();
    //                        String operatorNames = "";
    //                        if (StrUtil.isNotBlank(operator)) {
    //                            operatorNames = Arrays.stream(operator.split(",", 0))
    //                                    .map(op -> StrUtil.isNotBlank(collect.get(op)) ? collect.get(op) : op)
    //                                    .collect(Collectors.joining(","));
    //                        }
    //                        sheet1InfoExcel.setOperator(operatorNames);
    //                        String buyer = repBO.getBuyer();
    //                        sheet1InfoExcel.setBuyer(StrUtil.isNotBlank(collect.get(buyer)) ? collect.get(buyer) : buyer);
    //                        sheet1InfoExcel.setSubType(Optional.ofNullable(
    //                                        VirtualSubTypeEnum.ofCode(repBO.getSubType()))
    //                                .map(VirtualSubTypeEnum::getDesc)
    //                                .orElse(null));
    //                        sheet1InfoExcel.setProductStatus(Optional.ofNullable(
    //                                        VirtualProductStatusEnum.ofCode(repBO.getProductStatus()))
    //                                .map(VirtualProductStatusEnum::getDesc)
    //                                .orElse(null));
    //                        sheet1InfoExcel.setActualDailySalesNum(repBO.getActualDailySalesNum());
    //                        sheet1InfoExcel.setTargetDailySalesNum(repBO.getTargetSalesNum());
    //                        DecimalFormat decimalFormat = new DecimalFormat("#.###");
    //                        sheet1InfoExcel.setSubEntityRate(repBO.getSubEntityRate() != null ? decimalFormat.format(repBO.getSubEntityRate() * 100) + "%" : "0%");
    //                        sheet1InfoExcel.setParentEntityRate(repBO.getParentEntityRate() != null ? decimalFormat.format(repBO.getParentEntityRate() * 100) + "%" : "0%");
    //                        sheet1InfoExcel.setAdvicePurchaseDate(DateUtil.format(repBO.getAdvicePurchaseDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN));
    //                        sheet1InfoExcel.setExpectedFactoryFinishedDate(DateUtil.format(repBO.getExpectedFactoryFinishedDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN));
    //                        sheet1InfoExcel.setExpectedArrivingDate(DateUtil.format(repBO.getExpectedArrivingDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN));
    //                        sheet1InfoExcel.setDestinationWarehouse(idNameMap.get(repBO.getDestinationWarehouse()));
    //                        sheet1InfoExcel.setAdvicePurchaseNum(repBO.getAdvicePurchaseNum());
    //                        sheet1InfoExcel.setActualReplenishmentNum(repBO.getActualReplenishmentNum());
    //                        sheet1InfoExcel.setReason(Optional.ofNullable(ReplenishmentStatusReasonEnum.ofCode(repBO.getReason()))
    //                                .map(ReplenishmentStatusReasonEnum::getDesc)
    //                                .orElse(null));
    //                        sheet1InfoExcel.setOperationReason(confirmedReasonMap.getOrDefault(repBO.getConfirmedReason(), ""));
    //                        sheet1InfoExcel.setOperationConfirmedNum(repBO.getOperationConfirmedNum());
    //                        sheet1InfoExcel.setForeignRedundantDetails(String.valueOf(foreignRedundantDetails));
    //                        sheet1InfoExcel.setOnShippingRedundantDetails(String.valueOf(onShippingRedundantDetails));
    //                        sheet1InfoExcel.setProductDetails(String.valueOf(productDetails));
    //                        sheet1InfoExcel.setInProductDetails(String.valueOf(inProductDetails));
    //
    //                        String remarks = replenishmentWatchBoardService.getRemarks(repBO.getAdvicePurchaseDate(),
    //                                repBO.getExpectedFactoryFinishedDate(), repBO.getPurchaseDays(), repBO.getPurchaseDate(),
    //                                repBO.getIsChangedArrivingDate());
    //                        sheet1InfoExcel.setRemarks(remarks);
    //
    //                        sheetInfoExcelList.add(sheet1InfoExcel);
    //                    }
    //                }
    //            } else {
    //                for (Map.Entry<String, List<RepExcelTrialPurchaseBO>> entry : virtualSkuMap.entrySet()) {
    //                    for (RepExcelTrialPurchaseBO repBO : entry.getValue()) {
    //                        RepExportsheet1InfoExcel sheet1InfoExcel = new RepExportsheet1InfoExcel();
    //                        sheet1InfoExcel.setSelfSku(repBO.getSelfSku());
    //                        sheet1InfoExcel.setFactoryCode(repBO.getFactoryCode());
    //                        sheet1InfoExcel.setProductName(repBO.getProductName());
    //                        sheet1InfoExcel.setChannel(repBO.getChannel());
    //                        sheet1InfoExcel.setVirtualSku(entry.getKey());
    //                        String operator = repBO.getOperator();
    //                        String operatorNames = "";
    //                        if (StrUtil.isNotBlank(operator)) {
    //                            operatorNames = Arrays.stream(operator.split(",", 0))
    //                                    .map(op -> StrUtil.isNotBlank(collect.get(op)) ? collect.get(op) : op)
    //                                    .collect(Collectors.joining(","));
    //                        }
    //                        sheet1InfoExcel.setOperator(operatorNames);
    //                        String buyer = repBO.getBuyer();
    //                        sheet1InfoExcel.setBuyer(StrUtil.isNotBlank(collect.get(buyer)) ? collect.get(buyer) : buyer);
    //                        sheet1InfoExcel.setSubType(Optional.ofNullable(
    //                                        VirtualSubTypeEnum.ofCode(repBO.getSubType()))
    //                                .map(VirtualSubTypeEnum::getDesc)
    //                                .orElse(null));
    //                        sheet1InfoExcel.setProductStatus(Optional.ofNullable(
    //                                        VirtualProductStatusEnum.ofCode(repBO.getProductStatus()))
    //                                .map(VirtualProductStatusEnum::getDesc)
    //                                .orElse(null));
    //                        sheet1InfoExcel.setActualDailySalesNum(repBO.getActualDailySalesNum());
    //                        sheet1InfoExcel.setTargetDailySalesNum(repBO.getTargetSalesNum());
    //                        DecimalFormat decimalFormat = new DecimalFormat("#.###");
    //                        sheet1InfoExcel.setSubEntityRate(repBO.getSubEntityRate() != null ? decimalFormat.format(repBO.getSubEntityRate() * 100) + "%" : "0%");
    //                        sheet1InfoExcel.setParentEntityRate(repBO.getParentEntityRate() != null ? decimalFormat.format(repBO.getParentEntityRate() * 100) + "%" : "0%");
    //                        sheet1InfoExcel.setAdvicePurchaseDate(DateUtil.format(repBO.getAdvicePurchaseDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN));
    //                        sheet1InfoExcel.setExpectedFactoryFinishedDate(DateUtil.format(repBO.getExpectedFactoryFinishedDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN));
    //                        sheet1InfoExcel.setExpectedArrivingDate(DateUtil.format(repBO.getExpectedArrivingDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN));
    //                        sheet1InfoExcel.setDestinationWarehouse(idNameMap.get(repBO.getDestinationWarehouse()));
    //                        sheet1InfoExcel.setAdvicePurchaseNum(repBO.getAdvicePurchaseNum());
    //                        sheet1InfoExcel.setActualReplenishmentNum(repBO.getActualReplenishmentNum());
    //                        sheet1InfoExcel.setReason(Optional.ofNullable(ReplenishmentStatusReasonEnum.ofCode(repBO.getReason()))
    //                                .map(ReplenishmentStatusReasonEnum::getDesc)
    //                                .orElse(null));
    //                        sheet1InfoExcel.setOperationReason(confirmedReasonMap.getOrDefault(repBO.getConfirmedReason(), ""));
    //                        sheet1InfoExcel.setOperationConfirmedNum(repBO.getOperationConfirmedNum());
    //
    //                        String remarks = replenishmentWatchBoardService.getRemarks(repBO.getAdvicePurchaseDate(),
    //                                repBO.getExpectedFactoryFinishedDate(), repBO.getPurchaseDays(), repBO.getPurchaseDate(),
    //                                repBO.getIsChangedArrivingDate());
    //                        sheet1InfoExcel.setRemarks(remarks);
    //
    //                        sheetInfoExcelList.add(sheet1InfoExcel);
    //
    //
    //                    }
    //                }
    //            }
    //        }
    //    }
    //    // 按 selfSku 和 virtualSku 分组，并汇总多个字段，使用 BigDecimal 保证精度
    //    Map<String, Map<String, SumFields>> groupedData = sheetInfoExcelList.stream()
    //            .collect(Collectors.groupingBy(
    //                    RepExportsheet1InfoExcel::getSelfSku,
    //                    Collectors.groupingBy(
    //                            RepExportsheet1InfoExcel::getVirtualSku,
    //                            Collectors.collectingAndThen(
    //                                    Collectors.toList(),
    //                                    list -> {
    //                                        SumFields sums = new SumFields();
    //                                        for (RepExportsheet1InfoExcel item : list) {
    //                                            if (item.getAdvicePurchaseNum() != null) {
    //                                                sums.advicePurchaseNumSum = sums.advicePurchaseNumSum.add(BigDecimal.valueOf(item.getAdvicePurchaseNum()));
    //                                            }
    //                                            if (item.getActualReplenishmentNum() != null) {
    //                                                sums.actualReplenishmentNumSum = sums.actualReplenishmentNumSum.add(BigDecimal.valueOf(item.getActualReplenishmentNum()));
    //                                            }
    //                                            if (item.getOperationConfirmedNum() != null) {
    //                                                sums.operationConfirmedNumSum = sums.operationConfirmedNumSum.add(BigDecimal.valueOf(item.getOperationConfirmedNum()));
    //                                            }
    //                                        }
    //                                        return sums;
    //                                    }
    //                            )
    //                    )
    //            ));
    //
    //    // 将汇总结果赋值到对应的字段
    //    for (RepExportsheet1InfoExcel item : sheetInfoExcelList) {
    //        String selfSku = item.getSelfSku();
    //        String virtualSku = item.getVirtualSku();
    //        SumFields sums = groupedData.getOrDefault(selfSku, Collections.emptyMap()).getOrDefault(virtualSku, new SumFields());
    //        try {
    //            item.setAdvicePurchaseNumSum(sums.advicePurchaseNumSum.intValueExact());
    //            item.setActualReplenishmentNumSum(sums.actualReplenishmentNumSum.doubleValue());
    //            item.setOperationConfirmedNumSum(sums.operationConfirmedNumSum.doubleValue());
    //        } catch (ArithmeticException e) {
    //            // 处理 BigDecimal 转 Integer 时可能出现的异常，例如溢出或小数部分非零
    //            throw new RuntimeException("BigDecimal 转 Integer 失败: " + e.getMessage(), e);
    //        }
    //    }
    //
    //    sheetInfoExcelList.sort(Comparator
    //            .comparing(RepExportsheet1InfoExcel::getSelfSku)
    //            .thenComparing(RepExportsheet1InfoExcel::getChannel)
    //            .thenComparing(RepExportsheet1InfoExcel::getVirtualSku)
    //            .thenComparing(RepExportsheet1InfoExcel::getAdvicePurchaseDate)
    //    );
    //
    //
    //    ExcelWriter excelWriter = null;
    //    Stopwatch stopwatch = Stopwatch.createStarted();
    //    try {
    //        response.setContentType("application/vnd.ms-excel");
    //        response.setCharacterEncoding("utf-8");
    //        String fileName = URLEncoder.encode("补货计划导出", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
    //        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
    //        excelWriter = EasyExcel.write(response.getOutputStream()).build();
    //
    //        int mergeRowIndex = 2;
    //        int[] mergeColumnIndexes = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 18, 20, 21, 23, 24};
    //        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
    //        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
    //        contentWriteCellStyle.setWrapped(true);
    //        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
    //                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    //        WriteSheet writeSheet1 = EasyExcel.writerSheet(1, "建议采购量").head(RepExportsheet1InfoExcel.class)
    //                .registerWriteHandler(new RepSimpleCommonExcelMergeUtil(mergeRowIndex, mergeColumnIndexes))
    //                .registerWriteHandler(new ExcelWidthStyleStrategy())
    //                .registerWriteHandler(horizontalCellStyleStrategy).build();
    //
    //        excelWriter.write(sheetInfoExcelList, writeSheet1);
    //    } catch (Exception e) {
    //        log.error("获取输出流异常", e);
    //        throw new RuntimeException("获取输出流异常", e);
    //    } finally {
    //        if (excelWriter != null) {
    //            excelWriter.finish();
    //        }
    //    }
    //    log.info("导出补货计划耗时:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
    //}

    // 导出新版本
    @Override
    public void exportRepInfo(ReplenishmentProjectForm form, HttpServletResponse response) {
        ReplenishmentRecordDO recordByIdDO = replenishmentRecordRepositoryImpl.getById(form.getId());
        if (ObjectUtil.isEmpty(recordByIdDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "补货计划不存在");
        }
        String advicePurchaseStartDate = form.getAdvicePurchaseStartDate();
        String advicePurchaseEndDate = form.getAdvicePurchaseEndDate();
        if (StrUtil.isBlank(advicePurchaseStartDate) || StrUtil.isBlank(advicePurchaseEndDate)) {
            String defaultAdvicePurchaseStartDate = DateUtil.format(recordByIdDO.getAdvicePurchaseStartDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN);
            String defaultAdvicePurchaseEndDate = DateUtil.format(recordByIdDO.getAdvicePurchaseEndDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN);
            form.setAdvicePurchaseStartDate(defaultAdvicePurchaseStartDate);
            form.setAdvicePurchaseEndDate(defaultAdvicePurchaseEndDate);
        }
        List<RepExcelProjectSkuBO> excelRepProjectList = replenishmentProjectRepository.selectExcelRepProjectData(form);
        List<RepExportsheet1InfoExcel> sheetInfoExcelList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(excelRepProjectList)) {

            List<String> virtualSkuIds = excelRepProjectList.stream().map(RepExcelProjectSkuBO::getVirtualSkuId).collect(Collectors.toList());
            var excelRepPurchaseInventoryList = replenishmentProjectRepository.selectExcelRepTrialPurchaseData(form, virtualSkuIds);
            // 理论数据
            var adviceList = excelRepPurchaseInventoryList.stream().filter(i -> i.getPurchaseNumType().equals(THEORETICAL.getCode())).toList();
            // 建议数据
            var actualList = excelRepPurchaseInventoryList.stream().filter(i -> i.getPurchaseNumType().equals(ADVICE.getCode())).toList();
            // 运营确认数据
            var operatorList = excelRepPurchaseInventoryList.stream().filter(i -> i.getPurchaseNumType().equals(OPERATOR.getCode())).toList();

            // 将actualList转换为Map,以DestinationWarehouse和AdvicePurchaseDate为key
            Map<String, RepExcelTrialPurchaseBO> actualMap = actualList.stream()
                    .collect(Collectors.toMap(
                            bo -> bo.getVirtualSkuId() + "_" + bo.getDestinationWarehouse() + "_" + bo.getAdvicePurchaseDate(),
                            bo -> bo,
                            (existing, replacement) -> existing  // 如果有重复,保留第一个
                    ));

            // 将operatorList转换为Map,以DestinationWarehouse和AdvicePurchaseDate为key
            Map<String, RepExcelTrialPurchaseBO> adviceMap = adviceList.stream()
                    .collect(Collectors.toMap(
                            bo -> bo.getVirtualSkuId() + "_" + bo.getDestinationWarehouse() + "_" + bo.getAdvicePurchaseDate(),
                            bo -> bo,
                            (existing, replacement) -> existing  // 如果有重复,保留第一个
                    ));
            // 合并列表
            List<RepExcelTrialPurchaseBO> exportInfoList = new ArrayList<>();

            for (RepExcelTrialPurchaseBO operatorBO : operatorList) {
                String key = operatorBO.getVirtualSkuId() + "_" + operatorBO.getDestinationWarehouse() + "_" + operatorBO.getAdvicePurchaseDate();
                operatorBO.setOperationConfirmedNum(Double.valueOf(operatorBO.getAdvicePurchaseNum()));
                // 使用Optional来简化空值处理
                Optional.ofNullable(adviceMap.get(key))
                        .ifPresentOrElse(
                                advice -> operatorBO.setAdvicePurchaseNum(advice.getAdvicePurchaseNum()),
                                () -> operatorBO.setAdvicePurchaseNum(0)
                        );

                Optional.ofNullable(actualMap.get(key))
                        .ifPresentOrElse(
                                actual -> operatorBO.setActualReplenishmentNum(Double.valueOf(actual.getAdvicePurchaseNum())),
                                () -> operatorBO.setActualReplenishmentNum(0.0)
                        );
                exportInfoList.add(operatorBO);
            }

            var purchaseInventoryMap = exportInfoList.stream()
                    .collect(Collectors.groupingBy(
                            RepExcelTrialPurchaseBO::getSelfSkuId, // 第一个分组依据：selfSkuId
                            Collectors.groupingBy(
                                    RepExcelTrialPurchaseBO::getVirtualSku, // 第二个分组依据：virtualSku
                                    TreeMap::new,
                                    Collectors.toList()
                            )
                    ));

            List<String> selfSkuIdList = excelRepProjectList.stream().map(RepExcelProjectSkuBO::getSelfSkuId).toList();
            InventoryInfoQuery inventoryInfoQuery = new InventoryInfoQuery();
            inventoryInfoQuery.setSelfSkuIdList(selfSkuIdList);

            RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
            ResultDTO<InventoryFactoryAndForeignInfoDto> restResult = restTemplateUtil.post(
                    inventoryInfoQuery,
                    ResultDTO.class, INVENTORY_SYSTEM_FOREIGN_AND_FACTORY
            );
            String jsonObject = JSON.toJSONString(restResult.getData());
            InventoryFactoryAndForeignInfoDto inventoryFactoryAndForeignInfoDto = JSON.to(InventoryFactoryAndForeignInfoDto.class, jsonObject);
            Map<String, FactoryAndForeignInfoMapDto> factoryAndForeignInfoMap;
            if (inventoryFactoryAndForeignInfoDto != null) {
                factoryAndForeignInfoMap = inventoryFactoryAndForeignInfoDto.getFactoryAndForeignInfoMap();
            } else {
                factoryAndForeignInfoMap = new HashMap<>();
            }

            List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
            Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));
            Map<String, String> confirmedReasonMap = JSON.parseObject(stringRedisTemplate.opsForValue().get(OPERATOR_CONFIRMED_REASON), new TypeReference<>() {
            });

            Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();

            //运营
            List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
            Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

            for (var mapEntry : purchaseInventoryMap.entrySet()) {
                TreeMap<String, List<RepExcelTrialPurchaseBO>> virtualSkuMap = mapEntry.getValue();
                var factoryAndForeignInfoMapDto = factoryAndForeignInfoMap.get(mapEntry.getKey());

                for (Map.Entry<String, List<RepExcelTrialPurchaseBO>> entry : virtualSkuMap.entrySet()) {
                    for (RepExcelTrialPurchaseBO repBO : entry.getValue()) {
                        RepExportsheet1InfoExcel sheet1InfoExcel = new RepExportsheet1InfoExcel();
                        sheet1InfoExcel.setSelfSku(repBO.getSelfSku());
                        sheet1InfoExcel.setFactoryCode(repBO.getFactoryCode());
                        sheet1InfoExcel.setProductName(repBO.getProductName());
                        sheet1InfoExcel.setChannel(channelIdNameMap.getOrDefault(repBO.getChannel(), repBO.getChannel()));
                        sheet1InfoExcel.setVirtualSku(entry.getKey());
                        String operator = repBO.getOperator();
                        String operatorNames = "";
                        if (StrUtil.isNotBlank(operator)) {
                            operatorNames = Arrays.stream(operator.split(",", 0))
                                    .map(op -> StrUtil.isNotBlank(collect.get(op)) ? collect.get(op) : op)
                                    .collect(Collectors.joining(","));
                        }
                        sheet1InfoExcel.setOperator(operatorNames);
                        String buyer = repBO.getBuyer();
                        sheet1InfoExcel.setBuyer(StrUtil.isNotBlank(collect.get(buyer)) ? collect.get(buyer) : buyer);
                        String purchaser = repBO.getPurchaser();
                        sheet1InfoExcel.setPurchaser(StrUtil.isNotBlank(collect.get(purchaser)) ? collect.get(purchaser) : purchaser);
                        sheet1InfoExcel.setSubType(Optional.ofNullable(
                                        VirtualSubTypeEnum.ofCode(repBO.getSubType()))
                                .map(VirtualSubTypeEnum::getDesc)
                                .orElse(null));
                        sheet1InfoExcel.setProductStatus(Optional.ofNullable(
                                        VirtualProductStatusEnum.ofCode(repBO.getProductStatus()))
                                .map(VirtualProductStatusEnum::getDesc)
                                .orElse(null));
                        sheet1InfoExcel.setProductType(Optional.ofNullable(
                                        VirtualProductTypeEnum.ofCode(repBO.getProductType()))
                                .map(VirtualProductTypeEnum::getDesc)
                                .orElse(null));
                        sheet1InfoExcel.setActualDailySalesNum(repBO.getActualDailySalesNum());
                        sheet1InfoExcel.setTargetDailySalesNum(repBO.getTargetSalesNum());
                        DecimalFormat decimalFormat = new DecimalFormat("#.###");
                        sheet1InfoExcel.setSubEntityRate(repBO.getSubEntityRate() != null ? decimalFormat.format(repBO.getSubEntityRate() * 100) + "%" : "0%");
                        sheet1InfoExcel.setParentEntityRate(repBO.getParentEntityRate() != null ? decimalFormat.format(repBO.getParentEntityRate() * 100) + "%" : "0%");
                        sheet1InfoExcel.setFullLinkTheoreticalSoldOutDate(DateUtil.format(repBO.getFullLinkTheoreticalSoldOutDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN));
                        sheet1InfoExcel.setFullLinkDaysBeforeSoldOut(repBO.getFullLinkDaysBeforeSoldOut());
                        sheet1InfoExcel.setAdvicePurchaseDate(DateUtil.format(repBO.getAdvicePurchaseDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN));
                        sheet1InfoExcel.setExpectedFactoryFinishedDate(DateUtil.format(repBO.getExpectedFactoryFinishedDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN));
                        sheet1InfoExcel.setExpectedArrivingDate(DateUtil.format(repBO.getExpectedArrivingDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN));
                        sheet1InfoExcel.setDestinationWarehouse(idNameMap.get(repBO.getDestinationWarehouse()));
                        sheet1InfoExcel.setAdvicePurchaseNum(repBO.getAdvicePurchaseNum());
                        sheet1InfoExcel.setActualReplenishmentNum(repBO.getActualReplenishmentNum());
                        sheet1InfoExcel.setReason(repBO.getReason());
                        sheet1InfoExcel.setOperationReason(confirmedReasonMap.getOrDefault(repBO.getConfirmedReason(), ""));
                        sheet1InfoExcel.setOperatorRemark(repBO.getOperatorRemark());
                        sheet1InfoExcel.setOperationConfirmedNum(repBO.getOperationConfirmedNum());

                        if (TEST_SAMPLE_DELIVERY_SET.contains(repBO.getProductStatus())) {
                            String remarks = replenishmentWatchBoardService.getRemarks(repBO.getAdvicePurchaseDate(),
                                    repBO.getExpectedFactoryFinishedDate(), repBO.getPurchaseDays(), repBO.getPurchaseDate(),
                                    repBO.getIsChangedArrivingDate(), repBO.getProductStatus());
                            sheet1InfoExcel.setRemarks(remarks);
                        }
                        if (factoryAndForeignInfoMapDto != null) {
                            Map<String, String> virtualSkuByInventoryInfoId = factoryAndForeignInfoMapDto.getVirtualSkuByInventoryInfoId();
                            Map<String, Integer> borrowingStrategyMap = factoryAndForeignInfoMapDto.getBorrowingStrategyByInventoryInfoId();
                            List<InventoryForeignRedundantInfoDto> foreignRedundantInfo = factoryAndForeignInfoMapDto.getForeignRedundantInfo();
                            List<InventoryOnShippingRedundantBO> onShippingRedundantInfo = factoryAndForeignInfoMapDto.getOnShippingRedundantInfo();
                            List<InventoryFactoryRedundantInfoDto> factoryRedundantInfo = factoryAndForeignInfoMapDto.getFactoryRedundantInfo();

                            StringBuilder foreignRedundantDetails = new StringBuilder();
                            StringBuilder onShippingRedundantDetails = new StringBuilder();
                            StringBuilder factoryRedundantInfoDetails = new StringBuilder();

                            foreignRedundantInfo.forEach(i -> {
                                if (StrUtil.isNotBlank(foreignRedundantDetails)) {
                                    foreignRedundantDetails.append(System.lineSeparator());
                                }
                                foreignRedundantDetails.append(virtualSkuByInventoryInfoId.get(i.getInventoryInfoId())).append(",")
                                        .append(i.getWarehouse()).append(",").append(i.getRedundantNum().intValue());
                                if (ObjectUtil.isNotEmpty(borrowingStrategyMap)) {
                                    Integer borrowingStrategy = borrowingStrategyMap.get(i.getInventoryInfoId());
                                    if (borrowingStrategy != null) {
                                        foreignRedundantDetails.append(",").append(BorrowingStrategyEnum.ofCode(borrowingStrategy).getDesc());
                                    }
                                }
                            });
                            sheet1InfoExcel.setForeignRedundantDetails(String.valueOf(foreignRedundantDetails));
                            onShippingRedundantInfo.forEach(i -> {
                                if (StrUtil.isNotBlank(onShippingRedundantDetails)) {
                                    onShippingRedundantDetails.append(System.lineSeparator());
                                }
                                onShippingRedundantDetails.append(i.getShipmentCode()).append(",").append(virtualSkuByInventoryInfoId.get(i.getInventoryInfoId())).append(",")
                                        .append(i.getStartShippingDate()).append(",").append(i.getEnableUsingDate()).append(",").append(i.getWarehouse()).append(",").append(i.getRedundantNum());
                                if (ObjectUtil.isNotEmpty(borrowingStrategyMap)) {
                                    Integer borrowingStrategy = borrowingStrategyMap.get(i.getInventoryInfoId());
                                    if (borrowingStrategy != null) {
                                        onShippingRedundantDetails.append(",").append(BorrowingStrategyEnum.ofCode(borrowingStrategy).getDesc());
                                    }
                                }
                            });
                            sheet1InfoExcel.setOnShippingRedundantDetails(String.valueOf(onShippingRedundantDetails));

                            if (CollectionUtil.isNotEmpty(factoryRedundantInfo)) {
                                var sortFactoryRedundantList = factoryRedundantInfo.stream().sorted(Comparator.comparing(InventoryFactoryRedundantInfoDto::getFactoryFinishedDate)).collect(Collectors.toList());
                                sortFactoryRedundantList.forEach(i -> {
                                    if (StrUtil.isNotBlank(factoryRedundantInfoDetails)) {
                                        factoryRedundantInfoDetails.append(System.lineSeparator());
                                    }
                                    factoryRedundantInfoDetails.append(i.getContractCode()).append(",").append(virtualSkuByInventoryInfoId.get(i.getInventoryInfoId())).append(",")
                                            .append(i.getFactoryFinishedDate()).append(",").append(i.getRedundantNum().intValue());
                                    if (ObjectUtil.isNotEmpty(borrowingStrategyMap)) {
                                        Integer borrowingStrategy = borrowingStrategyMap.get(i.getInventoryInfoId());
                                        if (borrowingStrategy != null) {
                                            factoryRedundantInfoDetails.append(",").append(BorrowingStrategyEnum.ofCode(borrowingStrategy).getDesc());
                                        }
                                    }
                                });
                            }

                            sheet1InfoExcel.setProductDetails(String.valueOf(factoryRedundantInfoDetails));

                        }
                        sheetInfoExcelList.add(sheet1InfoExcel);
                    }
                }
            }
        }
        // 按 selfSku 和 virtualSku 分组，并汇总多个字段，使用 BigDecimal 保证精度
        Map<String, Map<String, SumFields>> groupedData = sheetInfoExcelList.stream()
                .collect(Collectors.groupingBy(
                        RepExportsheet1InfoExcel::getSelfSku,
                        Collectors.groupingBy(
                                RepExportsheet1InfoExcel::getVirtualSku,
                                Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        list -> {
                                            SumFields sums = new SumFields();
                                            for (RepExportsheet1InfoExcel item : list) {
                                                if (item.getAdvicePurchaseNum() != null) {
                                                    sums.advicePurchaseNumSum = sums.advicePurchaseNumSum.add(BigDecimal.valueOf(item.getAdvicePurchaseNum()));
                                                }
                                                if (item.getActualReplenishmentNum() != null) {
                                                    sums.actualReplenishmentNumSum = sums.actualReplenishmentNumSum.add(BigDecimal.valueOf(item.getActualReplenishmentNum()));
                                                }
                                                if (item.getOperationConfirmedNum() != null) {
                                                    sums.operationConfirmedNumSum = sums.operationConfirmedNumSum.add(BigDecimal.valueOf(item.getOperationConfirmedNum()));
                                                }
                                            }
                                            return sums;
                                        }
                                )
                        )
                ));

        // 将汇总结果赋值到对应的字段
        for (RepExportsheet1InfoExcel item : sheetInfoExcelList) {
            String selfSku = item.getSelfSku();
            String virtualSku = item.getVirtualSku();
            SumFields sums = groupedData.getOrDefault(selfSku, Collections.emptyMap()).getOrDefault(virtualSku, new SumFields());
            try {
                item.setAdvicePurchaseNumSum(sums.advicePurchaseNumSum.intValueExact());
                item.setActualReplenishmentNumSum(sums.actualReplenishmentNumSum.doubleValue());
                item.setOperationConfirmedNumSum(sums.operationConfirmedNumSum.doubleValue());
            } catch (ArithmeticException e) {
                // 处理 BigDecimal 转 Integer 时可能出现的异常，例如溢出或小数部分非零
                throw new RuntimeException("BigDecimal 转 Integer 失败: " + e.getMessage(), e);
            }
        }

        sheetInfoExcelList.sort(Comparator
                .comparing(RepExportsheet1InfoExcel::getSelfSku)
                .thenComparing(RepExportsheet1InfoExcel::getChannel)
                .thenComparing(RepExportsheet1InfoExcel::getVirtualSku)
                .thenComparing(RepExportsheet1InfoExcel::getAdvicePurchaseDate)
        );


        ExcelWriter excelWriter = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("补货计划导出", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();

            int mergeRowIndex = 1;
            int[] mergeColumnIndexes = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 20, 22, 24, 25, 27, 30, 31, 32, 33};
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            contentWriteCellStyle.setWrapped(true);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                    new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            WriteSheet writeSheet1 = EasyExcel.writerSheet(1, "建议采购量").head(RepExportsheet1InfoExcel.class)
                    .registerWriteHandler(new RepSimpleCommonExcelMergeUtil(mergeRowIndex, mergeColumnIndexes))
                    .registerWriteHandler(new ExcelWidthStyleStrategy())
                    .registerWriteHandler(horizontalCellStyleStrategy).build();

            excelWriter.write(sheetInfoExcelList, writeSheet1);
        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        log.info("导出补货计划耗时:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }

    // 辅助类用于存储多个汇总字段，使用 BigDecimal 保证精度
    private static class SumFields {
        BigDecimal advicePurchaseNumSum = BigDecimal.ZERO;
        BigDecimal actualReplenishmentNumSum = BigDecimal.ZERO;
        BigDecimal operationConfirmedNumSum = BigDecimal.ZERO;
    }

    @Override
    public void exportNotRestockRepInfo(ReplenishmentProjectForm form, HttpServletResponse response) {
        form.setIsNotReplenishment(ViReplenishmentStatusEnum.NOT_RESTOCK_REQUIRED.getCode());
        form.setSize(-1);
        form.setCurrent(1);
        ReplenishmentRecordStatusVo replenishmentRecordStatusVo = replenishmentProjectService.pageList(form);
        IPage<ReplenishmentVirtualSkuListVo> replenishmentVirtualSkuList = replenishmentRecordStatusVo.getReplenishmentVirtualSkuList();
        List<ReplenishmentVirtualSkuListVo> records = replenishmentVirtualSkuList.getRecords();
        List<RepExportNotRestockExcel> notRestockExcelList = new ArrayList<>();

        records.forEach(i -> notRestockExcelList.add(
                RepExportNotRestockExcel.builder()
                        .destinationSku(i.getDestinationSku())
                        .selfSku(i.getSelfSku())
                        .productName(i.getProductName())
                        .subType(VirtualSubTypeEnum.ofCode(Integer.valueOf(i.getSubType())).getDesc())
                        .productStatus(VirtualProductStatusEnum.ofCode(Integer.valueOf(i.getProductStatus())).getDesc())
                        .productType(VirtualProductTypeEnum.ofCode(Integer.valueOf(i.getProductType())).getDesc())
                        .purchaser(i.getPurchaser())
                        .buyer(i.getBuyer())
                        .channel(i.getChannel())
                        .operator(i.getOperator())
                        .factoryCode(i.getFactoryCode())
                        .fullLinkTheoreticalSoldOutDate(i.getFullLinkTheoreticalSoldOutDate())
                        .fullLinkDaysBeforeSoldOut(i.getFullLinkDaysBeforeSoldOut())
                        .overseasInventory(i.getOverseasInventory().entrySet().stream()
                                .map(entry -> entry.getKey() + ":" + entry.getValue())
                                .collect(Collectors.joining("\n")))

                        .overseasShipping(i.getOverseasShipping().entrySet().stream()
                                .map(entry -> entry.getKey() + ":" + entry.getValue())
                                .collect(Collectors.joining("\n")))
                        .localInventory(i.getLocalInventory().entrySet().stream()
                                .map(entry -> entry.getKey() + ":" + entry.getValue())
                                .collect(Collectors.joining("\n")))
                        .actualDailySalesNum(i.getActualDailySalesNum())
                        .targetSalesNum(i.getTargetSalesNum())
                        .subEntityRate(i.getSubEntityRate())
                        .parentEntityRate(i.getParentEntityRate())
                        .advicePurchaseNum(0)
                        .reason(i.getReason())
                        .updateDate(i.getUpdateDate())
                        .build()));
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("补货计划导出", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();

            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            contentWriteCellStyle.setWrapped(true);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                    new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            WriteSheet writeSheet1 = EasyExcel.writerSheet(1, "sheet").head(RepExportNotRestockExcel.class)
                    .registerWriteHandler(new ExcelWidthStyleStrategy())
                    .registerWriteHandler(horizontalCellStyleStrategy).build();

            excelWriter.write(notRestockExcelList, writeSheet1);

        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    @Override
    public ReplenishmentDetailWatchBoardShippingVo getWatchBoardShippingInfoOpertor(String replenishmentId, String purchaseNumType) {
        String sourceType = purchaseNumType.equals(ADVICE.getCode()) ?
                DISCOUNT_REPLENISH.getCode() : REPLENISH.getCode();
        TrialWatchBoardVo triaWatchBoard = getTriaWatchBoard(replenishmentId, sourceType);
        List<FactoryRemainInventoryDto> shippingInventoryList = getFactoryRemainInventory(replenishmentId, sourceType);

        ReplenishmentVirtualSkuPurchaseDO virtualSkuPurchase = repVirtualSkuPurchaseRepository.getById(replenishmentId);
        DateTime startDate = DateUtil.date(virtualSkuPurchase.getCreateDate());

        String targetSales;
        if (PurchaseNumTypeEnum.THEORETICAL.getCode().equals(purchaseNumType)) {
            targetSales = virtualSkuPurchase.getSaleDestination();
        } else if (PurchaseNumTypeEnum.ADVICE.getCode().equals(purchaseNumType)) {
            targetSales = virtualSkuPurchase.getDiscountSaleDestination();
        } else {
            targetSales = virtualSkuPurchase.getOperatorSaleDestination();
        }
        // 所有商品的销售月份一致，因此只需要取第一个商品的日期即可
        Map<String, Double> targetSalesMap = JSON.parseObject(targetSales, new TypeReference<>() {
        });

        DateTime maxCalDate = targetSalesMap.keySet().stream()
                .map(DateUtil::parse)
                .max(DateUtil::compare)
                .orElse(DateUtil.endOfMonth(startDate));
        int calCircle = (int) DateUtil.betweenDay(startDate, maxCalDate, true);

        ProductRulesDto productRules = replenishmentRulesService.getProductRulesById(virtualSkuPurchase.getRulesId());

        Map<String, Integer> headShippingDateMap = JSON.parseObject(productRules.getHeadShippingDays(), new TypeReference<>() {
        });

        List<FactoryRemainInventoryDto> importRemainInventoryDtoList = new ArrayList<>();
        var foreignInventoryList = replenishmentForeignInventoryRepository.getByReplenishmentVirtualPurchaseId(virtualSkuPurchase.getId());
        for (var foreignInventory : foreignInventoryList) {
            importRemainInventoryDtoList.add(FactoryRemainInventoryDto.builder()
                    .virtualSku(virtualSkuPurchase.getVirtualSkuId())
                    .warehouse(foreignInventory.getWarehouseId())
                    .shipmentCode(foreignInventory.getShipmentCode())
                    .enableUsingDate(foreignInventory.getEnableUsingDate())
                    .storeNum(foreignInventory.getStoreNum().doubleValue()).build());
        }

        List<FactoryRemainInventoryDto> remainInventoryListForCal = new ArrayList<>(importRemainInventoryDtoList);

        Map<String, Double> shippingRatioMap = JSON.parseObject(productRules.getShippingRatio(), new TypeReference<>() {
        });
        // 补货试算最大循环次数
        int minShippingDate = headShippingDateMap.entrySet().stream()
                .filter(h -> shippingRatioMap.containsKey(h.getKey()) && shippingRatioMap.get(h.getKey()) > 0)
                .map(Map.Entry::getValue)
                .sorted().findFirst().orElseThrow(() -> new NullPointerException("头程时间为空"));

        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, Integer> sortedMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSort));
        List<String> sortedWarehouseList = WarehouseSortUtil.getSortedWarehouseList(shippingRatioMap, sortedMap);

        MockTableCalDto mockTableCalDto = new MockTableCalDto(shippingInventoryList, calCircle, new ArrayList<>(),
                headShippingDateMap, startDate, remainInventoryListForCal, importRemainInventoryDtoList, new ArrayList<>(),
                minShippingDate, targetSalesMap, shippingRatioMap, sortedWarehouseList);
        TrialCalReplenishmentDto trialCalReplenishment = commonConsumptionService.calDeliveryResults(mockTableCalDto);

        Map<String, Map<String, Double>> oldEverydayOnShippingInventoryMap = trialCalReplenishment.getEverydayOnShippingInventoryMap();
        Map<String, Map<String, Double>> oldEverydayRemainInventoryMap = trialCalReplenishment.getEverydayRemainInventoryMap();
        Map<String, Map<String, Double>> oldEverydaySaleProductMap = trialCalReplenishment.getEverydaySaleProductMap();

        Map<String, TreeMap<String, Integer>> everydayOnShippingInventoryMap = oldEverydayOnShippingInventoryMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            TreeMap<String, Integer> treeMap = new TreeMap<>();
                            entry.getValue().forEach((k, v) -> treeMap.put(k, v.intValue()));
                            return treeMap;
                        },
                        (v1, v2) -> v1,
                        HashMap::new
                ));

        Map<String, TreeMap<String, Double>> everydayRemainInventoryMap = oldEverydayRemainInventoryMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> new TreeMap<>(entry.getValue()),
                        (v1, v2) -> v1,
                        HashMap::new
                ));
        Map<String, TreeMap<String, Double>> everydaySaleProductMap = oldEverydaySaleProductMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> new TreeMap<>(entry.getValue()),
                        (v1, v2) -> v1,
                        HashMap::new
                ));

        var rulesMap = new HashMap<String, Double>();
        rulesMap = JSONObject.parseObject(productRules.getShippingRatio(), new TypeReference<>() {
        });

        Set<String> zeroRatioSet = rulesMap.entrySet().stream()
                .filter(entry -> entry.getValue() == 0)
                .map(Map.Entry::getKey).collect(Collectors.toSet());

        Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

        MockResultUtils.checkEmptyWarehouse(zeroRatioSet, everydayOnShippingInventoryMap);
        MockResultUtils.checkEmptyWarehouse(zeroRatioSet, everydayRemainInventoryMap);
        MockResultUtils.removeUselessMockResult(everydayRemainInventoryMap, everydayOnShippingInventoryMap, everydaySaleProductMap, zeroRatioSet);

        // 在应用启动时或者需要使用此功能的地方初始化
        WarehouseSortUtil.initializeOrderMap(senboWarehouseList);

        // 使用工具类进行排序和键替换，同时将Double转为Integer
        var mockShippingInventoryMap = WarehouseSortUtil.sortAndReplaceWarehouseKeys(everydayOnShippingInventoryMap, idNameMap, Function.identity());
        var mockRemainInventoryMap = WarehouseSortUtil.sortAndReplaceWarehouseKeys(everydayRemainInventoryMap, idNameMap, Function.identity());
        var mockDaysSaleInventoryMap = WarehouseSortUtil.sortAndReplaceWarehouseKeys(everydaySaleProductMap, idNameMap, Function.identity());

        return ReplenishmentDetailWatchBoardShippingVo.builder()
                .trialWatchBoardVo(triaWatchBoard)
                .trialMockInventoryVo(
                        TrialMockInventoryVo.builder()
                                .mockShippingInventoryMap(mockShippingInventoryMap)
                                .mockRemainInventoryMap(mockRemainInventoryMap)
                                .mockDaysSaleInventoryMap(mockDaysSaleInventoryMap)
                                .build())
                .build();
    }

    @Override
    public ReplenishmentTrialConfirmQuantityVo getConfirmEditQuantity(ReplenishmentDetailForm form) {
        ReplenishmentRecordDO recordByIdDO = replenishmentRecordRepositoryImpl.selectOneByVirtualPurchaseId(form.getReplenishmentId());
        String advicePurchaseStartDate = form.getAdvicePurchaseStartDate();
        String advicePurchaseEndDate = form.getAdvicePurchaseEndDate();
        if (StrUtil.isBlank(advicePurchaseStartDate) || StrUtil.isBlank(advicePurchaseEndDate)) {
            RepVirtualSkuDateBO repVirtualSkuDateBO = replenishmentRecordRepositoryImpl.selectOneByRecordId(recordByIdDO.getId());
            TreeMap<String, TreeMap<String, Double>> destinationSale = JSON.parseObject(repVirtualSkuDateBO.getSaleDestination(), new TypeReference<>() {
            });
            DateTime startDateTime = DateUtil.beginOfDay(recordByIdDO.getCreateDate());
            form.setAdvicePurchaseStartDate(startDateTime.toString());
            advicePurchaseStartDate = DateUtil.format(startDateTime, YYYY_MM_DD_DATE_FORMAT_HYPHEN);

            Date endDate = DateUtil.parse(destinationSale.lastKey());
            DateTime endDateTime = DateUtil.endOfDay(DateUtil.endOfMonth(endDate));
            form.setAdvicePurchaseEndDate(endDateTime.toString());
            advicePurchaseEndDate = DateUtil.format(endDateTime, YYYY_MM_DD_DATE_FORMAT_HYPHEN);
        } else {
            Date startDate = DateUtil.parse(form.getAdvicePurchaseStartDate());
            advicePurchaseStartDate = DateUtil.format(startDate, YYYY_MM_DD_DATE_FORMAT_HYPHEN);
            Date endDate = DateUtil.parse(form.getAdvicePurchaseEndDate());
            advicePurchaseEndDate = DateUtil.format(endDate, YYYY_MM_DD_DATE_FORMAT_HYPHEN);
        }

        List<ReplenishmentTrialWatchBoardDto> trialWatchBoardList = repPurchaseInventoryRepository.getReplenishmentTrialWatchBoard(form);
        Map<String, Map<String, List<ReplenishmentTrialWatchBoardDto>>> groupedResult = trialWatchBoardList.stream()
                .collect(Collectors.groupingBy(
                        ReplenishmentTrialWatchBoardDto::getWarehouse,
                        Collectors.groupingBy(ReplenishmentTrialWatchBoardDto::getAdvicePurchaseDate)
                ));
        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));
        List<ReplenishmentTrialConfirmQuantityDto> watchBoardDtoList = new ArrayList<>();
        AtomicInteger advicePurchaseNumSum = new AtomicInteger();
        AtomicInteger actualReplenishmentNumSum = new AtomicInteger();
        AtomicInteger operationConfirmedNumSum = new AtomicInteger();
        groupedResult.forEach((warehouse, dateMap) -> dateMap.forEach((date, dtos) -> {
            Map<String, Integer> purchaseNumMap = dtos.stream()
                    .collect(Collectors.groupingBy(
                            ReplenishmentTrialWatchBoardDto::getPurchaseNumType,
                            Collectors.summingInt(ReplenishmentTrialWatchBoardDto::getAdvicePurchaseNum)
                    ));
            int advicePurchaseNum = purchaseNumMap.getOrDefault(THEORETICAL.getCode(), 0);
            int actualReplenishmentNum = purchaseNumMap.getOrDefault(ADVICE.getCode(), 0);
            int operationConfirmedNum = purchaseNumMap.getOrDefault(OPERATOR.getCode(), 0);

            String purchaseInventoryId = dtos.stream()
                    .filter(i -> OPERATOR.getCode().equals(i.getPurchaseNumType()))
                    .findFirst()
                    .map(ReplenishmentTrialWatchBoardDto::getPurchaseInventoryId)
                    .orElse(null);
            String adviceId = dtos.stream()
                    .filter(i -> ADVICE.getCode().equals(i.getPurchaseNumType()))
                    .findFirst()
                    .map(ReplenishmentTrialWatchBoardDto::getPurchaseInventoryId)
                    .orElse(null);
            String theoreticalId = dtos.stream()
                    .filter(i -> THEORETICAL.getCode().equals(i.getPurchaseNumType()))
                    .findFirst()
                    .map(ReplenishmentTrialWatchBoardDto::getPurchaseInventoryId)
                    .orElse(null);
            watchBoardDtoList.add(ReplenishmentTrialConfirmQuantityDto.builder()
                    .purchaseInventoryId(purchaseInventoryId)
                    .adviceId(adviceId)
                    .theoreticalId(theoreticalId)
                    .warehouse(idNameMap.get(warehouse))
                    .replenishmentRange(dtos.getFirst().getReplenishmentRange())
                    .containerLoad(dtos.getFirst().getContainerLoad())
                    .advicePurchaseDate(DateUtil.format(DateUtil.parse(date), YYYY_MM_DD_DATE_FORMAT_CHINESE))
                    .expectedArrivingDate(DateUtil.format(DateUtil.parse(dtos.getFirst().getExpectedArrivingDate()), YYYY_MM_DD_DATE_FORMAT_CHINESE))
                    .advicePurchaseNum(advicePurchaseNum)
                    .actualReplenishmentNum(actualReplenishmentNum)
                    .operationConfirmedNum(operationConfirmedNum)
                    .build());

            advicePurchaseNumSum.addAndGet(advicePurchaseNum);
            actualReplenishmentNumSum.addAndGet(actualReplenishmentNum);
            operationConfirmedNumSum.addAndGet(operationConfirmedNum);
        }));
        ReplenishmentVirtualSkuPurchaseDO repositoryDO = repVirtualSkuPurchaseRepository.getById(form.getReplenishmentId());

        // 使用 Comparator.comparing() 方法进行多重排序
        watchBoardDtoList.sort(
                Comparator.comparing(ReplenishmentTrialConfirmQuantityDto::getAdvicePurchaseDate)
                        .thenComparing(ReplenishmentTrialConfirmQuantityDto::getWarehouse)
        );

        return ReplenishmentTrialConfirmQuantityVo.builder()
                .watchBoardDtoList(watchBoardDtoList)
                .advicePurchaseNum(advicePurchaseNumSum.get())
                .actualReplenishmentNum(actualReplenishmentNumSum.get())
                .operationConfirmedNum(operationConfirmedNumSum.get())
                .advicePurchaseStartDate(advicePurchaseStartDate)
                .advicePurchaseEndDate(advicePurchaseEndDate)
                .reason(repositoryDO.getReason())
                .operationReason(repositoryDO.getConfirmedReason())
                .operatorRemark(repositoryDO.getOperatorRemark())
                .build();
    }

    private TrialWatchBoardVo getTriaWatchBoard(String shippingProjectId, String sourceType) {
        List<WatchBoardBO> trialWatchBoardInfo = repVirtualSkuPurchaseRepository.getTrialWatchBoardInfoByReplenishmentId(shippingProjectId, sourceType);
        if (CollectionUtil.isEmpty(trialWatchBoardInfo)) {
            return TrialWatchBoardVo.builder().watchBoardWarehouseList(new ArrayList<>()).build();
        }

        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

        List<ShippingProjectWatchBoardWarehouseDto> warehouseDtoList = trialWatchBoardInfo.stream().map(factory -> {
                    double volume = factory.getCaseWidth() * factory.getCaseHeight() * factory.getCaseLength() * factory.getPackageNum();
                    double volumeDouble = BigDecimal.valueOf(volume)
                            .divide(BigDecimal.valueOf(1000000), 3, HALF_UP).doubleValue();
                    return ShippingProjectWatchBoardWarehouseDto.builder()
                            .shippingAmount(factory.getShippingNum())
                            .contractCode(factory.getContractCode())
                            .warehouse(idNameMap.get(factory.getDestinationWarehouse()))
                            .arrivalDate(DateUtil.format(factory.getExpectedArrivingDate(), YYYY_MM_DD_DATE_FORMAT_CHINESE))
                            .shippingStartDate(DateUtil.format(factory.getRealShippingStartDate(), YYYY_MM_DD_DATE_FORMAT_CHINESE))
                            .remarks(factory.getRemarks())
                            .deliveryDateRange(factory.getDeliveryDateRange())
                            .perCaseNum(factory.getContainerLoad())
                            .caseAmount(factory.getPackageNum())
                            .outCaseLength(factory.getCaseLength())
                            .outCaseWidth(factory.getCaseWidth())
                            .outCaseHeight(factory.getCaseHeight())
                            .volume(volumeDouble)
                            .weight(BigDecimal.valueOf(factory.getSingleCaseGrossWeight() * factory.getPackageNum()).setScale(3, HALF_UP).doubleValue())
                            .factoryFinishedId(factory.getFactoryFinishedId())
                            .factoryFinishedDate(DateUtil.format(factory.getFactoryFinishedDate(), YYYY_MM_DD_DATE_FORMAT_SLASH))
                            .deliveryType(factory.getDeliveryType())
                            .build();
                })
                .sorted(Comparator.comparing((ShippingProjectWatchBoardWarehouseDto c) -> Objects.equals(c.getDeliveryType(), DeliveryTypeEnum.URGENT.getCode())).reversed()
                        .thenComparing((ShippingProjectWatchBoardWarehouseDto c) -> DateUtil.parse(c.getFactoryFinishedDate()))
                        .thenComparing(c -> DateUtil.parse(c.getArrivalDate()))
                        .thenComparing(ShippingProjectWatchBoardWarehouseDto::getWarehouse))
                .toList();
        double sum = warehouseDtoList.stream().mapToDouble(ShippingProjectWatchBoardWarehouseDto::getShippingAmount).sum();
        return TrialWatchBoardVo.builder()
                .watchBoardWarehouseList(warehouseDtoList)
                .totalShippingNum(BigDecimal.valueOf(sum).setScale(3, HALF_UP).doubleValue())
                .build();
    }

    private List<FactoryRemainInventoryDto> getFactoryRemainInventory(String shippingProjectId, String sourceType) {
        List<WatchBoardBO> trialWatchBoardInfo = repVirtualSkuPurchaseRepository.getTrialWatchBoardInfoByReplenishmentId(shippingProjectId, sourceType);
        if (CollectionUtil.isEmpty(trialWatchBoardInfo)) {
            return new ArrayList<>();
        }
        return trialWatchBoardInfo.stream()
                .map(factory -> FactoryRemainInventoryDto.builder()
                        .virtualSku(factory.getDestinationSku())
                        .warehouse(factory.getDestinationWarehouse())
                        .startShippingTime(factory.getRealShippingStartDate())
                        .enableUsingDate(factory.getExpectedArrivingDate())
                        .storeNum(Double.valueOf(factory.getShippingNum())).build())
                .toList();
    }

    @Override
    public ReplenishmentDetailWatchBoardReplenishmentVo getWatchBoardReplenishmentInfo(ReplenishmentDetailForm form) {
        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));
        String purchaseNumType = form.getPurchaseNumType();
        if (purchaseNumType.equals(OPERATOR.getCode())) {
            form.setPurchaseNumType(null);
        }
        List<ReplenishmentTrialWatchBoardDto> trialWatchBoardList = repPurchaseInventoryRepository.getReplenishmentTrialWatchBoard(form);

        AtomicReference<Integer> operatorSum = new AtomicReference<>(0);
        if (purchaseNumType.equals(OPERATOR.getCode())) {
            List<ReplenishmentTrialWatchBoardDto> newTrialWatchBoardList = new ArrayList<>();
            Map<String, Map<String, List<ReplenishmentTrialWatchBoardDto>>> groupedResult = trialWatchBoardList.stream()
                    .collect(Collectors.groupingBy(
                            ReplenishmentTrialWatchBoardDto::getWarehouse,
                            Collectors.groupingBy(ReplenishmentTrialWatchBoardDto::getAdvicePurchaseDate)
                    ));

            groupedResult.forEach((warehouse, dateMap) -> dateMap.forEach((date, dtos) -> {
                Map<String, Integer> purchaseNumMap = dtos.stream()
                        .collect(Collectors.groupingBy(
                                ReplenishmentTrialWatchBoardDto::getPurchaseNumType,
                                Collectors.summingInt(ReplenishmentTrialWatchBoardDto::getAdvicePurchaseNum)
                        ));
                int advicePurchaseNum = purchaseNumMap.getOrDefault(THEORETICAL.getCode(), 0);
                int actualReplenishmentNum = purchaseNumMap.getOrDefault(ADVICE.getCode(), 0);
                int operationConfirmedNum = purchaseNumMap.getOrDefault(OPERATOR.getCode(), 0);

                String purchaseInventoryId = dtos.stream()
                        .filter(i -> OPERATOR.getCode().equals(i.getPurchaseNumType()))
                        .findFirst()
                        .map(ReplenishmentTrialWatchBoardDto::getPurchaseInventoryId)
                        .orElse(null);
                newTrialWatchBoardList.add(ReplenishmentTrialWatchBoardDto.builder()
                        .purchaseInventoryId(purchaseInventoryId)
                        .warehouse(idNameMap.get(warehouse))
                        .containerLoad(dtos.getFirst().getContainerLoad())
                        .advicePurchaseDate(DateUtil.format(DateUtil.parse(date), YYYY_MM_DD_DATE_FORMAT_CHINESE))
                        .expectedFactoryFinishedDate(DateUtil.format(DateUtil.parse(dtos.getFirst().getExpectedFactoryFinishedDate()), YYYY_MM_DD_DATE_FORMAT_CHINESE))
                        .expectedShippingStartDate(DateUtil.format(DateUtil.parse(dtos.getFirst().getExpectedShippingStartDate()), YYYY_MM_DD_DATE_FORMAT_CHINESE))
                        .expectedArrivingDate(DateUtil.format(DateUtil.parse(dtos.getFirst().getExpectedArrivingDate()), YYYY_MM_DD_DATE_FORMAT_CHINESE))
                        .advicePurchaseNum(advicePurchaseNum)
                        .actualReplenishmentNum(actualReplenishmentNum)
                        .operationConfirmedNum(operationConfirmedNum)
                        .productStatus(dtos.getFirst().getProductStatus())
                        .purchaseDate(dtos.getFirst().getPurchaseDate())
                        .purchaseDays(dtos.getFirst().getPurchaseDays())
                        .build());
                operatorSum.updateAndGet(v -> v + operationConfirmedNum);
            }));
            trialWatchBoardList = newTrialWatchBoardList;
        } else {
            for (ReplenishmentTrialWatchBoardDto trial : trialWatchBoardList) {
                trial.setWarehouse(idNameMap.get(trial.getWarehouse()));
                trial.setAdvicePurchaseDate(DateUtil.format(DateUtil.parse(trial.getAdvicePurchaseDate()), YYYY_MM_DD_DATE_FORMAT_CHINESE));
                trial.setExpectedFactoryFinishedDate(DateUtil.format(DateUtil.parse(trial.getExpectedFactoryFinishedDate()), YYYY_MM_DD_DATE_FORMAT_CHINESE));
                trial.setExpectedShippingStartDate(DateUtil.format(DateUtil.parse(trial.getExpectedShippingStartDate()), YYYY_MM_DD_DATE_FORMAT_CHINESE));
                trial.setExpectedArrivingDate(DateUtil.format(DateUtil.parse(trial.getExpectedArrivingDate()), YYYY_MM_DD_DATE_FORMAT_CHINESE));
            }
        }

        // 小红点
        ReplenishmentDetailForm newForm = new ReplenishmentDetailForm();
        newForm.setReplenishmentId(form.getReplenishmentId());
        newForm.setPurchaseNumType(purchaseNumType);
        List<ReplenishmentTrialWatchBoardDto> trialWatchBoardDateList = repPurchaseInventoryRepository.getReplenishmentTrialWatchBoard(newForm);
        List<String> collect = trialWatchBoardDateList.stream().map(ReplenishmentTrialWatchBoardDto::getAdvicePurchaseDate).collect(Collectors.toList());

        ReplenishmentVirtualSkuPurchaseDO repositoryDO = repVirtualSkuPurchaseRepository.getById(form.getReplenishmentId());
        TreeMap<String, Double> targetSalesMap = JSON.parseObject(repositoryDO.getSaleDestination(), new TypeReference<>() {
        });

        // 使用 Comparator.comparing() 方法进行多重排序
        trialWatchBoardList.sort(
                Comparator.comparing(ReplenishmentTrialWatchBoardDto::getAdvicePurchaseDate)
                        .thenComparing(ReplenishmentTrialWatchBoardDto::getWarehouse)
        );
        double n1 = trialWatchBoardList.stream().mapToDouble(m -> Optional.ofNullable(m.getAdvicePurchaseNum()).orElse(0)).sum();
        double n2 = trialWatchBoardList.stream().mapToDouble(m -> Optional.ofNullable(m.getActualReplenishmentNum()).orElse(0)).sum();
        double n3 = trialWatchBoardList.stream().mapToDouble(m -> Optional.ofNullable(m.getOperationConfirmedNum()).orElse(0)).sum();
        log.info("理论：{}，建议：{}，运营：{}\n", n1, n2, n3);
        String startDate = DateUtil.format(repositoryDO.getCreateDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN);
        YearMonth yearMonth;
        if (CollectionUtil.isEmpty(targetSalesMap)) {
            yearMonth = YearMonth.parse(startDate, hyphenFormatter);
        } else {
            yearMonth = YearMonth.parse(targetSalesMap.lastKey(), formatter);
        }
        String endDate = LocalDateTimeUtil.format(yearMonth.atEndOfMonth(), YYYY_MM_DD_DATE_FORMAT_HYPHEN);

        for (var watchBoard : trialWatchBoardList) {
            // 如果是测款产品的话，判断当天下单补货能否顺利到货，若不能给标记
            int productStatus = watchBoard.getProductStatus();
            if (TEST_SAMPLE_DELIVERY_SET.contains(productStatus)) {
                String produceDays = watchBoard.getPurchaseDate();
                int purchaseDays = watchBoard.getPurchaseDays();
                String advicePurchaseDate = watchBoard.getAdvicePurchaseDate();
                String expectedFactoryDate = watchBoard.getExpectedFactoryFinishedDate();
                boolean enable = replenishmentWatchBoardService.checkEnableArrivingInTime(advicePurchaseDate, expectedFactoryDate, purchaseDays, produceDays);
                watchBoard.setEnableArrivingInTime(enable);
            } else {
                watchBoard.setEnableArrivingInTime(true);
            }
        }

        return ReplenishmentDetailWatchBoardReplenishmentVo.builder()
                .watchBoardDtoList(trialWatchBoardList)
                .watchBoardDateList(collect)
                .operatorSum(operatorSum.get())
                .operationReason(repositoryDO.getConfirmedReason())
                .operatorRemark(repositoryDO.getOperatorRemark())
                .replenishmentStartDate(StrUtil.isNotBlank(form.getAdvicePurchaseStartDate()) ? form.getAdvicePurchaseStartDate() : startDate)
                .replenishmentEndDate(StrUtil.isNotBlank(form.getAdvicePurchaseEndDate()) ? form.getAdvicePurchaseEndDate() : endDate)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveReplenishmentPurchaseNumChange(RetrialReplenishmentInventoryForm form) {
        List<ReplenishWarehouseInventoryDto> inventoryList = form.getInventoryList();
        if (CollectionUtil.isEmpty(inventoryList)) {
            throw new NullPointerException("请输入需要修改的数据");
        }
        // 获取虚拟sku维度的补货计划
        ReplenishmentVirtualSkuPurchaseDO virtualSkuPurchase = repVirtualSkuPurchaseRepository.getById(form.getVirtualPurchaseId());
        // 获取自定义sku维度的补货计划
        ReplenishmentProjectDO replenishmentProject = replenishmentProjectRepository.getById(virtualSkuPurchase.getReplenishmentProjectId());
        ReplenishmentRecordDO byId = replenishmentRecordRepositoryImpl.getById(replenishmentProject.getReplenishmentRecordId());
        if (!Objects.equals(byId.getReplenishmentStatus(), ReplenishmentStatusEnum.UNSAVED.getCode())) {
            throw new RuntimeException("操作失败，该计划状态已更新，请刷新页面查看");
        }

        if (inventoryList.stream().anyMatch(i -> i.getAdvicePurchaseNum() < 0)) {
            throw new RuntimeException("建议补货量需不小于0");
        }
        if (inventoryList.stream().anyMatch(i -> StrUtil.isBlank(i.getDestinationWarehouse()))) {
            throw new RuntimeException("请选择目标仓库");
        }
        List<ReplenishmentTrialPurchaseInventoryDO> trialbyIdList = repPurchaseInventoryRepository.listByIds(
                form.getInventoryList().stream().map(ReplenishWarehouseInventoryDto::getTrialId).collect(Collectors.toList()));
        if (trialbyIdList.size() != inventoryList.size()) {
            throw new RuntimeException("存在已删除的数据，此次更新保存失败，请刷新页面后重新修改");
        }

        // 获取自定义sku信息
        ProductSnapshotDO productSnapshotDO = productSnapshotRepository.getById(virtualSkuPurchase.getProductSnapshotId());
        SelfProductDO selfProductInfo = JSON.parseObject(productSnapshotDO.getSelfData(), SelfProductDO.class);
        List<String> produceDays = Arrays.asList(selfProductInfo.getPurchaseDate().split(","));

        VirtualProductDO virtualProductDO = JSON.parseObject(productSnapshotDO.getVirtualData(), VirtualProductDO.class);

        ProductRulesDto productRules = replenishmentRulesService.getProductRulesFromDatabase(virtualSkuPurchase.getRulesId());
        Map<String, Integer> headShippingDateMap = JSON.parseObject(productRules.getHeadShippingDays(), new TypeReference<>() {
        });
        Map<String, Double> shippingRatioMap = JSON.parseObject(productRules.getShippingRatio(), new TypeReference<HashMap<String, Double>>() {
        });
        ShippingProjectBaseParamDto projectRules = ShippingProjectBaseParamDto.builder()
                .ruleId(productRules.getRulesId())
                .virtualSku(virtualProductDO.getVirtualSku())
                .safeDays(productRules.getSafeDays())
                .transitDays(productRules.getTransitDays())
                .purchaseProjectDays(productRules.getPurchaseProjectDays())
                .produceDays(productRules.getProduceDays())
                .headShippingDays(headShippingDateMap)
                .purchaseCircle(productRules.getPurchaseCircle())
                .shippingFrequency(productRules.getShippingFrequency())
                .shippingRatio(shippingRatioMap)
                .build();
        projectRules.setContainLoader(selfProductInfo.getContainerLoad());

        Date replenishmentStartDate = DateUtil.beginOfDay(replenishmentProject.getCreateDate());
        Map<String, Map<String, Double>> salesMap = JSON.parseObject(virtualSkuPurchase.getSaleDestination(), new TypeReference<>() {
        });
        Map<String, Double> targetSalesMap = new HashMap<>();
        for (Map.Entry<String, Map<String, Double>> entry : salesMap.entrySet()) {
            String days = entry.getKey();
            Double totalSaleNum = entry.getValue().get("合计");
            targetSalesMap.put(days, totalSaleNum);
        }
        DateTime maxSaleDate = targetSalesMap.keySet().stream()
                .filter(key -> DateUtil.parse(key).compareTo(DateUtil.beginOfMonth(replenishmentStartDate)) >= 0)
                .map(key -> DateUtil.endOfMonth(DateUtil.parse(key)))
                .max(Comparator.comparing(k -> k))
                .orElse(DateUtil.endOfMonth(replenishmentStartDate));

        int between = (int) maxSaleDate.between(replenishmentStartDate, DateUnit.DAY);

        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> nameIdMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));
        // 计算待修改数据的交货时间、预计装柜时间、预计到仓时间以及箱数
        List<ReplenishmentTrialPurchaseInventoryDO> inventoryDOList = inventoryList.stream()
                .map(inventory -> {
                    DateTime calDate = DateUtil.parse(inventory.getAdvicePurchaseDate());
                    int produceDay = replenishmentProjectService.getTargetDateFactoryProduceDays(produceDays, calDate, projectRules.getPurchaseProjectDays());
                    DateTime factoryFinishedDate = calDate.offsetNew(DateField.DAY_OF_YEAR, projectRules.getPurchaseProjectDays() + produceDay);
                    DateTime shippingStartDate = factoryFinishedDate.offsetNew(DateField.DAY_OF_YEAR, projectRules.getTransitDays());
                    String warehouseId = nameIdMap.get(inventory.getDestinationWarehouse());
                    DateTime arrivingDate = shippingStartDate.offsetNew(DateField.DAY_OF_YEAR, projectRules.getHeadShippingDays().get(warehouseId));

                    return ReplenishmentTrialPurchaseInventoryDO.builder()
                            .id(inventory.getTrialId())
                            .packageNum((int) Math.ceil((double) inventory.getAdvicePurchaseNum() / inventory.getContainerLoad()))
                            .destinationWarehouse(warehouseId)
                            .advicePurchaseNum(inventory.getAdvicePurchaseNum())
                            .advicePurchaseDate(calDate)
                            .expectedFactoryFinishedDate(factoryFinishedDate)
                            .expectedShippingStartDate(shippingStartDate)
                            .expectedArrivingDate(arrivingDate)
                            .replenishmentVirtualPurchaseId(form.getVirtualPurchaseId())
                            .build();
                }).collect(Collectors.toList());

        // 获取导入的剩余库存
        List<FactoryRemainInventoryDto> remainInventoryDtoList = new ArrayList<>(inventoryDOList.stream()
                .map(inventory -> FactoryRemainInventoryDto.builder()
                        .enableUsingDate(inventory.getExpectedArrivingDate())
                        .warehouse(inventory.getDestinationWarehouse())
                        .storeNum(inventory.getAdvicePurchaseNum().doubleValue())
                        .build())
                .toList());
        Map<String, Map<String, Double>> overseasInventoryMap = JSON.parseObject(virtualSkuPurchase.getOverseasOriginalData(), new TypeReference<>() {
        });
        List<FactoryRemainInventoryDto> importRemainInventoryDtoList = new ArrayList<>();
        for (Map.Entry<String, Map<String, Double>> overseasEntry : overseasInventoryMap.entrySet()) {
            String enableUsingDate = overseasEntry.getKey();
            Map<String, Double> inventoryMap = overseasEntry.getValue();
            for (Map.Entry<String, Double> inventoryEntry : inventoryMap.entrySet()) {
                String warehouse = inventoryEntry.getKey();
                Double storeNum = inventoryEntry.getValue();
                FactoryRemainInventoryDto build = FactoryRemainInventoryDto.builder()
                        .enableUsingDate(DateUtil.parse(enableUsingDate))
                        .warehouse(warehouse)
                        .storeNum(storeNum)
                        .build();
                importRemainInventoryDtoList.add(build);
            }
        }

        // 工厂交付的货物数量集合
        List<FactoryFinishedInventoryDO> factoryFinishedInventoryDOList = factoryFinishedInventoryRepository.getListByShippingProjectId(Collections.singletonList(virtualSkuPurchase.getId()), ShippingSourceTypeEnum.REPLENISH.getCode());

        List<FactoryFinishedInventoryDto> finishedInventoryList = factoryFinishedInventoryDOList.stream()
                .map(f ->
                        FactoryFinishedInventoryDto.builder()
                                .virtualSku(f.getVirtualSkuId())
                                .shippingNum(f.getFactoryShippingPackageNum().doubleValue())
                                .factoryFinishedDate(f.getFactoryFinishedDate())
                                .build()
                ).collect(Collectors.toList());

        String replenishmentStartDateStr = DateUtil.format(replenishmentStartDate, YYYY_MM_DD_DATE_FORMAT_SLASH);
        DateTime replenishmentStartDateDateTime = DateUtil.parse(replenishmentStartDateStr);

        Map<String, Integer> sortedMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSort));
        List<String> sortedWarehouseList = WarehouseSortUtil.getSortedWarehouseList(projectRules.getShippingRatio(), sortedMap);
        int redundantDate = calRemainInventoryService.getRedundantDateNum();
        TrialCalReplenishmentDto trialCalReplenishment = shippingCalculationService.trialShippingCalculation(
                replenishmentStartDateDateTime, redundantDate, remainInventoryDtoList, importRemainInventoryDtoList,
                virtualProductDO, between, targetSalesMap, replenishmentStartDateDateTime, finishedInventoryList,
                projectRules, sortedWarehouseList, selfProductInfo.getProductName(), senboWarehouseList);

        inventoryDOList.forEach(i -> {
            DateTime purchaseStartDate = new DateTime(i.getAdvicePurchaseDate());
            trialCalReplenishment.getEverydayOnShippingInventoryMap().entrySet().stream()
                    .filter(entry -> entry.getKey().equals(DateUtil.format(purchaseStartDate, YYYY_MM_DD_DATE_FORMAT_SLASH)))
                    .forEach((entry) -> entry.getValue().putIfAbsent("purchaseStart", 1D));

            DateTime shippingStartDate = new DateTime(i.getExpectedShippingStartDate());
            trialCalReplenishment.getEverydayOnShippingInventoryMap().entrySet().stream()
                    .filter(entry -> entry.getKey().equals(DateUtil.format(shippingStartDate, YYYY_MM_DD_DATE_FORMAT_SLASH)))
                    .forEach((entry) -> entry.getValue().putIfAbsent("purchaseStartShipping", 1D));

            DateTime arrivingDate = new DateTime(i.getExpectedArrivingDate());
            trialCalReplenishment.getEverydayRemainInventoryMap().entrySet().stream()
                    .filter(entry -> entry.getKey().equals(DateUtil.format(arrivingDate, YYYY_MM_DD_DATE_FORMAT_SLASH)))
                    .forEach((entry) -> entry.getValue().putIfAbsent("purchaseStartSale", 1D));
        });

        // 保存操作日志
        OperationLogDO operationLogDO = OperationLogDO.builder()
                .operator("徐波")
                .content("修改")
                .operationType("补货计划操作")
                .trackNumber(virtualSkuPurchase.getId())
                .build();
        operationLogRepository.save(operationLogDO);

        // 更新自定义sku的补货信息
        replenishmentProjectRepository.updateById(ReplenishmentProjectDO.builder()
                .id(replenishmentProject.getId())
                .build());
        // 更新仓库数据
        repPurchaseInventoryRepository.updateBatchById(inventoryDOList);
        return true;
    }

    @Override
    public TrialMockInventoryAddSoldVo calDelivery(RetrialReplenishmentInventoryForm form) {
        List<ReplenishWarehouseInventoryDto> inventoryList = form.getInventoryList();

        List<ReplenishWarehouseInventoryDto> calInventoryList;
        TrialMockInventoryVo trialMockInventoryVo;

        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));
        RetrialReplenishmentInventoryForm inventoryForm = RetrialReplenishmentInventoryForm.builder()
                .virtualPurchaseId(form.getVirtualPurchaseId())
                .build();
        if (CollectionUtil.isEmpty(inventoryList)) {
            trialMockInventoryVo = trialMockInventory(inventoryForm, idNameMap, form.getPurchaseNumType());
            return TrialMockInventoryAddSoldVo.builder()
                    .mockShippingInventoryMap(trialMockInventoryVo.getMockShippingInventoryMap())
                    .mockRemainInventoryMap(trialMockInventoryVo.getMockRemainInventoryMap())
                    .mockDaysSaleInventoryMap(trialMockInventoryVo.getMockDaysSaleInventoryMap()).build();
        }
        List<ReplenishmentTrialPurchaseInventoryDO> repTrialPurchaseInventory = repPurchaseInventoryRepository.listByIds(
                inventoryList.stream().map(ReplenishWarehouseInventoryDto::getTrialId).collect(Collectors.toList())
        );
        //将repTrialPurchaseInventoryDOS装成map，key为id，value为ReplenishmentTrialPurchaseInventoryDO
        Map<String, ReplenishmentTrialPurchaseInventoryDO> repTrialPurchaseInventoryDOMap = repTrialPurchaseInventory.stream().collect(
                Collectors.toMap(ReplenishmentTrialPurchaseInventoryDO::getId, i -> i));
        for (ReplenishWarehouseInventoryDto inventory : inventoryList) {
            ReplenishmentTrialPurchaseInventoryDO trialInventory = repTrialPurchaseInventoryDOMap.get(inventory.getTrialId());
            inventory.setAdvicePurchaseNum(trialInventory.getAdvicePurchaseNum());
            inventory.setExpectedFactoryFinishedDate(new DateTime(trialInventory.getExpectedFactoryFinishedDate()).toString(YYYY_MM_DD_DATE_FORMAT_SLASH));
            inventory.setExpectedShippingStartDate(new DateTime(trialInventory.getExpectedShippingStartDate()).toString(YYYY_MM_DD_DATE_FORMAT_SLASH));
            inventory.setExpectedArrivingDate(new DateTime(trialInventory.getExpectedArrivingDate()).toString(YYYY_MM_DD_DATE_FORMAT_SLASH));
            inventory.setDestinationWarehouse(trialInventory.getDestinationWarehouse());
        }
        TreeMap<String, List<ReplenishWarehouseInventoryDto>> retrialRep = inventoryList.stream()
                .collect(Collectors.groupingBy(ReplenishWarehouseInventoryDto::getExpectedArrivingDate, TreeMap::new,
                        Collectors.toList()));

        ReplenishmentVirtualSkuPurchaseDO repVirtualSkuDO = repVirtualSkuPurchaseRepository.getById(form.getVirtualPurchaseId());
        // 目标日销
        calInventoryList = retrialRep.values().stream()
                .flatMap(List::stream)
                .toList();
        List<ReplenishWarehouseInventoryDto> collect = calInventoryList.stream()
                .filter(i -> i.getAdvicePurchaseNum() > 0)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            trialMockInventoryVo = trialMockInventory(inventoryForm, idNameMap, form.getPurchaseNumType());
            return TrialMockInventoryAddSoldVo.builder()
                    .mockShippingInventoryMap(trialMockInventoryVo.getMockShippingInventoryMap())
                    .mockRemainInventoryMap(trialMockInventoryVo.getMockRemainInventoryMap())
                    .mockDaysSaleInventoryMap(trialMockInventoryVo.getMockDaysSaleInventoryMap()).build();
        }
        inventoryForm.setInventoryList(collect);
        trialMockInventoryVo = trialMockInventory(inventoryForm, idNameMap, form.getPurchaseNumType());
        Map<String, TreeMap<String, Double>> mockRemainInventoryMap = trialMockInventoryVo.getMockRemainInventoryMap();
        TreeMap<String, Double> targetSalesMap = JSON.parseObject(repVirtualSkuDO.getSaleDestination(), new TypeReference<>() {
        });

        ReplenishmentVirtualSkuPurchaseDO virtualSkuPurchase = repVirtualSkuPurchaseRepository.getById(form.getVirtualPurchaseId());
        ProductSnapshotDO productSnapshotDO = productSnapshotRepository.getById(virtualSkuPurchase.getProductSnapshotId());
        VirtualProductDO virtualProductDO = JSON.parseObject(productSnapshotDO.getVirtualData(), VirtualProductDO.class);
        SelfProductDO selfProductDO = JSON.parseObject(productSnapshotDO.getSelfData(), SelfProductDO.class);


        ProductRulesDto productRules = replenishmentRulesService.getProductRulesFromDatabase(virtualSkuPurchase.getRulesId());
        Map<String, Integer> headShippingDateMap = JSON.parseObject(productRules.getHeadShippingDays(), new TypeReference<>() {
        });
        Map<String, Double> shippingRatioMap = JSON.parseObject(productRules.getShippingRatio(), new TypeReference<HashMap<String, Double>>() {
        });
        ShippingProjectBaseParamDto projectRules = ShippingProjectBaseParamDto.builder()
                .ruleId(productRules.getRulesId())
                .virtualSku(virtualProductDO.getVirtualSku())
                .safeDays(productRules.getSafeDays())
                .transitDays(productRules.getTransitDays())
                .purchaseProjectDays(productRules.getPurchaseProjectDays())
                .produceDays(productRules.getProduceDays())
                .headShippingDays(headShippingDateMap)
                .purchaseCircle(productRules.getPurchaseCircle())
                .shippingFrequency(productRules.getShippingFrequency())
                .shippingRatio(shippingRatioMap)
                .build();
        projectRules.setContainLoader(selfProductDO.getContainerLoad());

        TreeMap<String, TreeMap<String, Double>> warehouseTargetSalesMap = new TreeMap<>();
        for (var entry : targetSalesMap.entrySet()) {
            TreeMap<String, Double> map = new TreeMap<>();
            for (var ratioEntry : shippingRatioMap.entrySet()) {
                map.put(idNameMap.get(ratioEntry.getKey()), BigDecimal.valueOf(ratioEntry.getValue() * entry.getValue()).setScale(3, HALF_UP).doubleValue());
            }
            warehouseTargetSalesMap.put(entry.getKey(), map);
        }

        Map<String, String> mockSoldInventoryMap = new HashMap<>();
        for (Map.Entry<String, List<ReplenishWarehouseInventoryDto>> entry : retrialRep.entrySet()) {
            for (ReplenishWarehouseInventoryDto warehouseInventoryDto : entry.getValue()) {
                String warehouse = idNameMap.get(warehouseInventoryDto.getDestinationWarehouse());
                DateTime expectedArrivingDate = DateUtil.parse(warehouseInventoryDto.getExpectedArrivingDate(), YYYY_MM_DD_DATE_FORMAT_SLASH);
                String firstSoldOutDate = calRemainInventoryService.getFirstSoldOut(mockRemainInventoryMap, warehouse,
                        expectedArrivingDate, warehouseTargetSalesMap);
                if (firstSoldOutDate != null) {
                    mockSoldInventoryMap.put(warehouseInventoryDto.getTrialId(), firstSoldOutDate);
                }
            }
        }

        return TrialMockInventoryAddSoldVo.builder()
                .mockSoldInventoryMap(mockSoldInventoryMap)
                .mockShippingInventoryMap(trialMockInventoryVo.getMockShippingInventoryMap())
                .mockRemainInventoryMap(trialMockInventoryVo.getMockRemainInventoryMap())
                .mockDaysSaleInventoryMap(trialMockInventoryVo.getMockDaysSaleInventoryMap()).build();
    }

    private TrialMockInventoryVo trialMockInventory(RetrialReplenishmentInventoryForm form, Map<String, String> idNameMap,
                                                    String purchaseNumType) {
        List<FactoryRemainInventoryDto> factoryRemainInventoryList = Optional.ofNullable(form.getInventoryList()).orElse(new ArrayList<>())
                .stream()
                .map(i -> FactoryRemainInventoryDto.builder()
                        .enableUsingDate(new DateTime(i.getExpectedArrivingDate()))
                        .storeNum(i.getAdvicePurchaseNum().doubleValue())
                        .warehouse(i.getDestinationWarehouse())
                        .build())
                .collect(Collectors.toList());

        // 获取虚拟sku维度的补货计划
        ReplenishmentVirtualSkuPurchaseDO virtualSkuPurchase = repVirtualSkuPurchaseRepository.getById(form.getVirtualPurchaseId());
        // 获取自定义sku维度的补货计划
        ReplenishmentProjectDO replenishmentProject = replenishmentProjectRepository.getById(virtualSkuPurchase.getReplenishmentProjectId());

        // 获取自定义sku信息
        ProductSnapshotDO productSnapshotDO = productSnapshotRepository.getById(virtualSkuPurchase.getProductSnapshotId());
        SelfProductDO selfProductInfo = JSON.parseObject(productSnapshotDO.getSelfData(), SelfProductDO.class);

        VirtualProductDO virtualProductDO = JSON.parseObject(productSnapshotDO.getVirtualData(), VirtualProductDO.class);

        // 获取备货规则
        ProductRulesDto productRules = replenishmentRulesService.getProductRulesFromDatabase(virtualSkuPurchase.getRulesId());
        Map<String, Integer> headShippingDateMap = JSON.parseObject(productRules.getHeadShippingDays(), new TypeReference<>() {
        });
        Map<String, Double> shippingRatioMap = JSON.parseObject(productRules.getShippingRatio(), new TypeReference<HashMap<String, Double>>() {
        });
        ShippingProjectBaseParamDto projectRules = ShippingProjectBaseParamDto.builder()
                .ruleId(productRules.getRulesId())
                .virtualSku(virtualProductDO.getVirtualSku())
                .safeDays(productRules.getSafeDays())
                .transitDays(productRules.getTransitDays())
                .purchaseProjectDays(productRules.getPurchaseProjectDays())
                .produceDays(productRules.getProduceDays())
                .headShippingDays(headShippingDateMap)
                .purchaseCircle(productRules.getPurchaseCircle())
                .shippingFrequency(productRules.getShippingFrequency())
                .shippingRatio(shippingRatioMap)
                .build();
        projectRules.setContainLoader(selfProductInfo.getContainerLoad());

        DateTime replenishmentStartDate = DateUtil.beginOfDay(new DateTime(replenishmentProject.getCreateDate()));

        String saleDestination;
        if (purchaseNumType.equals(THEORETICAL.getCode())) {
            saleDestination = virtualSkuPurchase.getSaleDestination();
        } else if (purchaseNumType.equals(ADVICE.getCode())) {
            saleDestination = virtualSkuPurchase.getDiscountSaleDestination();
        } else {
            saleDestination = virtualSkuPurchase.getOperatorSaleDestination();
        }
        Map<String, Double> targetSalesMap = JSON.parseObject(saleDestination, new TypeReference<>() {
        });

        DateTime maxSaleDate = targetSalesMap.keySet().stream()
                .filter(key -> DateUtil.parse(key).compareTo(replenishmentStartDate) >= 0)
                .map(DateUtil::parse)
                .max(Comparator.comparing(k -> k))
                .orElse(DateUtil.endOfMonth(replenishmentStartDate));

        long calCircle = DateUtil.betweenDay(replenishmentStartDate, maxSaleDate, false);

        List<ReplenishmentTrialPurchaseInventoryDO> inventoryDOList = new ArrayList<>();
        List<ReplenishWarehouseInventoryDto> inventoryList = form.getInventoryList();
        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> nameIdMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));
        Map<String, Integer> headShippingDays = projectRules.getHeadShippingDays();
        if (CollectionUtil.isNotEmpty(inventoryList)) {
            inventoryDOList = inventoryList.stream().map(inventory -> {
                DateTime advicePurchaseDate = DateUtil.parse(inventory.getAdvicePurchaseDate());

                DateTime factoryFinishedDate = DateUtil.parse(inventory.getExpectedFactoryFinishedDate());
                DateTime shippingStartDate = DateUtil.parse(inventory.getExpectedShippingStartDate());
                DateTime arrivingDate = DateUtil.parse(inventory.getExpectedArrivingDate());

                return ReplenishmentTrialPurchaseInventoryDO.builder()
                        .id(inventory.getTrialId())
                        .packageNum((int) Math.ceil((double) inventory.getAdvicePurchaseNum() / inventory.getContainerLoad()))
                        .destinationWarehouse(nameIdMap.get(inventory.getDestinationWarehouse()))
                        .advicePurchaseNum(inventory.getAdvicePurchaseNum())
                        .advicePurchaseDate(advicePurchaseDate)
                        .expectedFactoryFinishedDate(factoryFinishedDate)
                        .expectedShippingStartDate(shippingStartDate)
                        .expectedArrivingDate(arrivingDate)
                        .replenishmentVirtualPurchaseId(form.getVirtualPurchaseId())
                        .build();
            }).collect(Collectors.toList());
        }

        var foreignInventoryList = replenishmentForeignInventoryRepository.getByReplenishmentVirtualPurchaseId(virtualSkuPurchase.getId());
        List<FactoryRemainInventoryDto> importRemainInventoryDtoList = new ArrayList<>();
        for (var foreignInventory : foreignInventoryList) {
            Date enableUsingDate = foreignInventory.getEnableUsingDate();
            String warehouse = foreignInventory.getWarehouseId();
            double storeNum = foreignInventory.getStoreNum().doubleValue();
            FactoryRemainInventoryDto build = FactoryRemainInventoryDto.builder()
                    .enableUsingDate(enableUsingDate)
                    .warehouse(warehouse)
                    .storeNum(storeNum)
                    .build();
            importRemainInventoryDtoList.add(build);
        }

        // 工厂交付的货物数量集合
        List<String> virPurchaseIdList = Collections.singletonList(virtualSkuPurchase.getId());
        List<FactoryFinishedInventoryDO> factoryFinishedInventoryDOList = factoryFinishedInventoryRepository.getListByShippingProjectId(virPurchaseIdList, REPLENISH.getCode());
        List<FactoryFinishedInventoryDto> finishedInventoryList = factoryFinishedInventoryDOList.stream()
                .map(f ->
                        FactoryFinishedInventoryDto.builder()
                                .virtualSku(f.getVirtualSkuId())
                                .contractCode(f.getContractCode())
                                .shippingNum(f.getFactoryShippingPackageNum().doubleValue())
                                .factoryFinishedDate(f.getFactoryFinishedDate())
                                .remark(f.getRemarks())
                                .build()
                ).collect(Collectors.toList());

        Map<String, Integer> sortedMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSort));
        List<String> sortedWarehouseList = WarehouseSortUtil.getSortedWarehouseList(shippingRatioMap, sortedMap);

        TrialCalReplenishmentDto trialCalReplenishment;
        List<FactoryRemainInventoryDto> shippingInventoryList = new ArrayList<>();
        List<FactoryRemainInventoryDto> priorDeliveryList = new ArrayList<>();
        if (!SpecialDeliveryCollection.NOT_DELIVERY_SET.contains(virtualProductDO.getProductStatus())) {
            int redundantDate = calRemainInventoryService.getRedundantDateNum();
            trialCalReplenishment = shippingCalculationService.trialShippingCalculation(replenishmentStartDate, redundantDate,
                    new ArrayList<>(), importRemainInventoryDtoList, virtualProductDO, (int) calCircle, targetSalesMap,
                    replenishmentStartDate, finishedInventoryList, projectRules, sortedWarehouseList,
                    selfProductInfo.getProductName(), senboWarehouseList);

            shippingInventoryList = Optional.ofNullable(trialCalReplenishment.getShippingInventoryList()).orElse(new ArrayList<>());
            priorDeliveryList = Optional.ofNullable(trialCalReplenishment.getPriorDeliveryList()).orElse(new ArrayList<>());
        }

        int minShippingDate = headShippingDays.entrySet().stream()
                .filter(h -> shippingRatioMap.containsKey(h.getKey()) && shippingRatioMap.get(h.getKey()) > 0)
                .map(Map.Entry::getValue)
                .sorted().findFirst().orElseThrow(() -> new NullPointerException("头程时间为空"));

        List<FactoryRemainInventoryDto> remainInventoryListForCal = new ArrayList<>(importRemainInventoryDtoList);
        MockTableCalDto mockTableCalDto = new MockTableCalDto(shippingInventoryList, (int) calCircle,
                factoryRemainInventoryList, headShippingDays, replenishmentStartDate, remainInventoryListForCal,
                importRemainInventoryDtoList, priorDeliveryList, minShippingDate, targetSalesMap, shippingRatioMap, sortedWarehouseList);
        trialCalReplenishment = commonConsumptionService.calDeliveryResults(mockTableCalDto);
        TrialCalReplenishmentDto finalTrialCalReplenishment = trialCalReplenishment;
        inventoryDOList.forEach(i -> {
            DateTime purchaseStartDate = new DateTime(i.getAdvicePurchaseDate());
            finalTrialCalReplenishment.getEverydayOnShippingInventoryMap().entrySet().stream()
                    .filter(entry -> entry.getKey().equals(DateUtil.format(purchaseStartDate, YYYY_MM_DD_DATE_FORMAT_SLASH)))
                    .forEach((entry) -> entry.getValue().putIfAbsent("purchaseStart", 1D));

            DateTime shippingStartDate = new DateTime(i.getExpectedShippingStartDate());
            finalTrialCalReplenishment.getEverydayOnShippingInventoryMap().entrySet().stream()
                    .filter(entry -> entry.getKey().equals(DateUtil.format(shippingStartDate, YYYY_MM_DD_DATE_FORMAT_SLASH)))
                    .forEach((entry) -> entry.getValue().putIfAbsent("purchaseStartShipping", 1D));

            DateTime arrivingDate = new DateTime(i.getExpectedArrivingDate());
            finalTrialCalReplenishment.getEverydayRemainInventoryMap().entrySet().stream()
                    .filter(entry -> entry.getKey().equals(DateUtil.format(arrivingDate, YYYY_MM_DD_DATE_FORMAT_SLASH)))
                    .forEach((entry) -> entry.getValue().putIfAbsent("purchaseStartSale", 1D));
        });

        Set<String> zeroRatioSet = shippingRatioMap.entrySet().stream()
                .filter(entry -> entry.getValue() == 0)
                .map(warehouseEntry -> idNameMap.getOrDefault(warehouseEntry.getKey(), warehouseEntry.getKey()))
                .collect(Collectors.toSet());

        // 在应用启动时或者需要使用此功能的地方初始化
        WarehouseSortUtil.initializeOrderMap(senboWarehouseList);

        // 使用工具类进行排序和键替换，同时将Double转为Integer
        Map<String, TreeMap<String, Integer>> onShippingInventoryMap = WarehouseSortUtil.sortAndReplaceWarehouseKeys(
                finalTrialCalReplenishment.getEverydayOnShippingInventoryMap(),
                idNameMap,
                Double::intValue // Double to Integer
        );
        MockResultUtils.checkEmptyWarehouse(zeroRatioSet, onShippingInventoryMap);

        Map<String, TreeMap<String, Double>> remainInventoryMap = WarehouseSortUtil.sortAndReplaceWarehouseKeys(
                finalTrialCalReplenishment.getEverydayRemainInventoryMap(),
                idNameMap,
                Function.identity() // 不进行类型转换
        );
        MockResultUtils.checkEmptyWarehouse(zeroRatioSet, remainInventoryMap);

        Map<String, TreeMap<String, Double>> daysSaleInventoryMap = WarehouseSortUtil.sortAndReplaceWarehouseKeys(
                finalTrialCalReplenishment.getEverydaySaleProductMap(),
                idNameMap,
                Function.identity() // 不进行类型转换
        );
        MockResultUtils.checkEmptyWarehouse(zeroRatioSet, daysSaleInventoryMap);

        MockResultUtils.removeUselessMockResult(remainInventoryMap, onShippingInventoryMap, daysSaleInventoryMap, zeroRatioSet);
        return TrialMockInventoryVo.builder()
                .mockShippingInventoryMap(onShippingInventoryMap)
                .mockRemainInventoryMap(remainInventoryMap)
                .mockDaysSaleInventoryMap(daysSaleInventoryMap)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(RetrialReplenishmentInventoryForm form) {
        ReplenishmentRecordDO replenishmentRecordDO = replenishmentRecordRepositoryImpl.selectOneByVirtualPurchaseId(form.getVirtualPurchaseId());
        if (!ReplenishmentStatusEnum.UNSAVED.getCode().equals(replenishmentRecordDO.getReplenishmentStatus())) {
            throw new RuntimeException("操作失败，该计划状态已更新，请刷新页面查看");
        }
        List<String> ids = form.getInventoryList().stream().map(ReplenishWarehouseInventoryDto::getTrialId).toList();
        for (String id : ids) {
            repPurchaseInventoryRepository.removeById(id);
            List<ReplenishmentTrialPurchaseInventoryDO> trialList = repPurchaseInventoryRepository.list(Wrappers.<ReplenishmentTrialPurchaseInventoryDO>lambdaQuery()
                    .eq(ReplenishmentTrialPurchaseInventoryDO::getReplenishmentVirtualPurchaseId, form.getVirtualPurchaseId()));
            if (CollectionUtil.isEmpty(trialList) || trialList.stream().mapToInt(ReplenishmentTrialPurchaseInventoryDO::getAdvicePurchaseNum).sum() == 0) {
                repVirtualSkuPurchaseRepository.updateById(ReplenishmentVirtualSkuPurchaseDO.builder()
                        .id(form.getVirtualPurchaseId())
                        .replenishmentStatus(ViReplenishmentStatusEnum.NOT_RESTOCK_REQUIRED.getCode())
                        .build());
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(content = "更新补货运营确认量", operationType = "补货计划操作")
    public Boolean updateOperation(OperationConfirmedNumForm form, LogTrackNumDto logTrackNumDto) {
        if (CollectionUtil.isEmpty(form.getRepVirtualPurchaseIds())) {
            throw new BusinessException(ResponseCodeEnum.ERROR, "请至少选择一条数据");
        }
        ReplenishmentVirtualSkuPurchaseDO virtualSkuPurchaseDO = repVirtualSkuPurchaseRepository.getById(form.getRepVirtualPurchaseIds().getFirst());
        ReplenishmentProjectDO projectDO = replenishmentProjectRepository.getById(virtualSkuPurchaseDO.getReplenishmentProjectId());
        ReplenishmentRecordDO recordDO = replenishmentRecordRepositoryImpl.getById(projectDO.getReplenishmentRecordId());
        if (!ReplenishmentStatusEnum.UNSAVED.getCode().equals(recordDO.getReplenishmentStatus())) {
            throw new BusinessException(ResponseCodeEnum.ERROR, "数据状态不是待保存，修改失败");
        }
        HashMap<String, String> logMap = new HashMap<>();
        List<ReplenishmentVirtualSkuPurchaseDO> virtualSkuPurchaseDOList = new ArrayList<>();
        form.getRepVirtualPurchaseIds().forEach(i -> {
            logMap.put(i, "编辑补货确认结果");
            virtualSkuPurchaseDOList.add(ReplenishmentVirtualSkuPurchaseDO.builder()
                    .id(i)
                    .confirmedReason(form.getConfirmedReason())
                    .operatorRemark(form.getOperatorRemark())
                    .build());

        });

        logTrackNumDto.setLogMap(logMap);
        logTrackNumDto.setAuthorization(SecurityUtils.getToken());
        List<ReplenishWarehouseInventoryDto> repWarehouseInventoryDtos = new ArrayList<>();
        try {
            if (CollectionUtil.isNotEmpty(form.getConfirmedNumList())) {

                List<ReplenishmentTrialPurchaseInventoryDO> trialPurchaseInventoryDOList = new ArrayList<>();


                form.getConfirmedNumList().forEach(i -> {
                    ReplenishmentDetailForm detailForm = new ReplenishmentDetailForm();
                    detailForm.setTrialPurchaseId(i.id());
                    ReplenishmentTrialWatchBoardDto replenishmentTrial = repPurchaseInventoryRepository.getReplenishmentTrialWatchBoardById(detailForm);
                    Integer packageNum = replenishmentTrial.getContainerLoad();
                    Integer num = i.num();
                    if (num == null) {
                        throw new BusinessException(ResponseCodeEnum.ERROR, "请填写确认量");
                    }
                    if (packageNum != 0 && num % packageNum != 0) {
                        throw new BusinessException(ResponseCodeEnum.ERROR, "非整箱不允许保存");
                    }
                    trialPurchaseInventoryDOList.add(ReplenishmentTrialPurchaseInventoryDO.builder()
                            .id(i.id())
                            .operationConfirmedStatus(OperationConfirmedStatusEnum.CONFIRMED.getCode())
                            .advicePurchaseNum(num)
                            .packageNum(num / packageNum)
                            .build());

                    detailForm.setTrialPurchaseId(null);
                    detailForm.setReplenishmentId(replenishmentTrial.getRepVirtualInventoryId());
                    detailForm.setPurchaseNumType(THEORETICAL.getCode());
                    detailForm.setDestinationWarehouse(replenishmentTrial.getWarehouse());
                    detailForm.setAdvicePurchaseDate(replenishmentTrial.getAdvicePurchaseDate());
                    var theoretical = repPurchaseInventoryRepository.getReplenishmentTrialWatchBoardById(detailForm);
                    if (ObjectUtil.isNotEmpty(theoretical)) {
                        trialPurchaseInventoryDOList.add(ReplenishmentTrialPurchaseInventoryDO.builder()
                                .id(theoretical.getPurchaseInventoryId())
                                .operationConfirmedStatus(OperationConfirmedStatusEnum.CONFIRMED.getCode()).build());
                    }
                    detailForm.setPurchaseNumType(ADVICE.getCode());
                    var advice = repPurchaseInventoryRepository.getReplenishmentTrialWatchBoardById(detailForm);
                    if (ObjectUtil.isNotEmpty(advice)) {
                        trialPurchaseInventoryDOList.add(ReplenishmentTrialPurchaseInventoryDO.builder()
                                .id(advice.getPurchaseInventoryId())
                                .operationConfirmedStatus(OperationConfirmedStatusEnum.CONFIRMED.getCode()).build());
                    }
                });

                repPurchaseInventoryRepository.updateBatchById(trialPurchaseInventoryDOList);

                ReplenishmentDetailForm detailForm = new ReplenishmentDetailForm();
                detailForm.setReplenishmentId(virtualSkuPurchaseDOList.getFirst().getId());
                detailForm.setPurchaseNumType(OPERATOR.getCode());
                detailForm.setOperationConfirmedStatus(OperationConfirmedStatusEnum.CONFIRMED.getCode());
                List<ReplenishmentTrialWatchBoardDto> replenishmentTrial = repPurchaseInventoryRepository.getReplenishmentTrialWatchBoard(detailForm);

                for (var trial : replenishmentTrial) {
                    repWarehouseInventoryDtos.add(ReplenishWarehouseInventoryDto.builder().trialId(trial.getPurchaseInventoryId())
                            .containerLoad(trial.getContainerLoad())
                            .advicePurchaseDate(trial.getAdvicePurchaseDate()).build());
                }
                TrialMockInventoryAddSoldVo trialMockInventoryAddSoldVo = calDelivery(RetrialReplenishmentInventoryForm.builder()
                        .virtualPurchaseId(virtualSkuPurchaseDOList.getFirst().getId())
                        .purchaseNumType(OPERATOR.getCode())
                        .inventoryList(repWarehouseInventoryDtos)
                        .build());
                Map<String, String> mockSoldInventoryMap = trialMockInventoryAddSoldVo.getMockSoldInventoryMap();
                if (mockSoldInventoryMap != null) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);
                    // 只获取最大日期的值
                    LocalDate maxDate = mockSoldInventoryMap.values().stream()
                            .map(dateStr -> LocalDate.parse(dateStr, formatter))
                            .max(LocalDate::compareTo)
                            .orElse(null);
                    virtualSkuPurchaseDOList.getFirst().setExpectedSoldOutDate(maxDate);
                }
            }
            if (CollectionUtil.isNotEmpty(virtualSkuPurchaseDOList)) {
                repVirtualSkuPurchaseRepository.updateBatchById(virtualSkuPurchaseDOList);
            }
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.ERROR, "修改运营确认量失败，" + e.getMessage());
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(content = "更新补货运营确认量", operationType = "补货计划操作")
    public Boolean batchUpdateOperation(OperationConfirmedNumForm form, LogTrackNumDto logTrackNumDto) {
        if (CollectionUtil.isEmpty(form.getRepVirtualPurchaseIds())) {
            throw new BusinessException(ResponseCodeEnum.ERROR, "请至少选择一条数据");
        }
        if (form.getRepVirtualPurchaseIds().size() > 50) {
            throw new BusinessException(ResponseCodeEnum.ERROR, "最多选择50条数据");
        }
        List<ReplenishmentVirtualSkuPurchaseDO> virtualSkuPurchaseDO = repVirtualSkuPurchaseRepository.listByIds(form.getRepVirtualPurchaseIds());
        ReplenishmentProjectDO projectDO = replenishmentProjectRepository.getById(virtualSkuPurchaseDO.getFirst().getReplenishmentProjectId());
        ReplenishmentRecordDO recordDO = replenishmentRecordRepositoryImpl.getById(projectDO.getReplenishmentRecordId());
        if (!ReplenishmentStatusEnum.UNSAVED.getCode().equals(recordDO.getReplenishmentStatus())) {
            throw new BusinessException(ResponseCodeEnum.ERROR, "数据状态不是待确认，修改失败");
        }
        HashMap<String, String> logMap = new HashMap<>();
        List<ReplenishmentVirtualSkuPurchaseDO> virtualSkuPurchaseDOList = new ArrayList<>();
        form.getRepVirtualPurchaseIds().forEach(i -> {
            logMap.put(i, "编辑补货确认结果");
        });

        logTrackNumDto.setLogMap(logMap);
        logTrackNumDto.setAuthorization(SecurityUtils.getToken());
        try {

            List<ReplenishmentTrialPurchaseInventoryDO> trialPurchaseInventoryDOList = new ArrayList<>();

            virtualSkuPurchaseDO.forEach(i -> {
                ReplenishmentDetailForm detailForm = new ReplenishmentDetailForm();
                detailForm.setAdvicePurchaseStartDate(form.getAdvicePurchaseStartDate());
                detailForm.setAdvicePurchaseEndDate(form.getAdvicePurchaseEndDate());
                detailForm.setReplenishmentId(i.getId());

                var trialWatchBoardList = repPurchaseInventoryRepository.getReplenishmentTrialWatchBoard(detailForm);
                Map<String, Map<String, List<ReplenishmentTrialWatchBoardDto>>> groupedResult = trialWatchBoardList.stream()
                        .collect(Collectors.groupingBy(
                                ReplenishmentTrialWatchBoardDto::getWarehouse,
                                Collectors.groupingBy(ReplenishmentTrialWatchBoardDto::getAdvicePurchaseDate)
                        ));
                List<ReplenishmentTrialConfirmQuantityDto> trialConfirmList = new ArrayList<>();
                groupedResult.forEach((warehouse, dateMap) -> dateMap.forEach((date, dtos) -> {
                    Map<String, Integer> purchaseNumMap = dtos.stream()
                            .collect(Collectors.groupingBy(
                                    ReplenishmentTrialWatchBoardDto::getPurchaseNumType,
                                    Collectors.summingInt(ReplenishmentTrialWatchBoardDto::getAdvicePurchaseNum)
                            ));
                    int advicePurchaseNum = purchaseNumMap.getOrDefault(THEORETICAL.getCode(), 0);
                    int actualReplenishmentNum = purchaseNumMap.getOrDefault(ADVICE.getCode(), 0);
                    int operationConfirmedNum = purchaseNumMap.getOrDefault(OPERATOR.getCode(), 0);

                    String purchaseInventoryId = dtos.stream()
                            .filter(j -> OPERATOR.getCode().equals(j.getPurchaseNumType()))
                            .findFirst()
                            .map(ReplenishmentTrialWatchBoardDto::getPurchaseInventoryId)
                            .orElse(null);
                    String adviceId = dtos.stream()
                            .filter(j -> ADVICE.getCode().equals(j.getPurchaseNumType()))
                            .findFirst()
                            .map(ReplenishmentTrialWatchBoardDto::getPurchaseInventoryId)
                            .orElse(null);
                    String theoreticalId = dtos.stream()
                            .filter(j -> THEORETICAL.getCode().equals(j.getPurchaseNumType()))
                            .findFirst()
                            .map(ReplenishmentTrialWatchBoardDto::getPurchaseInventoryId)
                            .orElse(null);
                    trialConfirmList.add(ReplenishmentTrialConfirmQuantityDto.builder()
                            .purchaseInventoryId(purchaseInventoryId)
                            .adviceId(adviceId)
                            .theoreticalId(theoreticalId)
                            .advicePurchaseNum(advicePurchaseNum)
                            .actualReplenishmentNum(actualReplenishmentNum)
                            .operationConfirmedNum(operationConfirmedNum)
                            .containerLoad(trialWatchBoardList.getFirst().getContainerLoad())
                            .build());
                }));
                List<ReplenishWarehouseInventoryDto> repWarehouseInventoryDtos = new ArrayList<>();

                if (CollectionUtil.isNotEmpty(trialConfirmList)) {
                    boolean reasonFlag = false;
                    int advicePurchaseNum = 0;
                    for (var trial : trialConfirmList) {
                        if (form.getConfirmedNumType().equals(ConfirmedNumTypeEnum.ADVICE_PURCHASE.getCode())) {
                            advicePurchaseNum = trial.getActualReplenishmentNum();
                        } else if (form.getConfirmedNumType().equals(ConfirmedNumTypeEnum.ACTUAL_REPLENISHMENT.getCode())) {
                            advicePurchaseNum = trial.getAdvicePurchaseNum();
                        }
                        if (advicePurchaseNum != trial.getActualReplenishmentNum()) {
                            reasonFlag = true;
                        }
                        trialPurchaseInventoryDOList.add(ReplenishmentTrialPurchaseInventoryDO.builder()
                                .id(trial.getPurchaseInventoryId())
                                .advicePurchaseNum(advicePurchaseNum)
                                .packageNum(advicePurchaseNum / trial.getContainerLoad())
                                .operationConfirmedStatus(OperationConfirmedStatusEnum.CONFIRMED.getCode())
                                .build());

                        if (StrUtil.isNotBlank(trial.getTheoreticalId())) {
                            trialPurchaseInventoryDOList.add(ReplenishmentTrialPurchaseInventoryDO.builder()
                                    .id(trial.getTheoreticalId())
                                    .operationConfirmedStatus(OperationConfirmedStatusEnum.CONFIRMED.getCode()).build());
                        }

                        if (StrUtil.isNotBlank(trial.getAdviceId())) {
                            trialPurchaseInventoryDOList.add(ReplenishmentTrialPurchaseInventoryDO.builder()
                                    .id(trial.getAdviceId())
                                    .operationConfirmedStatus(OperationConfirmedStatusEnum.CONFIRMED.getCode()).build());
                        }
                    }

                    ReplenishmentDetailForm repDetailForm = new ReplenishmentDetailForm();
                    repDetailForm.setReplenishmentId(i.getId());
                    repDetailForm.setPurchaseNumType(OPERATOR.getCode());
                    repDetailForm.setOperationConfirmedStatus(OperationConfirmedStatusEnum.CONFIRMED.getCode());
                    List<ReplenishmentTrialWatchBoardDto> replenishmentTrial = repPurchaseInventoryRepository.getReplenishmentTrialWatchBoard(repDetailForm);

                    for (var trial : replenishmentTrial) {
                        repWarehouseInventoryDtos.add(ReplenishWarehouseInventoryDto.builder()
                                .containerLoad(trial.getContainerLoad()).trialId(trial.getPurchaseInventoryId()).build());
                    }

                    TrialMockInventoryAddSoldVo trialMockInventoryAddSoldVo = calDelivery(RetrialReplenishmentInventoryForm.builder()
                            .virtualPurchaseId(i.getId())
                            .purchaseNumType(OPERATOR.getCode())
                            .inventoryList(repWarehouseInventoryDtos)
                            .build());
                    Map<String, String> mockSoldInventoryMap = trialMockInventoryAddSoldVo.getMockSoldInventoryMap();
                    LocalDate maxDate = null;
                    if (mockSoldInventoryMap != null) {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);
                        // 只获取最大日期的值
                        maxDate = mockSoldInventoryMap.values().stream()
                                .map(dateStr -> LocalDate.parse(dateStr, formatter))
                                .max(LocalDate::compareTo)
                                .orElse(null);
                    }

                    virtualSkuPurchaseDOList.add(ReplenishmentVirtualSkuPurchaseDO.builder()
                            .id(i.getId())
                            .confirmedReason(reasonFlag ? form.getConfirmedReason() : "")
                            .expectedSoldOutDate(maxDate)
                            .operatorRemark(form.getOperatorRemark())
                            .build());
                }

            });

            repPurchaseInventoryRepository.updateBatchById(trialPurchaseInventoryDOList);

            if (CollectionUtil.isNotEmpty(virtualSkuPurchaseDOList)) {
                repVirtualSkuPurchaseRepository.updateBatchById(virtualSkuPurchaseDOList);
            }
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.ERROR, "修改运营确认量失败，" + e.getMessage());
        }
        return Boolean.TRUE;
    }

    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void refreshData() {
        int pageSize = 2000;
        int pageNum1 = 1;
        int pageNum2 = 1;
        List<ReplenishmentTrialPurchaseInventoryCopyBO> records;
        List<FactoryFinishedInventoryDO> shippingRecords;
        try (ExecutorService executorService = Executors.newVirtualThreadPerTaskExecutor()) {
            List<Future<?>> futures = new ArrayList<>();

            do {
                IPage<ReplenishmentTrialPurchaseInventoryCopyBO> data = reCopy.selectByPage(pageSize, pageNum1);
                records = data.getRecords();

                if (CollectionUtil.isNotEmpty(records)) {
                    List<ReplenishmentTrialPurchaseInventoryCopyBO> finalRecords = records;
                    Future<?> future = executorService.submit(() -> processAndInsertRecords(finalRecords));
                    futures.add(future);
                }

                pageNum1++;
            } while (CollectionUtil.isNotEmpty(records));
            do {
                IPage<FactoryFinishedInventoryDO> page = new Page<>();
                page.setSize(pageSize);
                page.setCurrent(pageNum2);
                IPage<FactoryFinishedInventoryDO> shipData = factoryFinishedInventoryRepository.page(page, Wrappers.<FactoryFinishedInventoryDO>lambdaQuery()
                        .eq(FactoryFinishedInventoryDO::getSourceType, REPLENISH.getCode()));
                shippingRecords = shipData.getRecords();

                if (CollectionUtil.isNotEmpty(shippingRecords)) {
                    List<FactoryFinishedInventoryDO> finalRecords = shippingRecords;
                    Future<?> future = executorService.submit(() -> {
                        List<TrialShippingInventoryDO> insertTrial = new ArrayList<>();
                        for (FactoryFinishedInventoryDO ffid : finalRecords) {
                            List<TrialShippingInventoryDO> listByFactoryFinishedIdList = trialShippingInventoryRepository.getListByFactoryFinishedIdList(Collections.singletonList(ffid.getId()));
                            ffid.setId(null);
                            ffid.setSourceType(DISCOUNT_REPLENISH.getCode());
                            factoryFinishedInventoryRepository.save(ffid);
                            if (CollectionUtil.isNotEmpty(listByFactoryFinishedIdList)) {
                                List<TrialShippingInventoryDO> collect = listByFactoryFinishedIdList.stream()
                                        .peek(record -> {
                                            record.setId(null);
                                            record.setFactoryFinishedId(ffid.getId());
                                        }).toList();
                                insertTrial.addAll(collect);
                            }
                        }
                        int batchSize = 2000;
                        for (int i = 0; i < insertTrial.size(); i += batchSize) {
                            int endIndex = Math.min(i + batchSize, insertTrial.size());
                            List<TrialShippingInventoryDO> batch = insertTrial.subList(i, endIndex);
                            trialShippingInventoryRepository.saveBatch(batch);
                        }
                    });
                    futures.add(future);
                }
                pageNum2++;
            } while (CollectionUtil.isNotEmpty(shippingRecords));
            // 等待所有任务完成
            for (Future<?> future : futures) {
                try {
                    future.get();
                } catch (InterruptedException | ExecutionException e) {
                    log.error("处理记录时发生错误", e);
                }
            }
        }
    }


    private void processAndInsertRecords(List<ReplenishmentTrialPurchaseInventoryCopyBO> records) {
        List<ReplenishmentTrialPurchaseInventoryDO> insertList = records.stream()
                .flatMap(reBo -> {
                    Integer actualReplenishmentNum = Optional.ofNullable(reBo.getActualReplenishmentNum()).orElse(0);
                    Integer containerLoad = Optional.ofNullable(reBo.getContainerLoad()).orElse(1);
                    reBo.setContainerLoad(containerLoad);

                    ReplenishmentTrialPurchaseInventoryDO actualBuild = buildReplenishmentTrialPurchaseInventory(
                            reBo, actualReplenishmentNum, ADVICE);

                    Integer operationConfirmedNum = Optional.ofNullable(reBo.getOperationConfirmedNum()).orElse(0);
                    ReplenishmentTrialPurchaseInventoryDO operatorBuild = buildReplenishmentTrialPurchaseInventory(
                            reBo, operationConfirmedNum, OPERATOR);

                    return Stream.of(actualBuild, operatorBuild);
                })
                .collect(Collectors.toList());

        // 批量插入数据
        repPurchaseInventoryRepository.saveBatch(insertList);
    }

    private ReplenishmentTrialPurchaseInventoryDO buildReplenishmentTrialPurchaseInventory(
            ReplenishmentTrialPurchaseInventoryCopyBO reBo, Integer advicePurchaseNum, PurchaseNumTypeEnum purchaseNumType) {
        return ReplenishmentTrialPurchaseInventoryDO.builder()
                .advicePurchaseNum(advicePurchaseNum)
                .purchaseNumType(purchaseNumType.getCode())
                .destinationWarehouse(reBo.getDestinationWarehouse())
                .advicePurchaseDate(reBo.getAdvicePurchaseDate())
                .expectedFactoryFinishedDate(reBo.getExpectedFactoryFinishedDate())
                .expectedShippingStartDate(reBo.getExpectedShippingStartDate())
                .expectedArrivingDate(reBo.getExpectedArrivingDate())
                .packageNum(calculatePackageNum(advicePurchaseNum, reBo.getContainerLoad()))
                .remarks(reBo.getRemarks())
                .replenishmentVirtualPurchaseId(reBo.getReplenishmentVirtualPurchaseId())
                .trialStatus(reBo.getTrialStatus())
                .status(reBo.getStatus())
                .build();
    }

    private int calculatePackageNum(Integer advicePurchaseNum, Integer containerLoad) {
        return Optional.ofNullable(containerLoad)
                .filter(load -> load != 0)
                .map(load -> advicePurchaseNum / load)
                .orElse(0);
    }

    @Override
    public Boolean checkConfirmedReason(OperationConfirmedNumForm form) {
        List<ReplenishmentVirtualSkuPurchaseDO> virtualSkuPurchaseDOList = repVirtualSkuPurchaseRepository.listByIds(form.getRepVirtualPurchaseIds());

        ReplenishmentDetailForm detailForm = new ReplenishmentDetailForm();
        detailForm.setAdvicePurchaseStartDate(form.getAdvicePurchaseStartDate());
        detailForm.setAdvicePurchaseEndDate(form.getAdvicePurchaseEndDate());

        for (var i : virtualSkuPurchaseDOList) {
            detailForm.setReplenishmentId(i.getId());
            var repConfirmEditVO = getConfirmEditQuantity(detailForm);
            if (ObjectUtil.isNotEmpty(repConfirmEditVO)) {
                var trialConfirmList = repConfirmEditVO.getWatchBoardDtoList();
                if (CollectionUtil.isNotEmpty(trialConfirmList)) {
                    for (var trial : trialConfirmList) {
                        if (!trial.getAdvicePurchaseNum().equals(trial.getActualReplenishmentNum())) {
                            return Boolean.TRUE;
                        }
                    }
                }
            }
        }
        return Boolean.FALSE;
    }

    record ReplenishmentData(Map<String, Map<String, ReplenishDeliveryRangeAndNumDp>> map, String purchaseNumType) {
    }

    @Override
    public void saveTrialPurchaseInventoryInfo(ReplenishmentProjectSaveDto projectSaveDto, VirtualProductDO virtualProductDO,
                                               String skuPurchaseDOId) {
        var replenishmentDataList = List.of(
                new ReplenishmentData(projectSaveDto.getEverydayReplenishmentMap(), THEORETICAL.getCode()),
                new ReplenishmentData(projectSaveDto.getOperatorEverydayReplenishmentMap(), OPERATOR.getCode()),
                new ReplenishmentData(projectSaveDto.getDiscountEverydayReplenishmentMap(), ADVICE.getCode())
        );

        ShippingProjectBaseParamDto projectRules = projectSaveDto.getShippingRules();
        Map<String, Double> shippingRatio = projectRules.getShippingRatio();
        Map<String, Double> targetSalesMap = projectSaveDto.getTargetSalesMap();
        List<String> produceDayList = Arrays.asList(projectSaveDto.getSelfProductInfo().getPurchaseDate().split(","));
        int containerLoad = projectSaveDto.getSelfProductInfo().getContainerLoad();

        for (var replenishmentData : replenishmentDataList) {
            if (replenishmentData.map().isEmpty()) {
                continue;
            }

            if (projectSaveDto.getIsTest()) {
                int maxHeadShippingDate = projectRules.getHeadShippingDays().entrySet().stream()
                        .filter(m -> shippingRatio.getOrDefault(m.getKey(), 0D) > 0)
                        .max(Map.Entry.comparingByValue())
                        .map(Map.Entry::getValue)
                        .orElseThrow();

                replenishmentData.map.entrySet().stream().findFirst().ifPresent(replenish -> {
                    // 采购日期
                    DateTime purchaseDate = DateUtil.parse(replenish.getKey());

                    int purchaseProjectDays = projectRules.getPurchaseProjectDays();
                    // 工厂交期
                    int factoryProduceDays = getTargetDateFactoryProduceDays(produceDayList, purchaseDate, purchaseProjectDays);
                    var realFactoryFinishedDate = purchaseDate.offsetNew(DAY_OF_YEAR, factoryProduceDays + purchaseProjectDays);

                    // 到货日期
                    DateTime arrivingDate = targetSalesMap.entrySet().stream()
                            .filter(f -> {
                                LocalDate current = DateUtil.endOfMonth(new Date()).toLocalDateTime().toLocalDate();
                                return DateUtils.convertToLocalDate(f.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH).isAfter(current);
                            })
                            .filter(f -> f.getValue() > 0)
                            .map(m -> DateUtil.parse(m.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH))
                            .min(Comparator.comparing(c -> c))
                            .orElse(purchaseDate);
                    // 发货日期
                    DateTime shippingStartDate = arrivingDate.offsetNew(DAY_OF_YEAR, -maxHeadShippingDate);
                    // 工厂交货日期
                    DateTime expectedFactoryFinishedDate = shippingStartDate.offsetNew(DAY_OF_YEAR, -projectRules.getTransitDays());

                    String isChangedArrivingDate = "0";
                    if (realFactoryFinishedDate.isAfter(expectedFactoryFinishedDate) && virtualProductDO.getProductType().equals(VirtualProductTypeEnum.REGULAR.getCode())) {
                        expectedFactoryFinishedDate = realFactoryFinishedDate;
                        shippingStartDate = expectedFactoryFinishedDate.offsetNew(DAY_OF_YEAR, projectRules.getTransitDays());
                        arrivingDate = shippingStartDate.offsetNew(DAY_OF_YEAR, maxHeadShippingDate);
                        isChangedArrivingDate = "1";
                    }
                    for (var entry : replenish.getValue().entrySet()) {
                        ReplenishDeliveryRangeAndNumDp value = entry.getValue();
                        var replenishmentTrialPurchaseInventoryDO = ReplenishmentTrialPurchaseInventoryDO.builder()
                                .advicePurchaseNum(value.getNum().intValue())
                                .purchaseNumType(replenishmentData.purchaseNumType())
                                .destinationWarehouse(entry.getKey())
                                .replenishmentRange(value.getRange())
                                .advicePurchaseDate(purchaseDate)
                                .operationConfirmedStatus(null)
                                .expectedFactoryFinishedDate(expectedFactoryFinishedDate)
                                .expectedShippingStartDate(shippingStartDate)
                                .expectedArrivingDate(arrivingDate)
                                .packageNum((int) Math.ceil(value.getNum() / containerLoad))
                                .replenishmentVirtualPurchaseId(skuPurchaseDOId)
                                .trialStatus(TrialPurchaseStatusEnum.ADOPTED.getCode())
                                .isChangedArrivingDate(isChangedArrivingDate)
                                .build();
                        repPurchaseInventoryRepository.save(replenishmentTrialPurchaseInventoryDO);
                    }
                });
            } else {
                replenishmentData.map().forEach((date, replenishmentMap) -> {
                    var purchaseDate = DateUtil.parse(date);
                    int purchaseProjectDays = projectRules.getPurchaseProjectDays();
                    // 工厂交期
                    int factoryProduceDays = getTargetDateFactoryProduceDays(produceDayList, purchaseDate, purchaseProjectDays);

                    var factoryFinishedDate = purchaseDate.offsetNew(DAY_OF_YEAR, factoryProduceDays + purchaseProjectDays);
                    // 装柜时间
                    var shippingStartDate = factoryFinishedDate.offsetNew(DAY_OF_YEAR, projectRules.getTransitDays());

                    replenishmentMap.forEach((warehouse, dp) -> {
                        // 预计到仓时间
                        var expectedArrivingDay = shippingStartDate.offsetNew(DAY_OF_YEAR, projectRules.getHeadShippingDays().get(warehouse));

                        var replenishmentTrialPurchaseInventoryDO = ReplenishmentTrialPurchaseInventoryDO.builder()
                                .advicePurchaseNum(dp.getNum().intValue())
                                .purchaseNumType(replenishmentData.purchaseNumType())
                                .destinationWarehouse(warehouse)
                                .replenishmentRange(dp.getRange())
                                .advicePurchaseDate(purchaseDate)
                                .expectedFactoryFinishedDate(factoryFinishedDate)
                                .expectedShippingStartDate(shippingStartDate)
                                .expectedArrivingDate(expectedArrivingDay)
                                .operationConfirmedStatus(null)
                                .packageNum((int) Math.ceil(dp.getNum() / containerLoad))
                                .replenishmentVirtualPurchaseId(skuPurchaseDOId)
                                .isChangedArrivingDate("-1")
                                .trialStatus(TrialPurchaseStatusEnum.ADOPTED.getCode())
                                .build();

                        repPurchaseInventoryRepository.save(replenishmentTrialPurchaseInventoryDO);
                    });
                });
            }
        }
    }

    int getTargetDateFactoryProduceDays(List<String> produceDayList, DateTime nowDate, int purchaseProjectDays) {
        if (produceDayList.size() == 1) {
            return Integer.parseInt(produceDayList.getFirst());
        }
        int month = DateUtil.monthEnum(nowDate.offsetNew(DateField.DAY_OF_YEAR, purchaseProjectDays)).getValue();
        return Integer.parseInt(produceDayList.get(month));
    }
}