package com.purchase.purchase_server.service.factory.Impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.enums.UserPostEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.assembler.FactoryAssembler;
import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.consts.FactoryDefaultConstants;
import com.purchase.purchase_server.entity.dataObject.FactoryContainerDO;
import com.purchase.purchase_server.entity.dataObject.FactoryFinancialDO;
import com.purchase.purchase_server.entity.dataObject.FactoryInfoDO;
import com.purchase.purchase_server.entity.dataObject.SelfProductDO;
import com.purchase.purchase_server.entity.dto.factory.*;
import com.purchase.purchase_server.entity.excelObject.FactoryContainerExcel;
import com.purchase.purchase_server.entity.excelObject.FactoryInfoAndFinancialExcel;
import com.purchase.purchase_server.entity.form.FileMissionForm;
import com.purchase.purchase_server.entity.form.UserParams;
import com.purchase.purchase_server.entity.form.interior.ProductSnapshotForm;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.OrderTrackerSearchVo;
import com.purchase.purchase_server.entity.vo.UserInteriorVO;
import com.purchase.purchase_server.enums.DisableStatusEnum;
import com.purchase.purchase_server.enums.FactoryContainerDefaultIdentifierEnum;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.repository.dataRepository.FactoryInfoRepositoryImpl;
import com.purchase.purchase_server.repository.dataRepository.SelfProductRepositoryImpl;
import com.purchase.purchase_server.service.factory.IFactoryContainerService;
import com.purchase.purchase_server.service.factory.IFactoryFinancialService;
import com.purchase.purchase_server.service.factory.IFactoryInfoService;
import com.purchase.purchase_server.utils.easyExcelUtil.listener.factory.redundant.FactoryContainerImportListener;
import com.purchase.purchase_server.utils.easyExcelUtil.listener.factory.redundant.FactoryInfoAndFinancialImportListener;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SystemConstant.*;

/**
 * @Description 供应商的service层
 * <AUTHOR>
 * @Date 2024/6/6 16:38
 **/
@Service
@Slf4j
public class FactoryInfoServiceImpl implements IFactoryInfoService {
    @Resource
    protected RestTemplate restTemplate;
    @Resource
    private FactoryInfoRepositoryImpl factoryInfoRepository;
    @Resource
    private IFactoryContainerService factoryContainerService;
    @Resource
    private IFactoryFinancialService factoryFinancialService;
    @Resource
    private SelfProductRepositoryImpl selfProductRepositoryImpl;
    @Resource
    private FactoryAssembler factoryAssembler;
    @Resource
    private SelfProductRepositoryImpl selfProductRepository;
    @Resource
    private FactoryDefaultConstants factoryDefaultConstants;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(content = "供应商添加或更新", operationType = "供应商编辑")
    @Async
    public void importFactoryExcel(InputStream file, byte[] fileBytes, String fileName, LogTrackNumDto logTrackNumDto) {
        ExcelReader excelReader = null;
        // 保存产品导入进度
        String token = SecurityUtils.getToken();
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, token);
        FactoryImportDataDto factoryImportDataDto = new FactoryImportDataDto();
        List<String> errorList = new CopyOnWriteArrayList<>();
        //插入文件记录
        ResultDTO<String> restResult = restTemplateUtil.post(
                FileMissionForm.builder().fileName(fileName)
                        .importStatus("导入中").type("供应商信息导入")
                        .build(),
                ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL
        );
        String missionId = restResult.getData();
        try {

            if (!Objects.equals(restResult.getStatus(), ResponseCodeEnum.OK.getCode())) {
                log.error("导入供应商时插入文件中心失败，异常原因：{}", restResult.getMessage());
                throw new RuntimeException("导入供应商时插入文件中心失败，异常原因：" + restResult.getMessage());
            }
            ReadSheet readSheet1 =
                    EasyExcel.readSheet(0).headRowNumber(2).head(FactoryInfoAndFinancialExcel.class)
                            .registerReadListener(new FactoryInfoAndFinancialImportListener(fileName, errorList, factoryDefaultConstants, factoryImportDataDto))
                            .autoTrim(true).build();
            ReadSheet readSheet2 =
                    EasyExcel.readSheet(1).headRowNumber(1).head(FactoryContainerExcel.class).registerReadListener(new FactoryContainerImportListener(fileName, errorList, factoryImportDataDto)).autoTrim(true).build();
            excelReader = EasyExcel.read(file).build();

            if (excelReader.excelExecutor().sheetList().size() < 2) {
                throw new RuntimeException("请检查sheet是否正确");
            }
            excelReader.read(readSheet1, readSheet2);

            List<FactoryInfoDto> factoryInfoDto = factoryImportDataDto.getFactoryInfoDto();
            List<FactoryContainerDto> factoryContainerDto = factoryImportDataDto.getFactoryContainerDto();
            if (errorList.isEmpty() && CollectionUtil.isEmpty(factoryInfoDto)) {
                errorList.add("请确保基本&财务信息sheet与装柜信息sheet供应商对应");
            } else if (CollectionUtil.isNotEmpty(factoryContainerDto) && CollectionUtil.isNotEmpty(factoryInfoDto)) {
                for (int i = 0; i < factoryContainerDto.size(); i++) {
                    int finalI = i;
                    List<FactoryInfoDto> list = factoryInfoDto.stream().filter(factoryInfo -> factoryInfo.getFactoryCode().equals(factoryContainerDto.get(finalI).getFactoryCode())).toList();
                    if (CollectionUtil.isEmpty(list)) {
                        errorList.add(String.format("装柜信息第 %d 行数据异常，异常原因 供应商代码在基本信息中不存在，请确认数据有无问题，无法确认的可联系开发人员", i + 2));
                    }
                }
            }
            if (!errorList.isEmpty()) {
                restTemplateUtil.post(FileMissionForm.builder()
                        .importStatus("失败")
                        .importResult(String.format("失败：%s", errorList.size()))
                        .failedResultList(errorList)
                        .missionId(missionId)
                        .finishDate(new Date())
                        .build(), ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL);
            } else {
                List<FactoryInfoDO> factoryInfoDOS = insertFactoryByImport(factoryImportDataDto);
                // 将 FactoryInfoDto 的 id 提取到 Set 中
                Set<String> factoryInfoDtoIds = factoryInfoDOS.stream()
                        .map(FactoryInfoDO::getId)
                        .collect(Collectors.toSet());

                Map<String, String> resultMap = factoryInfoDOS.stream()
                        .collect(Collectors.toMap(
                                FactoryInfoDO::getId,
                                val -> factoryInfoDtoIds.contains(val.getId()) ? "修改" : "创建"
                        ));
                logTrackNumDto.setLogMap(resultMap);
                logTrackNumDto.setAuthorization(token);
                restTemplateUtil.post(
                        FileMissionForm.builder().fileName(fileName)
                                .importStatus("导入成功").type("供应商信息导入")
                                .importResult(String.format("成功：%s", factoryInfoDtoIds.size()))
                                .missionId(missionId)
                                .finishDate(new Date())
                                .build(),
                        ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL
                );
            }

        } catch (Exception e) {
            errorList.add(String.format("异常原因：%s", e.getMessage()));
            restTemplateUtil.post(FileMissionForm.builder()
                    .importStatus("失败")
                    .importResult(String.format("失败：%s", errorList.size()))
                    .failedResultList(errorList)
                    .missionId(missionId)
                    .finishDate(new Date())
                    .build(), ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL);
            log.error("导入供应商异常", e);
        } finally {
            if (excelReader != null) {
                log.warn("导入供应商关闭");
                excelReader.finish();
            }
        }

    }

    @Override
    public IPage<FactoryInfoIPageDto> pageList(FactoryDataPageQuery query) {
        IPage<FactoryInfoIPageDto> factoryInfoIPageDto = factoryInfoRepository.pageList(query);
        if (CollectionUtil.isEmpty(factoryInfoIPageDto.getRecords())) {
            return new Page<>(query.getCurrent(), query.getSize(), factoryInfoIPageDto.getTotal());
        }
        List<OrderTrackerSearchVo> orderTrackerList = getOrderTrackerList(new UserParams(), null);
        Map<String, String> collect = orderTrackerList.stream().collect(Collectors.toMap(OrderTrackerSearchVo::getUserName, OrderTrackerSearchVo::getNickName));
        List<FactoryInfoIPageDto> records = factoryInfoIPageDto.getRecords();
        List<FactoryInfoDto> factoryInfoDtoList = factoryInfoRepository.listByFactoryInfo(records.stream().map(FactoryInfoIPageDto::getId).toList());
        // 根据工厂id转为map
        Map<String, FactoryInfoDto> factoryInfoDtoMap = factoryInfoDtoList.stream().collect(Collectors.toMap(FactoryInfoDto::getId, Function.identity()));
        for (FactoryInfoIPageDto s : factoryInfoIPageDto.getRecords()) {
            String orderTracker = collect.get(s.getOrderTracker());
            if (StrUtil.isBlank(orderTracker)) {
                orderTracker = s.getOrderTracker();
            }
            s.setOrderTracker(orderTracker);

            String purchaser = collect.get(s.getPurchaser());
            if (StrUtil.isBlank(purchaser)) {
                purchaser = s.getPurchaser();
            }
            s.setPurchaser(purchaser);
            List<SelfProductDO> selfProductDOList = factoryInfoDtoMap.get(s.getId()).getSelfProductDOList();
            if (CollectionUtil.isNotEmpty(selfProductDOList)) {
                // 根据selfProductDOList的buyer先根据collect转为汉字，再连接起来，逗号分隔,注意去掉重复项
                Set<String> buyerSet = selfProductDOList.stream()
                        .map(SelfProductDO::getBuyer)
                        .filter(StrUtil::isNotBlank)
                        .map(buyer -> collect.getOrDefault(buyer, buyer))
                        .collect(Collectors.toSet());
                s.setBuyer(String.join(",", buyerSet));
            }
        }
        return factoryInfoIPageDto;
    }

    @Override
    public FactoryDetailDataDto queryDetail(FactoryInfoQuery query) {
        FactoryInfoDO factoryInfoDO = factoryInfoRepository.getById(query.getFactoryInfoId());
        if (factoryInfoDO == null) {
            throw new BusinessException(ResponseCodeEnum.ERROR.getCode(), "供应商不存在");
        }
        List<FactoryContainerDO> factoryContainerDOS = factoryContainerService.listByFactoryInfoIds(CollectionUtil.toList(factoryInfoDO.getId()));
        FactoryFinancialDO factoryFinancialDO = factoryFinancialService.getByFactoryInfoId(factoryInfoDO.getId());
        FactoryInfoDto factoryInfoDto = factoryAssembler.factoryInfoDoToDto(factoryInfoDO);
        FactoryFinancialDto factoryFinancialDto = factoryAssembler.factoryFinancialDoToDto(factoryFinancialDO);
        if (ObjectUtil.isEmpty(factoryFinancialDto)) {
            factoryFinancialDto = new FactoryFinancialDto();
        }
        factoryFinancialDto.setCurrency(factoryInfoDto.getCurrency());
        if (ObjectUtil.isNotEmpty(factoryInfoDto)) {
            List<OrderTrackerSearchVo> userList = getOrderTrackerList(new UserParams(), null);
            Map<String, String> collect = userList.stream().collect(Collectors.toMap(OrderTrackerSearchVo::getUserName, OrderTrackerSearchVo::getNickName));
            String purchase = collect.get(factoryInfoDto.getPurchaser());
            if (StrUtil.isBlank(purchase)) {
                purchase = factoryInfoDto.getPurchaser();
            }
            factoryInfoDto.setPurchaserUserName(factoryInfoDto.getPurchaser());
            factoryInfoDto.setPurchaser(purchase);

            String orderTracker = collect.get(factoryInfoDto.getOrderTracker());
            if (StrUtil.isBlank(orderTracker)) {
                orderTracker = factoryInfoDto.getOrderTracker();
            }
            factoryInfoDto.setOrderTrackerUserName(factoryInfoDto.getOrderTracker());
            factoryInfoDto.setOrderTracker(orderTracker);

        }

        return FactoryDetailDataDto.builder()
                .factoryInfoDto(ObjectUtil.isNull(factoryInfoDto) ? new FactoryInfoDto() : factoryInfoDto)
                .factoryContainerDtoList(factoryAssembler.factoryContainerDoListToDtoList(factoryContainerDOS))
                .factoryFinancialDto(factoryFinancialDto)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(content = "供应商添加或更新", operationType = "供应商编辑")
    public Boolean insertFactoryData(FactoryDataCommand factoryDataCommand, LogTrackNumDto logTrackNumDto) {
        FactoryInfoDto factoryInfoDto = factoryDataCommand.getFactoryInfoDto();
        List<FactoryContainerDto> factoryContainerDtoList = factoryDataCommand.getFactoryContainerDtoList();
        FactoryFinancialDto factoryFinancialDto = factoryDataCommand.getFactoryFinancialDto();

        try {
            UserParams params = new UserParams();
            if (StrUtil.isBlank(factoryInfoDto.getPurchaserUserName()) || factoryInfoDto.getPurchaserUserName().equals(factoryDefaultConstants.getPurchaser())) {
                factoryInfoDto.setPurchaserUserName(factoryDefaultConstants.getPurchaser());
                factoryInfoDto.setPurchaser(factoryDefaultConstants.getPurchaserName());
            } else {
                //判断采购员
                params.setUserName(factoryInfoDto.getPurchaserUserName());
                params.setNickName(factoryInfoDto.getPurchaser());
                getOrderTrackerList(params, UserPostEnum.PURCHASER);
            }
            ;
            if (StrUtil.isBlank(factoryInfoDto.getOrderTrackerUserName()) || factoryInfoDto.getOrderTrackerUserName().equals(factoryDefaultConstants.getOrderTracker())) {
                factoryInfoDto.setOrderTrackerUserName(factoryDefaultConstants.getOrderTracker());
                factoryInfoDto.setOrderTracker(factoryDefaultConstants.getOrderTrackerName());
            } else {
                //判断跟单人
                params.setUserName(factoryInfoDto.getOrderTrackerUserName());
                params.setNickName(factoryInfoDto.getOrderTracker());
                getOrderTrackerList(params, UserPostEnum.MERCHANDISER);
            }
            ;

            FactoryInfoDto factoryInfoQuery = new FactoryInfoDto();
            factoryInfoQuery.setFactoryCode(factoryInfoDto.getFactoryCode());
            List<FactoryInfoDO> factoryInfoDOList = factoryInfoRepository.listByfactoryInfoQuery(factoryInfoQuery);
            if (CollectionUtil.isNotEmpty(factoryInfoDOList)) {
                if (StrUtil.isBlank(factoryInfoDto.getId())) {
                    throw new BusinessException(ResponseCodeEnum.ERROR.getCode(), "供应商已存在无法添加");
                }
                FactoryInfoDO factoryInfoDO = factoryInfoDOList.getFirst();
                if (DisableStatusEnum.DISABLE.getCode().equals(factoryInfoDO.getDisableStatus())) {
                    throw new BusinessException(ResponseCodeEnum.ERROR.getCode(), "供应商已禁用无法更新");
                }
            } else {
                factoryInfoDto.setDisableStatus(DisableStatusEnum.NORMAL.getCode());
            }
            ArrayList<FactoryInfoDto> factoryInfoDtoList = CollectionUtil.toList(factoryInfoDto);
            List<FactoryInfoDO> factoryInfoDOS = insertOrUpdateFactoryInfo(factoryInfoDtoList);
            String id = factoryInfoDOS.getFirst().getId();

            if (StrUtil.isNotBlank(factoryInfoDto.getId())) {
                List<SelfProductDO> selfProductDOs = new ArrayList<>();
                List<FactoryInfoDto> selfFactoryInfoDtoList = factoryInfoRepository.listByFactoryInfo(Collections.singletonList(factoryInfoDto.getId()));
                for (var selfFactory : selfFactoryInfoDtoList) {
                    List<SelfProductDO> selfProductDOList = selfFactory.getSelfProductDOList();
                    if (CollectionUtil.isNotEmpty(selfProductDOList)) {
                        for (var self : selfProductDOList) {
                            if (!self.getCurrency().equals(selfFactory.getCurrency())){
                                selfProductDOs.add(SelfProductDO.builder()
                                        .id(self.getId())
                                        .image(self.getImage())
                                        .priceWithTaxes(self.getPriceWithTaxes())
                                        .remarks(self.getRemarks())
                                        .currency(factoryInfoDto.getCurrency()).build());
                            }
                        }
                    }
                }
                if (CollectionUtil.isNotEmpty(selfProductDOs)) {
                    selfProductRepository.updateBatchById(selfProductDOs);
                    log.info("更新产品成功：{}", selfProductDOs.size());
                }
            }

            // 将 FactoryInfoDto 的 id 提取到 Set 中
            Set<String> factoryInfoDtoIds = factoryInfoDtoList.stream()
                    .map(FactoryInfoDto::getId)
                    .collect(Collectors.toSet());

            Map<String, String> resultMap = factoryInfoDOS.stream()
                    .collect(Collectors.toMap(
                            FactoryInfoDO::getId,
                            val -> factoryInfoDtoIds.contains(val.getId()) ? "修改" : "创建"
                    ));
            logTrackNumDto.setLogMap(resultMap);

            List<FactoryContainerDto> factoryContainerFilterList = factoryContainerDtoList.stream()
                    .filter(i -> StrUtil.isNotBlank(i.getProvince()) || StrUtil.isNotBlank(i.getCity()) || StrUtil.isNotBlank(i.getDetailedAddress()) || StrUtil.isNotBlank(i.getAddressCode()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(factoryContainerFilterList)) {
                boolean defaultFlag = false;
                for (FactoryContainerDto factoryContainerDto : factoryContainerFilterList) {
                    if (factoryContainerDto.getDefaultIdentifier().equals(FactoryContainerDefaultIdentifierEnum.DEFAULT_ADDRESS.getCode())) {
                        defaultFlag = true;
                    }
                    factoryContainerDto.setFactoryInfoId(id);
                }
                if (!defaultFlag) {
                    throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "请选择一条有数据的地址作为默认地址");
                }
                factoryContainerService.insertOrUpdateContainerInfo(factoryContainerFilterList);
            } else {
                factoryContainerService.deleteByFactoryInfoId(FactoryContainerDto.builder().factoryInfoId(id).build());
            }
            factoryFinancialDto.setFactoryInfoId(id);
            factoryFinancialService.insertOrUpdateFinancialInfo(CollectionUtil.toList(factoryDataCommand.getFactoryFinancialDto()));

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
                    ResultDTO resultDTO = restTemplateUtils.post(ProductSnapshotForm.builder()
                            .factoryInfoIds(Collections.singletonList(id)).build(), ResultDTO.class, SNAPSHOT_SAVE_PRODUCTS_INSERT_FACTORY_URL);
                    if (!Objects.equals(resultDTO.getStatus(), ResponseCodeEnum.OK.getCode())) {
                        log.error("更新供应商后，更新产品快照失败，异常原因：{}", resultDTO.getMessage());
                        throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "更新供应商后，更新产品快照失败，异常原因：" + resultDTO.getMessage());
                    }
                }
            });
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, e.getMessage());
        }
        return true;
    }

    @Override
    public List<FactoryInfoDO> insertOrUpdateFactoryInfo(List<FactoryInfoDto> factoryInfoDtoList) {
        List<FactoryInfoDO> factoryInfoDOS = factoryAssembler.factoryInfoDtoListToDoList(factoryInfoDtoList);
        factoryInfoRepository.saveOrUpdateBatch(factoryInfoDOS);
        return factoryInfoDOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteInfo(String factoryInfoId) {
        List<SelfProductDO> slfProductDOList = selfProductRepositoryImpl.getSelfProductByFactoryInfoCode(factoryInfoId);
        if (CollectionUtil.isNotEmpty(slfProductDOList)) {
            throw new BusinessException(ResponseCodeEnum.ERROR.getCode(), "禁用失败，请先解绑商品后再删除");
        }
        factoryInfoRepository.updateById(FactoryInfoDO.builder().disableStatus(DisableStatusEnum.DISABLE.getCode()).id(factoryInfoId).build());
        //factoryFinancialService.deleteByFactoryInfoId(factoryInfoId);
        //factoryContainerService.deleteByFactoryInfoId(FactoryContainerDto.builder().factoryInfoId(factoryInfoId).build());
        return true;
    }

    @Override
    public List<OrderTrackerSearchVo> getOrderTrackerList(UserParams params, UserPostEnum userPostEnum) {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());

        ResultDTO resultDTO = restTemplateUtil.post(params, ResultDTO.class, SYS_USER_INFO);
        List userList = JSON.to(List.class, resultDTO.getData());

        if (userPostEnum != null && CollectionUtil.isEmpty(userList)) {
            throw new BusinessException(ResponseCodeEnum.ERROR.getCode(), params.getNickName() + "不存在");
        }
        List<OrderTrackerSearchVo> orderTrackerList = new ArrayList<>();
        userList.forEach(i -> {
            UserInteriorVO userInteriorVO = JSON.to(UserInteriorVO.class, i);
            if (userPostEnum == null || userPostEnum.getCode().equals(userInteriorVO.getPostId())) {
                orderTrackerList.add(OrderTrackerSearchVo.builder()
                        .userName(userInteriorVO.getUserName())
                        .nickName(userInteriorVO.getNickName())
                        .build());
            }
        });
        if (userPostEnum != null && CollectionUtil.isEmpty(orderTrackerList)) {
            throw new BusinessException(ResponseCodeEnum.ERROR.getCode(), params.getNickName() + "的职位不是" + userPostEnum.getMessage());
        }
        return orderTrackerList;
    }

    public List<FactoryInfoDO> insertFactoryByImport(FactoryImportDataDto factoryImportDataDto) {
        List<FactoryInfoDto> factoryInfoDto = factoryImportDataDto.getFactoryInfoDto();
        List<FactoryContainerDto> factoryContainerDto = factoryImportDataDto.getFactoryContainerDto();
        Map<String, FactoryFinancialDto> factoryFinancialDtoMap = factoryImportDataDto.getFactoryFinancialDto();
        List<FactoryInfoDO> factoryInfoDOS = new ArrayList<>();
        // 获取需要更新的currency不同的数据
        List<String> needUpdateCurrencyInfoList = new ArrayList<>();
        try {
            if (CollectionUtil.isNotEmpty(factoryInfoDto)) {
                needUpdateCurrencyInfoList = factoryInfoDto.stream()
                        .map(FactoryInfoDto::getId)
                        .filter(StrUtil::isNotBlank).collect(Collectors.toList());
                factoryInfoDOS = insertOrUpdateFactoryInfo(factoryInfoDto);
                if (CollectionUtil.isNotEmpty(factoryInfoDOS)) {
                    List<SelfProductDO> selfProductDOs = new ArrayList<>();
                    List<FactoryInfoDto> selfFactoryInfoDtoList = factoryInfoRepository.listByFactoryInfo(factoryInfoDOS.stream().map(FactoryInfoDO::getId).toList());
                    for (var selfFactory : selfFactoryInfoDtoList) {
                        List<SelfProductDO> selfProductDOList = selfFactory.getSelfProductDOList();
                        String currency = selfFactory.getCurrency();
                        if (CollectionUtil.isNotEmpty(selfProductDOList)) {
                            for (var self : selfProductDOList) {
                                if (!self.getCurrency().equals(currency)) {
                                    selfProductDOs.add(SelfProductDO.builder()
                                            .id(self.getId())
                                            .image(self.getImage())
                                            .priceWithTaxes(self.getPriceWithTaxes())
                                            .remarks(self.getRemarks())
                                            .currency(currency).build());
                                }
                            }
                        }
                    }
                    if (CollectionUtil.isNotEmpty(selfProductDOs)) {
                        selfProductRepository.updateBatchById(selfProductDOs);
                        log.info("更新产品成功：{}", selfProductDOs.size());
                    }
                }
                factoryInfoDOS.forEach(i -> {
                    FactoryFinancialDto factoryFinancialDto = factoryFinancialDtoMap.get(i.getFactoryCode());
                    if (ObjectUtil.isNotEmpty(factoryFinancialDto)) {
                        factoryFinancialDto.setFactoryInfoId(i.getId());
                        factoryFinancialService.insertOrUpdateFinancialInfo(CollectionUtil.toList(factoryFinancialDto));
                    }
                });
                if (CollectionUtil.isNotEmpty(factoryContainerDto)) {
                    Map<String, FactoryInfoDO> collect = factoryInfoDOS.stream().collect(
                            Collectors.toMap(FactoryInfoDO::getFactoryCode,
                                    infoDO -> infoDO,
                                    (existing, replacement) -> replacement));
                    //只处理sheet1中存在的供应商
                    List<FactoryContainerDto> containerList = factoryContainerDto.stream().filter(i -> collect.containsKey(i.getFactoryCode())).toList();

                    if (CollectionUtil.isNotEmpty(containerList)) {
                        for (FactoryContainerDto containerDto : containerList) {
                            containerDto.setFactoryInfoId(collect.get(containerDto.getFactoryCode()).getId());
                            containerDto.setDefaultIdentifier(FactoryContainerDefaultIdentifierEnum.ADDRESS.getCode());
                        }
                        //excel数据分组，查出数据库中不存在的数据，将第一个设置为默认地址
                        Map<String, List<FactoryContainerDto>> containerMap = containerList.stream().collect(Collectors.groupingBy(FactoryContainerDto::getFactoryInfoId));

                        List<String> factoryInfoIdList = new ArrayList<>(containerMap.keySet());
                        List<FactoryContainerDO> factoryContainerDOS = factoryContainerService.listByFactoryInfoIds(factoryInfoIdList);
                        List<String> newFactoryInfoIds = factoryInfoIdList;
                        if (CollectionUtil.isNotEmpty(factoryContainerDOS)) {
                            Set<String> dataBaseInfoIdSet = factoryContainerDOS.stream()
                                    .map(FactoryContainerDO::getFactoryInfoId)
                                    .collect(Collectors.toSet());

                            newFactoryInfoIds = factoryInfoIdList.stream()
                                    .filter(id -> !dataBaseInfoIdSet.contains(id))
                                    .toList();
                        }
                        newFactoryInfoIds.forEach(i -> {
                            List<FactoryContainerDto> factoryContainerDtos = containerMap.get(i);
                            if (CollectionUtil.isNotEmpty(factoryContainerDtos)) {
                                factoryContainerDtos.getFirst().setDefaultIdentifier(FactoryContainerDefaultIdentifierEnum.DEFAULT_ADDRESS.getCode());
                            }
                        });

                        List<FactoryContainerDto> allContainers = containerMap.values().stream()
                                .flatMap(Collection::stream)
                                .collect(Collectors.toList());
                        factoryContainerService.insertOrUpdateContainerInfoByImport(allContainers);
                    }
                }
            }
            List<String> finalNeedUpdateCurrencyInfoList = needUpdateCurrencyInfoList;
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    if (CollectionUtil.isNotEmpty(finalNeedUpdateCurrencyInfoList)) {
                        log.info("currency发生变化的供应商数量: {}", finalNeedUpdateCurrencyInfoList.size());
                        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
                        ResultDTO resultDTO = restTemplateUtils.post(ProductSnapshotForm.builder()
                                .factoryInfoIds(finalNeedUpdateCurrencyInfoList).build(), ResultDTO.class, SNAPSHOT_SAVE_PRODUCTS_INSERT_FACTORY_URL);
                        if (!Objects.equals(resultDTO.getStatus(), ResponseCodeEnum.OK.getCode())) {
                            log.error("更新供应商后，更新产品快照失败，异常原因：{}", resultDTO.getMessage());
                            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "更新供应商后，更新产品快照失败，异常原因：" + resultDTO.getMessage());
                        }
                    }
                }
            });
        } catch (Exception e) {
            log.error("更新工厂失败", e);
        }
        return factoryInfoDOS;

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void updateBuyerFromSelfProduct() {
        try {
            // 1. 获取所有FactoryInfoDto记录
            List<FactoryInfoDto> factoryInfoDtoList = factoryInfoRepository.listByFactoryInfo(null);

            String purchaser = factoryDefaultConstants.getPurchaser();
            String orderTracker = factoryDefaultConstants.getOrderTracker();
            List<SelfProductDO> newSelfProductDOList = new ArrayList<>();
            // 3. 更新FactoryInfoDO表
            List<FactoryInfoDO> factoryInfoDOS = factoryInfoDtoList.stream()
                    .map(dto -> {
                        String purchaserName = dto.getPurchaser();
                        String orderTrackerName = dto.getOrderTracker();
                        List<SelfProductDO> selfProductDOList = dto.getSelfProductDOList();
                        if (CollectionUtil.isNotEmpty(selfProductDOList)) {
                            for (var self : selfProductDOList) {
                                newSelfProductDOList.add(SelfProductDO.builder()
                                        .id(self.getId())
                                        .image(self.getImage())
                                        .priceWithTaxes(self.getPriceWithTaxes())
                                        .remarks(self.getRemarks())
                                        .currency(dto.getCurrency()).build());
                            }
                        }
                        return FactoryInfoDO.builder()
                                .id(dto.getId())
                                .shortName(dto.getShortName())
                                .purchaser(StrUtil.isNotBlank(purchaserName) ? purchaserName : purchaser)
                                .orderTracker(StrUtil.isNotBlank(orderTrackerName) ? orderTrackerName : orderTracker)
                                .remark(dto.getRemark())
                                .build();
                    })
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(factoryInfoDOS)) {
                factoryInfoRepository.updateBatchById(factoryInfoDOS);
                log.info("更新工厂成功：{}", factoryInfoDOS.size());
            }
            if (CollectionUtil.isNotEmpty(newSelfProductDOList)) {
                selfProductRepository.updateBatchById(newSelfProductDOList);
                log.info("更新产品成功：{}", newSelfProductDOList.size());
            }

            // 在事务提交后执行
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    if (CollectionUtil.isNotEmpty(newSelfProductDOList)) {
                        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
                        ResultDTO resultDTO = restTemplateUtils.post(ProductSnapshotForm.builder()
                                .selfSkuIds(newSelfProductDOList.stream().map(SelfProductDO::getId).toList()).build(), ResultDTO.class, SNAPSHOT_SAVE_PRODUCTS_INSERT_URL);
                        if (!Objects.equals(resultDTO.getStatus(), ResponseCodeEnum.OK.getCode())) {
                            log.error("更新供应商后，更新产品快照失败，异常原因：{}", resultDTO.getMessage());
                            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "更新供应商后，更新产品快照失败，异常原因：" + resultDTO.getMessage());
                        }
                    }
                }
            });

        } catch (Exception e) {
            log.error("更新工厂失败", e);
        }
    }
}
