package com.inventory_server.model.warning.urgentPurchase.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;


/**
 * @Description 加急补货实体类
 * <AUTHOR>
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_warning_urgent_purchase")
public class WarningUrgentPurchaseDO extends BaseEntity {

  private String snapshotId;
  private String inventoryInfoId;

  /**
   * 加急补货日
   */
  private LocalDate urgentPurchaseDate;
  private LocalDate urgentFactoryFinishedDate;
  private Integer urgentPurchaseNum;
  private String urgentPurchaseDateRange;

}
