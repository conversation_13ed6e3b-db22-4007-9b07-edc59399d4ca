package com.purchase.purchase_server.repository.dataRepository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.dataObject.SelfProductDO;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.mapper.SelfProductMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 自定义产品repository层
 * <AUTHOR>
 * @Date 2024/1/11 20:13
 **/
@Service
public class SelfProductRepositoryImpl extends ServiceImpl<SelfProductMapper, SelfProductDO> {

    public Integer getContainerLoadByVirtualSkuId(String virtualSkuId) {
        SelfProductDO selfProductDO = getSelfProductInfoByVirtualSkuId(virtualSkuId);
        return selfProductDO.getContainerLoad();
    }

    public Integer getContainerLoadByVirtualSku(String virtualSku) {
        SelfProductDO selfProductDO = getSelfProductInfoByVirtualSku(virtualSku);
        return selfProductDO.getContainerLoad();
    }

    public SelfProductDO getSelfProductInfoByVirtualSkuId(String virtualSkuId) {
        return baseMapper.selectJoinOne(SelfProductDO.class, new MPJLambdaWrapper<SelfProductDO>()
                .selectAll(SelfProductDO.class)
                .leftJoin(VirtualProductDO.class, VirtualProductDO::getSelfProductSkuId, SelfProductDO::getId)
                .eq(VirtualProductDO::getId, virtualSkuId));
    }

    public List<SelfProductDO> getSelfProductInfoBySelfIdList(List<String> selfIdList) {
        return baseMapper.selectBatchIds(selfIdList);
    }

    public SelfProductDO getSelfProductInfoByVirtualSku(String virtualSku) {
        return baseMapper.selectJoinOne(SelfProductDO.class, new MPJLambdaWrapper<SelfProductDO>()
                .selectAll(SelfProductDO.class)
                .leftJoin(VirtualProductDO.class, VirtualProductDO::getSelfProductSkuId, SelfProductDO::getId)
                .eq(VirtualProductDO::getVirtualSku, virtualSku));
    }

    public List<SelfProductDO> selectSelfProductListAndDeleted(List<String> selfProductIds) {
        return baseMapper.selectSelfProductListAndDeleted(selfProductIds);
    }

    public List<SelfProductDO> getSelfProductByFactoryInfoCode(String val) {
        return baseMapper.selectList(Wrappers.<SelfProductDO>lambdaQuery().eq(SelfProductDO::getFactoryId, val).last("limit 1"));
    }
}
