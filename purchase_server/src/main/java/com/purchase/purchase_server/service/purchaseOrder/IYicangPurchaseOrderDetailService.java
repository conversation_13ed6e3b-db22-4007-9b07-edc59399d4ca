package com.purchase.purchase_server.service.purchaseOrder;

import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderDetailDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrdersSummaryDto;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.YicangPurchaseOrderDetailDTO;
import com.purchase.purchase_server.entity.form.PurchaseOrdersForm;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrderVirtualVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cm_yicang_purchase_order_detail(易仓采购单明细)】的数据库操作Service
* @createDate 2024-11-01 14:30:43
*/
public interface IYicangPurchaseOrderDetailService {

    /**
     * 根据易仓采购单id查询数据
     * @param dto 采购单详情
     * @return 易仓采购单明细数据
     */
    List<YicangPurchaseOrderDetailDO> select(YicangPurchaseOrderDetailDTO dto);

    /**
     * 删除数据
     * @param sbPoId
     */
    void removeBySbPoId(String sbPoId);

    /**
     * 批量保存
     */
    void saveBatch(List<YicangPurchaseOrderDetailDO> detailDOS);


    void updateOrderDetails(OrdersSummaryDto order, String sbPoId,
                            List<String> failResult);


    void saveOrderDetails(OrdersSummaryDto order, String sbPoId ,String poCode, List<String> failResult);

    /**
     * 采购单详情
     *
     * @param form 查询条件
     */
    List<YicangPurchaseOrderVirtualVO> getOrderDetail(PurchaseOrdersForm form);

    }
