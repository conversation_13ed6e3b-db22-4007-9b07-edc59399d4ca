package com.purchase.purchase_server.utils.easyExcelUtil.listener.factory.redundant;


import com.purchase.purchase_server.utils.easyExcelUtil.listener.factory.AbstractImportListener;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


/**
 * @Description 导入供应商抽象类
 * <AUTHOR>
 * @Date 2024/6/6 11:35
 **/
@Slf4j
public abstract class AbstractFactoryImportListener<T> extends AbstractImportListener<T> {

    public AbstractFactoryImportListener(String fileName, List<String> errorList) {
        super(fileName, errorList);
    }
}
