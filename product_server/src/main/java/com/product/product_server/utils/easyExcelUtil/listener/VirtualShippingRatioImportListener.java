package com.product.product_server.utils.easyExcelUtil.listener;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.util.ConverterUtils;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.product.product_server.entity.dataObject.VirtualProductDO;
import com.product.product_server.entity.dataObject.VirtualShippingRatioDO;
import com.product.product_server.entity.dto.SenboWarehouseDto;
import com.product.product_server.model.virtualProduct.ShippingRatioDp;
import com.product.product_server.repository.VirtualProductRepositoryImpl;
import com.product.product_server.repository.VirtualShippingRatioRepositoryImpl;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @Description 导入虚拟sku
 * <AUTHOR>
 * @Date 2023/12/21 11:33
 **/
@Slf4j
public class VirtualShippingRatioImportListener extends AbstractProductImportListener<HashMap<Integer, String>> {

    private final VirtualProductRepositoryImpl virtualProductRepository = ApplicationContextProvider.getBean(VirtualProductRepositoryImpl.class);

    private final VirtualShippingRatioRepositoryImpl virtualShippingRatioRepository = ApplicationContextProvider.getBean(VirtualShippingRatioRepositoryImpl.class);


    private final List<SenboWarehouseDto> senboWarehouseList;

    private Map<Integer, Integer> headWarehouseMap;

    public VirtualShippingRatioImportListener(String fileName, String importExcelId, List<SenboWarehouseDto> senboWarehouseList) {
        super(fileName, importExcelId);
        this.senboWarehouseList = senboWarehouseList;
        this.headWarehouseMap = new HashMap<>();
    }

    @Override
    public void invoke(HashMap<Integer, String> data, AnalysisContext analysisContext) {
        log.warn("导入发货比例-------------导入excel进行中，成功解析excel");
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > 50000) {
            throw new RuntimeException("导入数据超过50000行，请缩小导入数据量");
        }
        String virtualProduct = data.get(0);
        // 校验虚拟sku是否存在
        VirtualProductDO virtualProductDo = virtualProductRepository.getVirtualProductByVirtualSku(virtualProduct);
        if (ObjectUtil.isEmpty(virtualProductDo)) {
            throw new IllegalArgumentException(virtualProduct + "该虚拟sku不存在");
        }
        Map<Integer, String> senboWarehouseKeyMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));
        HashMap<Integer, String> shippingRatioMap = new HashMap<>();
        for (Map.Entry<Integer, Integer> entry : headWarehouseMap.entrySet()) {
            shippingRatioMap.put(entry.getValue(), data.get(entry.getKey()));
        }
        new ShippingRatioDp(shippingRatioMap, senboWarehouseKeyMap);
        List<VirtualShippingRatioDO> virtualShippingRatioDOList = new ArrayList<>();
        shippingRatioMap.forEach((key, value) -> {
            // 移除百分号并转换为 BigDecimal
            BigDecimal ratio = new BigDecimal(value.replace("%", ""));
            // 除以 100 并保留 4 位小数，使用四舍五入
            BigDecimal adjustedRatio = ratio.divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);

            if (adjustedRatio.compareTo(BigDecimal.ZERO) > 0) {
                VirtualShippingRatioDO virtualShippingRatioDO = new VirtualShippingRatioDO();
                virtualShippingRatioDO.setVirtualSkuId(virtualProductDo.getId());
                virtualShippingRatioDO.setWarehouseId(key);
                virtualShippingRatioDO.setShippingRatio(adjustedRatio.doubleValue());
                virtualShippingRatioDOList.add(virtualShippingRatioDO);
            }
        });
        virtualShippingRatioRepository.deleteVirtualShippingRatio(virtualProductDo.getId());
        virtualShippingRatioRepository.saveBatch(virtualShippingRatioDOList);
        successfulTimes++;
    }

    /**
     * 这里会一行行的返回头
     *
     * @param headMap
     * @param context
     */
    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        log.info("解析到一条头数据:{}", JSON.toJSONString(headMap));

        Map<Integer, String> headIntegerMap = ConverterUtils.convertToStringMap(headMap, context);
        //删除headIntegerMap中的空值
        headIntegerMap.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty());

        Integer rowIndex = context.readRowHolder().getRowIndex();
        if (rowIndex == 0 && !headIntegerMap.get(0).equals("* 虚拟SKU")) {
            throw new RuntimeException("表头错误请确认表头");
        }
        if (rowIndex == 1) {
            Map<String, Integer> senboWarehouseMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));

            for (Map.Entry<Integer, String> entry : headIntegerMap.entrySet()) {
                Integer key = entry.getKey();
                if (key > 0) {
                    String warehouseName = entry.getValue();
                    Integer warehouseId = senboWarehouseMap.getOrDefault(warehouseName, null);
                    if (warehouseId == null) {
                        throw new RuntimeException(warehouseName + "仓库不存在，表头错误请确认表头");
                    }
                    headWarehouseMap.put(key, warehouseId);
                }
            }
            ;
        }
    }
}
