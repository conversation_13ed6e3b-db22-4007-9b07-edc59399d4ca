package com.operation.logs.operation_logs.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.utils.dp.LogTrackNumDto;
import com.crafts_mirror.utils.dp.LogTrackNumPageDto;
import com.operation.logs.operation_logs.entity.ResultDTO;
import com.operation.logs.operation_logs.entity.dto.LogListByTypeDto;
import com.operation.logs.operation_logs.entity.dto.LogListDto;
import com.operation.logs.operation_logs.entity.vo.LogListVo;
import com.operation.logs.operation_logs.service.IOperationLogService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * @Description 日志中心外部controller层
 * <AUTHOR>
 * @Date 2023/12/13 19:40
 **/
@RestController
@RequestMapping("operation/exterior")
public class OperationLogExteriorController {

    @Resource
    private IOperationLogService operationLogService;

    @PostMapping("/list")
    @ResponseBody
    ResultDTO<LogListVo> selectList(@RequestBody LogTrackNumDto num) {
        return ResultDTO.success(LogListVo.builder().list(operationLogService.getLogList(num)).build());
    }

    @PostMapping("/pageList")
    @ResponseBody
    ResultDTO<IPage<LogListDto>> selectPageList(@RequestBody LogTrackNumPageDto num) {
        return ResultDTO.success(operationLogService.getLogPageList(num));
    }

    @PostMapping("/list/byType")
    @ResponseBody
    ResultDTO<LogListVo> selectListByType(@RequestBody LogListByTypeDto num) {
        return ResultDTO.success(LogListVo.builder().list(operationLogService.getLogListByType(num)).build());
    }

    @PostMapping("/page/byType")
    ResultDTO<IPage<LogListDto>> selectPageByType(@RequestBody LogListByTypeDto num) {
        return ResultDTO.success(operationLogService.getLogPageByType(num));
    }
}
