package com.purchase.purchase_server.enums;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentVirtualSkuPurchaseDO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum RepVirtualSkuPurchaseDirectionAndSortEnum {

    SUB_ENTITY_TATE("subEntityRate", "子体达成率"),
    PARENT_ENTITY_RATE("parentEntityRate","父体达成率"),

    ASC("asc","升序"),
    DESC("desc","降序");

    private final String code;
    private final String desc;
    public static RepVirtualSkuPurchaseDirectionAndSortEnum ofCode(String code) {
        return Arrays.stream(RepVirtualSkuPurchaseDirectionAndSortEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
    public static List<String> getCodeList() {
        return Arrays.stream(RepVirtualSkuPurchaseDirectionAndSortEnum.values())
                .map(RepVirtualSkuPurchaseDirectionAndSortEnum::getCode).collect(Collectors.toList());
    }

    public static final Map<String, SFunction<ReplenishmentVirtualSkuPurchaseDO, ?>> sortMap;
    static {
        sortMap = Map.of(
                SUB_ENTITY_TATE.getCode(), ReplenishmentVirtualSkuPurchaseDO::getSubEntityRate,
                PARENT_ENTITY_RATE.getCode(), ReplenishmentVirtualSkuPurchaseDO::getParentEntityRate
        );
    }
}
