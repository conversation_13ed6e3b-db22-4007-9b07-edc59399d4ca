package com.purchase.purchase_server.aspect;

import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.dp.OperationLogListForm;
import com.crafts_mirror.utils.utils.JwtUtil;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.response.ResultDTO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Objects;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.Constants.AUTHORIZATION;
import static com.crafts_mirror.utils.constant.SystemConstant.OPERATION_LOGS_SYSTEM_SAVE_BATCH_LOG_URL;

/**
 * @Description 业务日志Aspect层
 * <AUTHOR>
 * @Date 2023/12/13 13:52
 **/
@Component
@Slf4j
@Aspect
public class OperationLogAspect {

    @Resource
    private RestTemplate restTemplate;

    @Pointcut("@annotation(com.crafts_mirror.utils.aop.OperationLog)")
    public void operationLogPointCut() {
    }

    @AfterReturning(value = "operationLogPointCut() && @annotation(operationLog)", returning = "obj")
    public void saveOperationLog(JoinPoint joinPoint, OperationLog operationLog, Object obj) {

        // 获取追踪号
        Object[] args = joinPoint.getArgs();
        LogTrackNumDto dto = Arrays.stream(args)
                .filter(arg -> arg instanceof LogTrackNumDto)
                .map(arg -> ((LogTrackNumDto) arg))
                .findFirst().orElse(new LogTrackNumDto(new HashMap<String, String>(), ""));

        // 获取操作人
        String authorization;
        if(RequestContextHolder.getRequestAttributes() != null) {
            HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            authorization = Optional.ofNullable(request.getHeader(AUTHORIZATION)).orElse(dto.getAuthorization());
        } else {
            authorization = dto.getAuthorization();
        }
        String username = (String) JwtUtil.getClaimsByToken(authorization).get("username");
        String operator = StrUtil.isNotBlank(username) ? username : operationLog.operator();

        // 保存操作日志
        OperationLogListForm form = new OperationLogListForm(operator, dto.getLogMap(), operationLog.operationType()
                , operator, operator);

        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, authorization);
        restTemplateUtils.post(form, ResultDTO.class, OPERATION_LOGS_SYSTEM_SAVE_BATCH_LOG_URL);
    }
}
