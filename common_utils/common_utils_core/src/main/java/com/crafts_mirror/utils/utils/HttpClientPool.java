package com.crafts_mirror.utils.utils;

import com.crafts_mirror.utils.common.entity.HttpRequestDetail;
import com.crafts_mirror.utils.handler.HttpClientResponseEntityHandler;
import com.crafts_mirror.utils.listener.HttpResponseListener;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.List;
import java.util.concurrent.*;

/**
 * @Description HTTP请求工具类
 * <AUTHOR>
 * @Date 2024/6/28 11:01
 **/
@Slf4j
public class HttpClientPool<T> {
    private final HttpClient httpClient;
    private final ExecutorService scheduler;
    private final List<HttpRequestDetail<T>> requestDetailList;
    private final HttpResponseListener<T> responseListener;

    private final RateLimiter rateLimiter;

    private static final int DEFAULT_LIMIT_PER_SEC = 1000;

    public HttpClientPool() {
        this.httpClient = HttpClient.newBuilder()
                .version(HttpClient.Version.HTTP_1_1)
                .connectTimeout(Duration.ofSeconds(1))
                .build();
        this.requestDetailList = null;
        scheduler = null;
        responseListener = null;
        rateLimiter = null;
    }

    public HttpClientPool(List<HttpRequestDetail<T>> requestDetailList) {
        this.httpClient = HttpClient.newBuilder()
                .version(HttpClient.Version.HTTP_1_1)
                .connectTimeout(Duration.ofSeconds(1))
                .build();
        this.requestDetailList = requestDetailList;
        scheduler = null;
        responseListener = null;
        rateLimiter = null;
    }

    public HttpClientPool(List<HttpRequestDetail<T>> requestDetailList, HttpResponseListener<T> responseListener, RateLimiter rateLimiter) {
        this.httpClient = HttpClient.newBuilder()
                .version(HttpClient.Version.HTTP_1_1)
                .connectTimeout(Duration.ofSeconds(1))
                .build();
        this.scheduler = Executors.newVirtualThreadPerTaskExecutor();
        this.requestDetailList = requestDetailList;
        this.responseListener = responseListener;
        this.rateLimiter = rateLimiter;
    }

    public void startAsyncRequests() {
        if (scheduler == null || responseListener == null) {
            throw new RuntimeException("该连接池实例不支持异步请求，请先初始化监听者以及线程池");
        }
        try {
            log.info("此次请求一共需要发送：{}", requestDetailList.size());
            List<CompletableFuture<Void>> collect = requestDetailList.stream().map(this::sendAsyncRequest).toList();

            // 所有请求完成后的通知操作
            CompletableFuture.allOf(collect.toArray(new CompletableFuture[0]))
                    .thenRun(responseListener::afterAll)
                    .exceptionally(e -> {
                        log.error("处理请求时发生错误", e);
                        return null;
                    });
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    private HttpRequest getHttpRequest(HttpRequestDetail<T> requestDetail) {
        HttpRequest request;
        switch (requestDetail.method()) {
            case GET -> request = HttpRequest.newBuilder()
                    .uri(requestDetail.uri())
                    .GET()
                    .timeout(Duration.ofSeconds(10))
                    .build();
            case POST -> request = HttpRequest.newBuilder()
                    .uri(requestDetail.uri())
                    .POST(HttpRequest.BodyPublishers.ofString(requestDetail.body()))
                    .timeout(Duration.ofSeconds(10))
                    .header("Content-Type", "application/json")
                    .build();
            default -> throw new IllegalArgumentException("暂不支持该请求方式， 请更换");
        }
        return request;
    }

    public T sendRequest(HttpRequestDetail<T> requestDetail) {
        HttpRequest httpRequest = getHttpRequest(requestDetail);
        try {
            HttpResponse<T> response = httpClient.send(httpRequest, new HttpClientResponseEntityHandler<>(requestDetail.clazz()));
            if (response.statusCode() != 200) {
                log.error("http请求异常，请求url：{}，请求参数：{}", httpRequest.uri().getPath(), requestDetail.body());
                throw new IllegalArgumentException(String.format("http请求异常，请求url：%s，请求参数：%s", httpRequest.uri().getPath(), requestDetail.body()));
            }
            return response.body();
        } catch (IOException | InterruptedException e) {
            log.error("http请求异常，请求url：{}，请求参数：{}，异常原因：{}", httpRequest.uri().getPath(), requestDetail.body(), e.getMessage());
            throw new IllegalArgumentException(String.format("http请求异常，请求url：%s，请求参数：%s，异常原因：%s", httpRequest.uri().getPath(), requestDetail.body(), e.getMessage()));
        }
    }

    private CompletableFuture<Void> sendAsyncRequest(HttpRequestDetail<T> requestDetail) {
        HttpRequest httpRequest = getHttpRequest(requestDetail);
        rateLimiter.acquire();
        return httpClient.sendAsync(httpRequest, new HttpClientResponseEntityHandler<>(requestDetail.clazz()))
                .thenAccept(responseListener::onResponse)
                .exceptionally(e -> {
                    responseListener.onError(e, requestDetail);
                    return null;
                });
    }

    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(60, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
