package com.auth.authorization_server.repository;

import com.auth.authorization_server.mapper.RoleMenuMapper;
import com.auth.authorization_server.model.dataObject.RoleMenuDO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class RoleMenuRepositoryImpl extends ServiceImpl<RoleMenuMapper, RoleMenuDO> {

    public Long checkMenuExistRole(String menuId){
        return baseMapper.selectCount(Wrappers.<RoleMenuDO>lambdaQuery()
                .eq(RoleMenuDO::getMenuId, menuId));
    }

    /**
     * 通过角色ID删除角色和菜单关联
     *
     * @param roleId 角色ID
     */
    public void deleteRoleMenuByRoleId(String roleId){
        baseMapper.delete(Wrappers.<RoleMenuDO>lambdaQuery()
                .eq(RoleMenuDO::getRoleId, roleId));
    };

    /**
     * 批量删除角色菜单关联信息
     *
     * @param ids 需要删除的数据ID
     */
    public void deleteRoleMenu(List<String> ids){
        baseMapper.delete(Wrappers.<RoleMenuDO>lambdaQuery()
                .in(RoleMenuDO::getRoleId, ids));
    };
}
