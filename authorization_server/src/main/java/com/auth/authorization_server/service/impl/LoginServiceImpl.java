package com.auth.authorization_server.service.impl;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.auth.authorization_server.enums.UserStatusEnum;
import com.auth.authorization_server.model.AccountUser;
import com.auth.authorization_server.model.dataObject.UserDO;
import com.auth.authorization_server.model.entity.menuDto;
import com.auth.authorization_server.repository.UserRepositoryImpl;
import com.auth.authorization_server.req.UserLoginDTO;
import com.auth.authorization_server.service.ILoginService;
import com.auth.authorization_server.service.ISysUserService;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.common.entity.LoginVo;
import com.auth.authorization_server.domain.dto.UserDTO;
import com.crafts_mirror.utils.constant.RedisKeyConstant;
import com.crafts_mirror.utils.utils.JwtUtil;
import com.crafts_mirror.utils.utils.RsaUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.Constants.AUTHORIZATION;

/**
 * @Description 注册、登录方法
 * <AUTHOR>
 * @Date 2023/11/30 13:45
 **/
@Service
@Slf4j
public class LoginServiceImpl implements ILoginService {

    @Resource
    private UserRepositoryImpl userRepositoryImpl;

    @Resource
    private ISysUserService userService;
    @Resource
    private RedisTemplate<String, LoginVo> redisTemplate;

    @Resource
    private RedisTemplate<String, String> redisTemplateSet;
    @Resource
    private AuthenticationManager authenticationManager;

    @Override
    public LoginVo login(UserLoginDTO userDTO, String authorization) throws Exception {
        UserDTO user = userService.selectUserByUserName(userDTO.getUsername());
        if (user == null) {
            throw new BadCredentialsException("用户不存在");
        }
        if (UserStatusEnum.NO_STATUS.getCode().equals(user.getUserStatus())){
            throw new BadCredentialsException("对不起，您的账号：" + user.getUserName() + " 已停用");
        }
        byte[] decode = Base64.decode(userDTO.getPassword());
        String password = RsaUtils.getPassword(decode);
        String saltPassword = password + user.getSalt();
        UsernamePasswordAuthenticationToken unauthenticated = UsernamePasswordAuthenticationToken.unauthenticated(userDTO.getUsername(), saltPassword);
        Authentication authenticate = authenticationManager.authenticate(unauthenticated);
        SecurityContextHolder.getContext().setAuthentication(authenticate);
        String token = JwtUtil.generateToken(user.getId(), authenticate.getName());

        AccountUser principal = (AccountUser) authenticate.getPrincipal();

        LoginVo loginVo = userService.info(principal.getName());
        loginVo.setToken(token);
        loginVo.setUserId(principal.getUserId());
        loginVo.setUserName(authenticate.getName());
        loginVo.setNickName(user.getNickName());


        // 删除老的登录信息
        redisTemplate.delete(RedisKeyConstant.USER_TOKEN_KEY + principal.getUserId() + ":" + authorization);
        // 将登录信息存入redis中，方便在其他微服务中获取
        redisTemplate.opsForValue().set(RedisKeyConstant.USER_TOKEN_KEY + principal.getUserId() + ":" + token, loginVo, 6, TimeUnit.HOURS);
        return loginVo;
    }

    private Set<String> getTargetAuthorizations(Authentication authenticate, String... targets) {
        return authenticate.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .filter(authority -> Arrays.stream(targets).anyMatch(t -> StrUtil.startWithIgnoreCase(authority, t)))
                .collect(Collectors.toSet());
    }

    @Override
    public void logout(HttpServletRequest request, HttpServletResponse response) {
        // 退出登录
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            //清除认证
            new SecurityContextLogoutHandler().logout(request, response, auth);
            String authorization = request.getHeader(AUTHORIZATION);
            redisTemplate.delete(RedisKeyConstant.USER_TOKEN_KEY + SecurityUtils.getUserId() + ":" + authorization);
            log.info(JwtUtil.getClaimsByToken(authorization).get("username") + "退出登录成功");
        }
    }

    @Override
    public void register(UserDO userDO) {
        //boolean exists = userRepositoryImpl.exists(new LambdaQueryWrapper<UserDO>().eq(UserDO::getUsername, userDO.getUsername()));
        //if (exists) {
        //    throw new RuntimeException("该用户已被注册");
        //}
        //String salt = UUID.randomUUID().toString();
        //String password = userDO.getPassword();
        //String passwordEncrypt = new BCryptPasswordEncoder().encode(password + salt);
        //userDO.setPassword(passwordEncrypt);
        //userDO.setSalt(salt);
        //
        //userRepositoryImpl.save(userDO);
    }

    @Override
    public menuDto getMenuList() {
        Map<String, Set<String>> result = new HashMap<>();

        // Scan for keys matching the pattern
        ScanOptions options = ScanOptions.scanOptions().match(RedisKeyConstant.MENU).count(1000).build();
        Set<String> keys = new HashSet<>();
        redisTemplateSet.execute((RedisCallback<Void>) connection -> {
            try (var cursor = connection.scan(options)) {
                while (cursor.hasNext()) {
                    keys.add(new String(cursor.next(), StandardCharsets.UTF_8));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        });

        // Retrieve values for all keys using pipeline
        List<Object> pipelineResults = redisTemplateSet.executePipelined((RedisCallback<Object>) connection -> {
            for (String key : keys) {
                connection.sMembers(redisTemplateSet.getStringSerializer().serialize(key));
            }
            return null;
        });

        // Process pipeline results
        int index = 0;
        for (String key : keys) {
            @SuppressWarnings("unchecked")
            Set<String> members = (Set<String>) pipelineResults.get(index++);
            if (members != null) {
                String mapKey = key.split(":")[1];
                result.put(mapKey, members);
            }
        }
        menuDto menuDto = new menuDto();
        menuDto.setMenuMap(result);
        return menuDto;
    }
}
