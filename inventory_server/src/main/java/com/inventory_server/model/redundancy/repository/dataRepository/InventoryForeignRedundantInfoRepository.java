package com.inventory_server.model.redundancy.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import com.inventory_server.model.redundancy.entity.bo.InventoryForeignRedundantBO;
import com.inventory_server.model.redundancy.entity.dos.InventoryForeignRedundantInfoDO;
import com.inventory_server.model.redundancy.entity.dos.InventoryForeignShipWatchBoardDO;
import com.inventory_server.model.redundancy.mapper.InventoryForeignRedundantInfoMapper;
import com.inventory_server.model.warehouse.entity.dos.WarehouseSenboInfoDO;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.inventory_server.model.redundancy.enums.RedundantTypeEnum.FOREIGN_INVENTORY;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/10 17:12
 **/
@Service
public class InventoryForeignRedundantInfoRepository extends ServiceImpl<InventoryForeignRedundantInfoMapper, InventoryForeignRedundantInfoDO> {

    public List<InventoryForeignRedundantBO> getForeignRedundantList(List<String> infoIdList) {
        return baseMapper.selectJoinList(InventoryForeignRedundantBO.class, new MPJLambdaWrapper<InventoryForeignRedundantInfoDO>()
                .select(WarehouseSenboInfoDO::getWarehouse)
                .select(InventoryForeignRedundantInfoDO::getRedundantNum)
                .select(InventoryForeignRedundantInfoDO::getWatchBoardId)
                .selectAs(InventoryForeignShipWatchBoardDO::getInventoryInfoId, "inventoryInfoId")
                .leftJoin(InventoryForeignShipWatchBoardDO.class, InventoryForeignShipWatchBoardDO::getId, InventoryForeignRedundantInfoDO::getWatchBoardId)
                .leftJoin(WarehouseSenboInfoDO.class, WarehouseSenboInfoDO::getId, InventoryForeignRedundantInfoDO::getWarehouse)
                .in(InventoryForeignShipWatchBoardDO::getInventoryInfoId, infoIdList)
                .eq(InventoryForeignShipWatchBoardDO::getRedundantType, FOREIGN_INVENTORY.getCode())
                .orderByAsc(InventoryForeignRedundantInfoDO::getWarehouse));
    }

    public List<InventoryForeignRedundantBO> getForeignRedundantListByWatchBoardId(String watchBoardId) {
        return baseMapper.selectJoinList(InventoryForeignRedundantBO.class, new MPJLambdaWrapper<InventoryForeignRedundantInfoDO>()
                .select(WarehouseSenboInfoDO::getWarehouse)
                .select(InventoryForeignRedundantInfoDO::getRedundantNum)
                .select(InventoryForeignRedundantInfoDO::getWatchBoardId)
                .leftJoin(InventoryForeignShipWatchBoardDO.class, InventoryForeignShipWatchBoardDO::getId, InventoryForeignRedundantInfoDO::getWatchBoardId)
                .leftJoin(WarehouseSenboInfoDO.class, WarehouseSenboInfoDO::getId, InventoryForeignRedundantInfoDO::getWarehouse)
                .eq(InventoryForeignShipWatchBoardDO::getId, watchBoardId)
                .eq(InventoryForeignShipWatchBoardDO::getRedundantType, FOREIGN_INVENTORY.getCode())
                .orderByAsc(InventoryForeignRedundantInfoDO::getWarehouse));
    }

    /**
     * 物理删除
     *
     * @param watchBoardIds 试算看板表格id
     */
    public void deleteByWatchBoardIds(List<String> watchBoardIds) {
        List<InventoryForeignRedundantInfoDO> list = baseMapper.selectList(Wrappers.<InventoryForeignRedundantInfoDO>lambdaQuery()
                .in(InventoryForeignRedundantInfoDO::getWatchBoardId, watchBoardIds));
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<String> idList = list.stream().map(PhysicalBaseEntity::getId).toList();
        baseMapper.deleteByIds(idList);
    }
}
