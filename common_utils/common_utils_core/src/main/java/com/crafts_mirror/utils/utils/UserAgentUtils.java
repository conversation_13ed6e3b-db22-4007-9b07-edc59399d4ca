package com.crafts_mirror.utils.utils;

import eu.bitwalker.useragentutils.UserAgent;
import lombok.Getter;

/**
 * @Description userAgent工具类
 * <AUTHOR>
 * @Date 2024/8/10 14:17
 **/
@Getter
public class UserAgentUtils {

    private final UserAgent userAgent;

    public UserAgentUtils(String userAgentStr) {
        userAgent = UserAgent.parseUserAgentString(userAgentStr);
    }

    public String getBrowser() {
        return userAgent.getBrowser().getName();
    }

    public String getDevice() {
        return userAgent.getOperatingSystem().getName();
    }
}
