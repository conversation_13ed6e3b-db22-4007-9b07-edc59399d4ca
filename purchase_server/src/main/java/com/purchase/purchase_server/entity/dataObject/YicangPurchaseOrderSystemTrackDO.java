package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 易仓系统跟单状态表
 * @TableName cm_yicang_purchase_order_system_track
 */
@TableName(value ="cm_yicang_purchase_order_system_track")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YicangPurchaseOrderSystemTrackDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 4272741804289769042L;

    private String id;
    /**
     * 跟单状态中文名称
     */
    private String name;

    /**
     * 跟单状态英文名称
     */
    private String nameEn;

    /**
     * 采购单表id
     */
    private String sbPoId;
}