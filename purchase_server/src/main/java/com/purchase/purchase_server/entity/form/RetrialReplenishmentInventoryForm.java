package com.purchase.purchase_server.entity.form;

import com.purchase.purchase_server.entity.dto.replenishment.ReplenishWarehouseInventoryDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 补货计划修改补货数量以及删除对应补货仓
 * <AUTHOR>
 * @Date 2024/1/25 11:56
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RetrialReplenishmentInventoryForm implements Serializable {

    private String virtualPurchaseId;

    private String purchaseNumType;

    private List<ReplenishWarehouseInventoryDto> inventoryList;
}
