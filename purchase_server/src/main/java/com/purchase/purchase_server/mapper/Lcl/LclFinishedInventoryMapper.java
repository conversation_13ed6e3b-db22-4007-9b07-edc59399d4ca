package com.purchase.purchase_server.mapper.Lcl;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.bo.Lcl.LclFinishedInventoryBO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclFinishedInventoryDO;
import com.purchase.purchase_server.entity.dto.Lcl.LclConsolidationNonFinishedInventoryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cm_lcl_finished_inventory(拼柜装柜计划数据表)】的数据库操作Mapper
* @createDate 2024-12-04 16:47:07
* @Entity generator.domain.CmLclFinishedInventory
*/
public interface LclFinishedInventoryMapper extends MPJBaseMapper<LclFinishedInventoryDO> {

    List<LclFinishedInventoryBO> listWithTrial(@Param("lclRecordId") String lclRecordId,
                                               @Param("shippingStartDate") String shippingStartDate);

    int delete(@Param("factoryFinishedId") String factoryFinishedId);

    /**
     * 丢弃该数据，但不是删除，页面上无法搜索到，导出可以拿到该计划
     * @param factoryFinishedId
     * @return
     */
    int dropById(@Param("factoryFinishedId") String factoryFinishedId);

    int deleteByLclRecordId(@Param("lclRecordId") String lclRecordId);

    /**
     * 根据拼柜记录ID查询未完成库存列表
     * @param lclRecordId 拼柜记录ID
     * @return 未完成库存DTO列表
     */
    List<LclConsolidationNonFinishedInventoryDTO> listByLclRecordId(@Param("lclRecordId") String lclRecordId);

}
