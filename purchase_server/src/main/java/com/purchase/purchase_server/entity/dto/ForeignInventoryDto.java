package com.purchase.purchase_server.entity.dto;

import cn.hutool.core.date.DateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 海外库存信息
 * <AUTHOR>
 * @Date 2024/3/12 13:53
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ForeignInventoryDto {

    private String warehouse;

    private String virtualSku;

    private String enableUsingDateString;

    private DateTime enableUsingDate;

    private String startShippingDate;

    private String shipmentCode;

    private Double storeNum;

    private String remarks;
}
