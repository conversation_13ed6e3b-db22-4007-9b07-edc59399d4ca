ALTER TABLE `cm_warehouse_senbo_info`
    ADD COLUMN `remarks` varchar(255) NULL COMMENT '备注' AFTER `sort`;

CREATE TABLE `cm_virtual_shipping_ratio` (
                                             `virtual_sku_id` varchar(50) NOT NULL COMMENT '虚拟skuId',
                                             `warehouse_id` int NOT NULL COMMENT '仓库ID',
                                             `shipping_ratio` double DEFAULT NULL COMMENT '发货比例',
                                             PRIMARY KEY (`virtual_sku_id`,`warehouse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='发货比例表';