package com.purchase.purchase_server.entity.dto.factory;

import com.crafts_mirror.utils.aop.validator.PositiveIntegerOrZero;
import com.purchase.purchase_server.aop.validator.EnumValue;
import com.crafts_mirror.utils.enums.FactoryCurrencyEnum;
import com.purchase.purchase_server.enums.FactoryFinancialPaymentTimeEnum;
import com.purchase.purchase_server.enums.FactoryFinancialSettementEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 供应商信息
 * <AUTHOR>
 * @Date 2024/6/5 20:39
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FactoryFinancialDto implements Serializable {


    @Serial
    private static final long serialVersionUID = -4271177411525096584L;
    private String id;

    /**
     * 币种
     */
    @NotBlank(message = "币种不能为空")
    @EnumValue(enumClass = FactoryCurrencyEnum.class, methodName = "ofDesc", message = "币种类型错误")
    private String currency;
    /**
     * 省份
     */
    @Length(max = 100, message = "省份最多20个字符")
    private String province;
    /**
     * 城市
     */
    @Length(max = 100, message = "城市最多100个字符")
    private String city;
    /**
     * 详情地址
     */
    @Length(max = 255, message = "城市最多255个字符")
    private String detailedAddress;
    /**
     * 结算类型
     * @see com.purchase.purchase_server.enums.FactoryFinancialSettementEnum
     */
    @EnumValue(enumClass = FactoryFinancialSettementEnum.class, message = "结算类型错误")
    private String settlementType;
    /**
     * 定金比例
     */
    @Pattern(regexp = "^(100(\\.0{1,2})?|([1-9]?\\d(\\.\\d{1,2})?)|)$",message = "定金比例最大为100，最多两位小数的非负数")
    private String depositRatio;
    /**
     * 定金支付时间
     * @see com.purchase.purchase_server.enums.FactoryFinancialPaymentTimeEnum
     */
    @EnumValue(enumClass = FactoryFinancialPaymentTimeEnum.class, message = "定金支付时间类型错误")
    private String paymentTime;
    /**
     * 尾款账期
     */
    @PositiveIntegerOrZero(message = "尾款账期必须为正整数", isRequired = false)
    private String finalPaymentTerms;
    /**
     * 备注
     */
    @Length(max = 255, message = "备注最多255个字符")
    private String remark;
    /**
     * 供应商id
     */
    private String factoryInfoId;
}
