alter table cm_factory_info
    comment '供应商信息表';
alter table cm_factory_info
    drop column address_code;
alter table cm_factory_info
    modify factory_code varchar(100) not null comment '供应商代码';
alter table cm_factory_info
    modify factory_name varchar(255) not null comment '供应商名称';
alter table cm_factory_info
    modify short_name varchar(100) not null comment '简称';
alter table cm_factory_info
    add order_tracker varchar(100) null comment '跟单人';
alter table cm_factory_info
    add remark longtext null comment '备注';
alter table cm_factory_info
    add disable_status tinyint not null comment '禁用状态：0-正常，1-禁用';

-- auto-generated definition
create table cm_factory_financial
(
    id                  varchar(50)  not null
        primary key,
    province            varchar(255) null comment '省份',
    city                varchar(255) null comment '城市',
    detailed_address    varchar(255) null comment '详情地址',
    settlement_type     tinyint      null comment '结算类型: 0-常规结算，1-承兑汇票',
    deposit_ratio       double      null comment '定金比例',
    payment_time        tinyint      null comment '定金支付时间: 0-采购下单后，1-发货后',
    final_payment_terms int          null comment '尾款账期',
    remark              longtext     null comment '备注',
    factory_info_id     varchar(50)  not null,
    create_by           varchar(50)  null,
    create_date         datetime     null,
    update_by           varchar(50)  null,
    update_date         datetime     null,
    status              char         not null
)
    comment '供应商-财务信息表' row_format = DYNAMIC;

-- auto-generated definition
create table cm_factory_container
(
    id                 varchar(50)  not null
        primary key,
    province           varchar(255)  null comment '省份',
    city               varchar(255)  null comment '城市',
    detailed_address   varchar(255) null comment '详情地址',
    address_code       varchar(50)  null comment '地址代号',
    default_identifier tinyint      not null comment '默认地址：0-不是，1-是',
    factory_info_id    varchar(50)  not null comment '供应商信息id',
    create_by          varchar(50)  null comment '创建人',
    create_date        datetime     null comment '创建时间',
    update_by          varchar(50)  null comment '更新人',
    update_date        datetime     null comment '更新时间',
    status             char         not null comment '状态'
)
    comment '供应商-装柜信息表' row_format = DYNAMIC;


