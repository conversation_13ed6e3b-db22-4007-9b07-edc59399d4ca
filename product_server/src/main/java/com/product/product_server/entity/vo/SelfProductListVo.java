package com.product.product_server.entity.vo;

import com.product.product_server.entity.dto.SelfProductDto;
import com.product.product_server.model.products.*;
import com.product.product_server.model.virtualProduct.VirtualSkuAndIdDp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 自定义商品列表页展示
 * <AUTHOR>
 * @Date 2023/12/6 14:21
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SelfProductListVo implements Serializable {
    @Serial
    private static final long serialVersionUID = -1439495840202434L;

    private String selfProductId;
    /**
     * 图片链接
     */
    private String image;

    private String sku;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 备注
     */
    private String remarks;

    private List<VirtualSkuAndIdDp> virtualSkuList;
    /**
     * 采购相关数据
     */
    private PurchaseInfoDp purchaseInfoDp;
    /**
     * 采购人员
     */
    private String buyer;
    /**
     * 单价
     */
    private Double price;
    /**
     * 是否含税
     */
    private Integer isTaxes;
    /**
     * 单品规格
     */
    private SpecificationDp outCaseSpecification;
    /**
     * 装箱量
     */
    private Integer containerLoad;
    /**
     * 单箱毛重
     */
    private Double caseGrossWeight;
    /**
     * 创建编辑相关数据
     */
    private CreatorEditorDp creatorEditorDp;

    /**
     * 供应商id
     */
    private String factoryId;

    /**
     * 供应商代码
     */
    private String factoryCode;

    /**
     * 是否商检
     */
    private Integer commodityInspection;

    /**
     * 件的类型
     *
     * @see com.product.product_server.enums.PCSTypeEnum
     */
    private Integer pcsType;


    /**
     * 大类id
     */
    private String categoryId;
    /**
     * 大类名称
     */
    private String categoryName;

    public static List<SelfProductListVo> convertSelfProductList(List<SelfProductDto> list, boolean showPrice) {
        return list.stream()
                .map(pro -> {
                            Double priceWithTaxes = pro.getPriceWithTaxes();
                            BigDecimal priceWithTaxesBigDecimal = null;
                            BigDecimal priceBigDecimal = null;
                            if (priceWithTaxes != null) {
                                priceWithTaxesBigDecimal = BigDecimal.valueOf(priceWithTaxes);
                            }
                            if (pro.getPrice() != null) {
                                priceBigDecimal = BigDecimal.valueOf(pro.getPrice());
                            }
                            return SelfProductListVo.builder()
                                    .selfProductId(pro.getId())
                                    .image(pro.getImage())
                                    .sku(pro.getSku())
                                    .productName(pro.getProductName())
                                    .remarks(pro.getRemarks())
                                    .purchaseInfoDp(new PurchaseInfoDp(
                                            new PurchaseDateDp(pro.getPurchaseDate()),
                                            pro.getBuyer(),
                                            showPrice ? pro.getCurrency() : null,
                                            showPrice ? new PriceDp(pro.getTaxes(), priceBigDecimal, priceWithTaxesBigDecimal) : null, null
                                    ))
                                    .outCaseSpecification(
                                            new SpecificationDp(pro.getCaseLength(), pro.getCaseWidth(), pro.getCaseHeight()))
                                    .containerLoad(pro.getContainerLoad())
                                    .caseGrossWeight(pro.getSingleCaseGrossWeight())
                                    .creatorEditorDp(
                                            new CreatorEditorDp(pro.getCreateBy(), null, pro.getCreateDate(), pro.getUpdateDate()))
                                    .factoryCode(pro.getFactoryCode())
                                    .categoryId(pro.getCategoryId())
                                    .commodityInspection(pro.getCommodityInspection())
                                    .pcsType(pro.getPcsType())
                                    .build();
                        }
                )
                .collect(Collectors.toList());
    }

}
