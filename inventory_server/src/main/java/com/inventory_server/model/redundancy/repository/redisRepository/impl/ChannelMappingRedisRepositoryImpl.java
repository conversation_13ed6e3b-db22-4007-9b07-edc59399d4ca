package com.inventory_server.model.redundancy.repository.redisRepository.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.inventory_server.model.redundancy.repository.redisRepository.IChannelMappingRedisRepository;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.RedisKeyConstant.CHANNEL_MAPPING;

/**
 * @Description 渠道redis层
 * <AUTHOR>
 * @Date 2025/2/18 11:52
 **/
@Service
public class ChannelMappingRedisRepositoryImpl implements IChannelMappingRedisRepository {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Map<String, String> getChannelMapping() {
        Map<String, String> postMap = JSON.parseObject(stringRedisTemplate.opsForValue().get(CHANNEL_MAPPING), new TypeReference<>() {});
        return Optional.ofNullable(postMap).orElse(new HashMap<>());
    }

}
