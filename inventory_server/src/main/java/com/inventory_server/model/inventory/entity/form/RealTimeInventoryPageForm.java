package com.inventory_server.model.inventory.entity.form;

import com.crafts_mirror.utils.dp.BasePageForm;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description 实时库存列表页搜索条件
 * <AUTHOR>
 * @Date 2025/2/19 10:45
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RealTimeInventoryPageForm extends BasePageForm {
    private String productName;

    private List<String> selfSkuList;

    private List<String> virtualSkuList;

    private List<String> oldSkuList;

    private List<String> YiCangSkuList;

    private String channel;

    private Integer productStatus;

    private List<Integer> productStatusList;

    private Integer productType;

    private Integer subType;

    private String operator;

    private String warehouse;
}
