package com.purchase.purchase_server.entity.dto.delivery;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DeliverySaleDestinationDto implements Serializable {


    @Serial
    private static final long serialVersionUID = 5281152418027976168L;
    private String saleDestinationDate;

   private Double saleDestinationNum;
}