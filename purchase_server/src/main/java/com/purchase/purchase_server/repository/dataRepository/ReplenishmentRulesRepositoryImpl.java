package com.purchase.purchase_server.repository.dataRepository;


import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentCalculationLogDO;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentRulesDO;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentVirtualSkuPurchaseDO;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectBaseParamDto;
import com.purchase.purchase_server.mapper.ReplenishmentRulesMapper;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/6 14:55
 **/
@Service
public class ReplenishmentRulesRepositoryImpl extends ServiceImpl<ReplenishmentRulesMapper, ReplenishmentRulesDO> {

    public ReplenishmentRulesDO getShippingProjectBaseParamByVirtualSku(String virtualSkuId) {
        return baseMapper.selectOne(Wrappers.<ReplenishmentRulesDO>lambdaQuery()
                .eq(ReplenishmentRulesDO::getVirtualSkuId, virtualSkuId)
                .orderByDesc(ReplenishmentRulesDO::getCreateDate), false);
    }

    public String saveReplenishmentRules(ShippingProjectBaseParamDto baseParamDto, VirtualProductDO virtualSku, int redundantDate) {
        ReplenishmentRulesDO rulesDO = ReplenishmentRulesDO.builder()
                .virtualSkuId(virtualSku.getId())
                .purchaseDays(baseParamDto.getPurchaseProjectDays())
                .transitDays(baseParamDto.getTransitDays())
                .safeDays(baseParamDto.getSafeDays())
                .changeableSafeDays(redundantDate)
                .purchaseCircle(baseParamDto.getPurchaseCircle())
                .shippingCircle(baseParamDto.getShippingFrequency())
                .headShippingDays(JSON.toJSONString(baseParamDto.getHeadShippingDays()))
                .shippingRatio(JSON.toJSONString(baseParamDto.getShippingRatio()))
                .build();
        baseMapper.insert(rulesDO);
        return rulesDO.getId();
    }

    public void insertLogInfo(String info, String type) {
        Date date = new Date();
        baseMapper.insertLogInfo(info, type, date);
    }

    public void insertLogInfo(ReplenishmentCalculationLogDO info) {
        baseMapper.insertLogInfoDO(info);
    }

    public ReplenishmentRulesDO getRuleByVirtualSkuPurchaseId(String virtualPurchaseId) {
        return baseMapper.selectJoinOne(ReplenishmentRulesDO.class, new MPJLambdaWrapper<ReplenishmentRulesDO>()
                .selectAll(ReplenishmentRulesDO.class)
                .leftJoin(ReplenishmentVirtualSkuPurchaseDO.class, ReplenishmentVirtualSkuPurchaseDO::getRulesId, ReplenishmentRulesDO::getId)
                .eq(ReplenishmentVirtualSkuPurchaseDO::getId, virtualPurchaseId)
        );
    }
}
