package com.sales_server.infrastructures.filter;

import jakarta.servlet.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;


/**
 * @Description 获取用户信息的过滤器
 * <AUTHOR>
 * @Date 2024/2/19 10:11
 **/
@Component
@Slf4j
@Order(10)
public class GenerateConsumerUtilFilter implements Filter {



    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws ServletException, IOException {
        chain.doFilter(request, response);
    }
}
