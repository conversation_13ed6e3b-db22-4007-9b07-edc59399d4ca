package com.product.product_server.assembler;

import com.product.product_server.entity.dataObject.ProductCategoryDO;
import com.product.product_server.entity.dto.ProductCategoryDTO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * 数据转换工具类
 *
 * <AUTHOR>
 * @date 2024/5/11
 */

@Mapper(componentModel = "spring", uses = SelfProductConvert.class)
public interface SelfProductAssembler {

    ProductCategoryDTO categoryDoToDto(ProductCategoryDO val);

    List<ProductCategoryDTO> categoryDoListToDto(List<ProductCategoryDO> val);


}














