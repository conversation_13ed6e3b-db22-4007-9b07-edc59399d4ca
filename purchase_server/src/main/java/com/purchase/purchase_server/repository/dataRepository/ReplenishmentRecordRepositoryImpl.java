package com.purchase.purchase_server.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.bo.RepVirtualSkuDateBO;
import com.purchase.purchase_server.entity.dataObject.FileMissionCenterDO;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentProjectDO;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentRecordDO;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentVirtualSkuPurchaseDO;
import com.purchase.purchase_server.entity.form.ReplenishmentProjectForm;
import com.purchase.purchase_server.entity.vo.ReplenishmentRecordPurchaseListVo;
import com.purchase.purchase_server.mapper.ReplenishmentRecordMapper;
import org.springframework.stereotype.Service;


/**
 * @Description 补货记录
 * <AUTHOR>
 * @Date 2024/4/9 14:42
 **/
@Service
public class ReplenishmentRecordRepositoryImpl extends ServiceImpl<ReplenishmentRecordMapper, ReplenishmentRecordDO> {

    public IPage<ReplenishmentRecordPurchaseListVo> getRecordPageList(ReplenishmentProjectForm form) {
        IPage<ReplenishmentRecordPurchaseListVo> iPage = new Page<>(form.getCurrent(), form.getSize());
        return this.baseMapper.selectJoinPage(iPage, ReplenishmentRecordPurchaseListVo.class, buildFlowPageQueryWrapper(form));
    }

    public static MPJLambdaWrapper<ReplenishmentRecordDO> buildFlowPageQueryWrapper(ReplenishmentProjectForm query) {
        MPJLambdaWrapper<ReplenishmentRecordDO> LambdaWrapper = new MPJLambdaWrapper<>();
        LambdaWrapper.distinct()
                .selectAs(ReplenishmentRecordDO::getId, "record_id")
                .select(ReplenishmentRecordDO::getReplenishmentStatus)
                .select(ReplenishmentRecordDO::getCreateBy)
                .select(ReplenishmentRecordDO::getCreateDate)
                .select(ReplenishmentRecordDO::getAdvicePurchaseStartDate)
                .select(ReplenishmentRecordDO::getAdvicePurchaseEndDate)
                .select(FileMissionCenterDO::getFileName)
                .leftJoin(FileMissionCenterDO.class, FileMissionCenterDO::getId, ReplenishmentRecordDO::getDataSourceId)
                .in(CollectionUtil.isNotEmpty(query.getRecordIds()), ReplenishmentRecordDO::getId, query.getRecordIds())
                .eq(StrUtil.isNotBlank(query.getReplenishmentStatus()), ReplenishmentRecordDO::getReplenishmentStatus, query.getReplenishmentStatus())
                .eq(StrUtil.isNotBlank(query.getCreateBy()), ReplenishmentRecordDO::getCreateBy, query.getCreateBy())
                .between(StrUtil.isNotBlank(query.getCreateStartDate()) && StrUtil.isNotBlank(query.getCreateEndDate()),
                        ReplenishmentRecordDO::getCreateDate, query.getCreateStartDate(), query.getCreateEndDate())
                .orderByDesc(ReplenishmentRecordDO::getCreateDate);
        return LambdaWrapper;
    }

    public FileMissionCenterDO selectFailResultList(String recordId) {
        return this.baseMapper.selectJoinOne(FileMissionCenterDO.class, new MPJLambdaWrapper<ReplenishmentRecordDO>()
                .select(FileMissionCenterDO::getFileName)
                .select(FileMissionCenterDO::getFilePath)
                .select(ReplenishmentRecordDO::getFailedResult)
                .leftJoin(FileMissionCenterDO.class, FileMissionCenterDO::getId, ReplenishmentRecordDO::getDataSourceId)
                .eq(ReplenishmentRecordDO::getId, recordId)
        );
    }

    public ReplenishmentRecordDO selectOneByVirtualPurchaseId(String virtualPurchaseId) {
        return this.baseMapper.selectJoinOne(ReplenishmentRecordDO.class, new MPJLambdaWrapper<ReplenishmentRecordDO>()
                .select(ReplenishmentRecordDO::getId)
                .select(ReplenishmentRecordDO::getReplenishmentStatus)
                .select(ReplenishmentRecordDO::getCreateBy)
                .select(ReplenishmentRecordDO::getCreateDate)
                .select(ReplenishmentRecordDO::getDataSourceId)
                .select(ReplenishmentRecordDO::getFailedResult)
                .select(ReplenishmentRecordDO::getTaskResult)
                .select(ReplenishmentRecordDO::getDataSourceType)
                .leftJoin(ReplenishmentProjectDO.class, ReplenishmentProjectDO::getReplenishmentRecordId, ReplenishmentRecordDO::getId)
                .leftJoin(ReplenishmentVirtualSkuPurchaseDO.class, ReplenishmentVirtualSkuPurchaseDO::getReplenishmentProjectId, ReplenishmentProjectDO::getId)
                .eq(ReplenishmentVirtualSkuPurchaseDO::getId, virtualPurchaseId)
        );
    }

    public RepVirtualSkuDateBO selectOneByRecordId(String record) {
        return this.baseMapper.selectJoinOne(RepVirtualSkuDateBO.class, new MPJLambdaWrapper<ReplenishmentRecordDO>()
                .select(ReplenishmentRecordDO::getId)
                .select(ReplenishmentVirtualSkuPurchaseDO::getSaleDestination)
                .leftJoin(ReplenishmentProjectDO.class, ReplenishmentProjectDO::getReplenishmentRecordId, ReplenishmentRecordDO::getId)
                .leftJoin(ReplenishmentVirtualSkuPurchaseDO.class, ReplenishmentVirtualSkuPurchaseDO::getReplenishmentProjectId, ReplenishmentProjectDO::getId)
                .eq(ReplenishmentRecordDO::getId, record)
                .last("limit 1")
        );
    }

    public void deleteById(String recordId){
        baseMapper.deleteById(recordId);
    }

    public void updateAllRecord(ReplenishmentRecordDO val){
        baseMapper.updateAllRecord(val);
    }
}
