package com.purchase.purchase_server.entity.vo;


import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 补货计划-虚拟sku
 * <AUTHOR>
 * @Date 2024/4/25 14:21
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ReplenishmentRecordStatusVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -7736251450680884778L;
    /**
     * 建议采购开始日
     */
    private String advicePurchaseStartDate;
    /**
     * 建议采购结束日
     */
    private String advicePurchaseEndDate;
    /**
     * 默认建议采购开始日
     */
    private String defaultAdvicePurchaseStartDate;
    /**
     * 默认建议采购结束日
     */
    private String defaultAdvicePurchaseEndDate;
    /**
     * 试算状态
     */
    private String replenishmentStatus;
    /**
     * 需补货数量
     */
    private Integer replenishmentQuantity;
    /**
     * 无补货数量
     */
    private Integer noReplenishmentQuantity;

    private IPage<ReplenishmentVirtualSkuListVo> replenishmentVirtualSkuList;
}
