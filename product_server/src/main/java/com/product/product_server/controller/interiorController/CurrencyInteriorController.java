package com.product.product_server.controller.interiorController;

import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.product.product_server.model.currency.dto.ExchangeRateDto;
import com.product.product_server.service.ICurrencyExchangeRateService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.crafts_mirror.utils.enums.FactoryCurrencyEnum.CNY;
import static com.crafts_mirror.utils.enums.FactoryCurrencyEnum.USD;

/**
 * @Description 货币内部接口类
 * <AUTHOR>
 * @Date 2025/4/11 10:11
 **/
@RestController
@RequestMapping(value = "/interior/currency")
@Slf4j
public class CurrencyInteriorController {

    @Resource
    private ICurrencyExchangeRateService currencyExchangeRateService;

    @GetMapping("/latest/rate/usd")
    public ResultDTO<ExchangeRateDto> getLatestUSDToCNYExchangeRate() {
        ExchangeRateDto exchangeRate = currencyExchangeRateService.getNewestExchangeRate(USD.getDesc(), CNY.getDesc());
        return ResultDTO.success(exchangeRate);
    }
}
