package com.inventory_server.model.redundancy.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 工厂交货实体类
 * <AUTHOR>
 * @Date 2024/1/10 14:30
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryFinishedInventoryDto implements Serializable {

    private String id;

    /**
     * 合同号
     */
    private String contractCode;

    /**
     * 工厂交货时间
     */
    private Date factoryFinishedDate;
    /**
     * 剩余数量
     */
    private Integer factoryRemainNum;
    /**
     * 工厂交货数量
     */
    private Integer factoryShippingPackageNum;
    /**
     * 发货计划id
     */
    private String shippingProjectId;
    /**
     * 虚拟skuId
     */
    private String virtualSkuId;

    private String virtualSku;

    /**
    * 快照记录id
    */
    private String productSnapshotId;

    private String remarks;

    /**
     * 标识该计划是否需要发货
     */
    private Boolean needToPriorDelivery;

    private Integer deliveryType;


}
