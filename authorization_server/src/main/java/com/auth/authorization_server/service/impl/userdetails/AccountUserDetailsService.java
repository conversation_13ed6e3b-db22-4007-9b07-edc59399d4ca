package com.auth.authorization_server.service.impl.userdetails;

import cn.hutool.core.util.StrUtil;
import com.auth.authorization_server.model.AccountUser;
import com.auth.authorization_server.model.entity.Permission;
import com.auth.authorization_server.service.ISysUserService;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.auth.authorization_server.domain.dto.UserDTO;
import jakarta.annotation.Resource;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class AccountUserDetailsService implements UserDetailsService {


    @Resource
    private ISysUserService userService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        UserDTO user = userService.selectUserByUserName(username);
        if (user == null) {
            throw new UsernameNotFoundException("用户不存在");
        }
        //List<UserRole> userRoleList = userRoleRepositoryImpl.list(Wrappers.<UserRole>lambdaQuery().eq(UserRole::getUserId, user.getId()));
        List<String> roleList = new ArrayList<>();
        //if(CollectionUtil.isNotEmpty(userRoleList)) {
        //    roleList = roleRepository.getRoleNameByIdList(userRoleList.stream().map(UserRole::getRoleId).collect(Collectors.toList()));
        //}
        username = user.getUserName();
        String password = user.getPassWord();
        return new AccountUser(user.getId(), user.getUserName(), username, roleList, password, getUserAuthority(username));
    }

    /**
     * 获取用户权限信息（角色、菜单权限）
     *
     * @param username
     * @return
     */
    public List<GrantedAuthority> getUserAuthority(String username) {
        // 角色(比如ROLE_admin)，菜单操作权限(比如sys:user:list)
        // 角色必须以ROLE_开头，security在判断角色时会自动截取ROLE_
        List<Permission> permissions = new ArrayList<>();
        // 比如ROLE_admin,ROLE_normal,sys:user:list,...
        String authority = "";
        if (CollectionUtils.isNotEmpty(permissions)) {
            List<String> authorizations = new ArrayList<>(permissions.stream().map(Permission::getUrlPerm).toList());
            authorizations.addAll(permissions.stream().map(Permission::getBtnPerm).toList());
            authority = StrUtil.join(",", authorizations);
        }
        return AuthorityUtils.commaSeparatedStringToAuthorityList(authority);
    }

    //public List<Permission> getPermissionByUsername(String username) {
    //    User user = userRepositoryImpl.getOneByUserName(username);
    //    return this.getPermissionByUser(user);
    //}
    //
    //public List<Permission> getPermissionByUserId(Integer userId) {
    //    User user = userRepositoryImpl.getOneByUserName(userId);
    //    return this.getPermissionByUser(user);
    //}

    //public List<Permission> getPermissionByUser(User user) {
    //    List<Permission> permissions = new ArrayList<>();
        //if (null == user) {
        //    return permissions;
        //}
        //// 获取用户角色
        //List<UserRole> userRoles = userRoleRepositoryImpl.list(Wrappers.<UserRole>lambdaQuery().eq(UserRole::getUserId, user.getId()));
        //if (CollectionUtils.isEmpty(userRoles)) {
        //    return permissions;
        //}
        //// 根据角色获取对应的权限
        //List<String> roleIds = userRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList());
        //List<RolePermission> rolePermissions = rolePermissionRepositoryImpl.list(Wrappers.<RolePermission>lambdaQuery().in(RolePermission::getRoleId, roleIds));
        //if (CollectionUtils.isNotEmpty(rolePermissions)) {
        //    List<Integer> permissionIds = rolePermissions.stream().map(RolePermission::getPermissionId).collect(Collectors.toList());
        //    permissions = permissionRepositoryImpl.list(Wrappers.<Permission>lambdaQuery().in(Permission::getId, permissionIds));
        //}
        //return permissions;
    //}
}
