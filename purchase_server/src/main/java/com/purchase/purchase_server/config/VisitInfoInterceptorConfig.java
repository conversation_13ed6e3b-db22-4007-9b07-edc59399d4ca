package com.purchase.purchase_server.config;

import com.purchase.purchase_server.interceptor.VisitInfoInterceptor;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 访问日志拦截器配置
 * <AUTHOR>
 * @Date 2024/8/10 14:41
 **/
@Configuration
public class VisitInfoInterceptorConfig implements WebMvcConfigurer {

    @Resource
    private VisitInfoInterceptor visitInfoInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        List<String> patterns = new ArrayList<>();
        patterns.add("/interior/**");

        registry.addInterceptor(visitInfoInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(patterns);

    }
}
