package com.product.product_server.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.product.product_server.entity.dataObject.ProductSnapshotDO;
import com.product.product_server.mapper.ProductSnapshotMapper;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 */
@Service
public class ProductSnapshotRepositoryImpl extends ServiceImpl<ProductSnapshotMapper , ProductSnapshotDO> {

    public Set<String> selectLatestSnapshots(String startDate,String endDate, int count){
        List<String> list = baseMapper.selectLatestSnapshots(startDate, endDate, count);
        return CollectionUtil.isEmpty(list) ? Collections.emptySet() :
                new HashSet<>(list);
    }
    public int deleteUnusedSnapshots(Set<String> ids,String startDate,String endDate){
        return baseMapper.deleteUnusedSnapshots(ids, startDate, endDate);
    }

    public List<String> getAllSnapshotIdList() {
        return baseMapper.getAllSnapshotIdList();
    }
}
