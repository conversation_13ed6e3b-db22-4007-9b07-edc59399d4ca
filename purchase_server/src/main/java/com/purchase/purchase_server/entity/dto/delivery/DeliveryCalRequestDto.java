package com.purchase.purchase_server.entity.dto.delivery;

import com.purchase.purchase_server.entity.dto.FactoryFinishedInventoryDto;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/5/9 17:05
 **/
@Data
public class DeliveryCalRequestDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -14343456429043L;

    private String virtualSku;

    /**
     * 安全天数
     */
    private Integer safeDays;

    /**
     * 安全天数
     */
    private Integer changeableSafeDays;

    /**
     * 中转天数
     */
    private Integer transitDays;

    /**
     * 发货频次
     */
    private Integer shippingCircle;

    /**
     * 发货结束时间
     */
    private String shippingEndDateStr;

    private LocalDate calEndDate;

    /**
     * 单箱数量
     */
    private Integer containLoader;

    /**
     * 海外仓库存
     */
    private List<FactoryRemainInventoryDto> factoryRemainInventoryList;

    /**
     * 计划里发货导致的海外仓库存（若需要展示图标，则将计划里发货放入此处）
     */
    private List<FactoryRemainInventoryDto> deliveryResultInventoryList;

    /**
     * 优先发货的数据
     */
    private List<FactoryRemainInventoryDto> priorDeliveryList;

    /**
     * 工厂计划
     */
    private List<FactoryFinishedInventoryDto> finishedInventoryList;

    /**
     * 头程时间
     */
    private Map<String, Integer> headShippingDays;

    /**
     * 发货比例
     */
    private Map<String, Double> shippingRatio;

    /**
     * 目标日销
     */
    private Map<String, Double> targetSalesMap;
}
