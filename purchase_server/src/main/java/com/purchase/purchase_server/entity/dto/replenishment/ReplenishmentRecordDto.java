package com.purchase.purchase_server.entity.dto.replenishment;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ReplenishmentRecordDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 5693987781165783997L;

    /**
     * 补货记录ID
     */
    @NotBlank(message = "补货记录ID不能为空")
    private String recordId;
    /**
     * 试算状态（-2：作废；-1：试算失败；0：试算中；1：待保存；2：已保存）
     * @see com.purchase.purchase_server.enums.ReplenishmentStatusEnum
     */
    private String replenishmentStatus;

    /**
     * 数据来源类型（0：导入；）
     */
    private String dataSourceType;

    /**
     * 数据来源ID
     */
    private String dataSourceId;

    /**
     * 理论补货日（开始）
     */
    @NotBlank(message = "理论补货日（开始）不能为空")
    private String advicePurchaseStartDate;

    /**
     * 理论补货日（结束）
     */
    @NotBlank(message = "理论补货日（结束）不能为空")
    private String advicePurchaseEndDate;

    /**
     * 试算失败原因
     */
    private String failedResult;

    /**
     * 任务结果
     */
    private String taskResult;
}