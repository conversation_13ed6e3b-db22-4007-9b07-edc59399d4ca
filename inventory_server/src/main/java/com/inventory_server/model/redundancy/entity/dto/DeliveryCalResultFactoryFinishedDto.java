package com.inventory_server.model.redundancy.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/5/10 9:57
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryCalResultFactoryFinishedDto {
    /**
     * 合同号
     */
    private String contractCode;

    /**
     * 工厂交货时间
     */
    private Date factoryFinishedDate;
    /**
     * 剩余数量
     */
    private Integer factoryRemainNum;
    /**
     * 工厂交货数量
     */
    private Integer factoryShippingPackageNum;

    /**
     * 目标sku
     */
    private String destinationSku;

    /**
     * 备注
     */
    private String remarks;

    private Boolean needPriorDelivery;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DeliveryCalResultFactoryFinishedDto that = (DeliveryCalResultFactoryFinishedDto) o;
        return Objects.equals(contractCode, that.contractCode) &&
                Objects.equals(factoryFinishedDate, that.factoryFinishedDate) &&
                Objects.equals(destinationSku, that.destinationSku) &&
                Objects.equals(remarks, that.remarks);
    }

    @Override
    public int hashCode() {
        return Objects.hash(contractCode, destinationSku, factoryFinishedDate, remarks);
    }
}
