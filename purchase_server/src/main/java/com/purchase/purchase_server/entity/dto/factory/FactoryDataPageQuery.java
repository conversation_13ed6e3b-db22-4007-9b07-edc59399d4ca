package com.purchase.purchase_server.entity.dto.factory;

import com.purchase.purchase_server.entity.form.BasePageForm;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/1
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class FactoryDataPageQuery extends BasePageForm implements Serializable {


    @Serial
    private static final long serialVersionUID = -7590167662513940555L;

    private String factoryCode;

    private String factoryName;

    private String purchaser;

    private String orderTracker;

    private String shortName;

    private String buyer;
}
