package com.purchase.purchase_server.service.purchaseOrder.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderDeliveryScheduleDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrderSaveScheduleDto;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.PurchaseOrderImportDTO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.YicangPurchaseOrderScheduleDto;
import com.purchase.purchase_server.entity.form.PurchaseOrdersScheduleForm;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrdersScheduleVO;
import com.purchase.purchase_server.repository.dataRepository.YicangPurchaseOrderDeliveryScheduleRepositoryImpl;
import com.purchase.purchase_server.service.purchaseOrder.IYicangPurchaseOrderDeliveryScheduleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;

/**
 * <AUTHOR>
 * @description 针对表【cm_yicang_purchase_order_delivery_schedule(易仓采购单交期计划表)】的数据库操作Service实现
 * @createDate 2024-11-01 14:30:43
 */
@Service
@Slf4j
public class YicangPurchaseOrderDeliveryScheduleServiceImpl implements IYicangPurchaseOrderDeliveryScheduleService {

    @Resource
    private YicangPurchaseOrderDeliveryScheduleRepositoryImpl scheduleRepository;

    @Override
    public void saveOrderVirtualByImport(PurchaseOrderImportDTO dto, String sbPoVirtualId) {

        List<YicangPurchaseOrderDeliveryScheduleDO> scheduleDOList = scheduleRepository.select(
                PurchaseOrdersScheduleForm.builder()
                        .sbPoVirtualId(sbPoVirtualId)
                        .deliveryDate(dto.getDeliveryDate())
                        .build());
        var scheduleDO = new YicangPurchaseOrderDeliveryScheduleDO();
        scheduleDO.setExpectedDeliveryDate(DateUtil.parse(dto.getDeliveryDate()));
        scheduleDO.setSbPoVirtualId(sbPoVirtualId);
        if (CollectionUtil.isNotEmpty(scheduleDOList)) {
            scheduleDO = scheduleDOList.get(0);
        }
        scheduleDO.setExpectedDeliveryQuantity(Integer.valueOf(dto.getDeliveryQuantity()));
        scheduleRepository.saveOrUpdate(scheduleDO);
    }

    @Override
    public void removeBySbPoVirtualIds(List<String> sbPoVirtualIds) {
        scheduleRepository.removeBySbPoVirtualIds(sbPoVirtualIds);
    }

    @Override
    public List<YicangPurchaseOrdersScheduleVO> getOrderSchedule(String sbPoVirtualId) {
        List<YicangPurchaseOrderDeliveryScheduleDO> select = scheduleRepository.select(PurchaseOrdersScheduleForm.builder().sbPoVirtualId(sbPoVirtualId).build());
        return select.stream().map(scheduleDO -> {
            YicangPurchaseOrdersScheduleVO scheduleVO = new YicangPurchaseOrdersScheduleVO();
            scheduleVO.setId(scheduleDO.getId());
            scheduleVO.setExpectedDeliveryDate(DateUtil.format(scheduleDO.getExpectedDeliveryDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN));
            scheduleVO.setExpectedDeliveryQuantity(scheduleDO.getExpectedDeliveryQuantity());
            return scheduleVO;
        }).toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrderSchedule(OrderSaveScheduleDto val) {
        scheduleRepository.removeBySbPoVirtualIds(Collections.singletonList(val.getSbPoVirtualId()));

        // 使用Map按日期分组并求和数量
         Map<String, Integer> dateQuantityMap = val.getScheduleList().stream()
                .collect(Collectors.groupingBy(
                        YicangPurchaseOrderScheduleDto::getExpectedDeliveryDate,
                        Collectors.summingInt(YicangPurchaseOrderScheduleDto::getExpectedDeliveryQuantity)
                ));

        // 将合计后的数据转换为DO对象列表
        List<YicangPurchaseOrderDeliveryScheduleDO> list = dateQuantityMap.entrySet().stream()
                .map(entry -> {
                    var scheduleDO = new YicangPurchaseOrderDeliveryScheduleDO();
                    scheduleDO.setExpectedDeliveryDate(DateUtil.parse(entry.getKey()));
                    scheduleDO.setExpectedDeliveryQuantity(entry.getValue());
                    scheduleDO.setSbPoVirtualId(val.getSbPoVirtualId());
                    return scheduleDO;
                }).toList();
        if (CollectionUtil.isNotEmpty(list)) {
            scheduleRepository.saveBatch(list);
        }
        return true;
    }

}




