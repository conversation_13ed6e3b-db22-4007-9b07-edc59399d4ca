package com.inventory_server.applications.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 工厂剩余库存
 * <AUTHOR>
 * @Date 2023/12/28 19:34
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryRemainInventoryDto implements Serializable {
    private String virtualSku;

    private String warehouse;

    private Date enableUsingDate;

    private Date startShippingTime;

    private String deliveryDateRange;

    private Double storeNum;

    private String shipmentCode;

    private String remarks;

    private Integer factoryHashCode;
}
