package com.inventory_server.model.redundancy.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;


/**
 * <AUTHOR>
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_inventory_sold_out_days")
public class InventorySoldOutDaysDO extends PhysicalBaseEntity {

  private String inventoryInfoId;
  private LocalDate daysBegin;
  private LocalDate daysEnd;
  private String daysGap;
  private Integer soldOutDays;
  private Double lackNum;
  /**
   * 0：售罄时间；1：全链路售罄时间
   */
  private String type;
}
