package com.inventory_server.model.redundancy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/5/14
 **/
@Getter
@AllArgsConstructor
public enum FactoryPlanInfoEnum {
    PRODUCT("produced", "已生产"),
    IN_PRODUCTION("inProduction", "生产中");

    private final String code;
    private final String desc;

    public static FactoryPlanInfoEnum ofCode(String code) {
        return Arrays.stream(FactoryPlanInfoEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
