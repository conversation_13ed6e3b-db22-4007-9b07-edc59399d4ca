package com.purchase.purchase_server.entity.form;

import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 补货计划查询表单
 * @date 2024/1/16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class ReplenishmentProjectForm extends BasePageForm implements Serializable {
    @Serial
    private static final long serialVersionUID = -1439125810202434L;
    /**
     * 补货Id
     */
    private String id;

    private String replenishmentProjectId;

    /**
     * 补货保存状态
     * @see com.purchase.purchase_server.enums.ReplenishmentStatusEnum
     */
    private String replenishmentStatus;
    /**
     * 自定义sku
     */
    private List<String> selfSkuList;
    /**
     * 虚拟sku
     */
    private List<String> virtualSkuList;
    /**
     * 老skuList
     */
    private List<String> oldSkuList;
    /**
     * 计划生成开始时间
     */
    private String createdStartDate;
    /**
     * 计划生成结束时间
     */
    private String createdEndDate;
    /**
     * 建议采购开始日
     */
    private String advicePurchaseStartDate;
    /**
     * 建议采购结束日
     */
    private String advicePurchaseEndDate;
    /**
     * 采纳状态(0-未采纳 1-已采纳)
     * @see com.purchase.purchase_server.enums.TrialPurchaseStatusEnum
     */
    private String trialStatus;
    /**
     * 是否要补货（0-正常；1-需补货）
     * @see com.purchase.purchase_server.enums.ViReplenishmentStatusEnum
     */
    private String isNotReplenishment;
    /**
     * 建议补货量是否打折（0-正常；1-打折）
     * @see com.purchase.purchase_server.enums.ViReplenishmentIsDiscountEnum
     */
    private String isDiscount;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createStartDate;
    private String createEndDate;
    /**
     * 供应商代码
     */
    private String factoryCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 运营
     */
    private String operator;

    private List<String> recordIds;

    /**
     * 运营确认状态
     */
    private String operationConfirmedStatus;

    /**
     * 产品状态
     */
    private String productStatus;

    /**
     * 产品状态
     */
    private List<String> productStatusList;
    /**
     * 子体类型
     */
    private String subType;
    /**
     * 排序
     */
    private String direction;

    /**
     * 排序目标
     */
    private String sort;
}
