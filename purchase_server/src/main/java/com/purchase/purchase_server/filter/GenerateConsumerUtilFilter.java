package com.purchase.purchase_server.filter;

import com.crafts_mirror.utils.common.entity.LoginVo;
import jakarta.annotation.Resource;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;

import static com.crafts_mirror.utils.constant.Constants.AUTHORIZATION;


/**
 * @Description 获取用户信息的过滤器
 * <AUTHOR>
 * @Date 2024/2/19 10:11
 **/
@Component
@Slf4j
@Order(10)
public class GenerateConsumerUtilFilter implements Filter {

    @Resource
    private RedisTemplate<String, LoginVo> redisTemplate;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, Filter<PERSON>hain chain) throws ServletException, IOException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String authorization = httpRequest.getHeader(AUTHORIZATION);
        chain.doFilter(request, response);
    }
}
