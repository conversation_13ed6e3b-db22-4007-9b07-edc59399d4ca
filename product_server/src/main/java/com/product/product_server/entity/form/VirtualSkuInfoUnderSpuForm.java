package com.product.product_server.entity.form;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 虚拟sku列表页按照spu展示，获取虚拟sku信息
 * <AUTHOR>
 * @Date 2023/12/19 17:29
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VirtualSkuInfoUnderSpuForm implements Serializable {
    @Serial
    private static final long serialVersionUID = -5278237572842883666L;
    private String spu;
    private String channel;
    private String productName;
    private String selfProductSku;
    private String buyer;
    private String virtualSku;
    private List<String> virtualSkuList;
    private List<String> selfSkuList;
    private List<String> oldSkuList;
    private String upgradeStatus;
    private String categoryId;
    private String subType;
    private String productStatus;
    private List<String> productStatusList;
    private String productType;
}
