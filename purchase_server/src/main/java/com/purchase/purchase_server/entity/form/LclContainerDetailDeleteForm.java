package com.purchase.purchase_server.entity.form;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 删除拼柜装柜数据
 * @date 2024/1/18
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LclContainerDetailDeleteForm implements Serializable {

    @Serial
    private static final long serialVersionUID = -1L;
    private String id;
}
