package com.inventory_server.model.warehouse.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.common.entity.ChannelSearchVo;
import com.inventory_server.infrastructures.assembler.WarehouseAssembler;
import com.inventory_server.infrastructures.entity.LogTrackNumDto;
import com.inventory_server.model.channel.service.IChannelInfoService;
import com.inventory_server.model.warehouse.entity.dos.WarehouseSenboInfoDO;
import com.inventory_server.model.warehouse.entity.dos.WarehouseYiCangInfoDO;
import com.inventory_server.model.warehouse.entity.dto.WarehouseDetailDto;
import com.inventory_server.model.warehouse.entity.form.ChangeSenboWarehouseForm;
import com.inventory_server.model.warehouse.entity.form.SenboWarehouseForm;
import com.inventory_server.model.warehouse.repository.databaseRepository.WarehouseRelationshipRepositoryImpl;
import com.inventory_server.model.warehouse.repository.databaseRepository.WarehouseSenboInfoRepositoryImpl;
import com.inventory_server.model.warehouse.repository.databaseRepository.WarehouseSenboSnapshotRepository;
import com.inventory_server.model.warehouse.repository.databaseRepository.WarehouseYiCangInfoRepositoryImpl;
import com.inventory_server.model.warehouse.service.IWarehouseSaveService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description 保存仓库信息的接口
 * <AUTHOR>
 * @Date 2024/7/3 16:19
 **/
@Service
@Slf4j
public class WarehouseSaveServiceImpl implements IWarehouseSaveService {

    @Resource
    private WarehouseYiCangInfoRepositoryImpl warehouseYiCangInfoRepository;

    @Resource
    private WarehouseSenboInfoRepositoryImpl warehouseSenboInfoRepository;

    @Resource
    private WarehouseRelationshipRepositoryImpl warehouseRelationshipRepository;

    @Resource
    private WarehouseSenboSnapshotRepository warehouseSenboSnapshotRepository;

    @Resource
    private IChannelInfoService channelInfoService;

    @Resource
    private WarehouseAssembler warehouseAssembler;

    @Override
    @OperationLog(operationType = "仓库管理", content = "修改易仓仓库信息")
    public void saveWarehouseInfo(List<WarehouseDetailDto> warehouseDetailList, LogTrackNumDto dto) {
        List<WarehouseYiCangInfoDO> warehouseYiCangInfoList = warehouseAssembler.convertYiCangDtoToYiCangInfoDO(warehouseDetailList);

        warehouseYiCangInfoRepository.saveYiCangInfo(warehouseYiCangInfoList, false);

        Set<Integer> ycWarehouseId = warehouseYiCangInfoList.stream().map(WarehouseYiCangInfoDO::getYcWarehouseId).collect(Collectors.toSet());
        List<WarehouseYiCangInfoDO> warehouseYiCangInfoDOList = warehouseYiCangInfoRepository.list(Wrappers.<WarehouseYiCangInfoDO>lambdaQuery()
                .in(WarehouseYiCangInfoDO::getYcWarehouseId, ycWarehouseId));
        for (var info : warehouseYiCangInfoDOList) {
            dto.getLogMap().put(String.valueOf(info.getId()), "修改易仓仓库信息");
        }
    }

    @Override
    @OperationLog(operationType = "仓库管理", content = "修改易仓仓库信息")
    public void changeWarehouseSenboInfo(ChangeSenboWarehouseForm form, LogTrackNumDto dto) {
        Integer senboWarehouseId = form.getSenboWarehouseId();
        boolean senboWarehouseExists = warehouseSenboInfoRepository.checkSenboWarehouseExists(senboWarehouseId);

        Integer ycWarehouseId = form.getYcWarehouseId();
        boolean ycWarehouseExists = warehouseYiCangInfoRepository.checkYcWarehouseExists(ycWarehouseId);

        if (!senboWarehouseExists || !ycWarehouseExists) {
            throw new IllegalArgumentException("选择的易仓仓库或者森帛仓库不存在");
        }

        ChannelSearchVo channelSearchVo = channelInfoService.getAllChannel();
        boolean notExisted = channelSearchVo.getChannelSearchDtoList().stream().noneMatch(f -> f.getChannelId().equals(form.getChannel()));
        if (StrUtil.isNotBlank(form.getChannel()) && notExisted) {
            throw new IllegalArgumentException("选择的渠道不存在");
        }

        warehouseRelationshipRepository.updateWarehouseRelationship(ycWarehouseId, senboWarehouseId);

        WarehouseYiCangInfoDO build = WarehouseYiCangInfoDO.builder()
                .channel(form.getChannel())
                .ycWarehouseId(ycWarehouseId)
                .build();

        warehouseYiCangInfoRepository.saveYiCangInfo(Collections.singletonList(build), true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOrUpdateSenBoWarehouseInfo(SenboWarehouseForm form) {
        int headShippingDate = Integer.parseInt(form.getHeadShippingDate());
        int urgent = Integer.parseInt(form.getUrgentHeadShippingDate());
        if (urgent > headShippingDate) {
            throw new IllegalArgumentException("加急头程比正常头程慢，请修改");
        }
        WarehouseSenboInfoDO warehouseSenboInfoDO = warehouseAssembler.convertSenboWarehouseFormToInfoDO(form);
         warehouseSenboInfoRepository.saveOrUpdate(warehouseSenboInfoDO);

         // 保存一份仓库的快照
        warehouseSenboSnapshotRepository.insertWarehouseSenboSnapshot(warehouseSenboInfoDO);

        return warehouseSenboInfoDO.getId();
    }
}
