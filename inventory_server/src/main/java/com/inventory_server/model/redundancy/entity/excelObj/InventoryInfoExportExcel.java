package com.inventory_server.model.redundancy.entity.excelObj;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * @Description 库存导出
 * <AUTHOR>
 * @Date 2024/1/19 17:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ContentStyle(wrapped = BooleanEnum.TRUE,
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER)
public class InventoryInfoExportExcel extends RedundancyInfoBaseExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = -7429914308550484991L;
    @ExcelIgnore
    private String inventoryInfoId;

    @ExcelProperty(value = "借货策略")
    private String borrowingStrategy;

    @ExcelProperty("运营")
    private String operatorList;

    @ExcelProperty("工厂库存")
    private String factoryInventory;

    @ExcelProperty("海外仓在途")
    private String foreignInTransit;

    @ExcelProperty("海外仓库存")
    private String foreignInventory;

    @ExcelProperty("海外仓理论售罄时间")
    private String foreignTheoreticalSoldOutDate;

    @ExcelProperty("海外仓缺货量")
    private Double lackNum;

    @ExcelProperty("海外仓售罄前断货天数")
    private Integer daysBeforeSoldOut;

    @ExcelProperty("海外仓断货天数详情")
    private String daysBeforeSoldOutDetail;

    @ExcelProperty("全链路理论售罄时间")
    private String fullLinkForeignTheoreticalSoldOutDate;

    @ExcelProperty("全链路缺货量")
    private Double fullLinkLackNum;

    @ExcelProperty("全链路售罄前断货天数")
    private Integer fullLinkDaysBeforeSoldOut;

    @ExcelProperty("全链路断货天数详情")
    private String fullLinkDaysBeforeSoldOutDetail;

    @ExcelProperty("海外仓冗余库存")
    private Double foreignRedundantInventory;

    @ExcelProperty("海外仓冗余详情")
    private String foreignRedundantInventoryDetail;

    @ExcelProperty("海外仓在途冗余库存")
    private Integer onShippingRedundantInventory;

    @ExcelProperty("海外仓在途冗余详情")
    private String onShippingRedundantInventoryDetail;

    @ExcelProperty("工厂冗余库存")
    private Double factoryRedundantInventory;

    @ExcelProperty("工厂冗余详情")
    private String factoryRedundantInventoryDetail;

    @ExcelProperty("理论可干预时间")
    private String leadTime;

    @ExcelProperty("更新时间")
    private String updateDate;

    /**
     * 7日销量
     */
    private String sevenDaySales;
    /**
     * 14日销量
     */
    private String fourteenDaySales;
    /**
     * 30日销量
     */
    private String thirtyDaySales;
    /**
     * 实际日均销量
     */
    private String actualDailySales;
    /**
     * 目标日销
     */
    private String targetSalesNum;
    /**
     * 子体达成率
     */
    private String subEntityRate;


    /**
     * 目标日销
     */
    private Map<String, Double> monthTargetSalesMap;
}
