package com.purchase.purchase_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description excel导入发货计划-计划 转换成的实体列
 * <AUTHOR>
 * @Date 2023/12/26 17:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryPurchaseInfoExcel extends BaseExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = -1439495840202434L;
//
//    @ExcelProperty("*公司")
//    private String vendor;

    @ExcelProperty("*合同号")
    private String contractCode;

    @ExcelProperty("*虚拟SKU")
    private String virtualSku;

    @ExcelProperty("*工厂交期")
    private String deliveryDate;

    @ExcelProperty("*数量")
    private String shipments;

    @ExcelProperty("备注")
    private String remark;
}
