package com.crafts_mirror.utils.common.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/7/22
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserParams {

    private String id;
    /**
     * 用户账号
     */
    private String userName;
    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 手机号码
     */
    private String phoneNumber;
    /**
     * 密码
     */
    private String passWord;
    /**
     * 帐号状态（0正常 1停用）
     */
    private String userStatus;
    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 角色组
     */
    private String[] roleIds;

    private String oldPassword;

    private String newPassword;

    /**
     * 用户昵称
     */
    private List<String> nickNameList;

    /**
     * 手机号码
     */
    private List<String> phoneNumberList;

    /**
     * 职位
     */
    private String postId;

    public String getUserName() {
        return Optional.ofNullable(userName)
                .map(String::strip)
                .orElse(null);
    }

    public String getNickName() {
        return Optional.ofNullable(nickName)
                .map(String::strip)
                .orElse(null);
    }

    public String getPhoneNumber() {
        return Optional.ofNullable(phoneNumber)
                .map(String::strip)
                .orElse(null);
    }
}
