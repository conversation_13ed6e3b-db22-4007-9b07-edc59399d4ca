package com.inventory_server.model.warning.urgentPurchase.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inventory_server.model.warning.unsalable.entity.form.UnsalablePageForm;
import com.inventory_server.model.warning.urgentPurchase.entity.form.UrgentPurchaseMQForm;
import com.inventory_server.model.warning.urgentPurchase.entity.vo.UrgentPurchasePageVo;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

public interface IUrgentPurchaseService {

    void calUrgentPurchaseNum(UrgentPurchaseMQForm purchaseForm);

    IPage<UrgentPurchasePageVo> getUrgentPurchasePage(UnsalablePageForm form);

    List<UrgentPurchasePageVo> getUrgentPurchaseList(UnsalablePageForm form);

    void exportUrgentPurchase(List<UrgentPurchasePageVo> urgentPurchaseList, HttpServletResponse response);

    void reCalUrgentPurchaseByWarehouse(String warehouse);
}
