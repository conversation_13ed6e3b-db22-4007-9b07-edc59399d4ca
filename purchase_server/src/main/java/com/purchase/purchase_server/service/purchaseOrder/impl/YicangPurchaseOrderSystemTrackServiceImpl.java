package com.purchase.purchase_server.service.purchaseOrder.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.purchase.purchase_server.assembler.YiCangPurchaseOrdersAssembler;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderSystemTrackDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrdersSummaryDto;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.YicangPurchaseOrderSystemTrackDTO;
import com.purchase.purchase_server.repository.dataRepository.YicangPurchaseOrderSystemTrackRepositoryImpl;
import com.purchase.purchase_server.service.purchaseOrder.IYicangPurchaseOrderSystemTrackService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【cm_yicang_purchase_order_system_track(易仓系统跟单状态表)】的数据库操作Service实现
* @createDate 2024-11-01 14:30:43
*/
@Service
@Slf4j
public class YicangPurchaseOrderSystemTrackServiceImpl implements IYicangPurchaseOrderSystemTrackService {

    @Resource
    private YicangPurchaseOrderSystemTrackRepositoryImpl purchaseOrderSystemTrackRepository;

    @Resource
    private YiCangPurchaseOrdersAssembler purchaseOrdersAssembler;
    /**
     * 根据采购单表id删除
     * @param sbPoId 采购单表id
     */
    public void removeBySbPoId(String sbPoId){
        purchaseOrderSystemTrackRepository.removeBySbPoId(sbPoId);
    }

    @Override
    public void save(YicangPurchaseOrderSystemTrackDO systemTrackDO) {
        purchaseOrderSystemTrackRepository.save(systemTrackDO);
    }

    @Override
    public void updateTrackingInfo(OrdersSummaryDto order, String sbPoId) {
        // 清除现有跟踪信息
        purchaseOrderSystemTrackRepository.removeBySbPoId(sbPoId);
        // 更新系统跟踪状态
        if (ObjectUtil.length(order.getSystemTrack()) > 0) {
            var systemTrackDTO = JSON.parseObject(JSON.toJSONString(order.getSystemTrack()), YicangPurchaseOrderSystemTrackDTO.class);
            var systemTrackDO = purchaseOrdersAssembler.systemTrackDTOtoDO(systemTrackDTO);
            systemTrackDO.setSbPoId(sbPoId);
            purchaseOrderSystemTrackRepository.save(systemTrackDO);
        }
    }
}




