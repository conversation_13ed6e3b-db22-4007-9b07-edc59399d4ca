package com.purchase.purchase_server.entity.dataObject;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * @Description 供应商表
 * <AUTHOR>
 * @Date 2024/1/15 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_factory_info")
public class FactoryInfoDO extends BaseEntity {
    /**
     * 供应商代码
     */
    private String factoryCode;
    /**
     * 供应商名称
     */
    private String factoryName;
    /**
     * 简称
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String shortName;
    /**
     * 采购人
     */
    private String purchaser;
    /**
     * 跟单人
     */
    private String orderTracker;
    /**
     * 币种
     */
    private String currency;
    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remark;
    /**
     * 禁用状态
     */
    private String disableStatus;
}
