package com.product.product_server.entity.form;

import com.product.product_server.entity.dto.VirtualSkuProductUpdateDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.crafts_mirror.utils.dp.BasePageForm;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 更新自定义商品sku
 * <AUTHOR>
 * @Date 2024/3/7 14:47
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class SelfProductUpdateForm extends BasePageForm implements Serializable {


    @Serial
    private static final long serialVersionUID = -7810990292051559594L;
    /**
     * 自定义skuId
     */
    private String selfProductId;

    /**
     * 新sku
     */
    private String newSku;

    /**
     * new虚拟skuList
     */
    private List<VirtualSkuProductUpdateDto> newVirtualSkuList;
}
