package com.purchase.purchase_server.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.dataObject.SelfProductDO;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SelfProductMapper extends MPJBaseMapper<SelfProductDO> {

    List<SelfProductDO> selectSelfProductListAndDeleted(@Param("selfProductIds") List<String> selfProductIds);

}
