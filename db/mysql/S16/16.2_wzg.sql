ALTER TABLE `crafts_mirror`.`cm_product_snapshot`
    ADD COLUMN `upgrade_type` char(1) NULL COMMENT '0-被升级款，1-升级款' AFTER `virtual_sku`;
ALTER TABLE `crafts_mirror`.`cm_product_snapshot`
    ADD COLUMN `upgrade_virtual_id` varchar(50) NULL COMMENT '升级款关联skuId' AFTER `upgrade_type`;

ALTER TABLE `crafts_mirror`.`cm_virtual_product`
    ADD COLUMN `upgrade_id` varchar(50) NULL COMMENT '升级款关系id' AFTER `product_type`;

CREATE TABLE `cm_virtual_upgrade_relation`
(
    `id`          varchar(50) NOT NULL,
    `original_id` varchar(50) NOT NULL COMMENT '被升级款商品ID',
    `upgrade_id`  varchar(50) NOT NULL COMMENT '升级款商品ID',
    `create_date` datetime    DEFAULT NULL,
    `update_date` datetime    DEFAULT NULL,
    `create_by`   varchar(50) DEFAULT NULL,
    `update_by`   varchar(50) DEFAULT NULL,
    `status`      char(1)     NOT NULL COMMENT '状态',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `original_id` (`original_id`) USING BTREE,
    UNIQUE KEY `upgrade_id` (`upgrade_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='产品升级款关系表';