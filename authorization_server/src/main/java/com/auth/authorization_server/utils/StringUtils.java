package com.auth.authorization_server.utils;

import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.utils.constant.Constants;

/**
 * <AUTHOR>
 * @date 2024/7/23
 **/
public class StringUtils {
    /**
     * 是否为http(s)://开头
     *
     * @param link 链接
     * @return 结果
     */
    public static boolean ishttp(String link) {
        return StrUtil.startWithAny(link, Constants.HTTP, Constants.HTTPS);
    }

    /**
     * 首字母大写
     *
     * @param inputString 字符串
     * @return 结果
     */
    public static String capitalize(String inputString) {

        if (StrUtil.isNotBlank(inputString)) {
            char firstLetter = inputString.charAt(0);

            char capitalFirstLetter = Character.toUpperCase(firstLetter);

            return inputString.replace(inputString.charAt(0), capitalFirstLetter);
        }
        return inputString;
    }
}
