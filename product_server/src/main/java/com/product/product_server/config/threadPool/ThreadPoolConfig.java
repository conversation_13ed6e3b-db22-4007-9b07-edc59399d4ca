package com.product.product_server.config.threadPool;

import com.alibaba.ttl.threadpool.TtlExecutors;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Description 库存试算线程池
 * <AUTHOR>
 * @Date 2024/4/25 13:40
 **/
@Configuration
public class ThreadPoolConfig {
    @Bean(name = "visitLogThreadPool")
    public Executor visitLogThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3); // 设置核心线程数
        executor.setMaxPoolSize(3); // 设置最大线程数
        executor.setQueueCapacity(100); // 设置队列容量
        executor.setThreadNamePrefix("VISIT_LOG_THREAD_POOL"); // 设置线程名前缀
        executor.setAllowCoreThreadTimeOut(true); // 允许核心线程数回收
        executor.setKeepAliveSeconds(60 * 30); // 空闲线程的存活时间，这里设置为半小时
        executor.initialize(); // 初始化线程池
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return TtlExecutors.getTtlExecutor(executor);
    }
}