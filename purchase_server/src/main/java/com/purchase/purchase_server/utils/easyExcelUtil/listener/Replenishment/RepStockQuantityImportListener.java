package com.purchase.purchase_server.utils.easyExcelUtil.listener.Replenishment;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.excelObject.RepStockQuantityInfoExcel;
import com.purchase.purchase_server.entity.vo.UpgradeInfoInteriorVo;
import com.purchase.purchase_server.enums.RepStockQuantityImportHeadEnum;
import com.purchase.purchase_server.model.purchase.RepStockQuantityInfoDp;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentManager;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentMapManager;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;

/**
 * @Description 导入补货计划-库存
 * <AUTHOR>
 * @Date 2024/1/16 15:49
 **/
@Slf4j
public class RepStockQuantityImportListener extends AbstractRepImportListener<RepStockQuantityInfoExcel> {

    private final Map<String, String> nameIdMap;

    public RepStockQuantityImportListener(List<SenboWarehouseDto> senboWarehouseList) {
        super(senboWarehouseList);
        this.nameIdMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));
    }

    @Override
    public void invoke(RepStockQuantityInfoExcel product, AnalysisContext analysisContext) {
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }

        if (StrUtil.isBlank(product.getShipmentCode())) {
            throw new IllegalArgumentException("货件号不能为空");
        }

        if (product.getShipmentCode().length() > 30) {
            throw new IllegalArgumentException("货件号长度超过30");
        }

        ReplenishmentMapManager replenishmentMapManager = ReplenishmentMapManager.getInstance();

        new RepStockQuantityInfoDp(product, nameIdMap);

        VirtualProductDO virtualProductDO = Optional.ofNullable(replenishmentMapManager.getSkuVirtualProductMap().get(product.getVirtualSku()))
                .orElseGet(() -> Optional.ofNullable(replenishmentMapManager.getOldSkuVirtualProductMap().get(product.getVirtualSku()))
                        .orElseThrow(() -> new IllegalArgumentException("虚拟sku不存在")));
        FactoryRemainInventoryDto factoryRemainInventoryDto = FactoryRemainInventoryDto.builder()
                .virtualSku(product.getVirtualSku())
                .warehouse(nameIdMap.get(product.getStoreHouse()))
                .shipmentCode(product.getShipmentCode())
                .enableUsingDate(DateUtil.parse(product.getInventoryTime(), YYYY_MM_DD_DATE_FORMAT_SLASH))
                .startShippingTime(DateUtil.parse(product.getStartShippingTime(), YYYY_MM_DD_DATE_FORMAT_SLASH))
                .storeNum(Double.valueOf(product.getStockQuantity()))
                .remarks(product.getRemarks())
                .build();

        String resultSku = virtualProductDO.getVirtualSku();
        if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())){
            UpgradeInfoInteriorVo upgradeInfoInteriorVo = selectUpgradeInfo(virtualProductDO.getUpgradeId());

            if (ObjectUtil.isNotEmpty(upgradeInfoInteriorVo)){
                if (upgradeInfoInteriorVo.getOriginalSkuId().equals(virtualProductDO.getId())){
                    resultSku = upgradeInfoInteriorVo.getUpgradeSku();
                }
            }
        }
        String finalResultSku = resultSku;
        Optional.ofNullable(replenishmentMapManager.getStockQuantityMap().get(finalResultSku))
                .ifPresentOrElse(
                        list -> list.stream()
                                .filter(item -> item.getWarehouse().equals(factoryRemainInventoryDto.getWarehouse())
                                        && item.getEnableUsingDate().equals(factoryRemainInventoryDto.getEnableUsingDate())
                                        && item.getStartShippingTime().equals(factoryRemainInventoryDto.getStartShippingTime())
                                        && item.getShipmentCode().equals(factoryRemainInventoryDto.getShipmentCode())
                                        && item.getVirtualSku().equals(factoryRemainInventoryDto.getVirtualSku())
                                        && Objects.equals(item.getRemarks(), factoryRemainInventoryDto.getRemarks())
                                )
                                .findFirst()
                                .ifPresentOrElse(
                                        item -> item.setStoreNum(item.getStoreNum() + factoryRemainInventoryDto.getStoreNum()),
                                        () -> list.add(factoryRemainInventoryDto)
                                ),
                        () -> {
                            ArrayList<FactoryRemainInventoryDto> newList = new ArrayList<>();
                            newList.add(factoryRemainInventoryDto);
                            replenishmentMapManager.putStockQuantityMap(finalResultSku, newList);
                        }
                );
        ReplenishmentManager manager = ReplenishmentManager.getInstance();
        manager.incrementSuccessfulTimes();
    }

    /* 这里会一行行的返回头
     *
     * @param headMap
     * @param context
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        List<String> headList = RepStockQuantityImportHeadEnum.getCodeList();
        //删除headMap中的空值
        headMap.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty());
        List<String> headMapList = new ArrayList<>(headMap.values());

        //比较两个list集合值是否相等
        if (!headList.equals(headMapList)) {
            throw new RuntimeException("库存表头错误，请检查表头是否正确");
        }
    }
}
