package com.purchase.purchase_server.service.targetSales.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.dp.CalculationForm;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.DateUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentRecordDO;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentVirtualSkuPurchaseDO;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.enums.ReplenishmentStatusEnum;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.model.targetSales.entity.dto.TargetDaySalesDto;
import com.purchase.purchase_server.model.targetSales.entity.form.DaySalesEditForm;
import com.purchase.purchase_server.model.targetSales.entity.form.DaySalesSynchronizeForm;
import com.purchase.purchase_server.repository.dataRepository.ReplenishmentRecordRepositoryImpl;
import com.purchase.purchase_server.repository.dataRepository.ReplenishmentVirtualSkuPurchaseRepositoryImpl;
import com.purchase.purchase_server.repository.interiorRepository.TargetSalesRepository;
import com.purchase.purchase_server.repository.redis.ITargetSalesRedisRepository;
import com.purchase.purchase_server.service.targetSales.ITargetSalesService;
import com.purchase.purchase_server.utils.commonUtils.TargetSalesLogUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.constant.SystemConstant.PREPARE_VIRTUAL_SKU_TARGET_SALES;

/**
 * <AUTHOR>
 * @Date 2025/3/17 17:44
 **/
@Service
@Slf4j
public class TargetSalesServiceImpl implements ITargetSalesService {
    @Resource
    private ReplenishmentVirtualSkuPurchaseRepositoryImpl repVirtualSkuPurchaseRepository;

    @Resource
    private TargetSalesRepository targetSalesRepository;

    @Resource
    private ITargetSalesRedisRepository targetSalesRedisRepository;

    @Resource
    private ReplenishmentRecordRepositoryImpl replenishmentRecordRepository;

    @Resource
    private RestTemplate restTemplate;

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);

    @Override
    @OperationLog(content = "一键复制目标日销", operationType = "补货计划操作")
    public void updateTargetSales(DaySalesSynchronizeForm form, LogTrackNumDto logDto) {
        String virPurchaseId = form.getReplenishmentProjectId();
        ReplenishmentRecordDO record = replenishmentRecordRepository.selectOneByVirtualPurchaseId(virPurchaseId);
        if (ObjectUtil.isEmpty(record) || record.getReplenishmentStatus().equals(ReplenishmentStatusEnum.SAVED.getCode()) ||
                record.getReplenishmentStatus().equals(ReplenishmentStatusEnum.ABROGATE.getCode())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "该补货计划已完成或已作废，不允许一键复制目标日销");
        }

        ReplenishmentVirtualSkuPurchaseDO skuPurchaseInfo = repVirtualSkuPurchaseRepository.getById(virPurchaseId);
        String targetSales = skuPurchaseInfo.getSaleDestination();
        Map<String, Double> targetSalesMap = JSON.parseObject(targetSales, new TypeReference<>() {
        });

        List<TargetDaySalesDto> editFormList = new ArrayList<>();
        for (Map.Entry<String, Double> entry : targetSalesMap.entrySet()) {
            LocalDate localDate = DateUtils.convertToLocalDate(entry.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH);
            Double value = entry.getValue();
            editFormList.add(TargetDaySalesDto.builder().targetDate(localDate).targetSales(value).build());
        }
        Map<String, String> logMap = new HashMap<>();
        logMap.put(form.getReplenishmentProjectId(), TargetSalesLogUtils.targetSalesLog(editFormList, "复制", "到目标日销"));
        logDto.setLogMap(logMap);
        logDto.setAuthorization(SecurityUtils.getToken());

        DaySalesEditForm build = DaySalesEditForm.builder()
                .targetDaySalesList(editFormList)
                .virtualSkuId(skuPurchaseInfo.getVirtualSkuId())
                .content("复制补货").build();
        targetSalesRepository.updateTargetSales(build);
    }

    @Override
    public void prepareTargetSales(List<String> virtualIdList, LocalDate startDate) {
        CalculationForm form = CalculationForm.builder().virtualSkuIdList(virtualIdList).startDate(startDate).build();
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO<Boolean> result = restTemplateUtils.post(form, ResultDTO.class, PREPARE_VIRTUAL_SKU_TARGET_SALES);
        if (result.getStatus() != 200 || !result.getData()) {
            log.error("准备目标日销失败：{}", result.getMessage());
            throw new IllegalArgumentException("获取目标日销失败，请联系研发人员处理");
        }
    }

    @Override
    public Map<String, Double> getTargetSales(String virtualId, LocalDate startDate) {
        Map<String, Double> targetSalesMap = targetSalesRedisRepository.getTargetSales(virtualId, startDate);
        implementTargetSalesMap(targetSalesMap, startDate);
        return targetSalesMap;
    }

    @Override
    public Map<String, Double> getTargetSales(String virtualId, LocalDate startDate, LocalDate endDate) {
        Map<String, Double> targetSalesMap = targetSalesRedisRepository.getTargetSales(virtualId, startDate);
        implementTargetSalesMap(targetSalesMap, startDate, endDate);
        return targetSalesMap;
    }

    private void implementTargetSalesMap(Map<String, Double> targetSalesMap, LocalDate startDate) {
        // 当天的6个月后的月底
        LocalDate sixMonthLater = startDate.plusMonths(7).withDayOfMonth(1);
        LocalDate calEndDate = targetSalesMap.keySet().stream()
                .map(m -> LocalDate.parse(m, formatter))
                .max(Comparator.comparing(m -> m))
                .orElse(sixMonthLater);
        calEndDate = calEndDate.isBefore(sixMonthLater) ? sixMonthLater : calEndDate;

        implementTargetSalesMap(targetSalesMap, startDate, calEndDate);
    }

    @Override
    public void implementTargetSalesMap(Map<String, Double> targetSalesMap, LocalDate startDate, LocalDate endDate) {
        LocalDate localDate = LocalDate.of(startDate.getYear(), startDate.getMonth(), startDate.getDayOfMonth());
        for(LocalDate calDate = localDate; calDate.isBefore(endDate); calDate = calDate.plusDays(1)) {
            String format = calDate.format(formatter);
            targetSalesMap.putIfAbsent(format, 0D);
        }
    }
}
