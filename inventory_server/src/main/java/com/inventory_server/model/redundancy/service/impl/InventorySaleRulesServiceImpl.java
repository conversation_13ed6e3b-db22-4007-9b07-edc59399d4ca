package com.inventory_server.model.redundancy.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.crafts_mirror.utils.dp.ProduceDaysDp;
import com.inventory_server.model.product.bo.SelfAndVirtualBO;
import com.inventory_server.model.product.service.ISelfProductService;
import com.inventory_server.model.redundancy.entity.dos.InventorySaleRulesDO;
import com.inventory_server.model.redundancy.entity.dp.InventoryCalDp;
import com.inventory_server.model.redundancy.entity.dp.InventoryRulesDp;
import com.inventory_server.model.redundancy.entity.dto.AchievementRateDto;
import com.inventory_server.model.redundancy.entity.dto.ImportActualDestinationSalesDto;
import com.inventory_server.model.redundancy.entity.dto.InterventionalTimeDto;
import com.inventory_server.model.redundancy.enums.UrgentCircleEditedEnum;
import com.inventory_server.model.redundancy.repository.dataRepository.InventorySaleRulesRepository;
import com.inventory_server.model.redundancy.service.IInventorySaleRulesService;
import com.inventory_server.model.targetSales.service.ITargetSalesService;
import com.inventory_server.model.warehouse.service.IWarehouseService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Description 冗余库存信息获取、初步处理、数据储存、展示的service层
 * <AUTHOR>
 * @Date 2024/5/7 16:38
 **/
@Service
@Slf4j
public class InventorySaleRulesServiceImpl implements IInventorySaleRulesService {

    @Resource
    private InventorySaleRulesRepository inventorySaleRulesRepository;

    @Resource
    private ITargetSalesService targetSalesService;

    @Resource
    private ISelfProductService selfProductService;

    @Resource
    private IWarehouseService warehouseService;

    @Override
    public List<InventorySaleRulesDO> getInventorySaleByInfoIds(List<String> infoIds) {
        return inventorySaleRulesRepository.getInventorySaleByInfoIds(infoIds);
    }

    @Override
    public InventorySaleRulesDO getInventorySaleByInfoId(String infoId) {
        return getInventorySaleByInfoIds(Collections.singletonList(infoId)).getFirst();
    }

    @Override
    public boolean updateInventorySaleRules(InventorySaleRulesDO inventorySaleRule) {
        return inventorySaleRulesRepository.updateById(inventorySaleRule);
    }

    @Override
    public InventorySaleRulesDO getInventorySaleById(String id) {
        return inventorySaleRulesRepository.getById(id);
    }

    @Override
    public InterventionalTimeDto calInterventionalTime(String inventoryId, Map<String, Double> shippingRatioMap, String virtualSkuId) {
        int normalProduceDays;
        InterventionalTimeDto interventionalTimeDto = new InterventionalTimeDto();
        InventorySaleRulesDO rulesDO = inventorySaleRulesRepository.getInventorySaleByInfoId(inventoryId);
        SelfAndVirtualBO selfAndVirtualBO = selfProductService.getSelfProductByVirtualId(virtualSkuId);
        ProduceDaysDp produceDaysDp = ProduceDaysDp.initProduceDaysDp(selfAndVirtualBO.getPurchaseDate());
        normalProduceDays = produceDaysDp.getProduceDay(LocalDate.now());
        // 获取加急头程天数、正常生产交期、加急生产交期
        if (rulesDO == null) {
            interventionalTimeDto.setNormalProduceDays(normalProduceDays);
            interventionalTimeDto.setUrgentProduceDays(normalProduceDays);
            interventionalTimeDto.setUrgentProduceEdited(UrgentCircleEditedEnum.UNCHANGED.getCode());

            interventionalTimeDto.setUrgentShipDateEdited(UrgentCircleEditedEnum.UNCHANGED.getCode());
            interventionalTimeDto.setUrgentShipDate(warehouseService.getMinHeadShipDate(shippingRatioMap));
        } else {
            int urgentShipDateEdited = Optional.ofNullable(rulesDO.getUrgentShipDateEdited()).orElse(0);
            int urgentProduceDaysEdited = Optional.ofNullable(rulesDO.getUrgentProduceDaysEdited()).orElse(0);
            interventionalTimeDto.setNormalProduceDays(normalProduceDays);
            interventionalTimeDto.setUrgentProduceEdited(rulesDO.getUrgentProduceDaysEdited());
            interventionalTimeDto.setUrgentShipDateEdited(urgentShipDateEdited);

            if (urgentProduceDaysEdited == UrgentCircleEditedEnum.CHANGED.getCode()) {
                interventionalTimeDto.setUrgentProduceDays(rulesDO.getUrgentProduceDays());
            } else {
                interventionalTimeDto.setUrgentProduceDays(normalProduceDays);
            }

            if (urgentShipDateEdited == UrgentCircleEditedEnum.CHANGED.getCode()) {
                interventionalTimeDto.setUrgentShipDate(rulesDO.getUrgentShipDate());
            } else {
                interventionalTimeDto.setUrgentShipDate(warehouseService.getMinHeadShipDate(shippingRatioMap));
            }
        }
        return interventionalTimeDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InventorySaleRulesDO saveInventorySaleRules(String inventoryId, InventoryCalDp inventoryCalDp, String headShippingDays,
                                       int redundantDate, AchievementRateDto achievementRateDto, InterventionalTimeDto interventionalTime) {
        String virtualSkuId = inventoryCalDp.virtualProduct().getId();
        InventorySaleRulesDO rulesDO = inventorySaleRulesRepository.getInventorySaleByInfoId(inventoryId);
        SelfAndVirtualBO selfAndVirtualBO = selfProductService.getSelfProductByVirtualId(virtualSkuId);
        String rulesId = rulesDO == null ? null : rulesDO.getId();

        InventoryRulesDp inventoryRules = inventoryCalDp.inventoryRules();
        Map<String, Double> shippingRatioMap = inventoryRules.shippingRulesDp().shippingRatioMap();

        ImportActualDestinationSalesDto actualDestinationSalesDto = inventoryCalDp.actualDestinationSalesDto();

        Map<String, BigDecimal> targetSalesMap = inventoryCalDp.targetSales().targetSalesMap();

        InventorySaleRulesDO build = InventorySaleRulesDO.builder()
                .id(rulesId)
                .headShippingDate(headShippingDays)
                .urgentShipDate(interventionalTime.getUrgentShipDate())
                .urgentShipDateEdited(interventionalTime.getUrgentShipDateEdited())
                .inventoryInfoId(inventoryId)
                .safeDays(inventoryRules.daysRulesDp().safeDays())
                .changeableSafeDays(redundantDate)
                .saleRatio(JSONObject.toJSONString(shippingRatioMap))
                .saleDestination(JSONObject.toJSONString(targetSalesMap))
                .purchaseDays(inventoryRules.daysRulesDp().purchaseDays())
                .produceDays(selfAndVirtualBO.getPurchaseDate())
                .purchaseCircle(inventoryRules.daysRulesDp().purchaseCircle())
                .shippingCircle(inventoryRules.daysRulesDp().shippingCircle())
                .transitDays(inventoryRules.daysRulesDp().transitDays())
                .sevenDaySales(Double.parseDouble(actualDestinationSalesDto.getSevenDaySales()))
                .fourteenDaySales(Double.parseDouble(actualDestinationSalesDto.getFourteenDaySales()))
                .thirtyDaySales(Double.parseDouble(actualDestinationSalesDto.getThirtyDaySales()))
                .actualDailySales(Double.parseDouble(actualDestinationSalesDto.getActualDailySales()))
                .targetSalesNum(achievementRateDto.getTargetSalesNum().doubleValue())
                .subEntityRate(achievementRateDto.getSubEntityRate().doubleValue())
                .parentEntityRate(achievementRateDto.getParentEntityRate().doubleValue())
                .normalProduceDays(interventionalTime.getNormalProduceDays())
                .urgentProduceDays(interventionalTime.getUrgentProduceDays())
                .urgentProduceDaysEdited(interventionalTime.getUrgentProduceEdited())
                .build();
        inventorySaleRulesRepository.saveOrUpdate(build);
        return build;
    }

    @Override
    public void batchSaleRules(List<InventorySaleRulesDO> list) {
        inventorySaleRulesRepository.saveBatch(list);
    }

    @Override
    public void deleteInventorySaleByInfoId(String inventoryId) {
        inventorySaleRulesRepository.deleteInventorySaleByInfoId(inventoryId);
    }

    @Override
    public List<InventorySaleRulesDO> getAllRatioBiggerThanZeroWarehouseSaleRules(String warehouse) {
        return inventorySaleRulesRepository.getAllRatioBiggerThanZeroWarehouseSaleRules(warehouse);
    }
}
