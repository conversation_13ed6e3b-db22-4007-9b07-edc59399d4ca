package com.purchase.purchase_server.entity.dto.delivery;

import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.entity.form.interior.SoldOutDateBeforeTheoryDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * @Description 发货试算数据
 * <AUTHOR>
 * @Date 2024/1/2 17:26
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TrialCalReplenishmentDto {
    private String virtualSku;
    private Map<String, Map<String, Double>> everydayOnShippingInventoryMap;
    private Map<String, Map<String, Double>> everydayRemainInventoryMap;
    private Map<String, Map<String, Double>> everydaySaleProductMap;
    private List<FactoryRemainInventoryDto> shippingInventoryList;
    /**
     * 全链路售罄
     */
    private List<SoldOutDateBeforeTheoryDto> soldOutDateBeforeTheoryList;

    /**
     * 全链路海外仓理论售罄时间
     */
    private LocalDate fullLinkTheoreticalSoldOutDate;

    /**
     * 优先发货的
     */
    private List<FactoryRemainInventoryDto> priorDeliveryList;
}
