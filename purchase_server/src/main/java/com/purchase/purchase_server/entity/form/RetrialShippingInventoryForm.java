package com.purchase.purchase_server.entity.form;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 重新试算发货计划表格的表单
 * <AUTHOR>
 * @Date 2024/1/12 13:48
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RetrialShippingInventoryForm implements Serializable {
    List<DeliverySaveWatchBoardForm> deliveryDetailList;

    private String shippingProjectId;
}
