package com.inventory_server.model.warehouse.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN;

/**
 * @Description 仓库信息列表页
 * <AUTHOR>
 * @Date 2024/7/4 14:54
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseDetailVo {
    /**
     * 易仓仓库在我们数据库里的主键
     */
    private String id;

    /**
     * 易仓仓库id
     */
    private Integer ycWarehouseId;
    /**
     * 易仓仓库代码
     */
    private String ycWarehouseCode;
    /**
     * 仓库名称
     */
    private String ycWarehouseDesc;
    /**
     * 仓库类型：0标准，1中转
     */
    private Integer warehouseType;
    /**
     * 状态：-1已废弃,0不可用,1可用
     */
    private Integer warehouseStatus;
    /**
     * 运营方式：0自营,1第三方
     */
    private Integer warehouseVirtual;
    /**
     * 第三方仓储服务
     */
    private String warehouseService;
    /**
     * 森帛仓库
     */
    private String senboWarehouse;
    /**
     * 头程时间
     */
    private Integer headShippingDate;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String state;
    /**
     * 电话
     */
    private String phoneNo;
    /**
     * 邮编
     */
    private String postcode;
    /**
     * 添加时间
     */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN)
    private LocalDateTime warehouseAddTime;
    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN)
    private LocalDateTime warehouseUpdateTime;
}
