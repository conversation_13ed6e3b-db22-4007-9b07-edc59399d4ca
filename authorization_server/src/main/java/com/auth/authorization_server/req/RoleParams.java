package com.auth.authorization_server.req;

import com.auth.authorization_server.enums.RoleStatusEnum;
import com.auth.authorization_server.model.BasePageForm;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/7/22
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoleParams extends BasePageForm {

    @Serial
    private static final long serialVersionUID = 7589908732755004898L;

    /**
     * 角色id
     */
    private String id;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 30, message = "角色名称长度不能超过30个字符")
    private String roleName;

    /**
     * 显示顺序
     */
    @NotNull(message = "排序不能为空")
    @Max(value = 999, message = "排序最大值为999")
    private Integer roleSort;

    /**
     * 角色状态（0正常 1停用）
     *
     * @see RoleStatusEnum
     */
    private String roleStatus;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 用户是否存在此角色标识 默认不存在
     */
    private boolean flag = false;

    /**
     * 菜单组
     */
    private String[] menuIds;

    /**
     * 用户ids
     */
    private List<String> userIds;

    /**
     * 角色菜单权限
     */
    private Set<String> permissions;

    /**
     * 部门ids
     */
    private List<String> deptIds;

    /**
     * 功能ids
     */
    private List<String> moduleIds;

    public String getRoleName() {
        return Optional.ofNullable(roleName)
                .map(String::strip)
                .orElse(null);
    }

}
