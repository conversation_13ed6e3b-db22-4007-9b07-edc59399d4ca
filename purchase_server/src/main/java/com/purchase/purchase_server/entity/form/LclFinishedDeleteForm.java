package com.purchase.purchase_server.entity.form;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 发货装柜
 * @date 2024/1/18
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LclFinishedDeleteForm implements Serializable {

    @Serial
    private static final long serialVersionUID = -6921622825212907015L;
    @NotBlank(message = "装柜计划id不能为空")
    private String id;
    /**
     * 发货装柜日期
     */
    @NotBlank(message = "发货装柜日期不能为空")
    private String shippingStartDate;

    /**
     * 是否整理
     * @see com.purchase.purchase_server.enums.lcl.LclConsolidationEnum
     */
    private String isLclConsolidation;
}
