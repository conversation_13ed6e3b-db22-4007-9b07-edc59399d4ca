package com.purchase.purchase_server.entity.bo;

import com.purchase.purchase_server.entity.dataObject.TrialShippingInventoryDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/1/10 14:30
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryFinishedInventoryBO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1522219642883899L;

    private String id;
    /**
     * 合同号
     */
    private String contractCode;

    /**
     * 工厂交货时间
     */
    private String factoryFinishedDate;
    /**
     * 剩余数量
     */
    private Integer factoryRemainNum;
    /**
     * 工厂交货数量
     */
    private Integer factoryShippingPackageNum;
    /**
     * 发货计划id
     */
    private String shippingProjectId;
    /**
     * 虚拟skuId
     */
    private String virtualSkuId;
    /**
     * sku类型
     * @see com.purchase.purchase_server.enums.IsOldStatusEnum
     */
    private String isOldStatus;

    /*
    * 快照记录id
    * */
    private String productSnapshotId;

    /**
     * 数据来源
     * @see com.purchase.purchase_server.enums.ShippingSourceTypeEnum
     */
    private String sourceType;

    /**
     * 发货类型
     */
    private Integer deliveryType;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 头程时间
     */
    private String headShippingDays;

    /**
     * 发货比例
     */
    private String shippingRatio;
    private List<TrialShippingInventoryDO> trialShippingInventoryList;
}
