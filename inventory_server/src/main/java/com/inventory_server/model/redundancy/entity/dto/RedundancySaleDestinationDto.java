package com.inventory_server.model.redundancy.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RedundancySaleDestinationDto implements Serializable {


    @Serial
    private static final long serialVersionUID = -335371902071946019L;
    private String saleDestinationDate;

   private Double saleDestinationNum;
}