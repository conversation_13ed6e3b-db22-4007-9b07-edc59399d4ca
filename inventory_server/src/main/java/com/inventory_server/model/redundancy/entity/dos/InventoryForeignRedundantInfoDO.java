package com.inventory_server.model.redundancy.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_inventory_foreign_redundant_info")
public class InventoryForeignRedundantInfoDO extends PhysicalBaseEntity {

  private String warehouse;
  private Double redundantNum;
  private String watchBoardId;
}
