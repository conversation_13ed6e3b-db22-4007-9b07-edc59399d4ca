<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.purchase.purchase_server.mapper.FactoryFinishedInventoryMapper">
    <!-- 批量删除 -->
    <delete id="deleteByShippingProjectIds">
        DELETE FROM cm_delivery_factory_inventory WHERE shipping_project_id IN
        <foreach collection="shippingProjectIds" item="shippingProjectId" open="(" separator="," close=")">
            #{shippingProjectId}
        </foreach>
        <if test="sourceType != null and sourceType != ''">
            AND source_type = #{sourceType}
        </if>

    </delete>
    <select id="getDeliveryNumInfo"
            resultType="com.purchase.purchase_server.entity.dto.delivery.DeliveryNumDto">
        SELECT
        A.shipping_num AS number,
        B.id AS factoryFinishedId,
        B.shipping_project_id
        FROM
        ( SELECT C.factory_finished_id, SUM( C.shipping_num ) AS shipping_num
            FROM cm_trial_shipping_inventory C
            LEFT JOIN cm_delivery_factory_inventory B ON B.id = C.factory_finished_id
            WHERE C.`status` = '0' AND C.real_shipping_start_date BETWEEN #{shippingStartDate} AND #{shippingEndDate} AND
            B.shipping_project_id IN
            <foreach collection="projectList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            GROUP BY factory_finished_id ) A
        LEFT JOIN cm_delivery_factory_inventory B ON B.id = A.factory_finished_id AND B.`status` = '0'
        WHERE
        B.shipping_project_id IN
        <foreach collection="projectList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getNotDeliveryNumInfo"
            resultType="com.purchase.purchase_server.entity.dto.delivery.DeliveryNumDto">
        SELECT
        A.id AS factoryFinishedId,
        A.shipping_project_id,
        A.factory_shipping_package_num - SUM(IF(B.shipping_num is NULL,0,B.shipping_num)) AS number
        FROM
        cm_delivery_factory_inventory A
        LEFT JOIN (SELECT IF(real_shipping_start_date &lt;= #{shippingEndDate},shipping_num,0) AS shipping_num, factory_finished_id FROM cm_trial_shipping_inventory WHERE `status` = '0')  B ON A.id = B.factory_finished_id
        LEFT JOIN cm_delivery_rules C ON A.shipping_project_id = C.delivery_project_id
        WHERE
        A.factory_finished_date &lt; DATE_SUB(#{shippingEndDate},INTERVAL IF(A.delivery_type = 0, 0, C.transit_days) DAY) AND A.factory_shipping_package_num >= 1
        AND A.`status` = '0' AND A.shipping_project_id IN
        <foreach collection="projectList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY A.id
    </select>
</mapper>
