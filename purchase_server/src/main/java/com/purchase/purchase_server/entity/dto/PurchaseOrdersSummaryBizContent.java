package com.purchase.purchase_server.entity.dto;

import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrdersSummaryDto;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 易仓获取采购单概述bizContent类
 * <AUTHOR>
 * @Date 2024/11/4 9:58
 **/
@Data
@Builder
public class PurchaseOrdersSummaryBizContent implements Serializable {
    @Serial
    private static final long serialVersionUID = 1185705357229869580L;
    private Integer total;

    private List<OrdersSummaryDto> data;
}
