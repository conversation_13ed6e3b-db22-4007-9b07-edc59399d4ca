package com.purchase.purchase_server.service.purchaseOrder.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.purchase.purchase_server.assembler.YiCangPurchaseOrdersAssembler;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderTrackDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrdersSummaryDto;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.YicangPurchaseOrderTrackDTO;
import com.purchase.purchase_server.repository.dataRepository.YicangPurchaseOrderTrackRepositoryImpl;
import com.purchase.purchase_server.service.purchaseOrder.IYicangPurchaseOrderTrackService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【cm_yicang_purchase_order_track(易仓采购单跟单状态表)】的数据库操作Service实现
* @createDate 2024-11-01 14:30:43
*/
@Service
@Slf4j
public class YicangPurchaseOrderTrackServiceImpl implements IYicangPurchaseOrderTrackService {

    @Resource
    private YicangPurchaseOrderTrackRepositoryImpl purchaseOrderTrackRepository;

    @Resource
    private YiCangPurchaseOrdersAssembler purchaseOrdersAssembler;
    @Override
    public void removeBySbPoId(String sbPoId) {
        purchaseOrderTrackRepository.removeBySbPoId(sbPoId);
    }

    @Override
    public void save(YicangPurchaseOrderTrackDO trackDO) {
        purchaseOrderTrackRepository.save(trackDO);
    }

    @Override
    public void updateTrackingInfo(OrdersSummaryDto order, String sbPoId) {
        // 清除现有跟踪信息
        purchaseOrderTrackRepository.removeBySbPoId(sbPoId);

        // 更新采购单跟踪状态
        if (ObjectUtil.length(order.getTrack()) > 0) {
            var trackDTO = JSON.parseObject(JSON.toJSONString(order.getTrack()), YicangPurchaseOrderTrackDTO.class);
            var trackDO = purchaseOrdersAssembler.trackDTOtoDO(trackDTO);
            trackDO.setSbPoId(sbPoId);
            purchaseOrderTrackRepository.save(trackDO);
        }
    }
}




