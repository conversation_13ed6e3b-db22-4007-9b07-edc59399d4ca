package com.product.product_server.scheduled;

import com.product.product_server.service.ISnapJobService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @Description 快照数据定时任务类
 * <AUTHOR>
 * @Date 2024/11/8 13:53
 **/
@Service
public class SnapScheduledTask {

    @Resource
    private ISnapJobService snapJobService;

    @XxlJob("cleanUnusedSnapshots")
    public void cleanUnusedSnapshots() {
        snapJobService.cleanUnusedSnapshots(XxlJobHelper.getJobParam());
    }

    @XxlJob("refreshSnapshots")
    public void refreshSnapshots() {
        snapJobService.refreshSnapshots();
    }
}
