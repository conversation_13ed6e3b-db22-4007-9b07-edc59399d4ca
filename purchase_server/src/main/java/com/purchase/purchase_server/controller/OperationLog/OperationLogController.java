package com.purchase.purchase_server.controller.OperationLog;

import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.purchase.purchase_server.entity.dto.OperationLog.LogListByTypeDto;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.OperationLog.LogListVo;
import com.purchase.purchase_server.service.IOperationLogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/11/19
 **/
@RestController
@RequestMapping(value = "/log")
@Slf4j
public class OperationLogController {

    @Resource
    private IOperationLogService operationLogService;

    /**
     * 日志-货件
     */
    @PostMapping("list/shipment")
    @RequiresPermissions("purchase:shipment:pageList")
    @ResponseBody
    ResultDTO<LogListVo> listOperationLog(@RequestBody LogListByTypeDto num) {
        return ResultDTO.success(LogListVo.builder().list(operationLogService.getLogListByType(num)).build());
    }
}
