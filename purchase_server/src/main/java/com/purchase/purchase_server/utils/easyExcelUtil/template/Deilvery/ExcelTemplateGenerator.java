package com.purchase.purchase_server.utils.easyExcelUtil.template.Deilvery;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.utils.easyExcelUtil.ExcelWidthStyleStrategy;
import com.purchase.purchase_server.utils.easyExcelUtil.template.WarehouseFilter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;

/**
 * <AUTHOR>
 * @date 2024/10/8
 **/
public class ExcelTemplateGenerator {
    public void generateTemplate(ExcelWriter excelWriter, List<SenboWarehouseDto> warehouses) {
        List<String> warehouseNames = warehouses.stream().map(SenboWarehouseDto::getSenboWarehouse).collect(Collectors.toList());

        // 生成sheet
        generateSheet1(excelWriter);
//        generateSheet2(excelWriter);
        generateSheet3(excelWriter, warehouseNames);
        generateSheet6(excelWriter, warehouseNames);
        generateSheet7(excelWriter);
        generateSheet8(excelWriter);
    }

    // 备货规则
    private void generateSheet1(ExcelWriter excelWriter) {
        List<List<String>> headList = new ArrayList<>();

        for (String column : ExcelConstants.Sheet1.FIXED_COLUMNS) {
            headList.add(List.of(column));
        }

        WriteSheet writeSheet = EasyExcel.writerSheet("备货规则").sheetNo(1).head(headList)
                .registerWriteHandler(new ExcelWidthStyleStrategy())
                .registerWriteHandler(createStyledWriteSheet()).build();
        excelWriter.write(new ArrayList<>(), writeSheet);
    }

    // 目标日销
    private void generateSheet2(ExcelWriter excelWriter) {
        List<List<String>> headList = new ArrayList<>();

        for (String column : ExcelConstants.Sheet2.FIXED_COLUMNS) {
            headList.add(List.of(column));
        }

        List<String> result = new ArrayList<>();
        DateTime currentDate = DateUtil.beginOfMonth(DateUtil.date());

        for (int i = 0; i < 6; i++) {
            result.add(DateUtil.format(currentDate, YYYY_MM_DD_DATE_FORMAT_SLASH));
            currentDate = DateUtil.offsetMonth(currentDate, 1);
        }
        for (String date : result) {
            headList.add(List.of(date));
        }
        WriteSheet writeSheet = EasyExcel.writerSheet("目标日销").sheetNo(2).head(headList)
                .registerWriteHandler(new ExcelWidthStyleStrategy())
                .registerWriteHandler(createStyledWriteSheet()).build();
        excelWriter.write(new ArrayList<>(), writeSheet);
    }

    // 亚马逊库存概况
    private void generateSheet3(ExcelWriter excelWriter, List<String> warehouseNames) {
        List<List<String>> headList = new ArrayList<>();

        for (String column : ExcelConstants.Sheet3.FIXED_COLUMNS) {
            headList.add(List.of(column));
        }
        List<String> filteredWarehouses = WarehouseFilter.filterForSheet1(warehouseNames);
        for (String name : filteredWarehouses) {
            headList.add(List.of(name));
        }

        WriteSheet writeSheet = EasyExcel.writerSheet("家具库存概况").sheetNo(3).head(headList)
                .registerWriteHandler(new ExcelWidthStyleStrategy())
                .registerWriteHandler(createStyledWriteSheet()).build();
        excelWriter.write(new ArrayList<>(), writeSheet);
    }

    //// BTB库存概况
    //private void generateSheet4(ExcelWriter excelWriter, List<String> warehouseNames) {
    //    List<List<String>> headList = new ArrayList<>();
    //
    //    for (String column : ExcelConstants.Sheet4.FIXED_COLUMNS) {
    //        headList.add(List.of(column));
    //    }
    //    List<String> filteredWarehouses = WarehouseFilter.filterForSheet2(warehouseNames);
    //    for (String name : filteredWarehouses) {
    //        headList.add(List.of(name));
    //    }
    //
    //    WriteSheet writeSheet = EasyExcel.writerSheet("BTB库存概况").sheetNo(4).head(headList)
    //            .registerWriteHandler(new ExcelWidthStyleStrategy())
    //            .registerWriteHandler(createStyledWriteSheet()).build();
    //    excelWriter.write(new ArrayList<>(), writeSheet);
    //}
    //
    //// 其他库存概况
    //private void generateSheet5(ExcelWriter excelWriter, List<String> warehouseNames) {
    //    List<List<String>> headList = new ArrayList<>();
    //
    //    for (String column : ExcelConstants.Sheet5.FIXED_COLUMNS) {
    //        headList.add(List.of(column));
    //    }
    //    List<String> filteredWarehouses = WarehouseFilter.filterForSheet3(warehouseNames);
    //    for (String name : filteredWarehouses) {
    //        headList.add(List.of(name));
    //    }
    //
    //    WriteSheet writeSheet = EasyExcel.writerSheet("其他库存概况").sheetNo(5).head(headList)
    //            .registerWriteHandler(new ExcelWidthStyleStrategy())
    //            .registerWriteHandler(createStyledWriteSheet()).build();
    //    excelWriter.write(new ArrayList<>(), writeSheet);
    //}

    // 灯具库存概况
    private void generateSheet6(ExcelWriter excelWriter, List<String> warehouseNames) {
        List<List<String>> headList = new ArrayList<>();

        for (String column : ExcelConstants.Sheet6.FIXED_COLUMNS) {
            headList.add(List.of(column));
        }
        List<String> filteredWarehouses = WarehouseFilter.filterForSheet4(warehouseNames);
        for (String name : filteredWarehouses) {
            headList.add(List.of(name));
        }

        WriteSheet writeSheet = EasyExcel.writerSheet("灯具库存概况").sheetNo(6).head(headList)
                .registerWriteHandler(new ExcelWidthStyleStrategy())
                .registerWriteHandler(createStyledWriteSheet()).build();
        excelWriter.write(new ArrayList<>(), writeSheet);
    }
    /**
     * 在途
     */
    private void generateSheet7(ExcelWriter excelWriter) {
        List<List<String>> headList = new ArrayList<>();

        for (String column : ExcelConstants.Sheet7.FIXED_COLUMNS) {
            headList.add(List.of(column));
        }

        WriteSheet writeSheet = EasyExcel.writerSheet("在途").sheetNo(7).head(headList)
                .registerWriteHandler(new ExcelWidthStyleStrategy())
                .registerWriteHandler(createStyledWriteSheet()).build();
        excelWriter.write(new ArrayList<>(), writeSheet);
    }
    // 计划
    private void generateSheet8(ExcelWriter excelWriter) {
        List<List<String>> headList = new ArrayList<>();

        for (String column : ExcelConstants.Sheet8.FIXED_COLUMNS) {
            headList.add(List.of(column));
        }

        WriteSheet writeSheet = EasyExcel.writerSheet("计划").sheetNo(8).head(headList)
                .registerWriteHandler(new ExcelWidthStyleStrategy())
                .registerWriteHandler(createStyledWriteSheet()).build();
        excelWriter.write(new ArrayList<>(), writeSheet);
    }

    private HorizontalCellStyleStrategy createStyledWriteSheet() {
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();

        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);

        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setWrapped(false);
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }
}
