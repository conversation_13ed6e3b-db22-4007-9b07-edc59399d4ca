package com.sales_server.model.targetSales.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * @Description 目标日销聚合实体类
 * <AUTHOR>
 * @Date 2024/11/12 13:43
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TargetSalesAggDto implements Serializable {

    private String virtualSkuId;
    private LocalDate targetDate;
    private Double targetSales;
}
