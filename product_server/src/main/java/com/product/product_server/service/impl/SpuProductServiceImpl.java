package com.product.product_server.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.dp.LogTrackNumDto;
import com.product.product_server.entity.dataObject.SpuProductDO;
import com.product.product_server.entity.form.RemarksSaveForm;
import com.product.product_server.entity.form.SpuInfoSaveForm;
import com.product.product_server.entity.form.VirtualProductSearchForm;
import com.product.product_server.entity.form.VirtualSkuInfoUnderSpuForm;
import com.product.product_server.entity.vo.SpuDetailVo;
import com.product.product_server.entity.vo.SpuProductListVo;
import com.product.product_server.entity.vo.VirtualProductForSpuVo;
import com.product.product_server.entity.vo.VirtualProductListVo;
import com.product.product_server.model.products.RemarksSaveDp;
import com.product.product_server.repository.SpuProductRepositoryImpl;
import com.product.product_server.repository.VirtualProductRepositoryImpl;
import com.product.product_server.service.ISpuProductService;
import com.product.product_server.service.IVirtualProductService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description sup相关数据
 * <AUTHOR>
 * @Date 2023/12/16 11:12
 **/
@Service
public class SpuProductServiceImpl implements ISpuProductService {

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepository;
    @Resource
    private SpuProductRepositoryImpl spuProductRepository;

    @Resource
    private IVirtualProductService virtualProductService;

    @Transactional(rollbackFor = Exception.class)
    public boolean saveSpuProductInfo(SpuInfoSaveForm form) {
        if (spuProductRepository.checkSpuExisted(form.spu(), form.spuId())) {
            throw new RuntimeException("父SPU重复，请更换spu");
        }

        String spuId = spuProductRepository.saveSpuProduct(form);

        if (StrUtil.isBlank(spuId)) {
            throw new RuntimeException("保存spu相关信息失败");
        }

        virtualProductRepository.updateSpuByVirtualId(form.virtualSkuList(), new LogTrackNumDto(spuId), spuId);
        return true;
    }

    @Override
    public IPage<SpuProductListVo> getSpuInfoPage(VirtualProductSearchForm form) {
        return spuProductRepository.getSpuProductPage(form);
    }

    @Override
    @OperationLog(content = "保存父SPU备注", operationType = "父SPU编辑")
    public boolean saveSpuRemarks(RemarksSaveForm form, LogTrackNumDto dto) {
        RemarksSaveDp saveDp = new RemarksSaveDp(form.getId(), form.getRemarks());
        return spuProductRepository.saveRemarks(saveDp) == 1;
    }

    @Override
    @OperationLog(content = "删除父SPU", operationType = "父SPU编辑")
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSpuProduct(String spuProductId, LogTrackNumDto dto) {
        boolean deleteSpuProduct = spuProductRepository.deleteSpuProduct(spuProductId);
        boolean deletedSpuInVirtualProduct = virtualProductRepository.deleteSpuInVirtualProduct(spuProductId);
        return deleteSpuProduct && deletedSpuInVirtualProduct;
    }

    @Override
    public SpuDetailVo getSpuDetailInfo(String spuProductId) {
        SpuProductDO spuProduct = spuProductRepository.getById(spuProductId);
        Objects.requireNonNull(spuProduct, "spu信息为空");
        List<VirtualProductListVo> list = virtualProductService.getVirtualProductListUnderSpu(
                VirtualSkuInfoUnderSpuForm.builder().spu(spuProductId).build()
        );
        return SpuDetailVo.builder()
                .spuProductName(spuProduct.getSpuProductName())
                .virtualProductList(list.stream().map(this::convertVirtualProductForSpu).collect(Collectors.toList()))
                .remarks(spuProduct.getRemarks())
                .spu(spuProduct.getSpu())
                .spuId(spuProductId)
                .build();
    }

    private VirtualProductForSpuVo convertVirtualProductForSpu(VirtualProductListVo vo) {
        return VirtualProductForSpuVo.builder()
                .productName(vo.getProductName())
                .virtualProductId(vo.getVirtualSkuId())
                .virtualSku(vo.getVirtualSku())
                .image(vo.getImage())
                .build();
    }
}
