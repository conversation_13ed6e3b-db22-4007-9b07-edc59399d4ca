package com.purchase.purchase_server.utils.easyExcelUtil;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.handler.context.WorkbookWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Package: com.stech.bms.buss.utils
 * @ClassName: ExcelMergeUtil
 * @Author: sgq
 * @Date: 2023/7/28 13:29
 * @Description: 仅处理单列数据相同合并单元格
 */
public class RepSimpleCommonExcelMergeUtil implements CellWriteHandler, WorkbookWriteHandler {

    private int mergeRowIndex;
    private int[] mergeColumnIndexes;

    private final String CONTEXT_KEY = "contextKey";

    private final String RANGE_CONTEXT_KEY = "rangeContextKey";

    public RepSimpleCommonExcelMergeUtil(int mergeRowIndex, int[] mergeColumnIndexes) {
        this.mergeRowIndex = mergeRowIndex;
        this.mergeColumnIndexes = mergeColumnIndexes;
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {

    }

    /**
     * 文件写完后写入合并区域信息
     *
     * @param context
     */
    @Override
    public void afterWorkbookDispose(final WorkbookWriteHandlerContext context) {
        //当前表格写完后，统一写入
        Map<Integer, List<CellRangeAddress>> rangeMap = ExcelThreadContext.getMap(RANGE_CONTEXT_KEY);
        if (CollUtil.isNotEmpty(rangeMap)) {
            rangeMap.values().stream().flatMap(list -> list.stream().filter(CollUtil::isNotEmpty))
                    .forEach(address -> context.getWriteContext().writeSheetHolder().getSheet().addMergedRegion(address));
        }
        //清除线程中的数据
        ExcelThreadContext.clear();
    }

    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        Cell cell = context.getCell();
        int curRowIndex = cell.getRowIndex();
        int curColIndex = cell.getColumnIndex();
        WriteSheetHolder writeSheetHolder = context.getWriteSheetHolder();
        if (curRowIndex > mergeRowIndex) {
            for (int i = 0; i < mergeColumnIndexes.length; i++) {
                if (curColIndex == mergeColumnIndexes[i]) {
                    mergeWithPrevRow(writeSheetHolder, cell, curRowIndex, curColIndex);
                    break;
                }
            }
        }

    }

    private void mergeWithPrevRow(WriteSheetHolder writeSheetHolder, Cell cell, int curRowIndex, int curColIndex) {
        //ExcelThreadContext 就是一个ThreadLocal,用于缓存当前表格数据
        //用于存储每列的最后一个合并区域的首行单元格  key：列序号 value：最后一个合并区域首行单元格
        Map<Integer, Cell> cellMap = ExcelThreadContext.getMap(CONTEXT_KEY);
        //用于存储每列的所有合并单元格信息 key：列序号 value：合并单元格列表
        Map<Integer, List<CellRangeAddress>> rangeMap = ExcelThreadContext.getMap(RANGE_CONTEXT_KEY);

        int preRowIndex = curRowIndex - 1;
        //1 获取该列最后一个合并区域
        List<CellRangeAddress> addressList = rangeMap.get(curColIndex);
        CellRangeAddress address = this.getLastRangeAddress(addressList);

        List<CellRangeAddress> firstColCell = rangeMap.get(0);
        CellRangeAddress firstColCellAddress = this.getLastRangeAddress(firstColCell);
        List<CellRangeAddress> fiveColCell = rangeMap.get(4);
        CellRangeAddress fiveColCellAddress = this.getLastRangeAddress(fiveColCell);

        // 首先判断当前列是否在需要合并的列中
        boolean isInMergeColumns = false;
        for (int mergeColumnIndex : mergeColumnIndexes) {
            if (curColIndex == mergeColumnIndex) {
                isInMergeColumns = true;
                break;
            }
        }
        if (!isInMergeColumns) {
            return;
        }
        if (curColIndex <= 3 || curColIndex == 29 || curColIndex == 30 || curColIndex == 31) {
            // 处理单元格合并逻辑
            handleCellMerge(0, address, cell, curColIndex, preRowIndex, curRowIndex, firstColCellAddress,
                    writeSheetHolder, cellMap, rangeMap, addressList);
        } else {
            handleCellMerge(4, address, cell, curColIndex, preRowIndex, curRowIndex, fiveColCellAddress,
                    writeSheetHolder, cellMap, rangeMap, addressList);
        }
        ExcelThreadContext.setData(CONTEXT_KEY, cellMap);
        ExcelThreadContext.setData(RANGE_CONTEXT_KEY, rangeMap);
    }

    private List<CellRangeAddress> addRangeAddress(final List<CellRangeAddress> list, final CellRangeAddress address) {
        List<CellRangeAddress> addressList = list;
        if (CollUtil.isEmpty(addressList)) {
            addressList = new ArrayList<>();
        }
        addressList.add(address);
        return addressList;
    }

    private CellRangeAddress getLastRangeAddress(List<CellRangeAddress> list) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.get(list.size() - 1);
    }

    private Object getCellData(Cell cell) {
        return cell.getCellType() == CellType.STRING ? cell.getStringCellValue() : cell.getNumericCellValue();
    }

    private void handleCellMerge(int checkNum, CellRangeAddress address, Cell cell, int curColIndex,
                                 int preRowIndex, int curRowIndex, CellRangeAddress colCellAddress,
                                 WriteSheetHolder writeSheetHolder, Map<Integer, Cell> cellMap,
                                 Map<Integer, List<CellRangeAddress>> rangeMap, List<CellRangeAddress> addressList) {

        // 情况1：上一行在合并区域内
        if (address != null && address.isInRange(preRowIndex, curColIndex)) {
            Cell firstCell = cellMap.get(curColIndex);
            if (getCellData(cell).equals(getCellData(firstCell))) {
                mergeCellIfNeeded(checkNum, curColIndex, curRowIndex, cell, address, colCellAddress);
            }
            return;
        }

        // 情况2：上一行不在合并区域内
        Row preRow = writeSheetHolder.getSheet().getRow(preRowIndex);
        Cell preCell = preRow.getCell(cell.getColumnIndex());

        if (!getCellData(cell).equals(getCellData(preCell))) {
            return;
        }

        // 处理新的合并区域
        if (shouldCreateNewMergeRegion(checkNum, curColIndex, curRowIndex, colCellAddress)) {
            CellRangeAddress newAddress = new CellRangeAddress(preRowIndex, curRowIndex, curColIndex, curColIndex);
            rangeMap.put(curColIndex, addRangeAddress(addressList, newAddress));
            cellMap.put(curColIndex, preCell);
            cell.setBlank();
        }
    }

    private boolean shouldCreateNewMergeRegion(int checkNum, int curColIndex, int curRowIndex,
                                               CellRangeAddress colCellAddress) {
        return curColIndex == checkNum ||
                (colCellAddress != null && colCellAddress.isInRange(curRowIndex, checkNum));
    }

    private void mergeCellIfNeeded(int checkNum, int curColIndex, int curRowIndex, Cell cell,
                                   CellRangeAddress address, CellRangeAddress colCellAddress) {

        if (curColIndex == checkNum ||
                (colCellAddress != null && colCellAddress.isInRange(curRowIndex, checkNum))) {
            address.setLastRow(curRowIndex);
            cell.setBlank();
        }
    }
}
