package com.purchase.purchase_server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum DeliveryImportSheetNameEnum {
    STOCKING_RULES("备货规则", "stockingRules"),
    TARGET_SALES("实际日销","targetSales"),
    AM_STOCK_QUANTITY("家具库存概况","amStockQuantity"),
    LAMP_STOCK_QUANTITY("灯具库存概况","lampStockQuantity"),
    STOCK_QUANTITY("在途","stockQuantity"),
    DELIVERY("计划","delivery");

    private final String code;
    private final String desc;
    public static DeliveryImportSheetNameEnum ofCode(String code) {
        return Arrays.stream(DeliveryImportSheetNameEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
    public static List<String> getCodeList() {
        return Arrays.stream(DeliveryImportSheetNameEnum.values())
                .map(DeliveryImportSheetNameEnum::getCode).collect(Collectors.toList());
    }
}
