package com.auth.authorization_server.model.runnable;

import com.auth.authorization_server.utils.commonUtils.AuthorizeUrlInfoUtils;
import com.crafts_mirror.common.security.runnable.AbstractVisitLogRunnable;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.common.entity.LoginVo;
import com.crafts_mirror.utils.context.SecurityContextHolder;
import com.crafts_mirror.utils.dp.VisitLogForm;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 访问记录线程
 * <AUTHOR>
 * @Date 2024/8/10 16:53
 **/
@Slf4j
public class VisitLogRunnable extends AbstractVisitLogRunnable {

    private final VisitLogForm visitLogForm;

    private final LoginVo loginVo;

    public VisitLogRunnable(VisitLogForm visitLogForm, Object handler) {
        super(handler);
        this.loginVo = SecurityUtils.getLoginUser();
        this.visitLogForm = visitLogForm;
    }

    @Override
    public void run() {
        try {
//            refreshSecurityContextHolder(loginVo);
            // 根据url获取用户操作的日志类型以及服务类型
            AuthorizeUrlInfoUtils purchaseUrlInfo = new AuthorizeUrlInfoUtils(visitLogForm.getRequestUrl());
            visitLogForm.setVisitType(purchaseUrlInfo.getLogType().getDesc());
            visitLogForm.setServiceType(purchaseUrlInfo.getServiceLogType().getDesc());

            // 处理日志标题
            visitLogForm.setVisitInfoTitle(getVisitLogTitle(visitLogForm.getRequestUrl()));

            sendVisitLogRequest(visitLogForm);
        } finally {
            SecurityContextHolder.remove();
        }
    }

    @Override
    public String getVisitLogTitle(String requestUrl) {
        if (requestUrl.contains("login")) {
            return "系统管理/用户管理/登录";
        }

        if (requestUrl.contains("logout")) {
            return "系统管理/用户管理/登出";
        }
        return super.getVisitLogTitle(requestUrl);
    }
}
