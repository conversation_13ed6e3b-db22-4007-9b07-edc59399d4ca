package com.purchase.purchase_server.entity.dataObject.Lcl;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.util.Date;

/**
 * 拼柜装柜计划数据表
 */
@TableName(value ="cm_lcl_finished_inventory")
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclFinishedInventoryDO extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 6921300407398802075L;

    /**
     * 货件号
     */
    private String shipmentCode;

    /**
     * 合同号
     */
    private String contractCode;

    /**
     * 工厂交货时间
     */
    private Date factoryFinishedDate;

    /**
     * 未安排数量
     */
    private Integer factoryRemainNum;

    /**
     * 工厂交货数量
     */
    private Integer factoryShippingPackageNum;

    /**
     * 拼柜计划id 
     */
    private String lclRecordId;

    /**
     * 虚拟skuId
     */
    private String virtualSkuId;

    /**
     * sku类型(0-虚拟sku 1-老sku)
     */
    private String isOldStatus;

    /**
     * 发货类型
     */
    private Integer deliveryType;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 快照记录id
     */
    private String productSnapshotId;

    /**
     * 是否在途（0-不是，1-是）
     */
    private String isForeignFlag;

    /**
     * 在途拆分id
     */
    private String lclForeignId;

    /**
     * 发货比例
     */
    private String shippingRatio;
}