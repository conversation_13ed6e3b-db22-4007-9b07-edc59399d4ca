package com.inventory_server.model.currency.repository.impl;

import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.inventory_server.applications.vo.response.ResultDTO;
import com.inventory_server.model.currency.entity.dto.ExchangeRateDto;
import com.inventory_server.model.currency.repository.ICurrencyInteriorRepository;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.RestTemplate;

import static com.crafts_mirror.utils.constant.SystemConstant.NEWEST_EXCHANGE_RATE_URL;

/**
 * @Description 汇率外部持久层
 * <AUTHOR>
 * @Date 2025/4/17 20:27
 **/
@Repository
public class CurrencyInteriorRepositoryImpl implements ICurrencyInteriorRepository {
    @Resource
    protected RestTemplate restTemplate;

    @Override
    public ExchangeRateDto getNewestExchangeFromUSDToCNY() {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO resultDTO = restTemplateUtil.get(NEWEST_EXCHANGE_RATE_URL, ResultDTO.class);
        return JSON.to(ExchangeRateDto.class, resultDTO.getData());
    }
}
