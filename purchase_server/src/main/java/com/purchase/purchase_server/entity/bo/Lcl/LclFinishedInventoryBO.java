package com.purchase.purchase_server.entity.bo.Lcl;

import com.purchase.purchase_server.entity.dto.Lcl.LclTrialShippingInventoryDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 拼柜装柜计划数据
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclFinishedInventoryBO implements Serializable {
    @Serial
    private static final long serialVersionUID = 2941354113886373986L;
    private String id;
    /**
     * 货件号
     */
    private String shipmentCode;

    /**
     * 合同号
     */
    private String contractCode;

    /**
     * 工厂交货时间
     */
    private String factoryFinishedDate;

    /**
     * 剩余数量
     */
    private Integer factoryRemainNum;

    /**
     * 工厂交货数量
     */
    private Integer factoryShippingPackageNum;

    /**
     * 拼柜计划id 
     */
    private String lclRecordId;

    /**
     * 虚拟skuId
     */
    private String virtualSkuId;

    /**
     * sku类型(0-虚拟sku 1-老sku)
     */
    private String isOldStatus;

    /**
     * 快照记录id
     */
    private String productSnapshotId;

    /**
     * 虚拟sku
     */
    private String destinationSku;

    /**
     * 自定义skuId
     */
    private String selfSkuId;

    /**
     * 外箱规格长
     */
    private Double caseLength;

    /**
     * 外箱规格宽
     */
    private Double caseWidth;

    /**
     * 外箱规格高
     */
    private Double caseHeight;

    /**
     * 单箱个数
     */
    private Integer containerLoad;

    /**
     * 发货比例
     */
    private String shippingRatio;

    /**
     * 发货类型
     */
    private Integer deliveryType;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 是否在途
     */
    private String isForeignFlag;

    /**
     * 头程时间
     */
    private String headShippingDays;

    /**
     * 货件大类
     */
    private String categoryId;

    private List<LclTrialShippingInventoryDTO> lclTrialDTOList;
}