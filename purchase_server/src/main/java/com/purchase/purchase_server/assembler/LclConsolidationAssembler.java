package com.purchase.purchase_server.assembler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.purchase.purchase_server.entity.bo.FactoryFinishedInventoryBO;
import com.purchase.purchase_server.entity.bo.Lcl.LclContainerLoadingBO;
import com.purchase.purchase_server.entity.bo.Lcl.LclFinishedInventoryBO;
import com.purchase.purchase_server.entity.dataObject.Lcl.*;
import com.purchase.purchase_server.entity.dataObject.TrialShippingInventoryDO;
import com.purchase.purchase_server.entity.dto.Lcl.LclConsolidationFinishedInventoryDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclConsolidationNonFinishedInventoryDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclContainerDetailExportDTO;
import com.purchase.purchase_server.entity.excelObject.Lcl.LclConsolidationNonFinishedExcel;
import com.purchase.purchase_server.entity.vo.Lcl.LclConsolidationRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Date;
import java.util.List;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN;


/**
 * 数据转换工具类
 *
 * <AUTHOR>
 * @date 2024/5/11
 */

@Mapper(componentModel = "spring", uses = LclConsolidationConvert.class)
public interface LclConsolidationAssembler {
    LclFinishedInventoryDO factoryFinishedBoToLclDO(FactoryFinishedInventoryBO val);

    @Mapping(source = "realShippingStartDate", target = "shippingStartDate")
    LclTrialShippingInventoryDO trialShippingDoToLclDO(TrialShippingInventoryDO val);

    List<LclTrialShippingInventoryDO> trialShippingDoListToLclDO(List<TrialShippingInventoryDO> val);

    @Mapping(source = "id", target = "lclFinishedInventoryId")
    LclConsolidationFinishedInventoryDTO factoryFinishedBoToLclDTO(LclFinishedInventoryBO val);

    List<LclConsolidationFinishedInventoryDTO> factoryFinishedBoListToLclDTO(List<LclFinishedInventoryBO> val);

    @Mapping(source = "createDate", target = "createDate", qualifiedByName = "createDate")
    LclConsolidationRecordVO lclRecordDOToVO(LclConsolidationRecordDO val);

    List<LclConsolidationRecordVO> lclRecordDOlToVOList(List<LclConsolidationRecordDO> val);

    LclContainerLoadingBO lclContainerLoading(LclContainerLoadingBO val);

    List<LclContainerLoadingBO> lclContainerLoadingList(List<LclContainerLoadingBO> val);

    @Mapping(source = "id", target = "consolidationFactoryFinishedId")
    LclContainerDetailDO lclContainerBOToDO(LclContainerLoadingBO val);

    List<LclContainerDetailDO> lclContainerListBOToDO(List<LclContainerLoadingBO> val);

    LclFinishedInventoryDO lclFinishedInventoryDO(LclFinishedInventoryDO val);

    LclConsolidationFinishedInventoryDO lclConFinishedInventoryDO(LclConsolidationFinishedInventoryDO val);

    LclConsolidationNonFinishedExcel lclNonContainerDTOToExcel(LclConsolidationNonFinishedInventoryDTO val);

    List<LclConsolidationNonFinishedExcel> lclNonContainerListDTOToExcel(List<LclConsolidationNonFinishedInventoryDTO> val);


    LclContainerDetailExportDTO lclNonContainerDetailExportToExcel(LclContainerDetailExportDTO val);

    LclConsolidationNonFinishedExcel lclNonContainerDetailExportDtoToExcel(LclContainerDetailExportDTO val);

    List<LclConsolidationNonFinishedExcel> lclNonContainerDetailExportDtoToExcel(List<LclContainerDetailExportDTO> val);

    @Named("createDate")
    default String createDate (Date value){
        if (ObjectUtil.isEmpty(value)) {
            return null;
        }
        return DateUtil.format(value, YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN);
    }
}














