package com.purchase.purchase_server.service.shipping.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.*;
import com.purchase.purchase_server.entity.dto.delivery.MockTableCalDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectBaseParamDto;
import com.purchase.purchase_server.entity.dto.delivery.TrialCalReplenishmentDto;
import com.purchase.purchase_server.service.AbstractShippingCalculationService;
import com.purchase.purchase_server.utils.commonUtils.WarehouseSortUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateField.DAY_OF_YEAR;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;

/**
 * @Description 发货试算，每次发差额量的货
 * <AUTHOR>
 * @Date 2023/12/28 17:09
 **/
@Slf4j
@Service(value = "differenceDeliveryCalculationService")
public class DifferentDeliveryCalculationServiceImpl extends AbstractShippingCalculationService {

    @Override
    @Deprecated
    public TrialCalReplenishmentDto trialShippingCalculation(
            DateTime shippingStartDate, int redundantDate, List<FactoryRemainInventoryDto> factoryRemainInventoryList,
            List<FactoryRemainInventoryDto> importFactoryRemainInventoryList, VirtualProductDO virtualSku, int maxDeliveryDayGap,
            Map<String, Double> saleNumPerDayMap, DateTime projectCreateDate, List<FactoryFinishedInventoryDto> factoryFinishedInventoryList,
            ShippingProjectBaseParamDto shippingRules, List<String> sortedWarehouseList, String productName, List<SenboWarehouseDto> warehouseLis) {
        Map<String, Double> inventorySaleDefRatio = shippingRules.getShippingRatio();
        Map<String, Integer> headShippingDateMap = shippingRules.getHeadShippingDays();
        int transitDays = shippingRules.getTransitDays();
        int safeDate = shippingRules.getSafeDays();
        int shippingFrequency = shippingRules.getShippingFrequency();

        int minShippingDate = headShippingDateMap.entrySet().stream()
                .filter(h -> inventorySaleDefRatio.containsKey(h.getKey()) && inventorySaleDefRatio.get(h.getKey()) > 0)
                .map(Map.Entry::getValue)
                .sorted().findFirst().orElseThrow(() -> new NullPointerException("头程时间为空"));
        Map<String, Map<String, Double>> everyDayWarehouseSaleMap = new TreeMap<>();
        List<FactoryRemainInventoryDto> remainInventoryListForCal = new ArrayList<>(importFactoryRemainInventoryList);

        String projectCreateDateString = projectCreateDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
        int mockTableRange = (int) (DateUtil.betweenDay(shippingStartDate, projectCreateDate, false) + maxDeliveryDayGap);

        // 计算最早一批货到仓库时，所有仓库的剩余库存
        List<FactoryRemainInventoryDto> firstInvenotryList = new ArrayList<>(factoryRemainInventoryList);
        firstInvenotryList.addAll(importFactoryRemainInventoryList);

        var mockShippingMap = getMockShippingMap(factoryRemainInventoryList, headShippingDateMap, projectCreateDate,
                mockTableRange, firstInvenotryList);

        var everyDayRemainInventoryMap = calRemainInventoryMap(projectCreateDate, firstInvenotryList,
                mockTableRange, minShippingDate, saleNumPerDayMap, inventorySaleDefRatio, everyDayWarehouseSaleMap,
                sortedWarehouseList);

        // 计算导入数据中每一天的在途量
        Map<Date, Double> importRemainInventoryMap = importFactoryRemainInventoryList.stream()
                .collect(Collectors.toMap(FactoryRemainInventoryDto::getEnableUsingDate, FactoryRemainInventoryDto::getStoreNum, Double::sum));

        // 计算一个周期的发货数据
        Map<DateTime, Double> shippingMap = calReplenishmentMap(factoryFinishedInventoryList, shippingStartDate,
                minShippingDate, minShippingDate + maxDeliveryDayGap, everyDayRemainInventoryMap,
                saleNumPerDayMap, safeDate, shippingFrequency, inventorySaleDefRatio, transitDays, importRemainInventoryMap);

        if (shippingMap.isEmpty()) {
            // 不需要发货的虚拟sku商品
            return TrialCalReplenishmentDto.builder()
                    .everydayOnShippingInventoryMap(getMockShippingMap(factoryRemainInventoryList, headShippingDateMap, projectCreateDate, mockTableRange, importFactoryRemainInventoryList))
                    .everydayRemainInventoryMap(everyDayRemainInventoryMap)
                    .everydaySaleProductMap(everyDayWarehouseSaleMap).build();
        }

        // 计算各仓发货数量
        List<FactoryRemainInventoryDto> inventoryDtos = calArrivedInventory(everyDayRemainInventoryMap, remainInventoryListForCal,
                inventorySaleDefRatio, saleNumPerDayMap, shippingMap, headShippingDateMap, projectCreateDateString, mockTableRange);
        MockTableCalDto mockTableCalDto = new MockTableCalDto(inventoryDtos, mockTableRange, factoryRemainInventoryList,
                headShippingDateMap, projectCreateDate, remainInventoryListForCal, importFactoryRemainInventoryList, new ArrayList<>(),
                minShippingDate, saleNumPerDayMap, inventorySaleDefRatio, sortedWarehouseList);
        return commonConsumptionService.calDeliveryResults(mockTableCalDto);
    }

    private Map<DateTime, Double> calReplenishmentMap(List<FactoryFinishedInventoryDto> factoryFinishedInventoryList,
                                                      DateTime shippingStartDate, int minShippingDate,
                                                      int calCircle, Map<String, Map<String, Double>> everyDayRemainInventoryMap,
                                                      Map<String, Double> saleNumPerDayMap, int safeDate, int shippingFrequency,
                                                      Map<String, Double> inventorySaleDefRatio, int transitDays,
                                                      Map<Date, Double> importRemainInventoryMap) {
        Map<DateTime, Double> replenishmentMap = new TreeMap<>(Comparator.comparing(a -> a));

        double totalTargetSaleNum = 0;
        for (DateTime calDate = shippingStartDate.offsetNew(DAY_OF_YEAR, minShippingDate);
             calDate.compareTo(shippingStartDate.offsetNew(DAY_OF_YEAR, calCircle)) <= 0;
             calDate.offset(DAY_OF_YEAR, 1)
        ) {
            // 计算是否需要发货
            String calDateString = calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
            double importRemainInventory = importRemainInventoryMap.entrySet().stream()
                    .filter(entry -> new DateTime(entry.getKey()).compareTo(calDate) > 0)
                    .mapToDouble(Map.Entry::getValue)
                    .sum();
            Map<String, Double> remainInventoryMap = everyDayRemainInventoryMap.getOrDefault(calDateString, new HashMap<>());
            // 计算当天之前有多少货能到
            double arrivingNum = replenishmentMap.entrySet().stream()
                    .filter(entry -> DateUtil.compare(entry.getKey(), calDate) <= 0)
                    .mapToDouble(Map.Entry::getValue)
                    .sum();

            // 简要记录每天剩余多少货，以此来判断是否需要发货
            double targetSaleNum = saleNumPerDayMap.getOrDefault(DateUtil.beginOfMonth(calDate).toString(YYYY_MM_DD_DATE_FORMAT_SLASH), 0D);
            double remainInventory = remainInventoryMap.entrySet().stream()
                    .filter(f -> inventorySaleDefRatio.get(f.getKey()) >= 0)
                    .mapToDouble(Map.Entry::getValue)
                    .sum();

            double afterSaleRemainInventory;
            if (remainInventory == 0) {
                totalTargetSaleNum += targetSaleNum;
                afterSaleRemainInventory = arrivingNum - totalTargetSaleNum + importRemainInventory;
            } else {
                afterSaleRemainInventory = remainInventory + arrivingNum + importRemainInventory - targetSaleNum - totalTargetSaleNum;
            }

            double safeInventory = calSafeInventory(safeDate, saleNumPerDayMap, calDate);
            // afterSaleRemainInventory是期末库存，需要加上当天的目标日销，使用期初库存区计算是否需要发货
            if (safeInventory >= afterSaleRemainInventory + targetSaleNum) {
                // 如果需要发货，则计入一个map中
                double maxShippingNum = getMaxShippingNum(shippingFrequency, saleNumPerDayMap, calDate, safeInventory - afterSaleRemainInventory);

                DateTime startShippingDate = calDate.offsetNew(DAY_OF_YEAR, -minShippingDate);
                double totalShipNum = factoryFinishedInventoryList.stream()
                        .filter(f -> DateUtil.offset(f.getFactoryFinishedDate(), DAY_OF_YEAR, transitDays).compareTo(startShippingDate) <= 0)
                        .map(FactoryFinishedInventoryDto::getShippingNum)
                        .mapToDouble(num -> num).sum();
                if (Double.isNaN(totalShipNum)) {
                    continue;
                }

                // 工厂交货量减去发货量
                double replenishmentNum = Math.min(totalShipNum, maxShippingNum);
                if (!Double.isNaN(replenishmentNum) && replenishmentNum != 0) {
                    replenishmentMap.put(startShippingDate, BigDecimal.valueOf(replenishmentNum).setScale(3, RoundingMode.HALF_UP).doubleValue());
                }

                for (var dto : factoryFinishedInventoryList) {
                    if (DateUtil.offset(dto.getFactoryFinishedDate(), DAY_OF_YEAR, transitDays).compareTo(startShippingDate) > 0) {
                        continue;
                    }
                    double shippingNum = dto.getShippingNum();
                    if (replenishmentNum > shippingNum) {
                        dto.setShippingNum(0D);
                        replenishmentNum -= shippingNum;
                    } else {
                        dto.setShippingNum(shippingNum - replenishmentNum);
                        break;
                    }
                }
            }
        }
        return replenishmentMap;
    }

    private double calSafeInventory(Integer safeDate, Map<String, Double> saleNumPerDayMap, DateTime startCalDate) {
        DateTime maxSafeDate = DateUtil.endOfMonth(startCalDate.offsetNew(DAY_OF_YEAR, safeDate));
        return saleNumPerDayMap.entrySet().stream().filter(map -> {
                    DateTime parse = DateUtil.parse(map.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH);
                    return parse.compareTo(maxSafeDate) <= 0 && parse.compareTo(DateUtil.beginOfMonth(startCalDate)) >= 0;
                })
                .mapToDouble(Map.Entry::getValue).max().orElse(0D) * safeDate;
    }

    private List<FactoryRemainInventoryDto> calArrivedInventory(Map<String, Map<String, Double>> everyDayRetainMap,
                                                                List<FactoryRemainInventoryDto> factoryRemainInventoryList,
                                                                Map<String, Double> inventorySaleDefRatio,
                                                                Map<String, Double> saleNumPerDayMap,
                                                                Map<DateTime, Double> shippingMap,
                                                                Map<String, Integer> headShippingDateMap,
                                                                String startDateString, int maxCalDate) {
        DateTime startDate = DateUtil.parse(startDateString, YYYY_MM_DD_DATE_FORMAT_SLASH);
        Set<String> warehouseSet = everyDayRetainMap.get(startDateString).keySet();
        var warehouseRemainList = new ArrayList<>(factoryRemainInventoryList);
        var shippingList = new ArrayList<FactoryRemainInventoryDto>();
        int maxHeadDay = 0;
        int minHeadDay = Integer.MAX_VALUE;
        for (var entry : headShippingDateMap.entrySet()) {
            if (maxHeadDay < entry.getValue() && inventorySaleDefRatio.get(entry.getKey()) > 0) {
                maxHeadDay = entry.getValue();
            }
            if (minHeadDay > entry.getValue() && inventorySaleDefRatio.get(entry.getKey()) > 0) {
                minHeadDay = entry.getValue();
            }
        }
        DateTime endDate = startDate.offsetNew(DAY_OF_YEAR, maxCalDate);

        for (var shippingEntry : shippingMap.entrySet()) {
            // 发货时间
            DateTime shippingStartDate = shippingEntry.getKey();
            double totalShippingInventory = shippingEntry.getValue();
            Map<String, Double> warehouseRemainInventoryMap = new HashMap<>();
            // 最早到货时间
            DateTime earliestArrivingDate = shippingStartDate.offsetNew(DAY_OF_YEAR, minHeadDay);
            for (DateTime calDate = new DateTime(earliestArrivingDate); calDate.compareTo(endDate) <= 0; calDate.offset(DAY_OF_YEAR, 1)) {
                String calDateString = calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
                DateTime nextDate = calDate.offsetNew(DAY_OF_YEAR, 1);
                String nextDateString = nextDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);

                // 2. 所有仓库按照自己的目标日销消耗剩余库存，有缺货情况的记录缺货数量
                double totalLackInventory = 0;
                Map<String, Double> warehouseLackNumMap = new HashMap<>();
                Map<String, Double> retainMap = everyDayRetainMap.get(calDateString);
                Map<String, Double> afterSaleRetainMap = new HashMap<>();
                // 该表记录当天每个仓库的实际消耗
                Map<String, Double> warehouseDaySaleMap = new HashMap<>();
                double destinationSales = saleNumPerDayMap.getOrDefault(DateUtil.beginOfMonth(calDate).toString(YYYY_MM_DD_DATE_FORMAT_SLASH), 0D);

                BigDecimal everydayTotalSaleNumDec = consumeZeroRatioWarehouseInventory(inventorySaleDefRatio, retainMap,
                        warehouseDaySaleMap, afterSaleRetainMap, destinationSales);
                // 如果当天的目标日销消耗完了，则直接进入下一天
                if (everydayTotalSaleNumDec.compareTo(BigDecimal.ZERO) <= 0) {
                    modifyNextDayInventory(everyDayRetainMap, warehouseRemainList, warehouseDaySaleMap, calDateString, nextDateString);
                    continue;
                }

                destinationSales = everydayTotalSaleNumDec.setScale(3, RoundingMode.HALF_UP).doubleValue();
                for (Map.Entry<String, Double> retainEntry : retainMap.entrySet()) {
                    String warehouse = retainEntry.getKey();
                    double ratio = inventorySaleDefRatio.get(warehouse);
                    if (ratio <= 0) {
                        continue;
                    }
                    double warehouseDestinationSales = destinationSales * ratio;
                    double saleDifference = retainEntry.getValue() - warehouseDestinationSales;
                    if (saleDifference >= 0) {
                        warehouseDaySaleMap.put(warehouse, warehouseDestinationSales);
                        warehouseLackNumMap.put(warehouse, 0D);
                        afterSaleRetainMap.put(warehouse, retainEntry.getValue() - warehouseDestinationSales);
                    } else {
                        warehouseDaySaleMap.put(warehouse, retainEntry.getValue());
                        totalLackInventory -= saleDifference;
                        warehouseLackNumMap.put(warehouse, -saleDifference);
                        afterSaleRetainMap.put(warehouse, 0D);
                    }
                }
                // 3. 如果缺货数量等于0，则当天不需要分配，进入下一天的发货计算
                if (totalLackInventory == 0) {
                    modifyNextDayInventory(everyDayRetainMap, warehouseRemainList, warehouseDaySaleMap, calDateString, nextDateString);
                    continue;
                }

                // 缺货且能到货的仓库
                Set<String> arrivingAndLackInventoryWarehouseSet = warehouseSet.stream()
                        .filter(warehouse -> {
                            int headShippingDate = headShippingDateMap.get(warehouse);
                            boolean arrive = calDate.compareTo(shippingStartDate.offsetNew(DAY_OF_YEAR, headShippingDate)) >= 0;
                            boolean lackInventoryWarehouse = warehouseLackNumMap.getOrDefault(warehouse, 0D) > 0;
                            return arrive && lackInventoryWarehouse;
                        })
                        .collect(Collectors.toSet());

                // 如果该批次发货数量不够缺货数量的话，则发货数量按照比例分配给各能到货的缺货仓库中去
                if (totalLackInventory > totalShippingInventory && CollectionUtil.isNotEmpty(arrivingAndLackInventoryWarehouseSet)) {
                    // 计算这些仓库的实际日销比例
                    double totalRatio = getRealTotalRatio(inventorySaleDefRatio, arrivingAndLackInventoryWarehouseSet);
                    // 发货数量按加权比例分配给各能到货的缺货仓
                    for (String warehouse : arrivingAndLackInventoryWarehouseSet) {
                        double ratio = inventorySaleDefRatio.get(warehouse);
                        double totalDispatchNum = (ratio / totalRatio) * totalShippingInventory;
                        String enableUsingDateString = getEnableUsingDateString(warehouse, shippingStartDate, headShippingDateMap);
                        warehouseRemainInventoryMap.compute(warehouse + "-" + enableUsingDateString, (k, v) -> Optional.ofNullable(v).orElse(0D) + totalDispatchNum);
                    }
                    shippingEntry.setValue(0D);
                    break;
                }

                // 各能到货的缺货仓补充自己的缺货数量
                for (String warehouse : arrivingAndLackInventoryWarehouseSet) {
                    double lackInventory = warehouseLackNumMap.get(warehouse);
                    // 修改发货数量、总缺货数量、该仓库的缺货数量以及该仓库当天的销售情况
                    totalShippingInventory -= lackInventory;
                    totalLackInventory -= lackInventory;
                    warehouseLackNumMap.replace(warehouse, 0D);
                    warehouseDaySaleMap.compute(warehouse, (k, v) -> Optional.ofNullable(v).orElse(0D) + lackInventory);

                    String enableUsingDateString = getEnableUsingDateString(warehouse, shippingStartDate, headShippingDateMap);
                    warehouseRemainInventoryMap.compute(warehouse + "-" + enableUsingDateString, (k, v) -> Optional.ofNullable(v).orElse(0D) + lackInventory);
                }
                shippingEntry.setValue(roundUpToThreeDecimal(totalShippingInventory));

                if (totalLackInventory == 0) {
                    modifyNextDayInventory(everyDayRetainMap, warehouseRemainList, warehouseDaySaleMap, calDateString, nextDateString);
                    continue;
                }

                // 获取有货仓以及可到货仓
                Set<String> arrivingOrFullInventoryWarehouseSet = getArrivingOrFullInventoryWarehouseSet(warehouseLackNumMap, inventorySaleDefRatio);
                if (CollectionUtil.isEmpty(arrivingOrFullInventoryWarehouseSet)) {
                    modifyNextDayInventory(everyDayRetainMap, warehouseRemainList, warehouseDaySaleMap, calDateString, nextDateString);
                    continue;
                }
                // 此时需要循环分配缺货数量，直到缺货数量被消耗殆尽
                while (totalLackInventory > 0) {
                    arrivingOrFullInventoryWarehouseSet = afterSaleRetainMap.entrySet().stream()
                            .filter(f -> {
                                Integer headShippingDate = headShippingDateMap.get(f.getKey());
                                return inventorySaleDefRatio.get(f.getKey()) > 0 && (f.getValue() > 0 || calDate.compareTo(shippingStartDate.offsetNew(DAY_OF_YEAR, headShippingDate)) >= 0);
                            })
                            .map(Map.Entry::getKey).collect(Collectors.toSet());
                    double totalRatio = getRealTotalRatio(inventorySaleDefRatio, arrivingOrFullInventoryWarehouseSet);
                    double totalSales = 0.0;
                    for (String warehouse : arrivingOrFullInventoryWarehouseSet) {
                        double afterSaleRetainNum = afterSaleRetainMap.get(warehouse);
                        double ratio = inventorySaleDefRatio.get(warehouse);
                        double needSales = roundUpToThreeDecimal((ratio / totalRatio) * totalLackInventory);
                        double realNeedSales = Math.max(needSales, 0.001);
                        if (afterSaleRetainNum >= realNeedSales) {
                            afterSaleRetainMap.compute(warehouse, (k, v) -> Optional.ofNullable(v).orElse(realNeedSales) - realNeedSales);
                            warehouseDaySaleMap.compute(warehouse, (k, v) -> Optional.ofNullable(v).orElse(0D) + realNeedSales);
                            totalSales += realNeedSales;
                        } else if (calDate.compareTo(shippingStartDate.offsetNew(DAY_OF_YEAR, headShippingDateMap.get(warehouse))) >= 0) {
                            afterSaleRetainMap.put(warehouse, 0D);
                            warehouseDaySaleMap.compute(warehouse, (k, v) -> Optional.ofNullable(v).orElse(0D) + realNeedSales);
                            totalSales += realNeedSales;
                            totalShippingInventory -= realNeedSales - afterSaleRetainNum;

                            String enableUsingDateString = getEnableUsingDateString(warehouse, shippingStartDate, headShippingDateMap);
                            warehouseRemainInventoryMap.compute(warehouse + "-" + enableUsingDateString, (k, v) -> Optional.ofNullable(v).orElse(0D) + realNeedSales - afterSaleRetainNum);
                        } else {
                            afterSaleRetainMap.put(warehouse, 0D);
                            warehouseDaySaleMap.compute(warehouse, (k, v) -> Optional.ofNullable(v).orElse(0D) + afterSaleRetainNum);
                            totalSales += afterSaleRetainNum;
                        }
                    }
                    totalLackInventory = roundUpToThreeDecimal(totalLackInventory - totalSales);
                }
                shippingEntry.setValue(roundUpToThreeDecimal(totalShippingInventory));
                modifyNextDayInventory(everyDayRetainMap, warehouseRemainList, warehouseDaySaleMap, calDateString, nextDateString);
            }

            List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
            Map<String, Integer> sortedMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSort));

            List<String> sortedWarehouseList = WarehouseSortUtil.getSortedWarehouseList(inventorySaleDefRatio, sortedMap);

            var factoryRemainInventoryDtoList = getFactoryRemainInventoryDto(warehouseRemainInventoryMap, shippingEntry.getValue(), headShippingDateMap);
            shippingList.addAll(factoryRemainInventoryDtoList);
            warehouseRemainList.addAll(factoryRemainInventoryDtoList);
            Map<String, Map<String, Double>> everyDayWarehouseSaleMap = new TreeMap<>();

            everyDayRetainMap = calRemainInventoryMap(startDate, warehouseRemainList, maxCalDate, maxHeadDay, saleNumPerDayMap,
                    inventorySaleDefRatio, everyDayWarehouseSaleMap, sortedWarehouseList);
        }
        return shippingList;
    }

    private List<FactoryRemainInventoryDto> getFactoryRemainInventoryDto(Map<String, Double> remainInventoryMap,
                                                                         double totalShippingNum, Map<String, Integer> headShippingDateMap) {
        Map<String, Double> roundedMap = new HashMap<>(remainInventoryMap.size());
        List<String> roundedWarehouse = new ArrayList<>();
        double remainingDifference = 0;
        for (var entry : remainInventoryMap.entrySet()) {
            double value = BigDecimal.valueOf(entry.getValue()).setScale(3, RoundingMode.HALF_UP).doubleValue();
            double rounded = Math.ceil(value);
            double difference = rounded - value;
            remainingDifference += difference;
            roundedMap.put(entry.getKey(), rounded);
            roundedWarehouse.add(entry.getKey());
        }
        BigDecimal totalShippingNumDec = BigDecimal.valueOf(totalShippingNum).setScale(3, RoundingMode.HALF_UP);
        BigDecimal remainingDifferenceDec = BigDecimal.valueOf(remainingDifference).setScale(3, RoundingMode.HALF_UP);
        if (totalShippingNumDec.compareTo(remainingDifferenceDec) < 0) {
            roundedWarehouse.sort(Comparator.comparingDouble(v -> roundedMap.get(v) - remainInventoryMap.get(v)));
            for (int i = 0, j = roundedWarehouse.size() - 1; i <= j; ) {
                String minWarehouse = roundedWarehouse.get(i);
                BigDecimal minDifference = BigDecimal.valueOf(roundedMap.get(minWarehouse) - remainInventoryMap.get(minWarehouse)).setScale(3, RoundingMode.HALF_UP);
                if (totalShippingNumDec.compareTo(minDifference) < 0) {
                    String maxWarehouse = roundedWarehouse.get(j);
                    double maxWarehouseInventory = remainInventoryMap.get(maxWarehouse);
                    BigDecimal maxDifference = BigDecimal.valueOf(maxWarehouseInventory - Math.floor(maxWarehouseInventory)).setScale(3, RoundingMode.HALF_UP);
                    totalShippingNumDec = totalShippingNumDec.add(maxDifference);
                    roundedMap.compute(maxWarehouse, (k, v) -> Optional.ofNullable(v).orElse(maxWarehouseInventory) - 1);
                    if (i == j) {
                        remainInventoryMap.compute(maxWarehouse, (k, v) -> Math.floor(Optional.ofNullable(v).orElse(0D)));
                    }
                    j--;
                }
                minDifference = BigDecimal.valueOf(roundedMap.get(minWarehouse) - remainInventoryMap.get(minWarehouse)).setScale(3, RoundingMode.HALF_UP);
                if (totalShippingNumDec.compareTo(minDifference) >= 0) {
                    totalShippingNumDec = totalShippingNumDec.subtract(minDifference);
                    i++;
                }
            }
            // 剩余没有分配的库存按照到货先后顺序分配
            List<String> sortedWarehouseList = headShippingDateMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).toList();
            for (String warehouse : sortedWarehouseList) {
                if (totalShippingNumDec.setScale(3, RoundingMode.HALF_UP).compareTo(BigDecimal.ONE) < 0) {
                    break;
                }
                for (var entry : roundedMap.entrySet()) {
                    if (!entry.getKey().contains(warehouse)) {
                        continue;
                    }
                    entry.setValue(entry.getValue() + 1);
                    totalShippingNumDec = totalShippingNumDec.subtract(BigDecimal.ONE);
                    break;
                }
            }
        }

        return roundedMap.entrySet().stream().map(entry -> {
                    String key = entry.getKey();
                    String warehouse = key.split("-")[0];
                    String startShippingUsingDate = key.split("-")[1];
                    double shippingNum = entry.getValue();
                    return FactoryRemainInventoryDto.builder()
                            .warehouse(warehouse)
                            .enableUsingDate(DateUtil.parse(startShippingUsingDate, YYYY_MM_DD_DATE_FORMAT_SLASH))
                            .storeNum(shippingNum)
                            .build();
                })
                .collect(Collectors.toList());
    }

    /**
     * 计算每次发货最多发多少货
     *
     * @return 每次发货最多发多少货
     */
    private double getMaxShippingNum(int shippingFrequency, Map<String, Double> saleNumPerDayMap, DateTime startCalDate, double lackInventory) {
        DateTime calDate = startCalDate;

        // 获取发货日当月还要售卖多少天
        DateTime endOfMonth = DateUtil.endOfMonth(calDate);
        long remainSaleDay = shippingFrequency;
        double saleNums = 0;
        long gapDate = DateUtil.betweenDay(calDate, endOfMonth, true) + 1;

        do {
            double startDateSaleNum = saleNumPerDayMap.getOrDefault(DateUtil.beginOfMonth(calDate).toString(YYYY_MM_DD_DATE_FORMAT_SLASH), 0D);
            // 当月目标日销 * 需要售卖天数得到当月一共要售出多少件
            if (remainSaleDay <= gapDate) {
                saleNums += startDateSaleNum * remainSaleDay;
                remainSaleDay = 0;
            } else {
                saleNums += gapDate * startDateSaleNum;
                // 获取直到到货为止还需要售卖多少天
                remainSaleDay -= gapDate;
                calDate = DateUtil.offsetMonth(DateUtil.beginOfMonth(calDate), 1);
                // 获取下一个月还需要售卖多少天
                gapDate = Math.min(calDate.between(calDate.offsetNew(DAY_OF_YEAR, calDate.getLastDayOfMonth()), DateUnit.DAY), remainSaleDay);
            }
        } while (remainSaleDay > 0);
        // 一次性发货至安全库存以上，避免多次发货
        saleNums *= Math.ceil(Math.max(lackInventory / saleNums, 1));
        return Double.isNaN(saleNums) ? 1 : Math.ceil(saleNums);
    }
}
