package com.inventory_server.model.file.entity.form;

import com.crafts_mirror.utils.dp.BasePageForm;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/2/18 14:54
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class FileMissionPageForm extends BasePageForm implements Serializable {
    @Serial
    private static final long serialVersionUID = -5439495840202434L;

    private String missionId;
    /**
     * 类型
     */
    private List<String> type;
}
