package com.purchase.purchase_server.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.purchase_server.entity.form.FileMissionPageForm;
import com.purchase.purchase_server.entity.vo.MissionCenterVo;
import org.springframework.util.MultiValueMap;

/**
 * <AUTHOR>
 * @date 2024/4/16
 **/
public interface FileCenterService {

    MultiValueMap<String, Object> putFile(byte[] byteArrayResource, String fileName, String key, String expireTime);

    IPage<MissionCenterVo> getMissionPage(FileMissionPageForm form);

    /**
     * 下载源文件
     */
    String selectFileInfo(String missionId, String filePath);
}
