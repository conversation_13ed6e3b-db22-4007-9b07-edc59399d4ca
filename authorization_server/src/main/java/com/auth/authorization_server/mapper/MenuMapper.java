package com.auth.authorization_server.mapper;

import com.auth.authorization_server.model.dataObject.MenuDO;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MenuMapper extends MPJBaseMapper<MenuDO> {

    List<MenuDO> selectMenuTreeListByUrlAndPermission(@Param("url") String url, @Param("permission") String permission);
}