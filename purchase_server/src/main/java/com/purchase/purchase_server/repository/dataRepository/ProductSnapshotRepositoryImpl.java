package com.purchase.purchase_server.repository.dataRepository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.purchase.purchase_server.entity.dataObject.ProductSnapshotDO;
import com.purchase.purchase_server.mapper.ProductSnapshotMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ProductSnapshotRepositoryImpl extends ServiceImpl<ProductSnapshotMapper , ProductSnapshotDO> {
}
