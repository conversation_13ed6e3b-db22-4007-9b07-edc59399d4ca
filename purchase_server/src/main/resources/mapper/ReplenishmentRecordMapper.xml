<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.purchase.purchase_server.mapper.ReplenishmentRecordMapper">

    <delete id="deleteById">
        DELETE FROM cm_replenishment_record WHERE id = #{recordId}
    </delete>

    <!-- 批量更新 -->
    <update id="updateAllRecord">
        UPDATE cm_replenishment_record
        <set>
            advice_purchase_start_date = #{val.advicePurchaseStartDate},
            advice_purchase_end_date = #{val.advicePurchaseEndDate}
        </set>
        WHERE id = #{val.id}
    </update>
</mapper>
