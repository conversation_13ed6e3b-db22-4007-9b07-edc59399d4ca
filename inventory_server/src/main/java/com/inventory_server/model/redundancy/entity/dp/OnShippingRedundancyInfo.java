package com.inventory_server.model.redundancy.entity.dp;

import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Description 在途冗余信息
 * <AUTHOR>
 * @Date 2024/12/11 11:37
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
public class OnShippingRedundancyInfo extends RedundancyInfo {

    /**
     * 期初海外仓总冗余的在途冗余库存
     */
    @Builder.Default
    private final List<OnShippingWarehouseRedundancy> shippingWarehouseTotalRedundancies = new ArrayList<>();

    /**
     * 当天到货的在途冗余库存
     */
    @Builder.Default
    private final List<OnShippingWarehouseRedundancy> shippingWarehouseRedundancies = new ArrayList<>();

    public void addShippingWarehouseTotalRedundancy(OnShippingWarehouseRedundancy redundancy) {
        shippingWarehouseTotalRedundancies.add(redundancy);
    }

    public void addShippingWarehouseRedundancy(OnShippingWarehouseRedundancy redundancy) {
        shippingWarehouseRedundancies.add(redundancy);
        setRedundantNum(Optional.ofNullable(getRedundantNum()).orElse(0) + redundancy.getRedundantInventory());
    }
}
