package com.inventory_server.model.redundancy.repository.redisRepository.impl;

import com.inventory_server.model.redundancy.repository.redisRepository.IRedundantDateNumRepository;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.crafts_mirror.utils.constant.RedisKeyConstant.DELIVERY_CAL_REDUNDANT_DATE_NUM;

/**
 * @Description 获取冗余
 * <AUTHOR>
 * @Date 2025/2/10 11:10
 **/
@Service
public class RedundantDateNumRepositoryImpl implements IRedundantDateNumRepository {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public int getRedundantDateNum() {
        int redundantDate;
        try {
            redundantDate = Integer.parseInt(Objects.requireNonNull(stringRedisTemplate.opsForValue().get(DELIVERY_CAL_REDUNDANT_DATE_NUM)));
        } catch (Exception e) {
            redundantDate = 7;
            stringRedisTemplate.opsForValue().set(DELIVERY_CAL_REDUNDANT_DATE_NUM, String.valueOf(redundantDate));
        }
        return redundantDate;
    }
}
