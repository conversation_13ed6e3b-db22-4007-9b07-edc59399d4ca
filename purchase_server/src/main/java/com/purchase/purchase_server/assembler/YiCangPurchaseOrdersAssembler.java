package com.purchase.purchase_server.assembler;

import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderDetailDO;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderSystemTrackDO;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderTrackDO;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrdersDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrdersSummaryDto;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.YicangPurchaseOrderDetailDTO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.YicangPurchaseOrderSystemTrackDTO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.YicangPurchaseOrderTrackDTO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * 数据转换工具类
 *
 * <AUTHOR>
 * @date 2024/5/11
 */

@Mapper(componentModel = "spring", uses = YiCangPurchaseOrdersConvert.class)
public interface YiCangPurchaseOrdersAssembler {

    //@Mapping(source = "singleNetNumber", target = "singleNetNumber", qualifiedByName = "listToString")
    YicangPurchaseOrdersDO orderDtotoDO(OrdersSummaryDto val);


    YicangPurchaseOrderDetailDO detailDTOtoDO(YicangPurchaseOrderDetailDTO val);

    List<YicangPurchaseOrderDetailDO> detailListDTOtoDO(List<YicangPurchaseOrderDetailDTO> val);

    YicangPurchaseOrderTrackDO trackDTOtoDO(YicangPurchaseOrderTrackDTO val);

    YicangPurchaseOrderSystemTrackDO systemTrackDTOtoDO(YicangPurchaseOrderSystemTrackDTO val);

}














