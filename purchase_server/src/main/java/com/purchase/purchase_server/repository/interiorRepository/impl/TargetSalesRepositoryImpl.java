package com.purchase.purchase_server.repository.interiorRepository.impl;

import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.model.targetSales.entity.form.DaySalesEditForm;
import com.purchase.purchase_server.repository.interiorRepository.TargetSalesRepository;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import static com.crafts_mirror.utils.constant.SystemConstant.EDIT_DAY_TARGET_SALES;

/**
 * <AUTHOR>
 * @Date 2025/3/17 17:48
 **/
@Service
public class TargetSalesRepositoryImpl implements TargetSalesRepository {

    @Resource
    protected RestTemplate restTemplate;

    @Override
    public boolean updateTargetSales(DaySalesEditForm form) {
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO<Boolean> result = restTemplateUtils.post(form, ResultDTO.class, EDIT_DAY_TARGET_SALES);
        return result.getData();
    }
}
