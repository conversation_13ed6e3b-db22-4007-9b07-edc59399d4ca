package com.purchase.purchase_server.service.lcl.impl;

import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.vo.Lcl.LclConsolidationFinishedInventoryVO;
import com.purchase.purchase_server.enums.lcl.IsChangeEnum;
import com.purchase.purchase_server.enums.lcl.IsPackageFullEnum;
import com.purchase.purchase_server.service.lcl.IExcelExportService;
import lombok.val;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/18
 **/
@Service
public class ExcelExportServiceImpl implements IExcelExportService {

    private static final HorizontalCellStyleStrategy CELL_STYLE_STRATEGY;

    static {
        // 表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 设置表头背景色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);

        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteCellStyle.setWriteFont(contentWriteFont);

        // 高亮样式（绿色背景）
        WriteCellStyle highlightStyle = new WriteCellStyle();
        highlightStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());

        CELL_STYLE_STRATEGY = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    @Override
    public List<List<String>> createHeaders(List<SenboWarehouseDto> dynamicHeaders) {
        List<List<String>> headers = new ArrayList<>();
        // 添加固定表头
        for (String head : FIXED_COLUMNS) {
            headers.add(List.of(head, head));
        }
        // 添加动态表头
        List<String> warehouseSet = dynamicHeaders.stream().map(SenboWarehouseDto::getSenboWarehouse).toList();

        List<String> firstHeader = List.of("整理后", "整理前", "整理后体积", "整理前体积");

        for (int i = 0; i < 4; i++) {
            String firstHead = firstHeader.get(i);
            for (String head : warehouseSet) {
                headers.add(List.of(firstHead, head));
            }
        }
        return headers;
    }

    public static final List<String> FIXED_COLUMNS = List.of(
            "合同号",
            "虚拟SKU",
            "产品名称",
            "渠道",
            "自定义SKU",
            "单箱数量",
            "外箱尺寸长",
            "外箱尺寸宽",
            "外箱尺寸高",
            "单箱体积",
            "单箱毛重",
            "采购人员",
            "发货装柜时间",
            "工厂交期",
            "未装满"
    );

    public List<List<Object>> processData(String isChange, Map<String, Boolean> sheet1Map, Map<String, Boolean> fieldChanges, Map<String, Boolean> dynamicFieldChanges,
                                          TreeMap<String, TreeMap<String, List<LclConsolidationFinishedInventoryVO>>> groupedResult,
                                          List<SenboWarehouseDto> dynamicHeaders, Map<String, String> channelIdNameMap) {

        List<List<Object>> rows = new ArrayList<>();
        List<String> warehouseSet = dynamicHeaders.stream().map(SenboWarehouseDto::getSenboWarehouseId).toList();
        Map<String, String> senboWarehouseKeyMap = dynamicHeaders.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));
        for (val startDate : groupedResult.values()) {
            AtomicBoolean sheet1flag = new AtomicBoolean(false);
            AtomicReference<String> key = new AtomicReference<>();
            for (var result : startDate.entrySet()) {
                List<LclConsolidationFinishedInventoryVO> value = result.getValue();
                Map<String, LclConsolidationFinishedInventoryVO> inventoryMap = value.stream()
                        .collect(Collectors.toMap(
                                LclConsolidationFinishedInventoryVO::getDestinationWarehouse,  // key映射函数
                                Function.identity(),                         // value映射函数
                                (existing, replacement) -> existing          // 如果有重复key的处理策略
                        ));
                LclConsolidationFinishedInventoryVO first = value.getFirst();
                List<Object> row = new ArrayList<>();
                row.add(first.getContractCode());
                row.add(first.getDestinationSku());
                row.add(first.getProductName());
                row.add(channelIdNameMap.getOrDefault(first.getChannel(), first.getChannel()));
                row.add(first.getSelfSku());
                row.add(first.getContainerLoad());
                row.add(first.getCaseLength());
                row.add(first.getCaseWidth());
                row.add(first.getCaseHeight());
                row.add(first.getCaseLength()
                        .multiply(first.getCaseWidth())
                        .multiply(first.getCaseHeight())
                        .divide(BigDecimal.valueOf(1000000), 5, RoundingMode.HALF_UP));
                row.add(first.getSingleCaseGrossWeight());
                row.add(first.getBuyer());
                row.add(first.getShippingStartDate());
                row.add(first.getFactoryFinishedDate());
                boolean allPackageFull = value.stream()
                        .allMatch(vo -> vo.getIsPackageFull().equals(IsPackageFullEnum.NOT_FULL.getCode()));
                row.add(allPackageFull ? "是" : "否");

                // 遍历4种数据类型:0-拼箱数量,1-整箱数量,2-拼箱体积,3-整箱体积
                for (int i = 0; i < 4; i++) {
                    int dataType = i;
                    AtomicBoolean flag = new AtomicBoolean(false);
                    warehouseSet.forEach(warehouseId -> {
                        var finInventory = inventoryMap.getOrDefault(warehouseId, null);
                        if (finInventory == null) {
                            row.add(null);
                            return;
                        }
                        switch (dataType) {
                            case 0 -> {
                                boolean isChangeFlag = finInventory.getIsChange().equals(IsChangeEnum.CHANGED.getCode());
                                key.set(finInventory.getContractCode() + "-" + finInventory.getDestinationSku() + "-" +
                                        finInventory.getFactoryFinishedDate());
                                dynamicFieldChanges.put(finInventory.getContractCode() + "-" + finInventory.getDestinationSku() + "-" +
                                        finInventory.getFactoryFinishedDate() + "-" + finInventory.getShippingStartDate() + "-" +
                                        senboWarehouseKeyMap.get(finInventory.getDestinationWarehouse()), isChangeFlag);
                                if (isChangeFlag) {
                                    flag.set(true);
                                    sheet1flag.set(true);
                                }

                                row.add(finInventory.getLclShippingNum());
                            }
                            case 1 -> row.add(finInventory.getShippingNum());
                            case 2, 3 -> {
                                // 计算装箱数
                                int quantity = (dataType == 2) ?
                                        finInventory.getLclShippingNum() :
                                        finInventory.getShippingNum();
                                int neededContainers = (quantity + finInventory.getContainerLoad() - 1) /
                                        finInventory.getContainerLoad();

                                // 计算体积
                                BigDecimal volume = finInventory.getCaseLength()
                                        .multiply(finInventory.getCaseWidth())
                                        .multiply(finInventory.getCaseHeight())
                                        .multiply(BigDecimal.valueOf(neededContainers))
                                        .divide(BigDecimal.valueOf(1000000), 5, RoundingMode.HALF_UP);
                                row.add(volume);
                            }
                        }
                        if (dataType == 0) {
                            fieldChanges.put(finInventory.getContractCode() + "-" + finInventory.getDestinationSku() + "-" +
                                    finInventory.getFactoryFinishedDate() + "-" + finInventory.getShippingStartDate(), flag.get());
                        }
                    });
                }
                rows.add(row);
            }
            sheet1Map.put(key.get(), sheet1flag.get());
        }
        return rows;
    }

}
