package com.purchase.purchase_server.entity.form;

import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.util.List;

/**
 * @Description 采购单查询
 * <AUTHOR>
 * @Date 2023/12/11 14:31
 **/
@Data
@Builder
public class PurchaseOrdersForm extends BasePageForm {


    @Serial
    private static final long serialVersionUID = 6091892341012823258L;
    /**
     * sb采购单号
     */
    private String sbPoId;

    private List<String> sbPoIdList;
    /**
     * 采购单状态
     */
    private String poStaus;
    /**
     * 是否需要复审
     */
    private String isReExamine;
    /**
     * 参考号
     */
    private List<String> refNoList;
    /**
     * 采购单号
     */
    private List<String> poCodeList;
    /**
     * 供应商代码
     */
    private List<String> supplierCodeList;
    /**
     * senbo虚拟sku
     */
    private String destinationSku;

    /**
     * 虚拟skuId
     */
    private String virtualSkuId;

    /**
     * sb采购单虚拟表id
     */
    private String sbPoVirtualId;

    /**
     * 采购人员
     */
    private String purchaser;

    /**
     * 跟单人
     */
    private String orderTracker;

    /**
     * 数据范围
     */
    private String dataScope;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * SKU
     */
    private List<String> selfSkuList;

    /**
     * 虚拟SKU
     */
    private List<String> virtualSkuList;

    /**
     * 工厂交期
     */
    private String expectedDeliveryStartDate;
    private String expectedDeliveryEndDate;

    /**
     * 导出标识
     */
    private Boolean exportFlag;
}
