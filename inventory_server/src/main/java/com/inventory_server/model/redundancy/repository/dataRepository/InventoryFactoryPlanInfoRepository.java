package com.inventory_server.model.redundancy.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.redundancy.entity.aggregate.InventoryFactoryPlanInfoA;
import com.inventory_server.model.redundancy.entity.dos.InventoryFactoryPlanInfoDO;
import com.inventory_server.model.redundancy.mapper.InventoryFactoryPlanInfoMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description repository类
 * <AUTHOR>
 * @Date 2024/5/10 17:12
 **/
@Service
public class InventoryFactoryPlanInfoRepository extends ServiceImpl<InventoryFactoryPlanInfoMapper, InventoryFactoryPlanInfoDO> {

    public List<InventoryFactoryPlanInfoA> getPlanInfoListByInfoIds(List<String> infoIds) {
        return baseMapper.selectJoinList(InventoryFactoryPlanInfoA.class, new MPJLambdaWrapper<InventoryFactoryPlanInfoDO>()
                .select(InventoryFactoryPlanInfoDO::getInventoryInfoId)
                .select(InventoryFactoryPlanInfoDO::getContractCode)
                .select(InventoryFactoryPlanInfoDO::getFactoryFinishedDate)
                .select(InventoryFactoryPlanInfoDO::getStoreNum)
                .select(InventoryFactoryPlanInfoDO::getShippingNum)
                .select(InventoryFactoryPlanInfoDO::getRemarks)
                .select("t.store_num - t.shipping_num as remain_num")
                .select("CASE t.is_old WHEN '1' THEN t1.old_sku WHEN '0' THEN t1.virtual_sku END as destination_sku")
                .leftJoin(VirtualProductDO.class, VirtualProductDO::getId, InventoryFactoryPlanInfoDO::getVirtualSkuId)
                .in(InventoryFactoryPlanInfoDO::getInventoryInfoId, infoIds)
                .orderByAsc(InventoryFactoryPlanInfoDO::getInventoryInfoId)
                .orderByAsc(InventoryFactoryPlanInfoDO::getFactoryFinishedDate)
                .orderByAsc(VirtualProductDO::getVirtualSku)
                .orderByAsc(InventoryFactoryPlanInfoDO::getContractCode)
        );
    }

    public List<InventoryFactoryPlanInfoA> getPlanInfoListByInfoId(String infoId) {
        return baseMapper.selectJoinList(InventoryFactoryPlanInfoA.class, new MPJLambdaWrapper<InventoryFactoryPlanInfoDO>()
                .select(InventoryFactoryPlanInfoDO::getId)
                .select(InventoryFactoryPlanInfoDO::getInventoryInfoId)
                .select(InventoryFactoryPlanInfoDO::getContractCode)
                .select(InventoryFactoryPlanInfoDO::getFactoryFinishedDate)
                .select(InventoryFactoryPlanInfoDO::getStoreNum)
                .select(InventoryFactoryPlanInfoDO::getShippingNum)
                .select(InventoryFactoryPlanInfoDO::getRemarks)
                .select("t.store_num - t.shipping_num as remain_num")
                .select("CASE t.is_old WHEN '1' THEN t1.old_sku WHEN '0' THEN t1.virtual_sku END as destination_sku")
                .leftJoin(VirtualProductDO.class, VirtualProductDO::getId, InventoryFactoryPlanInfoDO::getVirtualSkuId)
                .eq(InventoryFactoryPlanInfoDO::getInventoryInfoId, infoId)
                .orderByAsc(InventoryFactoryPlanInfoDO::getInventoryInfoId)
                .orderByAsc(InventoryFactoryPlanInfoDO::getFactoryFinishedDate)
                .orderByAsc(VirtualProductDO::getVirtualSku)
                .orderByAsc(InventoryFactoryPlanInfoDO::getContractCode)
        );
    }

    public void deleteByInventoryInfoId(String inventoryInfoId) {
        List<InventoryFactoryPlanInfoDO> list = baseMapper.selectList(Wrappers.<InventoryFactoryPlanInfoDO>lambdaQuery()
                .eq(InventoryFactoryPlanInfoDO::getInventoryInfoId, inventoryInfoId));
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<String> idList = list.stream().map(PhysicalBaseEntity::getId).toList();
        baseMapper.deleteByIds(idList);
    }
}
