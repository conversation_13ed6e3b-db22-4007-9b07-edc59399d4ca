package com.purchase.purchase_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelProperty;
import com.crafts_mirror.utils.aop.validator.PositiveIntegerOrZero;
import com.purchase.purchase_server.aop.validator.EnumValue;
import com.crafts_mirror.utils.enums.FactoryCurrencyEnum;
import com.purchase.purchase_server.enums.FactoryFinancialPaymentTimeEnum;
import com.purchase.purchase_server.enums.FactoryFinancialSettementEnum;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.*;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description excel导入供应商-sheet1转换成的实体列
 * <AUTHOR>
 * @Date 2024/6/6 17:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryInfoAndFinancialExcel extends BaseExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = -7269037261813237455L;

    @NotNull(message = "供应商代码不能为空")
    @Length(max = 20, message = "供应商代码不超20字符，支持数字、字母和两者结合")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "供应商代码只能是数字和字母")
    @ExcelProperty("* 供应商代码")
    private String factoryCode;

    @NotNull(message = "供应商名称不能为空")
    @Length(max = 100, message = "供应商名称不超100字符")
    @ExcelProperty("* 供应商名称")
    private String factoryName;

    @Length(max = 20, message = "简称不超20字符")
    @ExcelProperty("简称")
    private String shortName;

    @Length(max = 20, message = "采购员不超20字符")
    @ExcelProperty("采购员")
    private String purchaser;

    @Length(max = 20, message = "跟单人不超20字符")
    @ExcelProperty("跟单人")
    private String orderTracker;

    @Length(max = 255, message = "供应商备注不超过255字符")
    @ExcelProperty("供应商备注")
    private String factoryRemark;

    @NotNull(message = "币种不能为空")
    @EnumValue(enumClass = FactoryCurrencyEnum.class, methodName = "ofDesc", message = "币种类型错误")
    @ExcelProperty("* 币种")
    private String currency;

    @Length(max = 100, message = "省份不超过100字符")
    @ExcelProperty("省份")
    private String province;

    @Length(max = 100, message = "城市不超100字符")
    @ExcelProperty("城市")
    private String city;

    @Length(max = 255, message = "详细地址不超255字符")
    @ExcelProperty("详细地址")
    private String detailedAddress;

    @EnumValue(enumClass = FactoryFinancialSettementEnum.class, methodName = "ofDesc", message = "结算类型错误")
    @ExcelProperty("结算类型")
    private String settlementType;

    @Pattern(regexp = "^(100(\\.0{1,2})?|([1-9]?\\d(\\.\\d{1,2})?))$",message = "定金比例最大为100，最多两位小数的非负数")
    @ExcelProperty("定金比例")
    private String depositRatio;

    @EnumValue(enumClass = FactoryFinancialPaymentTimeEnum.class, methodName = "ofDesc", message = "定金支付时间类型错误")
    @ExcelProperty("定金支付时间")
    private String paymentTime;

    @PositiveIntegerOrZero(message = "尾款账期必须是正整数", isRequired = false)
    @ExcelProperty("尾款账期")
    private String finalPaymentTerms;

    @Length(max = 255, message = "结算备注不超过255字符")
    @ExcelProperty("结算备注")
    private String financialRemark;
}
