package com.purchase.purchase_server.entity.dto;

import cn.hutool.core.date.DateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 补货导出-海外库存冗余数据
 * <AUTHOR>
 * @Date 2024/6/26 13:53
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryInventoryExportDto {

    private String inventoryInfoId;
    private String virtaulSkuId;

    private String virtaulSku;

    //工厂已生产好
    private Double productFactoryRedundantSum;
    //剩余分配
    private Double addProductFactoryRedundantSum;
    private String productContractCode;
    //工厂已生产好
    private Double isProductFactoryRedundantSum;
    //剩余分配
    private Double addIsProductFactoryRedundantSum;
    private String isProductContractCode;

    private DateTime enableUsingDate;
}
