package com.product.product_server.model.currency.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/4/10 15:24
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfficialMERDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 15665986543L;

    private String vrtCode;

    private BigDecimal price;

    private String vrtName;

    private String vrtEName;

    private String foreignCName;
}
