package com.inventory_server.applications.dto;

import com.inventory_server.model.redundancy.entity.aggregate.InventoryFactoryPlanInfoA;
import com.inventory_server.model.redundancy.entity.bo.InventoryForeignStoreBO;
import com.inventory_server.model.redundancy.entity.bo.InventoryRulesBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2024/5/11
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DetailInventoryRulesDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 8366842670924876815L;
    /**
     * 基本参数
     */
    private InventoryRulesBO baseParamDto;

    /**
     * 仓库顺序数组
     */
    private List<String> warehouseSortList;

    /**
     * 目标日销
     */
    private TreeMap<String, Double> destinationEverydaySale;
    /**
     * 库存信息-库存
     */
    private List<InventoryForeignStoreBO> enableUseInventoryList;
    /**
     * 库存信息-在途
     */
    private List<InventoryForeignStoreBO> onShippingInventoryList;
    /**
     * 计划
     */
    private List<InventoryFactoryPlanInfoA> shippingDetailPlainDtoList;

    private String calFinishedDate;

    private String virtualSku;

    private String productName;

    private Integer productStatus;

    private Integer subType;

    /**
     * 当前版本为3月份生产交期
     */
    private Integer produceDay;

    private String endDateDesc;

    private StopSellingProductsRedundancyDto stopSellingProductsRedundancy;

    private List<DetailInventoryWatchBoardDto> watchBoardDtoList;

    private List<FactoryWatchBoardDto> factoryWatchBoardDtoList;

    private Map<String, Map<String, Double>> mockShippingInventoryMap;

    private Map<String, Map<String, Double>> mockRemainInventoryMap;

    private Map<String, Map<String, Double>> mockDaysSaleInventoryMap;
}
