package com.auth.authorization_server.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("cm_sys_permission")
public class Permission implements Serializable {
    @Serial
    private static final long serialVersionUID = -1L;

    private Integer id;
    private String name;
    private String urlPerm;
    private String btnPerm;
    private Integer method;
    private String serviceName;
    private Integer menuId;

}
