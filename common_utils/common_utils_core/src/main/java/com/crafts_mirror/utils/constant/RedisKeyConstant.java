package com.crafts_mirror.utils.constant;

/**
 * @author: 雪竹
 * @description: redis键名常量
 * @dateTime: 2023/11/28 10:38
 **/
public class RedisKeyConstant {

    public static final String USER_TOKEN_KEY = "user:token:key:";

    public static final String CHANNEL_MAPPING = "channel:mapping";

    public static final String PREVENT_RESUBMIT_REQUEST_PREFIX = "prevent:resubmit:request:prefix:";

    public static final String BLACK_LIST_PREFIX = "sys:black:list:";

    public static final String DELIVERY_CAL_REDUNDANT_DATE_NUM = "delivery:redundant:date:num";

    public static final String PURCHASE_CAL_REDUNDANT_DATE_NUM = "purchase:redundant:date:num";

    public static final String MENU = "menu:*:set";

    public static final String NO_NEED_TO_LOG_VISIT_LOG_SET = "visit:log:whit:set";

    public static final String POST = "post:string";

    public static final String IMPORT_DELIVERY_LOCK = "purchase:import:deliveryLock";

    public static final String EXPORT_DELIVERY_LOCK = "purchase:export:deliveryLock";

    public static final String IMPORT_REPLENISHMENT_LOCK = "purchase:import:replenishmentLock";

    public static final String SYNCHRONIZE_PURCHASE_ORDER_LOCK = "purchase:synchronize:purchaseOrder";

    public static final String SYNCHRONIZE_WAREHOUSE_LOCK = "inventory:synchronize:warehouse";

    public static final String OPERATOR_CONFIRMED_REASON = "purchase:operator:confirmedReasonList";

    public static final String CREATE_LCL_LOCK = "purchase:create:lclLock";

    public static final String EDIT_LCL_DATA_CLEAN_LOCK = "purchase:edit:lclDataCLeanLock";

    public static final String EDIT_LCL_SHIPPING_NUM_LOCK = "purchase:edit:lclShippingNumLock";

    public static final String MOVE_SPLIT_CONTAINER_LOCK_ = "purchase:edit:moveSplitContainerLock_";

    public static final String SPECIAL_REDUNDANCY_END_DATE = "inventory:special:redundancyEndDate";
    /**
     * 是否需要开启冗余库存特殊截止时间
     */
    public static final String SPECIAL_REDUNDANCY_NEEDED = "inventory:special:needed";

    public static final String INVENTORY_EXTRA_REDUNDANT_DATE = "inventory:extra:redundant:date";

    public static final String REDUNDANCY_SHIPPING_DATE_FLAG = "inventory:shipping:date:flag";

    public static final String TARGET_SALES_VIRTUAL_SKU_PREFIX = "sales:targetSales:daySales:";

    /**
     * 冗余库存正在计算的锁
     */
    public static final String REDUNDANCY_IN_CAL = "redundancy:import:lock";

    public static final String WAREHOUSE_UPDATE_LOCK = "warehouse:update:lock";
}
