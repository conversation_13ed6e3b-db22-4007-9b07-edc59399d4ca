package com.product.product_server.model.products;

import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.utils.utils.PatternUtils;
import com.product.product_server.model.virtualProduct.VirtualProductSkuStatusDp;

public record ImportVirtualSkuInfoDp(String channel, String spu, String virtualSku, String operator,
                                     VirtualProductSkuStatusDp virtualProductSkuStatusDp) {
    public ImportVirtualSkuInfoDp {
        validVirtualSku(channel, spu, virtualSku, operator, virtualProductSkuStatusDp);
    }

    private void validVirtualSku(String channel, String spu, String virtualSku, String operator, VirtualProductSkuStatusDp virtualProductSkuStatusDp) {
        if ((StrUtil.isBlank(channel) && StrUtil.isBlank(spu) && StrUtil.isBlank(virtualSku) && StrUtil.isBlank(operator)
                && StrUtil.isBlank(virtualProductSkuStatusDp.subType()) && StrUtil.isBlank(virtualProductSkuStatusDp.productStatus()) && StrUtil.isBlank(virtualProductSkuStatusDp.productType())) ||
                (StrUtil.isNotBlank(channel) && StrUtil.isNotBlank(spu) && StrUtil.isNotBlank(virtualSku) && StrUtil.isNotBlank(operator) &&
                        StrUtil.isNotBlank(virtualProductSkuStatusDp.subType()) && StrUtil.isNotBlank(virtualProductSkuStatusDp.productStatus()) && StrUtil.isNotBlank(virtualProductSkuStatusDp.productType()))) {
            if (StrUtil.isNotBlank(spu) && spu.length() > 50) {
                throw new IllegalArgumentException("spu长度超出限制");
            }

            if (StrUtil.isNotBlank(virtualSku) && virtualSku.length() > 20) {
                throw new IllegalArgumentException("虚拟sku长度超出限制");
            } else if (StrUtil.isNotBlank(virtualSku) && !PatternUtils.UPPERCASE_LOWERCASE_NUMBER_DOT_PATTERN.matcher(virtualSku).matches()) {
                throw new IllegalArgumentException("虚拟sku只能英文、数字和符号的组合");
            }
            return;
        }
        throw new IllegalArgumentException("渠道、spu虚拟、sku值、运营、子体类型、产品状态、产品类型状态不统一，要么都设置值，要么都不能有值");
    }
}
