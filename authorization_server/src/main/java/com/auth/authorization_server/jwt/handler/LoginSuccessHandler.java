package com.auth.authorization_server.jwt.handler;

import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Component
public class LoginSuccessHandler implements AuthenticationSuccessHandler {
    

    @Override
    public void onAuthenticationSuccess(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Authentication authentication) throws IOException {
        httpServletResponse.setContentType("application/json;charset=UTF-8");

        // 生成token，并放置到请求头中
//        String token = JwtUtil.generateToken(authentication.getName());
//        httpServletResponse.setHeader(JwtUtil.HEADER, token);

        ResultDTO resultDTO = ResultDTO.success("SuccessLogin");

        ServletOutputStream outputStream = httpServletResponse.getOutputStream();
        outputStream.write(JSON.toJSONBytes(resultDTO));
        outputStream.flush();
        outputStream.close();
    }
}

