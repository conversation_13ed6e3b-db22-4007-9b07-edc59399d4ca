package com.auth.authorization_server.model.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 角色和部门关联表 cm_sys_role_dept
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_sys_role_dept")
public class RoleDeptDO implements Serializable {

    @Serial
    private static final long serialVersionUID = -2047533192029155286L;
    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 部门ID
     */
    private String deptId;

}
