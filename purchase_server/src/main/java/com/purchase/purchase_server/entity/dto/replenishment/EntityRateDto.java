package com.purchase.purchase_server.entity.dto.replenishment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.Serial;
import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Component
@RefreshScope
public class EntityRateDto implements Serializable {
    @Serial
    private static final long serialVersionUID = -1954298542032414707L;

    @Value("${entityRate.subAchievementRate}")
    private String subAchievementRate;

    @Value("${entityRate.parentAchievementRate}")
    private String parentAchievementRate;
}