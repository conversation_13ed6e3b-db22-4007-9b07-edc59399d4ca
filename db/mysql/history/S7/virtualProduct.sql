ALTER TABLE `cm_virtual_product` ADD COLUMN `sub_type` tinyint NOT NULL
    COMMENT '子体类型：0-流量子体，1-转化子体，2-利润子体，3-三无子体，4-测试子体' AFTER `spu_id`;

ALTER TABLE `cm_virtual_product` ADD COLUMN `product_status` tinyint NOT NULL
    COMMENT '产品状态：0-正常，1-测款，2-停发，3-售完停售，4-停售' AFTER `sub_type`;

ALTER TABLE `cm_virtual_product` ADD COLUMN `product_type` tinyint NOT NULL
    COMMENT '产品类型：0-常规产品，1-儿童产品，2-季节性产品' AFTER `product_status`;