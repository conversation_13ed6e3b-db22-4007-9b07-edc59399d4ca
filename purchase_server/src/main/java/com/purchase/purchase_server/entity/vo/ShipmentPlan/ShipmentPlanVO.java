package com.purchase.purchase_server.entity.vo.ShipmentPlan;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * 货件计划
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentPlanVO implements Serializable {

    /**
     * ID
     */
    private String id;
    /**
     * 货件计划编号
     */
    private String shipmentCode;

    /**
     * 仓库id
     */
    private String warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 实际装柜时间
     */
    private String actualLoadingTime;

    /**
     * 开船时间
     */
    private String shippingDate;

    /**
     * 预计到港时间
     */
    private String estimatedArrivalTime;

    /**
     * 实际到港时间
     */
    private String actualArrivalTime;

    /**
     * 实际签收时间
     */
    private String actualSignTime;

    /**
     * 预计签收时间
     */
    private String estimatedSignTime;

    /**
     * 海外仓上架时间
     */
    private String warehouseShelfTime;

    /**
     * 实际签收
     */
    private String actualSign;
    /**
     * 货件类型 0-灯具，1-家具，2-灯具+家具，3-家具+灯具
     */
    private String shipmentType;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private String updateDate;

}