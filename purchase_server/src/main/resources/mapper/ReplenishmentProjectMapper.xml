<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.purchase.purchase_server.mapper.ReplenishmentProjectMapper">

    <resultMap id="repProjectSkuMap" type="com.purchase.purchase_server.entity.bo.RepProjectSkuBO">
        <id column="id" property="id"/>
        <result column="self_sku" property="selfSku"/>
        <result column="product_name" property="productName"/>
        <result column="image" property="image"/>
        <result column="factory_code" property="factoryCode"/>
        <result column="overseas_inventory" property="overseasInventory"/>
        <result column="overseas_shipping" property="overseasShipping"/>
        <result column="local_inventory" property="localInventory"/>
        <result column="advice_purchase_num" property="advicePurchaseNum"/>
        <result column="accepted_purchase_num" property="acceptedPurchaseNum"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 批量删除 -->
    <delete id="deleteByIds">
        DELETE FROM cm_replenishment_project WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
