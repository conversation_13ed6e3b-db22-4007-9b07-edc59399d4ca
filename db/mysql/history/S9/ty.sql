ALTER TABLE `crafts_mirror`.`cm_warehouse_senbo_info`
    ADD COLUMN `sort` int NULL DEFAULT NULL COMMENT '发补货冗余试算时的排序值，越小的排序越靠前' AFTER `head_shipping_date`;

UPDATE `crafts_mirror`.`cm_warehouse_senbo_info` SET `head_shipping_date` = 35, `sort` = 5 WHERE `id` = 1;
UPDATE `crafts_mirror`.`cm_warehouse_senbo_info` SET `head_shipping_date` = 50, `sort` = 25 WHERE `id` = 2;
UPDATE `crafts_mirror`.`cm_warehouse_senbo_info` SET `head_shipping_date` = 55, `sort` = 15 WHERE `id` = 3;
UPDATE `crafts_mirror`.`cm_warehouse_senbo_info` SET `head_shipping_date` = 35, `sort` = 0 WHERE `id` = 4;
UPDATE `crafts_mirror`.`cm_warehouse_senbo_info` SET `head_shipping_date` = 45, `sort` = 10 WHERE `id` = 5;
UPDATE `crafts_mirror`.`cm_warehouse_senbo_info` SET `head_shipping_date` = 50, `sort` = 20 WHERE `id` = 6;
UPDATE `crafts_mirror`.`cm_warehouse_senbo_info` SET `head_shipping_date` = 60, `sort` = 30 WHERE `id` = 7;


-- 商品库存
UPDATE `crafts_mirror`.`cm_inventory_foreign_store` SET `warehouse` = '1', `update_date` = NOW() WHERE warehouse = "美西仓";
UPDATE `crafts_mirror`.`cm_inventory_foreign_store` SET `warehouse` = '2', `update_date` = NOW() WHERE warehouse = "美南仓";
UPDATE `crafts_mirror`.`cm_inventory_foreign_store` SET `warehouse` = '3', `update_date` = NOW() WHERE warehouse = "美东仓";
UPDATE `crafts_mirror`.`cm_inventory_foreign_store` SET `warehouse` = '4', `update_date` = NOW() WHERE warehouse = "FBA仓";
UPDATE `crafts_mirror`.`cm_inventory_foreign_store` SET `warehouse` = '5', `update_date` = NOW() WHERE warehouse = "CG仓";
UPDATE `crafts_mirror`.`cm_inventory_foreign_store` SET `warehouse` = '6', `update_date` = NOW() WHERE warehouse = "美中仓";
UPDATE `crafts_mirror`.`cm_inventory_foreign_store` SET `warehouse` = '7', `update_date` = NOW() WHERE warehouse = "美东南仓";

-- 商品海外仓冗余
UPDATE `crafts_mirror`.`cm_inventory_foreign_redundant_info` SET `warehouse` = '1', `update_date` = NOW() WHERE warehouse = "美西仓";
UPDATE `crafts_mirror`.`cm_inventory_foreign_redundant_info` SET `warehouse` = '2', `update_date` = NOW() WHERE warehouse = "美南仓";
UPDATE `crafts_mirror`.`cm_inventory_foreign_redundant_info` SET `warehouse` = '3', `update_date` = NOW() WHERE warehouse = "美东仓";
UPDATE `crafts_mirror`.`cm_inventory_foreign_redundant_info` SET `warehouse` = '4', `update_date` = NOW() WHERE warehouse = "FBA仓";
UPDATE `crafts_mirror`.`cm_inventory_foreign_redundant_info` SET `warehouse` = '5', `update_date` = NOW() WHERE warehouse = "CG仓";
UPDATE `crafts_mirror`.`cm_inventory_foreign_redundant_info` SET `warehouse` = '6', `update_date` = NOW() WHERE warehouse = "美中仓";
UPDATE `crafts_mirror`.`cm_inventory_foreign_redundant_info` SET `warehouse` = '7', `update_date` = NOW() WHERE warehouse = "美东南仓";

-- 库存冗余的库存衰减情况
UPDATE `crafts_mirror`.`cm_inventory_mock_table` SET `warehouse` = '1', `update_date` = NOW() WHERE warehouse = "美西仓";
UPDATE `crafts_mirror`.`cm_inventory_mock_table` SET `warehouse` = '2', `update_date` = NOW() WHERE warehouse = "美南仓";
UPDATE `crafts_mirror`.`cm_inventory_mock_table` SET `warehouse` = '3', `update_date` = NOW() WHERE warehouse = "美东仓";
UPDATE `crafts_mirror`.`cm_inventory_mock_table` SET `warehouse` = '4', `update_date` = NOW() WHERE warehouse = "FBA仓";
UPDATE `crafts_mirror`.`cm_inventory_mock_table` SET `warehouse` = '5', `update_date` = NOW() WHERE warehouse = "CG仓";
UPDATE `crafts_mirror`.`cm_inventory_mock_table` SET `warehouse` = '6', `update_date` = NOW() WHERE warehouse = "美中仓";
UPDATE `crafts_mirror`.`cm_inventory_mock_table` SET `warehouse` = '7', `update_date` = NOW() WHERE warehouse = "美东南仓";

-- 修改头程时间
UPDATE cm_inventory_sale_rules
SET head_shipping_date = JSON_SET( JSON_REMOVE( head_shipping_date, '$."美西仓"' ), '$."1"', head_shipping_date -> '$."美西仓"' )
WHERE  head_shipping_date ->> '$."1"' IS NULL;

UPDATE cm_inventory_sale_rules
SET head_shipping_date = JSON_SET( JSON_REMOVE( head_shipping_date, '$."美南仓"' ), '$."2"', head_shipping_date -> '$."美南仓"' )
WHERE  head_shipping_date ->> '$."2"' IS NULL;

UPDATE cm_inventory_sale_rules
SET head_shipping_date = JSON_SET( JSON_REMOVE( head_shipping_date, '$."美东仓"' ), '$."3"', head_shipping_date -> '$."美东仓"' )
WHERE  head_shipping_date ->> '$."3"' IS NULL;

UPDATE cm_inventory_sale_rules
SET head_shipping_date = JSON_SET( JSON_REMOVE( head_shipping_date, '$."FBA仓"' ), '$."4"', head_shipping_date -> '$."FBA仓"' )
WHERE  head_shipping_date ->> '$."4"' IS NULL;

UPDATE cm_inventory_sale_rules
SET head_shipping_date = JSON_SET( JSON_REMOVE( head_shipping_date, '$."CG仓"' ), '$."5"', head_shipping_date -> '$."CG仓"' )
WHERE  head_shipping_date ->> '$."5"' IS NULL;

UPDATE cm_inventory_sale_rules
SET head_shipping_date = JSON_SET( JSON_REMOVE( head_shipping_date, '$."美中仓"' ), '$."6"', head_shipping_date -> '$."美中仓"' )
WHERE  head_shipping_date ->> '$."6"' IS NULL;

UPDATE cm_inventory_sale_rules
SET head_shipping_date = JSON_SET( JSON_REMOVE( head_shipping_date, '$."美东南仓"' ), '$."7"', head_shipping_date -> '$."美东南仓"' )
WHERE  head_shipping_date ->> '$."7"' IS NULL;

-- 修改发货比例
UPDATE cm_inventory_sale_rules
SET sale_ratio = JSON_SET( JSON_REMOVE( sale_ratio, '$."美西仓"' ), '$."1"', sale_ratio -> '$."美西仓"' )
WHERE  sale_ratio ->> '$."1"' IS NULL;

UPDATE cm_inventory_sale_rules
SET sale_ratio = JSON_SET( JSON_REMOVE( sale_ratio, '$."美南仓"' ), '$."2"', sale_ratio -> '$."美南仓"' )
WHERE  sale_ratio ->> '$."2"' IS NULL;

UPDATE cm_inventory_sale_rules
SET sale_ratio = JSON_SET( JSON_REMOVE( sale_ratio, '$."美东仓"' ), '$."3"', sale_ratio -> '$."美东仓"' )
WHERE  sale_ratio ->> '$."3"' IS NULL;

UPDATE cm_inventory_sale_rules
SET sale_ratio = JSON_SET( JSON_REMOVE( sale_ratio, '$."FBA仓"' ), '$."4"', sale_ratio -> '$."FBA仓"' )
WHERE  sale_ratio ->> '$."4"' IS NULL;

UPDATE cm_inventory_sale_rules
SET sale_ratio = JSON_SET( JSON_REMOVE( sale_ratio, '$."CG仓"' ), '$."5"', sale_ratio -> '$."CG仓"' )
WHERE  sale_ratio ->> '$."5"' IS NULL;

UPDATE cm_inventory_sale_rules
SET sale_ratio = JSON_SET( JSON_REMOVE( sale_ratio, '$."美中仓"' ), '$."6"', sale_ratio -> '$."美中仓"' )
WHERE  sale_ratio ->> '$."6"' IS NULL;

UPDATE cm_inventory_sale_rules
SET sale_ratio = JSON_SET( JSON_REMOVE( sale_ratio, '$."美东南仓"' ), '$."7"', sale_ratio -> '$."美东南仓"' )
WHERE  sale_ratio ->> '$."7"' IS NULL;

-- 修改试算看板中的仓库
UPDATE `crafts_mirror`.`cm_inventory_watch_board` SET `warehouse` = '1', `update_date` = NOW() WHERE warehouse = "美西仓";
UPDATE `crafts_mirror`.`cm_inventory_watch_board` SET `warehouse` = '2', `update_date` = NOW() WHERE warehouse = "美南仓";
UPDATE `crafts_mirror`.`cm_inventory_watch_board` SET `warehouse` = '3', `update_date` = NOW() WHERE warehouse = "美东仓";
UPDATE `crafts_mirror`.`cm_inventory_watch_board` SET `warehouse` = '4', `update_date` = NOW() WHERE warehouse = "FBA仓";
UPDATE `crafts_mirror`.`cm_inventory_watch_board` SET `warehouse` = '5', `update_date` = NOW() WHERE warehouse = "CG仓";
UPDATE `crafts_mirror`.`cm_inventory_watch_board` SET `warehouse` = '6', `update_date` = NOW() WHERE warehouse = "美中仓";
UPDATE `crafts_mirror`.`cm_inventory_watch_board` SET `warehouse` = '7', `update_date` = NOW() WHERE warehouse = "美东南仓";

-- 修改发货的头程时间仓库名称
UPDATE cm_prepare_products_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."美西仓"' ), '$."1"', head_shipping_days -> '$."美西仓"' )
WHERE  head_shipping_days ->> '$."1"' IS NULL;

UPDATE cm_prepare_products_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."美南仓"' ), '$."2"', head_shipping_days -> '$."美南仓"' )
WHERE  head_shipping_days ->> '$."2"' IS NULL;

UPDATE cm_prepare_products_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."美东仓"' ), '$."3"', head_shipping_days -> '$."美东仓"' )
WHERE  head_shipping_days ->> '$."3"' IS NULL;

UPDATE cm_prepare_products_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."FBA仓"' ), '$."4"', head_shipping_days -> '$."FBA仓"' )
WHERE  head_shipping_days ->> '$."4"' IS NULL;

UPDATE cm_prepare_products_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."CG仓"' ), '$."5"', head_shipping_days -> '$."CG仓"' )
WHERE  head_shipping_days ->> '$."5"' IS NULL;

UPDATE cm_prepare_products_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."美中仓"' ), '$."6"', head_shipping_days -> '$."美中仓"' )
WHERE  head_shipping_days ->> '$."6"' IS NULL;

UPDATE cm_prepare_products_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."美东南仓"' ), '$."7"', head_shipping_days -> '$."美东南仓"' )
WHERE  head_shipping_days ->> '$."7"' IS NULL;

-- 修改发货的发货比例仓库名称
UPDATE cm_prepare_products_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."美西仓"' ), '$."1"', shipping_ratio -> '$."美西仓"' )
WHERE  shipping_ratio ->> '$."1"' IS NULL;

UPDATE cm_prepare_products_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."美南仓"' ), '$."2"', shipping_ratio -> '$."美南仓"' )
WHERE  shipping_ratio ->> '$."2"' IS NULL;

UPDATE cm_prepare_products_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."美东仓"' ), '$."3"', shipping_ratio -> '$."美东仓"' )
WHERE  shipping_ratio ->> '$."3"' IS NULL;

UPDATE cm_prepare_products_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."FBA仓"' ), '$."4"', shipping_ratio -> '$."FBA仓"' )
WHERE  shipping_ratio ->> '$."4"' IS NULL;

UPDATE cm_prepare_products_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."CG仓"' ), '$."5"', shipping_ratio -> '$."CG仓"' )
WHERE  shipping_ratio ->> '$."5"' IS NULL;

UPDATE cm_prepare_products_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."美中仓"' ), '$."6"', shipping_ratio -> '$."美中仓"' )
WHERE  shipping_ratio ->> '$."6"' IS NULL;

UPDATE cm_prepare_products_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."美东南仓"' ), '$."7"', shipping_ratio -> '$."美东南仓"' )
WHERE  shipping_ratio ->> '$."7"' IS NULL;

-- 补货计划中的发货比例修改
UPDATE cm_replenishment_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."美西仓"' ), '$."1"', shipping_ratio -> '$."美西仓"' )
WHERE  shipping_ratio ->> '$."1"' IS NULL;

UPDATE cm_replenishment_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."美南仓"' ), '$."2"', shipping_ratio -> '$."美南仓"' )
WHERE  shipping_ratio ->> '$."2"' IS NULL;

UPDATE cm_replenishment_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."美东仓"' ), '$."3"', shipping_ratio -> '$."美东仓"' )
WHERE  shipping_ratio ->> '$."3"' IS NULL;

UPDATE cm_replenishment_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."FBA仓"' ), '$."4"', shipping_ratio -> '$."FBA仓"' )
WHERE  shipping_ratio ->> '$."4"' IS NULL;

UPDATE cm_replenishment_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."CG仓"' ), '$."5"', shipping_ratio -> '$."CG仓"' )
WHERE  shipping_ratio ->> '$."5"' IS NULL;

UPDATE cm_replenishment_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."美中仓"' ), '$."6"', shipping_ratio -> '$."美中仓"' )
WHERE  shipping_ratio ->> '$."6"' IS NULL;

UPDATE cm_replenishment_rules
SET shipping_ratio = JSON_SET( JSON_REMOVE( shipping_ratio, '$."美东南仓"' ), '$."7"', shipping_ratio -> '$."美东南仓"' )
WHERE  shipping_ratio ->> '$."7"' IS NULL;

-- 补货计划中头程时间修改仓库名称
UPDATE cm_replenishment_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."美西仓"' ), '$."1"', head_shipping_days -> '$."美西仓"' )
WHERE  head_shipping_days ->> '$."1"' IS NULL;

UPDATE cm_replenishment_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."美南仓"' ), '$."2"', head_shipping_days -> '$."美南仓"' )
WHERE  head_shipping_days ->> '$."2"' IS NULL;

UPDATE cm_replenishment_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."美东仓"' ), '$."3"', head_shipping_days -> '$."美东仓"' )
WHERE  head_shipping_days ->> '$."3"' IS NULL;

UPDATE cm_replenishment_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."FBA仓"' ), '$."4"', head_shipping_days -> '$."FBA仓"' )
WHERE  head_shipping_days ->> '$."4"' IS NULL;

UPDATE cm_replenishment_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."CG仓"' ), '$."5"', head_shipping_days -> '$."CG仓"' )
WHERE  head_shipping_days ->> '$."5"' IS NULL;

UPDATE cm_replenishment_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."美中仓"' ), '$."6"', head_shipping_days -> '$."美中仓"' )
WHERE  head_shipping_days ->> '$."6"' IS NULL;

UPDATE cm_replenishment_rules
SET head_shipping_days = JSON_SET( JSON_REMOVE( head_shipping_days, '$."美东南仓"' ), '$."7"', head_shipping_days -> '$."美东南仓"' )
WHERE  head_shipping_days ->> '$."7"' IS NULL;

-- 修改补货计划目标仓库
UPDATE `crafts_mirror`.`cm_replenishment_trial_purchase_inventory` SET `destination_warehouse` = '1', `update_date` = NOW() WHERE destination_warehouse = "美西仓";
UPDATE `crafts_mirror`.`cm_replenishment_trial_purchase_inventory` SET `destination_warehouse` = '2', `update_date` = NOW() WHERE destination_warehouse = "美南仓";
UPDATE `crafts_mirror`.`cm_replenishment_trial_purchase_inventory` SET `destination_warehouse` = '3', `update_date` = NOW() WHERE destination_warehouse = "美东仓";
UPDATE `crafts_mirror`.`cm_replenishment_trial_purchase_inventory` SET `destination_warehouse` = '4', `update_date` = NOW() WHERE destination_warehouse = "FBA仓";
UPDATE `crafts_mirror`.`cm_replenishment_trial_purchase_inventory` SET `destination_warehouse` = '5', `update_date` = NOW() WHERE destination_warehouse = "CG仓";
UPDATE `crafts_mirror`.`cm_replenishment_trial_purchase_inventory` SET `destination_warehouse` = '6', `update_date` = NOW() WHERE destination_warehouse = "美中仓";
UPDATE `crafts_mirror`.`cm_replenishment_trial_purchase_inventory` SET `destination_warehouse` = '7', `update_date` = NOW() WHERE destination_warehouse = "美东南仓";

-- 修改补货计划海外仓库存
UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_inventory = JSON_SET( JSON_REMOVE( overseas_inventory, '$."美西仓"' ), '$."1"', overseas_inventory -> '$."美西仓"' )
WHERE  overseas_inventory ->> '$."1"' IS NULL;

UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_inventory = JSON_SET( JSON_REMOVE( overseas_inventory, '$."美南仓"' ), '$."2"', overseas_inventory -> '$."美南仓"' )
WHERE  overseas_inventory ->> '$."2"' IS NULL;

UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_inventory = JSON_SET( JSON_REMOVE( overseas_inventory, '$."美东仓"' ), '$."3"', overseas_inventory -> '$."美东仓"' )
WHERE  overseas_inventory ->> '$."3"' IS NULL;

UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_inventory = JSON_SET( JSON_REMOVE( overseas_inventory, '$."FBA仓"' ), '$."4"', overseas_inventory -> '$."FBA仓"' )
WHERE  overseas_inventory ->> '$."4"' IS NULL;

UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_inventory = JSON_SET( JSON_REMOVE( overseas_inventory, '$."CG仓"' ), '$."5"', overseas_inventory -> '$."CG仓"' )
WHERE  overseas_inventory ->> '$."5"' IS NULL;

UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_inventory = JSON_SET( JSON_REMOVE( overseas_inventory, '$."美中仓"' ), '$."6"', overseas_inventory -> '$."美中仓"' )
WHERE  overseas_inventory ->> '$."6"' IS NULL;

UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_inventory = JSON_SET( JSON_REMOVE( overseas_inventory, '$."美东南仓"' ), '$."7"', overseas_inventory -> '$."美东南仓"' )
WHERE  overseas_inventory ->> '$."7"' IS NULL;
DROP PROCEDURE IF EXISTS remove_null_keys;
DELIMITER //

CREATE PROCEDURE remove_null_keys(IN record_id VARCHAR(50))
BEGIN
    DECLARE json_keys JSON;
    DECLARE new_json JSON;
		DECLARE value_name VARCHAR(50);
    DECLARE key_name VARCHAR(255);
    DECLARE i INT DEFAULT 0;

    -- 获取JSON字段中的所有键
    SET json_keys = (
        SELECT JSON_KEYS(overseas_inventory)
        FROM cm_replenishment_virtual_sku_purchase
        WHERE id = record_id
    );

    -- 获取JSON字段的当前值
    SET new_json = (
        SELECT overseas_inventory
        FROM cm_replenishment_virtual_sku_purchase
        WHERE id = record_id
    );

    -- 遍历所有键并删除值为NULL的键
    WHILE i < JSON_LENGTH(json_keys) DO
        SET key_name = JSON_UNQUOTE(JSON_EXTRACT(json_keys, CONCAT('$[', i, ']')));
        SET value_name = JSON_EXTRACT(new_json, CONCAT('$."', key_name, '"'));
        IF value_name = 'null' OR value_name IS NULL THEN
            SET new_json = JSON_REMOVE(new_json, CONCAT('$."', key_name, '"'));
END IF;

        SET i = i + 1;
END WHILE;

    -- 更新表中的JSON字段
UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_inventory = new_json
WHERE id = record_id;
END //
DELIMITER ;

DROP PROCEDURE IF EXISTS remove_all_null_keys;
DELIMITER //

CREATE PROCEDURE remove_all_null_keys()
BEGIN
	DECLARE record_id VARCHAR(50);
	DECLARE done INT DEFAULT 0;
	 -- 声明一个游标来遍历所有记录的ID
	DECLARE cur CURSOR FOR SELECT id FROM cm_replenishment_virtual_sku_purchase;
-- 声明一个CONTINUE HANDLER来处理游标结束的情况
DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

OPEN cur;

read_loop: LOOP
	FETCH cur INTO record_id;

	IF done THEN
		LEAVE read_loop;
END IF;
CALL remove_null_keys(record_id);
END LOOP read_loop;
CLOSE cur;
END //
DELIMITER;

CALL remove_all_null_keys();


UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_shipping = JSON_SET( JSON_REMOVE( overseas_shipping, '$."美西仓"' ), '$."1"', overseas_shipping -> '$."美西仓"' )
WHERE  overseas_shipping ->> '$."1"' IS NULL;

UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_shipping = JSON_SET( JSON_REMOVE( overseas_shipping, '$."美南仓"' ), '$."2"', overseas_shipping -> '$."美南仓"' )
WHERE  overseas_shipping ->> '$."2"' IS NULL;

UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_shipping = JSON_SET( JSON_REMOVE( overseas_shipping, '$."美东仓"' ), '$."3"', overseas_shipping -> '$."美东仓"' )
WHERE  overseas_shipping ->> '$."3"' IS NULL;

UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_shipping = JSON_SET( JSON_REMOVE( overseas_shipping, '$."FBA仓"' ), '$."4"', overseas_shipping -> '$."FBA仓"' )
WHERE  overseas_shipping ->> '$."4"' IS NULL;

UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_shipping = JSON_SET( JSON_REMOVE( overseas_shipping, '$."CG仓"' ), '$."5"', overseas_shipping -> '$."CG仓"' )
WHERE  overseas_shipping ->> '$."5"' IS NULL;

UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_shipping = JSON_SET( JSON_REMOVE( overseas_shipping, '$."美中仓"' ), '$."6"', overseas_shipping -> '$."美中仓"' )
WHERE  overseas_shipping ->> '$."6"' IS NULL;

UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_shipping = JSON_SET( JSON_REMOVE( overseas_shipping, '$."美东南仓"' ), '$."7"', overseas_shipping -> '$."美东南仓"' )
WHERE  overseas_shipping ->> '$."7"' IS NULL;

DROP PROCEDURE IF EXISTS remove_null_keys;
DELIMITER //

CREATE PROCEDURE remove_null_keys(IN record_id VARCHAR(50))
BEGIN
    DECLARE json_keys JSON;
    DECLARE new_json JSON;
		DECLARE value_name VARCHAR(50);
    DECLARE key_name VARCHAR(255);
    DECLARE i INT DEFAULT 0;

    -- 获取JSON字段中的所有键
    SET json_keys = (
        SELECT JSON_KEYS(overseas_shipping)
        FROM cm_replenishment_virtual_sku_purchase
        WHERE id = record_id
    );

    -- 获取JSON字段的当前值
    SET new_json = (
        SELECT overseas_shipping
        FROM cm_replenishment_virtual_sku_purchase
        WHERE id = record_id
    );

    -- 遍历所有键并删除值为NULL的键
    WHILE i < JSON_LENGTH(json_keys) DO
        SET key_name = JSON_UNQUOTE(JSON_EXTRACT(json_keys, CONCAT('$[', i, ']')));
        SET value_name = JSON_EXTRACT(new_json, CONCAT('$."', key_name, '"'));
        IF value_name = 'null' OR value_name IS NULL THEN
            SET new_json = JSON_REMOVE(new_json, CONCAT('$."', key_name, '"'));
END IF;

        SET i = i + 1;
END WHILE;

    -- 更新表中的JSON字段
UPDATE cm_replenishment_virtual_sku_purchase
SET overseas_shipping = new_json
WHERE id = record_id;
END //
DELIMITER ;

DROP PROCEDURE IF EXISTS remove_all_null_keys;
DELIMITER //

CREATE PROCEDURE remove_all_null_keys()
BEGIN
	DECLARE record_id VARCHAR(50);
	DECLARE done INT DEFAULT 0;
	 -- 声明一个游标来遍历所有记录的ID
	DECLARE cur CURSOR FOR SELECT id FROM cm_replenishment_virtual_sku_purchase;
-- 声明一个CONTINUE HANDLER来处理游标结束的情况
DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

OPEN cur;

read_loop: LOOP
	FETCH cur INTO record_id;

	IF done THEN
		LEAVE read_loop;
END IF;
CALL remove_null_keys(record_id);
END LOOP read_loop;
CLOSE cur;
END //
DELIMITER;

CALL remove_all_null_keys();

-- 修改发货数据仓库名称
UPDATE `crafts_mirror`.`cm_trial_shipping_inventory` SET `destination_warehouse` = '1', `update_date` = NOW() WHERE destination_warehouse = "美西仓";
UPDATE `crafts_mirror`.`cm_trial_shipping_inventory` SET `destination_warehouse` = '2', `update_date` = NOW() WHERE destination_warehouse = "美南仓";
UPDATE `crafts_mirror`.`cm_trial_shipping_inventory` SET `destination_warehouse` = '3', `update_date` = NOW() WHERE destination_warehouse = "美东仓";
UPDATE `crafts_mirror`.`cm_trial_shipping_inventory` SET `destination_warehouse` = '4', `update_date` = NOW() WHERE destination_warehouse = "FBA仓";
UPDATE `crafts_mirror`.`cm_trial_shipping_inventory` SET `destination_warehouse` = '5', `update_date` = NOW() WHERE destination_warehouse = "CG仓";
UPDATE `crafts_mirror`.`cm_trial_shipping_inventory` SET `destination_warehouse` = '6', `update_date` = NOW() WHERE destination_warehouse = "美中仓";
UPDATE `crafts_mirror`.`cm_trial_shipping_inventory` SET `destination_warehouse` = '7', `update_date` = NOW() WHERE destination_warehouse = "美东南仓";
