package com.sales_server.model.targetSales.listener;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.sales_server.infrastructures.entity.LogTrackNumDto;
import com.sales_server.infrastructures.listener.AbstractImportListener;
import com.sales_server.infrastructures.utils.TargetSalesLogUtils;
import com.sales_server.model.targetSales.entity.dos.TargetSalesInfoDO;
import com.sales_server.model.targetSales.repository.dataRepository.TargetSalesRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static java.math.RoundingMode.HALF_UP;

/**
 * <AUTHOR>
 * @Date 2024/11/8 17:29
 **/
public abstract class AbstractTargetSalesListener<T> extends AbstractImportListener<T> {

    protected static final Integer TOTAL_ROW_NUMBER = 50000;

    /**
     * 每隔1000条存一次数据
     */
    protected static final int BATCH_COUNT = 1000;

    protected List<TargetSalesInfoDO> salesInfoList = new ArrayList<>(BATCH_COUNT);

    protected TargetSalesRepository targetSalesRepository;

    private final Map<String, String> virtualSkuIdMap;

    protected final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);

    public AbstractTargetSalesListener(List<String> errorList, Map<String, String> virtualSkuIdMap, TargetSalesRepository targetSalesRepository) {
        super(errorList);
        this.virtualSkuIdMap = virtualSkuIdMap;
        this.targetSalesRepository = targetSalesRepository;
    }

    protected TargetSalesInfoDO convertTargetSalesInfo(String virtualSkuId, LocalDate targetDate, BigDecimal targetSales) {
        return TargetSalesInfoDO.builder()
                .virtualSkuId(virtualSkuId)
                .targetSales(targetSales.setScale(3, HALF_UP).doubleValue())
                .targetDate(targetDate)
                .build();
    }

    protected void validTotalNum(Integer approximateRowNumber) {
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }
    }

    protected BigDecimal validateDecimal(String targetStr, String virtualSku) {
        if (StrUtil.isBlank(targetStr)) {
            throw new IllegalArgumentException("目标日销不能为空");
        }
        try {
            BigDecimal bigDecimal = new BigDecimal(targetStr);
            if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                throw new IllegalArgumentException("目标日销不能为负数");
            }
            return bigDecimal;
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException(String.format("%s 虚拟sku对应的目标日销不合规，请填写正确的数字", virtualSku));
        }
    }

    protected void addSalesInfo(String virtualId, LocalDate localDate, BigDecimal targetSales) {
        salesInfoList.add(convertTargetSalesInfo(virtualId, localDate, targetSales));
    }

    protected LocalDate validateDate(String dateStr) {
        if (StrUtil.isBlank(dateStr)) {
            throw new IllegalArgumentException("日期不能为空");
        }
        // 根据年月计算出当月的天数以及目标日销
        DateTime parse = DateTime.of(dateStr, YYYY_MM_DD_DATE_FORMAT_SLASH);
        LocalDate localDate = LocalDateTimeUtil.of(parse).toLocalDate();

        LocalDate oneYearAndOneMonthLater = LocalDate.now().plusYears(1).plusMonths(1).withDayOfMonth(1);
        if (!localDate.isBefore(oneYearAndOneMonthLater)) {
            LocalDate minusMonths = oneYearAndOneMonthLater.minusMonths(1);
            throw new IllegalArgumentException(String.format("目标日销最远能填写到%s", YearMonth.from(minusMonths)));
        }
        return localDate;
    }

    protected String validateSku(String virtualSku) {
        if (StrUtil.isBlank(virtualSku)) {
            throw new IllegalArgumentException("虚拟sku为空");
        }
        String virtualId = virtualSkuIdMap.get(virtualSku);
        if (StrUtil.isEmpty(virtualId)) {
            throw new IllegalArgumentException(String.format("%s 不存在", virtualSku));
        }
        return virtualId;
    }

    protected void saveData() {
        LogTrackNumDto logTrackNumDto = new LogTrackNumDto();
        Map<String, String> map = new HashMap<>();

        Map<String, List<TargetSalesInfoDO>> collect = salesInfoList.stream()
                .collect(Collectors.groupingBy(TargetSalesInfoDO::getVirtualSkuId));
        for (var entry : collect.entrySet()) {
            String content = TargetSalesLogUtils.targetSalesLog(entry.getValue(), "导入", "目标日销");
            map.put(entry.getKey(), content);
        }

        logTrackNumDto.setLogMap(map);
        targetSalesRepository.batchSaveTargetSalesInfo(salesInfoList, logTrackNumDto);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        saveData();
    }

    protected abstract List<String> getExistedHeadList();
}