package com.inventory_server.model.redundancy.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;


@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_inventory_on_shipping_redundant_info")
public class InventoryShippingRedundantInfoDO extends PhysicalBaseEntity {

    private String shipmentCode;
    private String warehouse;
    private LocalDate enableUsingDate;
    private Integer redundantNum;
    private String onShippingWatchBoardId;
    private LocalDate startShippingDate;


}
