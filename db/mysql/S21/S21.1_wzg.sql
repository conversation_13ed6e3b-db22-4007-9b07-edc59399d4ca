ALTER TABLE `crafts_mirror`.`cm_lcl_finished_inventory`
    ADD COLUMN `shipment_code`   varchar(255) NULL COMMENT '货件号' AFTER `id`,
    ADD COLUMN `delivery_type`   int          NULL COMMENT '发货类型' AFTER `is_old_status`,
    ADD COLUMN `remarks`         varchar(500) NULL COMMENT '备注' AFTER `delivery_type`,
    ADD COLUMN `is_foreign_flag` char(1)      NULL DEFAULT '0' COMMENT '是否在途（0-不是，1-是）' AFTER `product_snapshot_id`,
    ADD COLUMN `shipping_ratio`  json         NULL COMMENT '发货比例' AFTER `is_foreign_flag`,
    ADD COLUMN `lcl_foreign_id`  varchar(50)  NULL COMMENT '在途拆分id' AFTER `shipping_ratio`,
    MODIFY COLUMN `factory_finished_date` date NULL COMMENT '工厂交货时间' AFTER `contract_code`;

ALTER TABLE `crafts_mirror`.`cm_lcl_consolidation_finished_inventory`
    ADD COLUMN `shipment_code`   varchar(255) NULL COMMENT '货件号' AFTER `id`,
    ADD COLUMN `delivery_type`   int          NULL COMMENT '发货类型' AFTER `is_old_status`,
    ADD COLUMN `remarks`         varchar(500) NULL COMMENT '备注' AFTER `delivery_type`,
    ADD COLUMN `is_foreign_flag` char(1)      NULL DEFAULT '0' COMMENT '是否在途（0-不是，1-是）' AFTER `product_snapshot_id`,
    ADD COLUMN `shipping_ratio`  json         NULL COMMENT '发货比例' AFTER `is_foreign_flag`,
    ADD COLUMN `lcl_foreign_id`  varchar(50)  NULL COMMENT '在途拆分id' AFTER `shipping_ratio`,
    MODIFY COLUMN `factory_finished_date` date NULL COMMENT '工厂交货时间' AFTER `contract_code`;

ALTER TABLE `crafts_mirror`.`cm_lcl_trial_shipping_inventory`
    ADD COLUMN `remarks` varchar(500) NULL COMMENT '备注' AFTER `is_package_full`;

ALTER TABLE `crafts_mirror`.`cm_lcl_consolidation_trial_shipping_inventory`
    ADD COLUMN `remarks` varchar(500) NULL COMMENT '备注' AFTER `is_package_full`;

ALTER TABLE `crafts_mirror`.`cm_lcl_consolidation_record`
    ADD COLUMN `head_shipping_days`       json NULL COMMENT '头程时间' AFTER `min_volume`,
    ADD COLUMN `lcl_container_start_date` date DEFAULT NULL COMMENT '排柜开始时间' AFTER `lcl_end_date`,
    ADD COLUMN `lcl_container_end_date`   date DEFAULT NULL COMMENT '排柜结束时间' AFTER `lcl_container_start_date`;

ALTER TABLE `crafts_mirror`.`cm_lcl_shipping_num_history`
    MODIFY COLUMN `change_type` varchar(255) NULL DEFAULT NULL COMMENT '变更类型';


ALTER TABLE `crafts_mirror`.`cm_delivery_foreign_inventory`
    ADD COLUMN `product_snapshot_id` varchar(50) NULL COMMENT '快照记录id' AFTER `remarks`,
    ADD COLUMN `virtual_sku_id`      varchar(50) NULL COMMENT '虚拟skuId' AFTER `product_snapshot_id`,
    ADD COLUMN `is_old_status`       char(1)     NULL COMMENT 'sku类型(0-虚拟sku 1-老sku)' AFTER `virtual_sku_id`;

ALTER TABLE `crafts_mirror`.`cm_product_snapshot`
    ADD COLUMN `spu_data` json         NULL COMMENT 'spu快照' AFTER `self_sku`,
    ADD COLUMN `buyer`    varchar(50)  NULL COMMENT '产品经理' AFTER `upgrade_type`,
    ADD COLUMN `operator` varchar(500) NULL COMMENT '运营人员' AFTER `buyer`;


CREATE TABLE cm_lcl_container_info
(
    `id`                      VARCHAR(50) COMMENT '主键ID',
    `lcl_record_id`           VARCHAR(50) NOT NULL COMMENT '拼柜计划id',
    `shipment_code`           VARCHAR(50) COMMENT '货件号',
    `shipping_start_date`     DATE        NOT NULL COMMENT '发货装柜时间',
    `source_type`             VARCHAR(50) NOT NULL COMMENT '货件来源',
    `destination_warehouse`   VARCHAR(50) NOT NULL COMMENT '目的仓',
    `address_code`            VARCHAR(20) NOT NULL COMMENT '地址代号',
    `factory_count`           INT         NOT NULL COMMENT '供应商数量',
    `shipping_quantity`       INT         NOT NULL COMMENT '发货数量',
    `shipping_volume`         DOUBLE      NOT NULL COMMENT '发货体积',
    `container_loading_rate`  DOUBLE      NOT NULL COMMENT '容器装载率(%)',
    `surplus_volume`          DOUBLE      NOT NULL COMMENT '盈余体积(m3)',
    `inspection_volume_ratio` DOUBLE      NOT NULL COMMENT '商检体积占比(%)',
    `shipping_weight`         DOUBLE      NOT NULL COMMENT '发货重量(kg)',
    `shipment_category`       VARCHAR(50) NOT NULL COMMENT '货件大类',
    `create_date`             datetime    DEFAULT NULL,
    `update_date`             datetime    DEFAULT NULL,
    `create_by`               varchar(50) DEFAULT NULL,
    `update_by`               varchar(50) DEFAULT NULL,
    `status`                  char(1)     NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci
  ROW_FORMAT = DYNAMIC COMMENT ='拼柜信息表';

CREATE TABLE `cm_lcl_container_detail`
(
    `id`                                VARCHAR(50) COMMENT '主键ID',
    `container_info_id`                 varchar(50) NOT NULL COMMENT '柜子ID',
    `consolidation_factory_finished_id` varchar(50) not null comment '整理后发货计划表id',
    `contract_code`                     varchar(50) COMMENT '合同号',
    `shipment_code`                      varchar(50) COMMENT '货件号',
    `is_old_status`                     char(1)     NOT NULL COMMENT 'sku类型(0-虚拟sku 1-老sku)',
    `shipping_start_date`               datetime    NOT NULL COMMENT '发货装柜时间',
    `delivery_type`                     varchar(32) NOT NULL COMMENT '发货类型',
    `factory_finished_date`             date COMMENT '工厂交期',
    `factory_shipping_package_num`      int COMMENT '工厂交货数量',
    `lcl_shipping_num`                  int COMMENT '发货数量',
    `factory_remain_num`                int COMMENT '未安排数量',
    `remarks`                           varchar(500) DEFAULT NULL COMMENT '计划备注',
    `detail_remarks`                    varchar(500) DEFAULT NULL COMMENT '备注',
    `is_package_full`                   char        null comment '是否装满',
    `product_snapshot_id`               varchar(50) NOT NULL comment '快照记录id',
    `create_date`                       datetime     DEFAULT NULL,
    `update_date`                       datetime     DEFAULT NULL,
    `create_by`                         varchar(50)  DEFAULT NULL,
    `update_by`                         varchar(50)  DEFAULT NULL,
    `status`                            char(1)     NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci
  ROW_FORMAT = DYNAMIC COMMENT ='拼柜详细信息表';
