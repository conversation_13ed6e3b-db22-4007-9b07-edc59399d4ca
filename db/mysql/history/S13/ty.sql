-- 发货试算海外仓库存
CREATE TABLE `cm_delivery_foreign_inventory`
(
    `id`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `create_date`         datetime                                                              DEFAULT NULL,
    `update_date`         datetime                                                              DEFAULT NULL,
    `create_by`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL,
    `update_by`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL,
    `status`              char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL DEFAULT '0',
    `enable_using_date`   date                                                         NOT NULL COMMENT '库存可用日期，精确到天',
    `warehouse_id`        varchar(50)                                                  NOT NULL COMMENT '内部仓库id',
    `shipment_code`       varchar(50)                                                           DEFAULT NULL COMMENT '货件号',
    `shipping_project_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发货计划id',
    `store_num`           int                                                          NOT NULL COMMENT '海外仓库存量',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 补货试算海外仓库存
CREATE TABLE `cm_replenishment_foreign_inventory`
(
    `id`                                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `create_date`                       datetime                                                              DEFAULT NULL,
    `update_date`                       datetime                                                              DEFAULT NULL,
    `create_by`                         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL,
    `update_by`                         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL,
    `status`                            char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL DEFAULT '0',
    `enable_using_date`                 date                                                         NOT NULL COMMENT '库存可用日期，精确到天',
    `warehouse_id`                      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内部仓库id',
    `shipment_code`                     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL COMMENT '货件号',
    `replenishment_virtual_purchase_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '补货计划id',
    `store_num`                         int                                                          NOT NULL COMMENT '海外仓库存量',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 冗余库存海外仓信息增加货件号
ALTER TABLE `crafts_mirror`.`cm_inventory_foreign_store`
    ADD COLUMN `shipment_code` varchar(50) NULL COMMENT '货件号' AFTER `inventory_info_id`;

-- 部分不需要用到的字段去除必填项，几个版本后可去除掉这些字段
ALTER TABLE `crafts_mirror`.`cm_trial_inventory_sale_destination`
    MODIFY COLUMN `foreign_inventory` json NULL COMMENT '海外仓状态（根据当前时间与库存可用时间去判断是已经到货还是在途）' AFTER `id`;

ALTER TABLE `crafts_mirror`.`cm_replenishment_project`
    MODIFY COLUMN `self_sku_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '自定义产品的skuId' AFTER `id`,
    MODIFY COLUMN `overseas_inventory` json NULL COMMENT '海外仓库存' AFTER `self_sku_id`,
    MODIFY COLUMN `overseas_shipping` json NULL COMMENT '海外仓在途' AFTER `overseas_inventory`,
    MODIFY COLUMN `local_inventory` json NULL COMMENT '本地库存' AFTER `overseas_shipping`;

ALTER TABLE `crafts_mirror`.`cm_replenishment_virtual_sku_purchase`
    MODIFY COLUMN `overseas_original_data` json NULL COMMENT '海外仓库存原始数据' AFTER `virtual_sku_id`,
    MODIFY COLUMN `overseas_inventory` json NULL COMMENT '海外仓库存' AFTER `overseas_original_data`,
    MODIFY COLUMN `overseas_shipping` json NULL COMMENT '海外仓在途' AFTER `overseas_inventory`;