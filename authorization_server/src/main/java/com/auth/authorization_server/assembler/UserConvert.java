package com.auth.authorization_server.assembler;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

import java.util.Date;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN;

/**
 * 转换工具类
 *
 * <AUTHOR>
 * @date 2021/8/26
 */

public class UserConvert {

    public static Date StringToDate(String value) {
        if (ObjectUtil.isNull(value)) {
            return null;
        }
        return DateUtil.parse(value);
    }

    public static String DateToString(Date value) {
        if (ObjectUtil.isNull(value)) {
            return null;
        }
        return DateUtil.format(value, YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN);
    }
}
