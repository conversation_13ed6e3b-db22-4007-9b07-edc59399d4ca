package com.purchase.purchase_server.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.dataObject.ShippingProjectDO;
import com.purchase.purchase_server.entity.dataObject.TrialInventorySaleDestinationDO;
import com.purchase.purchase_server.mapper.TrialInventorySaleDestinationMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Description 目标日销repository类
 * <AUTHOR>
 * @Date 2023/12/6 14:55
 **/
@Service
public class TrialInventorySaleDestinationRepositoryImpl extends ServiceImpl<TrialInventorySaleDestinationMapper, TrialInventorySaleDestinationDO> {

    public TrialInventorySaleDestinationDO getTrialInventorySaleDestination(String shippingProjectId) {
        return baseMapper.selectOne(Wrappers.<TrialInventorySaleDestinationDO>lambdaQuery()
                .eq(TrialInventorySaleDestinationDO::getShippingProjectId, shippingProjectId));
    }

    public String insertTrialInventoryAndSaleDestination(String shippingProjectId, Map<String, Double> targetSalesMap) {

        String targetSalesMapStr = JSON.toJSONString(targetSalesMap);
        // 保存海外仓库和目标日销
        TrialInventorySaleDestinationDO destinationDO = TrialInventorySaleDestinationDO.builder()
                .destinationEverydaySale(targetSalesMapStr)
                .shippingProjectId(shippingProjectId).build();
        baseMapper.insert(destinationDO);
        return destinationDO.getId();
    }

    public List<TrialInventorySaleDestinationDO> getAllForeignInventory() {
        return baseMapper.selectList(Wrappers.lambdaQuery());
    }

    public void deleteByShippingIds(List<String> shippingProjectIds) {
        if (CollectionUtil.isNotEmpty(shippingProjectIds)) {
            baseMapper.deleteByShippingProjectIds(shippingProjectIds);
        }
    }

    public List<TrialInventorySaleDestinationDO> selectByRecordId(String recordId) {
        return baseMapper.selectJoinList(TrialInventorySaleDestinationDO.class, new MPJLambdaWrapper<TrialInventorySaleDestinationDO>()
                .leftJoin(ShippingProjectDO.class, ShippingProjectDO::getId, TrialInventorySaleDestinationDO::getShippingProjectId)
                .eq(ShippingProjectDO::getShippingRecordId, recordId));
    }

    public List<TrialInventorySaleDestinationDO> select(TrialInventorySaleDestinationDO trialDO) {
        return baseMapper.selectList(new MPJLambdaWrapper<TrialInventorySaleDestinationDO>()
                .eq(TrialInventorySaleDestinationDO::getShippingProjectId, trialDO.getShippingProjectId()));
    }
}
