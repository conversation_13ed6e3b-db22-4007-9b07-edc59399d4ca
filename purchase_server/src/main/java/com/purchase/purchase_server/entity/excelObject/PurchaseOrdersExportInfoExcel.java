package com.purchase.purchase_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description excel导出补货计划-建议采购量 转换成的实体列
 * <AUTHOR>
 * @Date 2024/1/19 17:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
//excel内容居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
//excel字体大小
//@ContentFontStyle(fontHeightInPoints = 15)
public class PurchaseOrdersExportInfoExcel extends BaseExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = 399385230655376877L;
    @ExcelProperty("参考号")
    private String refNo;
    @ExcelProperty("采购单号")
    private String poCode;
    @ExcelProperty("采购状态")
    private String poStatus;
    @ExcelProperty("供应商代码")
    private String supplierCode;
    @ExcelProperty("采购总数")
    private Integer qtyEtaAll;
    @ExcelProperty("实收总数")
    private Integer qtyRecevingAll;
    @ExcelProperty("待到货量")
    private Integer pendingDeliveryQty;
    @ExcelProperty("虚拟SKU/老SKU")
    private String destinationSku;
    @ExcelProperty("采购数量")
    private Integer qtyEta;
    @ExcelProperty("工厂交期")
    private String expectedDeliveryDate;
    @ExcelProperty("未安排数量")
    private Integer expectedDeliveryQuantity;
}
