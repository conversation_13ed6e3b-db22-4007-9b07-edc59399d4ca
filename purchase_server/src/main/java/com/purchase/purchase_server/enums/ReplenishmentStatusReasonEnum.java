package com.purchase.purchase_server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ReplenishmentStatusReasonEnum {
    NORMAL(0, "正常"),
    PARENT_STANDARD_CHILD_NOT(1, "父体达标、子体未达标，按实际达标率补货"),
    PARENT_NOT_CHILD_STANDARD(2, "父体未达标、流量子体达标，按实际达标率补货"),
    PARENT_NOT(3, "父体未达标，非流量子建议不补货"),
    PARENT_CHILD_NOT(4, "父体、流量子体均未达标，建议不补货"),
    NO_NEED_REPLENISHMENT(5, "试算后无需补货"),
    STOP_SEND(6, "产品状态:停发"),
    STOP_SELL_OUT(7, "产品状态:售完停售"),
    STOP_SELL(8, "产品状态:淘汰"),
    NOT_ABANDONED(9, "存在非撤销状态采购单"),
    SHIPPING_FINISHED(10, "有本地计划库存");

    private final Integer code;
    private final String desc;

    public static ReplenishmentStatusReasonEnum ofCode(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(ReplenishmentStatusReasonEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
