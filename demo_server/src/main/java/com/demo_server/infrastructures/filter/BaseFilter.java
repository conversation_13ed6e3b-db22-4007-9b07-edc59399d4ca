package com.demo_server.infrastructures.filter;

import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.common.security.config.CachedBodyHttpServletRequest;
import jakarta.servlet.*;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;

import java.io.IOException;

/**
 * @author: 雪竹
 * @description: 基础过滤器
 * @dateTime: 2023/11/23 17:13
 **/
@Slf4j
@WebFilter(filterName = "BaseFilter", urlPatterns = {"/*"})
@Order(1)
public class BaseFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) {
        log.info("init filter");
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, <PERSON>lter<PERSON>hain filterChain) throws IOException, ServletException {
        log.info("进入过滤器========");
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String gateway = request.getHeader("gatewayKey");
        if (!"key".equals(gateway)) {
            log.error("非法访问，请通过网关访问");
            throw new IllegalAccessError("非法访问，请通过网关访问");
        }

        String contentType = request.getContentType();
        if (StrUtil.isBlank(contentType) || !contentType.startsWith("multipart/form-data")) {
            CachedBodyHttpServletRequest wrappedRequest = new CachedBodyHttpServletRequest(request);
            filterChain.doFilter(wrappedRequest, servletResponse);
        } else {
            filterChain.doFilter(request, servletResponse);
        }
    }

    @Override
    public void destroy() {
        log.info("destroy filter");
    }

}
