package com.inventory_server.model.warehouse.entity.dos;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.inventory_server.infrastructures.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.experimental.SuperBuilder;


/**
 * @Description 国家或地区信息
 * <AUTHOR>
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_country_info")
public class CountryInfoDO extends BaseEntity{

  private String countryCode;
  private String countryName;

}
