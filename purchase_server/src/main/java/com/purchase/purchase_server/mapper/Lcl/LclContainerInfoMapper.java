package com.purchase.purchase_server.mapper.Lcl;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclContainerInfoDO;
import com.purchase.purchase_server.entity.dto.Lcl.LclShipmentCategoryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/5/22
 **/
public interface LclContainerInfoMapper extends MPJBaseMapper<LclContainerInfoDO> {

    int deleteByLclRecordId(@Param("lclRecordId") String lclRecordId);

    List<LclShipmentCategoryDto> aggShipmentTypeByRecordId(@Param("lclRecordId") String lclRecordId);

    Set<String> selectAllShipmentCodeFromLclAndShipmentManagement();
}
