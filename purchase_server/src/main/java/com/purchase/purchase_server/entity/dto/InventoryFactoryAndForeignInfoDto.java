package com.purchase.purchase_server.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryFactoryAndForeignInfoDto implements Serializable {


    @Serial
    private static final long serialVersionUID = 1362787306791182325L;
    private Map<String, FactoryAndForeignInfoMapDto> factoryAndForeignInfoMap;
}
