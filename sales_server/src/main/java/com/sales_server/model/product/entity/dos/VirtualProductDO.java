package com.sales_server.model.product.entity.dos;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sales_server.infrastructures.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.JdbcType;

/**
 * @Description 虚拟sku产品
 * <AUTHOR>
 * @Date 2023/12/6 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_virtual_product")
public class VirtualProductDO extends BaseEntity {
    private String selfProductSkuId;

    private String channel;

    @TableField(jdbcType = JdbcType.BINARY)
    private String virtualSku;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String oldSku;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String spuId;

    /**
     * 子体类型
     */
    private Integer subType;

    /**
     * 产品状态
     */
    private Integer productStatus;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 运营人员
     */
    private String operator;

    /**
     * 升级款关系id
     */
    private String upgradeId;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remarks;
    /**
     * 借货策略
     */
    private Integer borrowingStrategy;
}
