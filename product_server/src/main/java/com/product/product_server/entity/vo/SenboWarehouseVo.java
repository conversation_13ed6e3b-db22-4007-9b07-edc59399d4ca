package com.product.product_server.entity.vo;

import com.product.product_server.entity.dto.SenboWarehouseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * @Description 森帛仓库返回给前端的实体类
 * <AUTHOR>
 * @Date 2024/7/4 17:00
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SenboWarehouseVo {

    List<SenboWarehouseDto> senboWarehouseList;

    List<Integer> virtualShippingRatioDtoList;
}
