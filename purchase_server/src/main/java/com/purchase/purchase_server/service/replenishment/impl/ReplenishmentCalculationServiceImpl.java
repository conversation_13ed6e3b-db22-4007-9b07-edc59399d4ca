package com.purchase.purchase_server.service.replenishment.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.DateUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.consts.SpecialDeliveryCollection;
import com.purchase.purchase_server.entity.dataObject.*;
import com.purchase.purchase_server.entity.dto.*;
import com.purchase.purchase_server.entity.dto.delivery.MockTableCalDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectBaseParamDto;
import com.purchase.purchase_server.entity.dto.delivery.TrialCalReplenishmentDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentProjectSaveDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentStatusReasonDto;
import com.purchase.purchase_server.entity.form.ReplenishmentDetailForm;
import com.purchase.purchase_server.entity.form.interior.InventoryIdSoldOutQuery;
import com.purchase.purchase_server.entity.form.interior.SoldOutDateBeforeTheoryDto;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.enums.PurchaseNumTypeEnum;
import com.purchase.purchase_server.enums.ReplenishmentStatusEnum;
import com.purchase.purchase_server.enums.ShippingSourceTypeEnum;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.model.purchase.PurchaseDayNeedNumDp;
import com.purchase.purchase_server.model.purchase.ReplenishDeliveryRangeAndNumDp;
import com.purchase.purchase_server.repository.dataRepository.*;
import com.purchase.purchase_server.repository.interiorRepository.WarehouseRepository;
import com.purchase.purchase_server.service.ICalRemainInventoryService;
import com.purchase.purchase_server.service.ICommonConsumptionService;
import com.purchase.purchase_server.service.purchaseOrder.IPurchaseOrdersService;
import com.purchase.purchase_server.service.replenishment.IReplenishmentCalculationService;
import com.purchase.purchase_server.service.replenishment.IReplenishmentOrShippingRulesService;
import com.purchase.purchase_server.service.replenishment.IReplenishmentProjectService;
import com.purchase.purchase_server.service.shipping.IShippingCalculationService;
import com.purchase.purchase_server.service.targetSales.ITargetSalesService;
import com.purchase.purchase_server.utils.commonUtils.WarehouseSortUtil;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentManager;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentMapManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateField.DAY_OF_YEAR;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.PURCHASE_CAL_REDUNDANT_DATE_NUM;
import static com.crafts_mirror.utils.constant.SystemConstant.INVENTORY_SYSTEM_SELECT_CAL_ALL_THEORETICAL;
import static com.crafts_mirror.utils.constant.SystemConstant.INVENTORY_SYSTEM_SELECT_CAL_THEORETICAL;
import static com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum.*;
import static com.purchase.purchase_server.enums.PurchaseNumTypeEnum.ADVICE;
import static com.purchase.purchase_server.enums.PurchaseNumTypeEnum.THEORETICAL;
import static com.purchase.purchase_server.enums.ReplenishmentStatusReasonEnum.PARENT_CHILD_NOT;
import static com.purchase.purchase_server.enums.ReplenishmentStatusReasonEnum.PARENT_NOT;
import static java.math.RoundingMode.CEILING;
import static java.math.RoundingMode.HALF_UP;

/**
 * @Description 补货计算服务层
 * <AUTHOR>
 * @Date 2024/1/18 10:03
 **/
@Service
@Slf4j
public class ReplenishmentCalculationServiceImpl implements IReplenishmentCalculationService {

    @Value("${spring.profiles.active}")
    private String environment;

    @Resource(name = "noRepeatableCalculationServiceImpl")
    private IShippingCalculationService shippingCalculationService;

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepository;

    @Resource
    private SelfProductRepositoryImpl selfProductRepository;

    @Resource
    private IReplenishmentProjectService replenishmentProjectService;

    @Resource(name = "replenishmentRulesService")
    private IReplenishmentOrShippingRulesService replenishmentRulesService;

    @Resource
    private ReplenishmentRulesRepositoryImpl replenishmentRulesRepository;

    @Resource
    private ICalRemainInventoryService calRemainInventoryService;

    @Resource
    private ICommonConsumptionService commonConsumptionService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IPurchaseOrdersService purchaseOrdersService;

    @Resource
    private ITargetSalesService targetSalesService;

    @Resource
    private ReplenishmentForeignInventoryRepositoryImpl foreignInventoryRepository;

    @Resource
    private ReplenishmentVirtualSkuPurchaseRepositoryImpl virtualSkuPurchaseRepository;

    @Resource
    private ProductSnapshotRepositoryImpl snapshotRepository;

    @Resource
    protected RestTemplate restTemplate;

    @Resource
    private WarehouseRepository warehouseRepository;

    @Resource
    private FactoryFinishedInventoryRepositoryImpl factoryFinishedInventoryRepository;

    @Resource
    private ReplenishmentRecordRepositoryImpl replenishmentRecordRepository;

    private static final SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD_DATE_FORMAT_SLASH);

    private static final DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void calReplenishment(List<SenboWarehouseDto> senboWarehouseList) {
        ReplenishmentMapManager instance = ReplenishmentMapManager.getInstance();
        // 规则
        var skuStockingRulesMap = instance.getStockingRulesMap();

        // 海外库存
        var skuStockQuantityMap = instance.getStockQuantityMap();

        List<VirtualProductDO> productDOList = virtualProductRepository.convertDestinationSkuToVirtualSku(skuStockingRulesMap.keySet());

        // 获取目标日销
        List<String> virtualIdList = productDOList.stream().map(VirtualProductDO::getId).toList();
        LocalDate now = LocalDate.now();
        LocalDate twoMonthBefore = now.minusMonths(2).withDayOfMonth(1);
        targetSalesService.prepareTargetSales(virtualIdList, twoMonthBefore);

        List<String> warehouseList = senboWarehouseList.stream().map(SenboWarehouseDto::getSenboWarehouseId).toList();
        Map<String, Integer> headShippingDateMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getHeadShippingDate));
        // 保存并获取备货规则
        int redundantDate = getRedundantDateNum();
        Map<String, ShippingProjectBaseParamDto> virtualSkuReplenishmentRulesMap = productDOList.parallelStream()
                .map(virtualProductDO -> {
                    ShippingProjectBaseParamDto shippingRules = replenishmentRulesService.getShippingRules(virtualProductDO, warehouseList, skuStockingRulesMap);
                    if (StrUtil.isBlank(shippingRules.getRuleId())) {
                        // 保存
                        shippingRules.setHeadShippingDays(headShippingDateMap);
                        String rulesId = replenishmentRulesRepository.saveReplenishmentRules(shippingRules, virtualProductDO, redundantDate);
                        shippingRules.setRuleId(rulesId);
                    }
                    return shippingRules;
                })
                .collect(Collectors.toMap(ShippingProjectBaseParamDto::getVirtualSku, rules -> rules));

        Map<String, String> virtualSkuAndOldSkuMap = shippingCalculationService.convertOldSkuInfoToVirtualSkuInfo(instance);
        Map<String, AchievementRateDto> achievementRate = calAchievementRateMap(productDOList);

        List<String> selfIdList = productDOList.stream().map(VirtualProductDO::getSelfProductSkuId).toList();
        List<SelfProductDO> selfProductList = selfProductRepository.getSelfProductInfoBySelfIdList(selfIdList);
        Map<String, SelfProductDO> idSelfProductMap = selfProductList.stream().collect(Collectors.toMap(SelfProductDO::getId, m -> m));
        for (VirtualProductDO virtualSkuDO : productDOList) {
            String virtualSku = virtualSkuDO.getVirtualSku();
            String virtualProductId = virtualSkuDO.getId();
            log.info("新建补货计划——————虚拟sku {} 开始计算", virtualSku);

            SelfProductDO selfProductDO = idSelfProductMap.get(virtualSkuDO.getSelfProductSkuId());
            ShippingProjectBaseParamDto shippingRules = virtualSkuReplenishmentRulesMap.get(virtualSku);
            String jsonShippingRules = JSON.toJSONString(shippingRules);

            Map<String, Double> targetSalesMap = targetSalesService.getTargetSales(virtualProductId, now);
            AchievementRateDto achievementRateDto = achievementRate.get(virtualSku);
            // 调用新提取的方法
            var result = calculateReplenishment(virtualSkuDO, selfProductDO, shippingRules, achievementRateDto,
                    virtualSkuAndOldSkuMap, skuStockQuantityMap, targetSalesMap, senboWarehouseList, headShippingDateMap,
                    THEORETICAL, redundantDate, null);
            // 如果不需要补货的话，直接计算下一件产品
            if (result == null) {
                continue;
            }
            // 打折后试算
            ReplenishmentCalculationResult discountResult;
            Map<String, Map<String, ReplenishDeliveryRangeAndNumDp>> everydayReplenishmentMap = result.everydayReplenishmentMap;
            ReplenishmentStatusReasonDto replenishmentStatusReason = replenishmentProjectService.getReplenishmentStatusReason(
                    achievementRateDto, virtualSkuDO, everydayReplenishmentMap, shippingRules, result.isTest);
            // 折后发货数据
            TrialCalReplenishmentDto discountShippingTrial;
            // 折后补货数据
            Map<String, Map<String, ReplenishDeliveryRangeAndNumDp>> discountEverydayReplenishmentMap;
            // 折后目标日销
            Map<String, Double> updatedTargetSalesMap = new HashMap<>();
            boolean needMultiply = false;
            switch (replenishmentStatusReason.getReasonEnum()) {
                case PARENT_STANDARD_CHILD_NOT, PARENT_NOT_CHILD_STANDARD -> {
                    // 目标日销*达成率
                    updatedTargetSalesMap = calculateUpdatedTargetSalesMap(targetSalesMap, achievementRateDto.getSubEntityRate().doubleValue());
                    needMultiply = true;
                    // 打折后试算
                    ShippingProjectBaseParamDto baseParam = JSON.parseObject(jsonShippingRules, ShippingProjectBaseParamDto.class);
                    discountResult = calculateReplenishment(
                            virtualSkuDO, selfProductDO, baseParam, achievementRateDto, virtualSkuAndOldSkuMap, skuStockQuantityMap,
                            updatedTargetSalesMap, senboWarehouseList, headShippingDateMap, ADVICE, redundantDate, null
                    );
                    if (discountResult != null) {
                        discountShippingTrial = JSON.to(TrialCalReplenishmentDto.class, JSON.toJSONString(discountResult.shippingTrial));
                        discountEverydayReplenishmentMap = discountResult.everydayReplenishmentMap;
                    } else {
                        discountShippingTrial = JSON.to(TrialCalReplenishmentDto.class, JSON.toJSONString(result.shippingTrial));
                        discountEverydayReplenishmentMap = new HashMap<>();
                    }
                }
                case NORMAL, NOT_ABANDONED, SHIPPING_FINISHED -> {
                    discountEverydayReplenishmentMap = everydayReplenishmentMap;
                    updatedTargetSalesMap = targetSalesMap;
                    discountShippingTrial = JSON.to(TrialCalReplenishmentDto.class, JSON.toJSONString(result.shippingTrial));
                }
                default -> {
                    discountEverydayReplenishmentMap = new HashMap<>();
                    if (replenishmentStatusReason.getReasonEnum().equals(PARENT_NOT) ||
                            replenishmentStatusReason.getReasonEnum().equals(PARENT_CHILD_NOT)) {
                        // 目标日销*达成率
                        updatedTargetSalesMap = calculateUpdatedTargetSalesMap(targetSalesMap, achievementRateDto.getSubEntityRate().doubleValue());
                        needMultiply = true;
                    }
                    discountShippingTrial = JSON.to(TrialCalReplenishmentDto.class, JSON.toJSONString(result.shippingTrial));
                }
            }

            Map<String, Map<String, ReplenishDeliveryRangeAndNumDp>> operatorEverydayReplenishmentMap = new HashMap<>();
            for (var entry : everydayReplenishmentMap.entrySet()) {
                String key = entry.getKey();
                Map<String, ReplenishDeliveryRangeAndNumDp> innerMap = new HashMap<>();
                for (String innerKey : entry.getValue().keySet()) {
                    innerMap.put(innerKey, new ReplenishDeliveryRangeAndNumDp(0D));
                }
                operatorEverydayReplenishmentMap.put(key, innerMap);
            }

            for (var entry : discountEverydayReplenishmentMap.entrySet()) {
                String key = entry.getKey();
                Map<String, ReplenishDeliveryRangeAndNumDp> innerMap = operatorEverydayReplenishmentMap.getOrDefault(key, new HashMap<>());
                for (String innerKey : entry.getValue().keySet()) {
                    innerMap.put(innerKey, new ReplenishDeliveryRangeAndNumDp(0D));
                }
                operatorEverydayReplenishmentMap.put(key, innerMap);
            }

            // 保存试算数据
            ReplenishmentProjectSaveDto projectSaveDto = ReplenishmentProjectSaveDto.builder()
                    .replenishmentTrial(result.trialCalReplenishment)
                    .shippingTrial(result.shippingTrial)
                    .isTest(result.isTest)
                    .discountShippingTrial(discountShippingTrial)
                    .selfSku(selfProductDO.getSku())
                    .selfProductInfo(selfProductDO)
                    .everydayReplenishmentMap(everydayReplenishmentMap)
                    .discountEverydayReplenishmentMap(discountEverydayReplenishmentMap)
                    .operatorEverydayReplenishmentMap(operatorEverydayReplenishmentMap)
                    .shippingRules(shippingRules)
                    .virtualSku(virtualSku)
                    .virtualSkuAndOldSkuMap(virtualSkuAndOldSkuMap)
                    .targetSalesMap(targetSalesMap)
                    .updatedTargetSalesMap(updatedTargetSalesMap)
                    .needMultiply(needMultiply)
                    .build();
            replenishmentProjectService.saveReplenishmentProjectInfo(projectSaveDto, virtualSkuDO, achievementRateDto);
        }
    }

    private Map<String, Double> calculateUpdatedTargetSalesMap(Map<String, Double> targetSalesMap, double subEntityRate) {
        BigDecimal rate = BigDecimal.valueOf(subEntityRate);
        return targetSalesMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            Double originalValue = entry.getValue();
                            if (originalValue != null && originalValue != 0) {
                                // 保留三位小数
                                return BigDecimal.valueOf(originalValue)
                                        .multiply(rate)
                                        .setScale(3, RoundingMode.HALF_UP)
                                        .doubleValue();
                            } else {
                                return 0.0;
                            }
                        }
                ));
    }

    private ReplenishmentCalculationResult calculateReplenishment(
            VirtualProductDO virtualSkuDO, SelfProductDO selfProductDO, ShippingProjectBaseParamDto shippingRules,
            AchievementRateDto achievementRate, Map<String, String> virtualSkuAndOldSkuMap,
            Map<String, List<FactoryRemainInventoryDto>> skuStockQuantityMap, Map<String, Double> targetSalesMap,
            List<SenboWarehouseDto> senboWarehouseList, Map<String, Integer> headShippingDateMap, PurchaseNumTypeEnum purchaseNumType,
            int redundantDate, ReplenishmentVirtualSkuPurchaseDO virRepDO) {

        var virtualSku = virtualSkuDO.getVirtualSku();
        var shippingRatioMap = shippingRules.getShippingRatio();
        var importFactoryRemainInventoryList = new ArrayList<>(skuStockQuantityMap.getOrDefault(virtualSku, new ArrayList<>()));

        Map<String, Double> sortedTargetSalesMap = new TreeMap<>(Comparator.comparing(c -> LocalDate.parse(c, dateFormat)));
        sortedTargetSalesMap.putAll(targetSalesMap);

        // 获取计划里发货后的模拟销售数据
        shippingRules.setContainLoader(selfProductDO.getContainerLoad());
        var trialCalReplenishment = calDeliveryResult(virtualSkuDO, shippingRules, achievementRate, virtualSkuAndOldSkuMap,
                sortedTargetSalesMap, purchaseNumType, senboWarehouseList, redundantDate, virRepDO, selfProductDO);
        // 如果不需要补货的话，直接返回null
        if (trialCalReplenishment == null) {
            return null;
        }

        List<SoldOutDateBeforeTheoryDto> soldOutDateBeforeTheoryList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(trialCalReplenishment.getSoldOutDateBeforeTheoryList())) {
            soldOutDateBeforeTheoryList.addAll(trialCalReplenishment.getSoldOutDateBeforeTheoryList());
        }
        LocalDate fullLinkTheoreticalSoldOutDate = trialCalReplenishment.getFullLinkTheoreticalSoldOutDate();

        shippingRules.setSafeDays(shippingRules.getSafeDays() + redundantDate);
        String shippingTrialJson = JSON.toJSONString(trialCalReplenishment);
        TrialCalReplenishmentDto shippingTrial = JSON.parseObject(shippingTrialJson, TrialCalReplenishmentDto.class);

        // 计划里面的发货数据
        List<FactoryRemainInventoryDto> shippingInventoryList = trialCalReplenishment.getShippingInventoryList()
                .stream().map(ObjectUtil::clone).collect(Collectors.toList());
//        shippingInventoryList.addAll(trialCalReplenishment.getPriorDeliveryList());
        DateTime replenishmentCreateDate = ReplenishmentManager.getInstance().getCreateTime();
        DateTime maxSaleDate = targetSalesMap.keySet().stream()
                .map(DateUtil::parse)
                .max(Comparator.comparing(k -> k))
                .orElse(DateUtil.endOfMonth(replenishmentCreateDate));

        long between = DateUtil.between(replenishmentCreateDate, maxSaleDate, DateUnit.DAY);

        Map<String, Integer> sortedMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSort));
        List<String> sortedWarehouseList = WarehouseSortUtil.getSortedWarehouseList(shippingRules.getShippingRatio(), sortedMap);

        int productStatus = virtualSkuDO.getProductStatus();
        if (productStatus == NEW_ARRIVAL_TEST_SAMPLE.getCode()) {
            return calTestProductSales(selfProductDO, sortedTargetSalesMap, replenishmentCreateDate, importFactoryRemainInventoryList,
                    fullLinkTheoreticalSoldOutDate, shippingInventoryList, between, shippingTrial, headShippingDateMap,
                    shippingRatioMap, virtualSku, sortedWarehouseList, soldOutDateBeforeTheoryList);
        } else if (productStatus == NEW_ARRIVAL_NORMAL_SAMPLE.getCode()) {
            ReplenishmentMapManager instance = ReplenishmentMapManager.getInstance();
            var skuDeliveryMap = instance.getFactoryPlanMap();

            List<FactoryFinishedInventoryDto> finishedInventoryList = skuDeliveryMap.getOrDefault(virtualSku, new ArrayList<>());
            List<FactoryFinishedInventoryDto> maybeShippingFinishedInventoryList = finishedInventoryList.stream()
                    .filter(m -> maxSaleDate.offsetNew(DAY_OF_YEAR, -shippingRules.getTransitDays()).compareTo(m.getFactoryFinishedDate()) >= 0)
                    .toList();
            if (CollectionUtil.isEmpty(maybeShippingFinishedInventoryList)) {
                String virtualSkuId = virtualSkuDO.getId();
                Integer notAbandonedCount = purchaseOrdersService.getNotAbandonedDetail(virtualSkuId);

                // 在既无计划，也无非撤销状态采购单的情况下，新品不断货产品需要按照新品测款补货，否则按照正常品补货
                if (notAbandonedCount == null || notAbandonedCount == 0) {
                    return calTestProductSales(selfProductDO, sortedTargetSalesMap, replenishmentCreateDate,
                            importFactoryRemainInventoryList, fullLinkTheoreticalSoldOutDate, shippingInventoryList, between,
                            shippingTrial, headShippingDateMap, shippingRatioMap, virtualSku, sortedWarehouseList,
                            soldOutDateBeforeTheoryList);
                }
            }
            return calculateNormalProductSales(selfProductDO, shippingRules, targetSalesMap, maxSaleDate, importFactoryRemainInventoryList,
                    shippingInventoryList, trialCalReplenishment, between, headShippingDateMap, virtualSku, shippingTrial, sortedWarehouseList);
        } else {
            return calculateNormalProductSales(selfProductDO, shippingRules, targetSalesMap, maxSaleDate, importFactoryRemainInventoryList,
                    shippingInventoryList, trialCalReplenishment, between, headShippingDateMap, virtualSku, shippingTrial, sortedWarehouseList);
        }
    }

    private ReplenishmentCalculationResult calTestProductSales(
            SelfProductDO selfProductDO, Map<String, Double> sortedTargetSalesMap, DateTime replenishmentCreateDate,
            List<FactoryRemainInventoryDto> importFactoryRemainInventoryList, LocalDate fullLinkTheoreticalSoldOutDate,
            List<FactoryRemainInventoryDto> shippingInventoryList, long between, TrialCalReplenishmentDto shippingTrial,
            Map<String, Integer> headShippingDateMap, Map<String, Double> shippingRatioMap, String virtualSku,
            List<String> sortedWarehouseList, List<SoldOutDateBeforeTheoryDto> soldOutDateBeforeTheoryList
    ) {
        Map<String, Map<String, ReplenishDeliveryRangeAndNumDp>> everydayReplenishmentMap = new HashMap<>();
        List<FactoryRemainInventoryDto> replenishmentRemainInventoryList = new ArrayList<>();
        // 计算测款的补货量
        Map<String, ReplenishDeliveryRangeAndNumDp> replenishmentMap = calculateTestProductSalesSum(sortedTargetSalesMap,
                shippingRatioMap, selfProductDO.getContainerLoad(), virtualSku, replenishmentCreateDate);
        everydayReplenishmentMap.put(replenishmentCreateDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH), replenishmentMap);

        int minShippingDate = headShippingDateMap.entrySet().stream()
                .filter(h -> shippingRatioMap.containsKey(h.getKey()) && shippingRatioMap.get(h.getKey()) > 0)
                .map(Map.Entry::getValue)
                .sorted().findFirst().orElseThrow(() -> new NullPointerException("头程时间为空"));

        // 计算测款的模拟日销表格
        List<FactoryRemainInventoryDto> tempRemainInventoryListForCal = new ArrayList<>(importFactoryRemainInventoryList);
        MockTableCalDto mockTableCalDto = new MockTableCalDto(shippingInventoryList, (int) between,
                replenishmentRemainInventoryList, headShippingDateMap, replenishmentCreateDate, tempRemainInventoryListForCal,
                importFactoryRemainInventoryList, shippingTrial.getPriorDeliveryList(), minShippingDate, sortedTargetSalesMap, shippingRatioMap, sortedWarehouseList);
        TrialCalReplenishmentDto trialCalReplenishment = commonConsumptionService.calDeliveryResults(mockTableCalDto);
        trialCalReplenishment.setFullLinkTheoreticalSoldOutDate(fullLinkTheoreticalSoldOutDate);
        trialCalReplenishment.setSoldOutDateBeforeTheoryList(soldOutDateBeforeTheoryList);
        return new ReplenishmentCalculationResult(trialCalReplenishment, shippingTrial, everydayReplenishmentMap, true);
    }

    private ReplenishmentCalculationResult calculateNormalProductSales(
            SelfProductDO selfProductDO, ShippingProjectBaseParamDto shippingRules, Map<String, Double> targetSalesMap,
            DateTime maxSaleDate, List<FactoryRemainInventoryDto> importFactoryRemainInventoryList,
            List<FactoryRemainInventoryDto> shippingInventoryList, TrialCalReplenishmentDto trialCalReplenishment, long between,
            Map<String, Integer> headShippingDateMap, String virtualSku, TrialCalReplenishmentDto shippingTrial,
            List<String> sortedWarehouseList) {
        List<SoldOutDateBeforeTheoryDto> soldOutDateBeforeTheoryList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(trialCalReplenishment.getSoldOutDateBeforeTheoryList())) {
            soldOutDateBeforeTheoryList.addAll(trialCalReplenishment.getSoldOutDateBeforeTheoryList());
        }
        LocalDate fullLinkTheoreticalSoldOutDate = trialCalReplenishment.getFullLinkTheoreticalSoldOutDate();
        // 获取各仓最早到货日期
        String purchaseDate = selfProductDO.getPurchaseDate();
        Set<String> targetSalesMonthBeginSet = targetSalesMap.keySet().stream()
                .map(m -> DateUtil.beginOfMonth(DateTime.of(m, YYYY_MM_DD_DATE_FORMAT_SLASH)).toString(YYYY_MM_DD_DATE_FORMAT_SLASH))
                .collect(Collectors.toSet());
        Map<String, DateTime> fastestArrivingDateMap = calAllWarehouseFastestArrivingDate(shippingRules, purchaseDate,
                targetSalesMonthBeginSet);
        int containerLoad = selfProductDO.getContainerLoad();
        var shippingRatioMap = shippingRules.getShippingRatio();

        int minShippingDate = headShippingDateMap.entrySet().stream()
                .filter(h -> shippingRatioMap.containsKey(h.getKey()) && shippingRatioMap.get(h.getKey()) > 0)
                .map(Map.Entry::getValue)
                .sorted().findFirst().orElseThrow(() -> new NullPointerException("头程时间为空"));

        DateTime replenishmentCreateDate = ReplenishmentManager.getInstance().getCreateTime();
        // 从所有仓的最早到货日开始试算，到目标日销的最后一天截止
        DateTime dateTime = fastestArrivingDateMap.values().stream().min(DateTime::compareTo).orElse(null);
        Map<String, DateTime> lastTimeArrivedDateMap = new HashMap<>();
        var replenishmentRemainInventoryList = new ArrayList<FactoryRemainInventoryDto>();
        Map<String, Map<String, ReplenishDeliveryRangeAndNumDp>> everydayReplenishmentMap = new HashMap<>();

        // 根据生产交期计算出一份下单月份-发货时间的映射关系
        Map<DateTime, Set<DateTime>> factoryStartDateAndDeliveryDateSetMap = calFactoryStartDateAndDeliveryDateSetMap(
                targetSalesMonthBeginSet, purchaseDate, shippingRules, replenishmentCreateDate
        );

        if (dateTime != null) {
            for (DateTime calDate = new DateTime(dateTime);
                 !calDate.isAfter(maxSaleDate);
                 calDate.offset(DAY_OF_YEAR, 1)) {
                String calDateStr = calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
                List<FactoryRemainInventoryDto> allOnShippingInventoryList = new ArrayList<>(replenishmentRemainInventoryList);
                allOnShippingInventoryList.addAll(importFactoryRemainInventoryList);
                allOnShippingInventoryList.addAll(shippingInventoryList);

                // 获取哪些仓库已经到过货了，这些仓库在之后计算安全库存以及实际日销时都当做有货处理
                Set<String> everArrivedWarehouse = fastestArrivingDateMap.entrySet().stream()
                        .filter(f -> f.getValue().isBeforeOrEquals(calDate))
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toSet());

                Map<String, Map<String, Double>> everydayRemainMap = trialCalReplenishment.getEverydayRemainInventoryMap();
                Map<String, Double> warehouseRemainMap = everydayRemainMap.getOrDefault(calDateStr, new HashMap<>());

                // 计算剩余库存
                double totalRemainInventory = warehouseRemainMap.values().stream().mapToDouble(m -> m).sum();
                // 计算总仓安全库存
                double generalSafeInventory = calGeneralSafeInventory(shippingRules.getSafeDays(), calDate, targetSalesMap);

                // 总仓剩余库存大于总仓安全库存，直接计算下一天
                if (generalSafeInventory <= 0 || totalRemainInventory > generalSafeInventory) {
                    continue;
                }

                // 计算能到货仓的安全库存
                for (String warehouse : everArrivedWarehouse) {
                    everydayRemainMap = trialCalReplenishment.getEverydayRemainInventoryMap();
                    warehouseRemainMap = everydayRemainMap.getOrDefault(calDateStr, new HashMap<>());
                    List<FactoryRemainInventoryDto> tempList = new ArrayList<>(shippingInventoryList);
                    tempList = tempList.stream()
                            .filter(f -> !f.getWarehouse().equals(warehouse) || f.getEnableUsingDate().after(calDate))
                            .collect(Collectors.toList());
                    for (var entry : warehouseRemainMap.entrySet()) {
                        if (entry.getKey().contains("start")) {
                            continue;
                        }
                        String tempWarehouse = entry.getKey();
                        DateTime arrivingDate = fastestArrivingDateMap.get(tempWarehouse);
                        if (arrivingDate != null && !tempWarehouse.equals(warehouse)) {
                            // 若不是待计算的仓库，且可以到货，将剩余库存补满，模拟一直有货的情况
                            double remainInventory = Integer.MAX_VALUE;
                            tempList.add(FactoryRemainInventoryDto.builder()
                                    .storeNum(remainInventory)
                                    .warehouse(tempWarehouse)
                                    .enableUsingDate(arrivingDate)
                                    .build());
                        }

                        if (tempWarehouse.equals(warehouse)) {
                            tempList.add(FactoryRemainInventoryDto.builder()
                                    .storeNum(warehouseRemainMap.getOrDefault(warehouse, 0D))
                                    .warehouse(tempWarehouse)
                                    .enableUsingDate(calDate)
                                    .build());
                        }
                    }

                    List<FactoryRemainInventoryDto> remainInventoryListForCal = new ArrayList<>(importFactoryRemainInventoryList);
                    remainInventoryListForCal = remainInventoryListForCal.stream().filter(f -> !f.getWarehouse().equals(warehouse)).collect(Collectors.toList());

                    List<FactoryRemainInventoryDto> priorDeliveryList = shippingTrial.getPriorDeliveryList();
                    MockTableCalDto mockTableCalDto = new MockTableCalDto(tempList, (int) between, new ArrayList<>(),
                            headShippingDateMap, replenishmentCreateDate, remainInventoryListForCal, importFactoryRemainInventoryList,
                            priorDeliveryList, minShippingDate, targetSalesMap, shippingRatioMap, sortedWarehouseList);
                    trialCalReplenishment = commonConsumptionService.calDeliveryResults(mockTableCalDto);

                    ReplenishmentCalculationLogDO logDO = new ReplenishmentCalculationLogDO("补货试算", virtualSku, warehouse);

                    everydayRemainMap = trialCalReplenishment.getEverydayRemainInventoryMap();
                    warehouseRemainMap = everydayRemainMap.getOrDefault(calDateStr, new HashMap<>());

                    double safeInventory = calSafeInventory(shippingRules, targetSalesMap, warehouse, fastestArrivingDateMap,
                            calDate, everydayRemainMap, allOnShippingInventoryList);
                    if (safeInventory <= 0) {
                        List<FactoryRemainInventoryDto> tempRemainInventoryListForCal = new ArrayList<>(importFactoryRemainInventoryList);
                        MockTableCalDto mockTableCalDto1 = new MockTableCalDto(shippingInventoryList, (int) between,
                                replenishmentRemainInventoryList, headShippingDateMap, replenishmentCreateDate, tempRemainInventoryListForCal,
                                importFactoryRemainInventoryList, priorDeliveryList, minShippingDate, targetSalesMap, shippingRatioMap, sortedWarehouseList);
                        trialCalReplenishment = commonConsumptionService.calDeliveryResults(mockTableCalDto1);
                        continue;
                    }
                    double remainInventory = warehouseRemainMap.getOrDefault(warehouse, 0D);

                    PurchaseDayNeedNumDp dayNeedNumDp = new PurchaseDayNeedNumDp();
                    StringBuilder sb = new StringBuilder("到货日期为：").append(calDateStr).append("；")
                            .append("安全库存为").append(roundUpToThreeDecimal(safeInventory));
                    int i = 0;
                    String replenishmentRange = "";
                    while (remainInventory + dayNeedNumDp.getTotalNeedNum() + dayNeedNumDp.getShippingNum() <= safeInventory) {
                        // 计算补货量区间的开始时间
                        DateTime lastTimeArrivedDate = lastTimeArrivedDateMap.getOrDefault(warehouse, new DateTime(calDate));
                        lastTimeArrivedDate = lastTimeArrivedDate.isBefore(calDate) ? new DateTime(calDate) : lastTimeArrivedDate;
                        warehouseRemainMap = everydayRemainMap.getOrDefault(lastTimeArrivedDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH), new HashMap<>());

                        DateTime replenishmentNumStartDate = commonConsumptionService.calReplenishmentOrDeliveryNumStartDate(
                                lastTimeArrivedDate, allOnShippingInventoryList, warehouse, fastestArrivingDateMap,
                                targetSalesMap, maxSaleDate, warehouseRemainMap, replenishmentCreateDate, shippingRules,
                                sortedWarehouseList, priorDeliveryList);

                        Integer purchaseCircle = shippingRules.getPurchaseCircle();
                        lastTimeArrivedDateMap.put(warehouse, replenishmentNumStartDate.offsetNew(DAY_OF_YEAR, purchaseCircle));
                        if (replenishmentNumStartDate.isAfter(maxSaleDate)) {
                            break;
                        }
                        DateTime lastTime = lastTimeArrivedDateMap.get(warehouse);
                        lastTime = lastTime.isAfter(maxSaleDate) ? maxSaleDate.offsetNew(DAY_OF_YEAR, 1) : lastTime;
                        sb.append("；剩余库存为").append(remainInventory + dayNeedNumDp.getTotalNeedNum()).append("；第 ")
                                .append(++i).append(" 次补货。").append("补货量区间为：").append(replenishmentNumStartDate)
                                .append("~").append(lastTime).append("。");
                        // 计算补货量
                        Map<String, Double> shippingRatio = shippingRules.getShippingRatio();
                        PurchaseDayNeedNumDp tempNeedNumDp = commonConsumptionService.calNeedReplenishmentNum(everydayRemainMap,
                                targetSalesMap, purchaseCircle, shippingRatio, warehouse, replenishmentNumStartDate,
                                fastestArrivingDateMap, allOnShippingInventoryList, priorDeliveryList);
                        dayNeedNumDp.addPurchaseDayNeedNumDp(tempNeedNumDp);
                        sb.append("在补货量区间内有 ").append(tempNeedNumDp.getShippingNum()).append(" 在途到货被抵消；")
                                .append("补货量为：").append(tempNeedNumDp.getTotalNeedNum()).append("；");
                        // 计算补货量区间
                        String range = replenishmentNumStartDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH) + "~" + lastTime.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
                        replenishmentRange = ReplenishDeliveryRangeAndNumDp.addRange(replenishmentRange, range);
                    }

                    if (dayNeedNumDp.getTotalNeedNum() > 0) {
                        double needNum = dayNeedNumDp.getTotalNeedNum();
                        sb.append("总补货量为").append(needNum).append("；");
                        needNum = needNum % containerLoad == 0 ? needNum : needNum + (containerLoad - needNum % containerLoad);
                        sb.append("凑整箱后总补货量为").append(needNum).append("；");
                        if (!environment.equals("prod")) {
                            logDO.setInfo(sb.toString());
                            replenishmentRulesRepository.insertLogInfo(logDO);
                        }
                        // 根据到货时间反推出下单时间
                        int headShippingDate = headShippingDateMap.get(warehouse);
                        DateTime deliveryStartDate = calDate.offsetNew(DAY_OF_YEAR, -headShippingDate);

                        // 获取工厂开工时间
                        DateTime factoryStartDate = factoryStartDateAndDeliveryDateSetMap.entrySet().stream()
                                .filter(f -> f.getValue().contains(deliveryStartDate))
                                .min(Map.Entry.comparingByKey())
                                .map(m -> {
                                    DateTime targetMonth = m.getKey();
                                    List<String> produceDays = Arrays.asList(purchaseDate.split(","));
                                    int produceDay = replenishmentProjectService.getTargetDateFactoryProduceDays(produceDays, targetMonth, 0);
                                    return deliveryStartDate.offsetNew(DAY_OF_YEAR, -produceDay - shippingRules.getTransitDays());
                                })
                                .orElse(null);

                        if (factoryStartDate == null) {
                            // 若获取不到对应的工厂开工时间，则往前找离该次补货最近的开工时间
                            factoryStartDate = factoryStartDateAndDeliveryDateSetMap.entrySet().stream()
                                    .filter(entry -> entry.getValue().stream().anyMatch(startDate -> startDate.isBefore(deliveryStartDate)))
                                    .max(Map.Entry.comparingByKey())
                                    .map(entry -> DateUtil.endOfMonth(entry.getKey()))
                                    .orElse(null);
                            // 不可干预时间，因此该次补货无法完成
                            if (factoryStartDate == null) {
                                continue;
                            }
                        }
                        int purchaseProjectDays = shippingRules.getPurchaseProjectDays();
                        DateTime orderDate = factoryStartDate.offsetNew(DAY_OF_YEAR, -purchaseProjectDays);
                        String orderDateStr = orderDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);

                        Map<String, ReplenishDeliveryRangeAndNumDp> todayReplenishmentMap = everydayReplenishmentMap.getOrDefault(orderDateStr, new HashMap<>(8));
                        double finalNeedNum = needNum;
                        String finalReplenishmentRange = replenishmentRange;
                        todayReplenishmentMap.compute(warehouse, (k, v) -> {
                            ReplenishDeliveryRangeAndNumDp numDp = Optional.ofNullable(v).orElse(new ReplenishDeliveryRangeAndNumDp(0D));
                            numDp.addNum(finalNeedNum);
                            numDp.mergeAllDateRanges(finalReplenishmentRange);
                            return numDp;
                        });
                        everydayReplenishmentMap.put(orderDateStr, todayReplenishmentMap);

                        // 将补货数据转化成发货数据，带入到下一次的补货试算中去
                        List<String> produceDays = Arrays.asList(purchaseDate.split(","));
                        int produceDay = replenishmentProjectService.getTargetDateFactoryProduceDays(produceDays, orderDate, purchaseProjectDays);
                        replenishmentRemainInventoryList.add(FactoryRemainInventoryDto.builder()
                                .warehouse(warehouse)
                                .storeNum(needNum)
                                .enableUsingDate(orderDate.offsetNew(DAY_OF_YEAR, purchaseProjectDays + produceDay + shippingRules.getTransitDays() + headShippingDate))
                                .build());
                    }

                    List<FactoryRemainInventoryDto> tempRemainInventoryListForCal = new ArrayList<>(importFactoryRemainInventoryList);
                    MockTableCalDto mockTableCalDto1 = new MockTableCalDto(shippingInventoryList, (int) between,
                            replenishmentRemainInventoryList, headShippingDateMap, replenishmentCreateDate, tempRemainInventoryListForCal,
                            importFactoryRemainInventoryList, priorDeliveryList, minShippingDate, targetSalesMap, shippingRatioMap, sortedWarehouseList);
                    trialCalReplenishment = commonConsumptionService.calDeliveryResults(mockTableCalDto1);
                }
            }
        }

        trialCalReplenishment.setSoldOutDateBeforeTheoryList(soldOutDateBeforeTheoryList);
        trialCalReplenishment.setFullLinkTheoreticalSoldOutDate(fullLinkTheoreticalSoldOutDate);
        return new ReplenishmentCalculationResult(trialCalReplenishment, shippingTrial, everydayReplenishmentMap, false);
    }

    private Map<String, ReplenishDeliveryRangeAndNumDp> calculateTestProductSalesSum(Map<String, Double> targetSalesMap,
                                                                                     Map<String, Double> shippingRatioMap,
                                                                                     int containerLoad, String virtualSku,
                                                                                     DateTime replenishmentCreateDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);
        LocalDate repCreateDate = DateUtils.convertToLocalDate(replenishmentCreateDate);
        Optional<Map.Entry<String, Double>> first = targetSalesMap.entrySet().stream()
                .filter(entry -> !LocalDate.parse(entry.getKey(), formatter).isBefore(repCreateDate) && entry.getValue() > 0) // 更精确的零值判断
                .findFirst();
        if (first.isEmpty()) {
            return new HashMap<>(0);
        }
        LocalDate startDate = LocalDate.parse(first.get().getKey(), formatter);
        LocalDate endDate = startDate.plusMonths(2);
        String range = startDate.format(formatter) + "~" + endDate.format(formatter);

        double totalReplenishment = targetSalesMap.entrySet().stream()
                .filter(entry -> !LocalDate.parse(entry.getKey(), formatter).isBefore(startDate) && LocalDate.parse(entry.getKey(), formatter).isBefore(endDate))
                .map(entry -> BigDecimal.valueOf(entry.getValue()))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(3, HALF_UP)
                .doubleValue();

        // 计算总和
        Map<String, ReplenishDeliveryRangeAndNumDp> replenishmentMap = new HashMap<>(shippingRatioMap.size());
        for (var entry : shippingRatioMap.entrySet()) {
            Double ratio = entry.getValue();
            if (ratio == null || ratio <= 0) {
                continue;
            }
            StringBuilder sb = new StringBuilder();
            BigDecimal needNum = BigDecimal.valueOf(totalReplenishment).multiply(BigDecimal.valueOf(ratio));
            sb.append(" 补货量为：").append(needNum).append("；");
            double needNumDouble = needNum.setScale(0, CEILING).doubleValue();
            sb.append(" 凑整后补货量为：").append(needNumDouble).append("；");
            needNumDouble = needNumDouble % containerLoad == 0 ? needNumDouble : needNumDouble + (containerLoad - needNumDouble % containerLoad);
            sb.append("凑整箱后补货量为：").append(needNumDouble);
            ReplenishDeliveryRangeAndNumDp numDp = new ReplenishDeliveryRangeAndNumDp(needNumDouble);
            replenishmentMap.put(entry.getKey(), numDp);
            numDp.mergeAllDateRanges(range);

            if (!environment.equals("prod")) {
                ReplenishmentCalculationLogDO logDO = new ReplenishmentCalculationLogDO("测款产品补货", virtualSku, entry.getKey());
                logDO.setInfo(sb.toString());
                replenishmentRulesRepository.insertLogInfo(logDO);
            }
        }
        return replenishmentMap;
    }

    // 使用Record替代内部类
    private record ReplenishmentCalculationResult(
            TrialCalReplenishmentDto trialCalReplenishment,
            TrialCalReplenishmentDto shippingTrial,
            Map<String, Map<String, ReplenishDeliveryRangeAndNumDp>> everydayReplenishmentMap,
            Boolean isTest
    ) {
    }


    private Map<DateTime, Set<DateTime>> calFactoryStartDateAndDeliveryDateSetMap(Set<String> targetSalesMonthSet, String produceDaysStr,
                                                                                  ShippingProjectBaseParamDto shippingRules,
                                                                                  DateTime replenishmentCreateDate) {
        int transitDays = shippingRules.getTransitDays();
        int purchaseProjectDay = shippingRules.getPurchaseProjectDays();
        List<String> produceDays = Arrays.asList(produceDaysStr.split(","));
        Map<DateTime, Set<DateTime>> factoryStartDateAndDeliveryDateSetMap = new TreeMap<>(Comparator.comparing(c -> c));

        for (String targetMonthStr : targetSalesMonthSet) {
            DateTime targetStart = DateUtil.parse(targetMonthStr, YYYY_MM_DD_DATE_FORMAT_SLASH);
            DateTime targetEnd = DateUtil.endOfMonth(targetStart);
            Set<DateTime> deliveryStartDateSet = new HashSet<>(32);
            // 当天开始生产的生产交期
            int produceDay = replenishmentProjectService.getTargetDateFactoryProduceDays(produceDays, targetStart, 0);

            for (DateTime calDate = new DateTime(targetMonthStr); calDate.isBeforeOrEquals(targetEnd); calDate.offset(DAY_OF_YEAR, 1)) {
                if (calDate.isBefore(replenishmentCreateDate.offsetNew(DAY_OF_YEAR, purchaseProjectDay))) {
                    // 补货开始试算当天无法达到的生产开始时间都去除掉
                    continue;
                }
                DateTime deliveryStartDate = calDate.offsetNew(DAY_OF_YEAR, produceDay + transitDays);
                deliveryStartDateSet.add(deliveryStartDate);
            }
            factoryStartDateAndDeliveryDateSetMap.put(targetStart, deliveryStartDateSet);
        }

        return factoryStartDateAndDeliveryDateSetMap;
    }

    /**
     * 目标达成率
     *
     * @param productDOList 虚拟商品列表
     * @return 各虚拟sku对应的目标达成率
     */
    private Map<String, AchievementRateDto> calAchievementRateMap(List<VirtualProductDO> productDOList) {
        Map<String, Map<String, List<VirtualProductDO>>> virtualBySpuIdMap = productDOList.stream()
                .collect(Collectors.groupingBy(VirtualProductDO::getSpuId,
                        // 第二个分组依据：channel
                        Collectors.groupingBy(VirtualProductDO::getChannel)
                ));

        Map<String, AchievementRateDto> achievementRate = new HashMap<>();
        ReplenishmentMapManager instance = ReplenishmentMapManager.getInstance();
        HashMap<String, ActualDailySalesDto> actualDailySalesMap = instance.getActualDailySalesMap();
        for (var virtualEntry : virtualBySpuIdMap.entrySet()) {
            if (!environment.equals("prod")) {
                replenishmentRulesRepository.insertLogInfo(JSON.toJSONString(virtualEntry), "补货导入数据-spu分组");
            }
            Map<String, List<VirtualProductDO>> virtualProducts = virtualEntry.getValue();
            virtualProducts.forEach((channel, virtualProductList) -> {
                BigDecimal actualDailySalesSum = BigDecimal.ZERO;
                BigDecimal targetSalesSum = BigDecimal.ZERO;
                Map<String, BigDecimal> actualDailySalesMapSum = new HashMap<>();
                Map<String, BigDecimal> targetSalesMapSum = new HashMap<>();

                for (VirtualProductDO virtualProductDO : virtualProductList) {
                    String virtualSku = virtualProductDO.getVirtualSku();
                    ActualDailySalesDto actualDailySalesDto = actualDailySalesMap.get(virtualSku);
                    BigDecimal actualDailySalesNum = BigDecimal.valueOf(actualDailySalesDto.getActualDailySalesNum());
                    BigDecimal targetSalesNum = calCurrentMonthTargetSales(virtualProductDO);
                    actualDailySalesDto.setTargetSalesNum(targetSalesNum.doubleValue());
                    actualDailySalesSum = actualDailySalesSum.add(actualDailySalesNum);
                    targetSalesSum = targetSalesSum.add(targetSalesNum);

                    actualDailySalesMapSum.put(virtualSku, actualDailySalesNum);
                    targetSalesMapSum.put(virtualSku, targetSalesNum);
                }
                BigDecimal totalResult = targetSalesSum.compareTo(BigDecimal.ZERO) > 0 ?
                        actualDailySalesSum.divide(targetSalesSum, 3, HALF_UP) :
                        BigDecimal.ZERO;
                BigDecimal max = BigDecimal.ONE;
                if (totalResult.compareTo(max) > 0) {
                    totalResult = max;
                }
                for (VirtualProductDO virtualProductDO : virtualProductList) {
                    String virtualSku = virtualProductDO.getVirtualSku();
                    BigDecimal actualDailySalesNum = actualDailySalesMapSum.get(virtualSku);
                    BigDecimal targetSalesNum = targetSalesMapSum.get(virtualSku);

                    BigDecimal subEntityRate = targetSalesNum.compareTo(BigDecimal.ZERO) > 0 ?
                            actualDailySalesNum.divide(targetSalesNum, 3, HALF_UP) :
                            BigDecimal.ZERO;
                    if (subEntityRate.compareTo(max) > 0) {
                        subEntityRate = max;
                    }

                    AchievementRateDto build = AchievementRateDto.builder()
                            .subEntityRate(subEntityRate)
                            .parentEntityRate(totalResult)
                            .build();
                    achievementRate.put(virtualSku, build);
                }
            });
        }
        return achievementRate;
    }

    /**
     * 计算当月的加权目标日销
     * @param virtualProductDO 虚拟商品信息
     * @return 加权目标日销
     */
    private BigDecimal calCurrentMonthTargetSales(VirtualProductDO virtualProductDO) {
        String virtualId = virtualProductDO.getId();
        DateTime createTime = ReplenishmentManager.getInstance().getCreateTime();
        LocalDate calDate = DateUtils.convertToLocalDate(createTime);
        LocalDate thirtyDaysBefore = calDate.minusDays(30);
        Map<String, Double> targetSalesMap = targetSalesService.getTargetSales(virtualId, thirtyDaysBefore);
        if (CollectionUtil.isEmpty(targetSalesMap)) {
            return BigDecimal.ZERO;
        }

        BigDecimal thirtyTargetSales = calTotalTargetSales(targetSalesMap, thirtyDaysBefore, calDate);
        if (thirtyTargetSales.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal avgThirtyTargetSales = BigDecimal.valueOf(0.2).multiply(thirtyTargetSales).divide(BigDecimal.valueOf(30), 9, HALF_UP);

        LocalDate fourteenDaysBefore = calDate.minusDays(14);
        BigDecimal fourteenTargetSales = calTotalTargetSales(targetSalesMap, fourteenDaysBefore, calDate);
        BigDecimal avgFourteenTargetSales = BigDecimal.valueOf(0.3).multiply(fourteenTargetSales).divide(BigDecimal.valueOf(14), 9, HALF_UP);

        LocalDate sevenDaysBefore = calDate.minusDays(7);
        BigDecimal sevenTargetSales = calTotalTargetSales(targetSalesMap, sevenDaysBefore, calDate);
        BigDecimal avgSevenTargetSales = BigDecimal.valueOf(0.5).multiply(sevenTargetSales).divide(BigDecimal.valueOf(7), 9, HALF_UP);

        BigDecimal avgTargetSales = avgThirtyTargetSales.add(avgFourteenTargetSales).add(avgSevenTargetSales);
        if (avgFourteenTargetSales.compareTo(BigDecimal.ZERO) <= 0) {
            return avgTargetSales.divide(BigDecimal.valueOf(0.2), 3, HALF_UP);
        } else if (avgSevenTargetSales.compareTo(BigDecimal.ZERO) <= 0) {
            return avgTargetSales.divide(BigDecimal.valueOf(0.5), 3, HALF_UP);
        }
        return avgTargetSales.setScale(3, HALF_UP);
    }

    private BigDecimal calTotalTargetSales(Map<String, Double> targetSalesMap, LocalDate startDate, LocalDate endDate) {
        return targetSalesMap.entrySet().stream()
                .filter(entry -> !LocalDate.parse(entry.getKey(), dateFormat).isBefore(startDate) && LocalDate.parse(entry.getKey(), dateFormat).isBefore(endDate))
                .map(entry -> BigDecimal.valueOf(entry.getValue()))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(3, HALF_UP);
    }

    /**
     * 计算各仓的最早到货日期
     *
     * @param shippingRules  备货规则
     * @param produceDays    工厂生产交期
     * @param targetMonthSet 目标日销的月份
     * @return 各仓最早到货日期
     */
    private Map<String, DateTime> calAllWarehouseFastestArrivingDate(ShippingProjectBaseParamDto shippingRules, String produceDays,
                                                                     Set<String> targetMonthSet) {
        ReplenishmentManager replenishmentManager = ReplenishmentManager.getInstance();
        DateTime replenishmentStartDate = replenishmentManager.getCreateTime();
        Map<String, Integer> headShippingDays = shippingRules.getHeadShippingDays();
        int transitDays = shippingRules.getTransitDays();
        int purchaseProjectDay = shippingRules.getPurchaseProjectDays();
        List<String> produceDayArray = Arrays.asList(produceDays.split(","));

        Map<String, DateTime> fastestArrivingDateMap = new HashMap<>();
        for (String targetMonthStr : targetMonthSet) {
            // 工厂生产时间是每月月初时，为当月最快的交货，因此此处提前一个下单天数下单，正好月初交给工厂生产
            DateTime targetMonth = DateUtil.parse(targetMonthStr).offsetNew(DAY_OF_YEAR, -purchaseProjectDay);

            // 若补货开始时间比目标日销填写的时间还要晚的话，则取补货开始时间为下单天数
            if (replenishmentStartDate.isAfter(targetMonth)) {
                targetMonth = replenishmentStartDate;
            }

            // 获取 targetMonth 日期下补货单，一个下单天数后，工厂的生产交期
            int factoryProduceDays = replenishmentProjectService.getTargetDateFactoryProduceDays(produceDayArray, targetMonth, purchaseProjectDay);

            Map<String, Double> shippingRatioMap = shippingRules.getShippingRatio();
            // 每个有发货比例仓的最早到货时间
            for (var entry : headShippingDays.entrySet()) {
                if (shippingRatioMap.get(entry.getKey()) <= 0) {
                    continue;
                }
                int enableUsingDayGap = purchaseProjectDay + factoryProduceDays + transitDays + entry.getValue();
                DateTime arrivingDate = targetMonth.offsetNew(DAY_OF_YEAR, enableUsingDayGap);
                fastestArrivingDateMap.compute(entry.getKey(), (k, v) -> {
                    DateTime localDate = Optional.ofNullable(v).orElse(arrivingDate);
                    return localDate.isBefore(arrivingDate) ? localDate : arrivingDate;
                });
            }
        }
        return fastestArrivingDateMap;
    }

    /**
     * 先计算发货结果，再根据发货结果判断是否需要补货
     *
     * @param virtualSkuDO           虚拟商品
     * @param shippingRules          备货规则
     * @param achievementRateDto     目标达成率
     * @param virtualSkuAndOldSkuMap 虚拟sku和老sku的映射关系
     * @return 发货试算结果
     */
    private TrialCalReplenishmentDto calDeliveryResult(
            VirtualProductDO virtualSkuDO, ShippingProjectBaseParamDto shippingRules, AchievementRateDto achievementRateDto,
            Map<String, String> virtualSkuAndOldSkuMap, Map<String, Double> targetSalesMap, PurchaseNumTypeEnum purchaseNumType,
            List<SenboWarehouseDto> senboWarehouseList, int redundantDate, ReplenishmentVirtualSkuPurchaseDO virRepDO,
            SelfProductDO selfProductInfo) {
        ReplenishmentMapManager instance = ReplenishmentMapManager.getInstance();
        ReplenishmentManager replenishmentManager = ReplenishmentManager.getInstance();

        DateTime replenishmentStartDate = replenishmentManager.getCreateTime();
        // 库存
        var skuStockQuantityMap = instance.getStockQuantityMap();
        // 计划
        var skuDeliveryMap = instance.getFactoryPlanMap();
        String virtualSku = virtualSkuDO.getVirtualSku();

        List<FactoryRemainInventoryDto> importFactoryRemainInventoryList = new ArrayList<>(skuStockQuantityMap.getOrDefault(virtualSku, new ArrayList<>()));

        // 获取计算周期：直到目标日销的最后一天的剩余库存依然在安全库存周围浮动
        DateTime maxSaleDate = targetSalesMap.keySet().stream()
                .map(DateUtil::parse)
                .max(Comparator.comparing(k -> k))
                .orElse(DateUtil.endOfMonth(replenishmentStartDate));
        long calCircle = DateUtil.betweenDay(replenishmentStartDate, maxSaleDate, false);

        // 单个虚拟sku的发货试算
        int between = (int) maxSaleDate.between(replenishmentStartDate, DateUnit.DAY);

        List<FactoryFinishedInventoryDto> finishedInventoryList = skuDeliveryMap.getOrDefault(virtualSku, new ArrayList<>());
        skuDeliveryMap.putIfAbsent(virtualSku, finishedInventoryList);

        // 进行发货试算
        TrialCalReplenishmentDto trialCalReplenishment;

        Map<String, Integer> sortedMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSort));
        List<String> sortedWarehouseList = WarehouseSortUtil.getSortedWarehouseList(shippingRules.getShippingRatio(), sortedMap);

        int productStatus = virtualSkuDO.getProductStatus();
        if (SpecialDeliveryCollection.NOT_DELIVERY_SET.contains(productStatus)) {
            // 状态是停发、淘汰、测款的产品不发货，只计算模拟库存衰减
            Map<String, Double> inventorySaleDefRatio = shippingRules.getShippingRatio();

            Map<String, Integer> headShippingDateMap = shippingRules.getHeadShippingDays();
            int minShippingDate = headShippingDateMap.entrySet().stream()
                    .filter(h -> inventorySaleDefRatio.containsKey(h.getKey()) && inventorySaleDefRatio.get(h.getKey()) > 0)
                    .min(Map.Entry.comparingByValue())
                    .orElseThrow(() -> new NullPointerException("头程时间为空"))
                    .getValue();

            MockTableCalDto mockTableCalDto = new MockTableCalDto(new ArrayList<>(), (int) calCircle,
                    new ArrayList<>(), headShippingDateMap, replenishmentStartDate, new ArrayList<>(),
                    importFactoryRemainInventoryList, new ArrayList<>(), minShippingDate, targetSalesMap, inventorySaleDefRatio, sortedWarehouseList);
            trialCalReplenishment = commonConsumptionService.calDeliveryResults(mockTableCalDto);
        } else {
            trialCalReplenishment = shippingCalculationService.trialShippingCalculation(replenishmentStartDate, redundantDate,
                    new ArrayList<>(), importFactoryRemainInventoryList, virtualSkuDO, between, targetSalesMap, replenishmentStartDate,
                    finishedInventoryList, shippingRules, sortedWarehouseList, selfProductInfo.getProductName(), senboWarehouseList);
        }

        trialCalReplenishment.setVirtualSku(virtualSku);
        String shippingTrialJson = JSON.toJSONString(trialCalReplenishment);
        TrialCalReplenishmentDto shippingTrial = JSON.parseObject(shippingTrialJson, TrialCalReplenishmentDto.class);

        // 全链路
        var inventoryIdSoldOutQuery = InventoryIdSoldOutQuery.builder()
                .trialCalReplenishmentDto(shippingTrialJson)
                .destinationSalesMap(JSON.toJSONString(targetSalesMap))
                .build();

        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO resultDTO = restTemplateUtil.post(inventoryIdSoldOutQuery, ResultDTO.class, INVENTORY_SYSTEM_SELECT_CAL_THEORETICAL);
        String soldOutDateMap = JSON.toJSONString(resultDTO.getData());
        inventoryIdSoldOutQuery.setSoldOutDateMap(soldOutDateMap);

        ResultDTO allResultDTO = restTemplateUtil.post(inventoryIdSoldOutQuery, ResultDTO.class, INVENTORY_SYSTEM_SELECT_CAL_ALL_THEORETICAL);
        String soldOutDateBefore = JSON.toJSONString(allResultDTO.getData());
        Map<String, List<Object>> soldOutDateBeforeMap = JSON.parseObject(soldOutDateBefore, Map.class);

        List<Object> soldOutDateBeforeList = soldOutDateBeforeMap.getOrDefault(virtualSku, new ArrayList<>());
        List<SoldOutDateBeforeTheoryDto> soldOutDateBeforeTheoryList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(soldOutDateBeforeList)) {
            soldOutDateBeforeTheoryList = soldOutDateBeforeList.stream()
                    .map(dto ->
                            JSON.parseObject(JSON.toJSONString(dto), SoldOutDateBeforeTheoryDto.class)
                    )
                    .collect(Collectors.toList());
        }

        Map<String, String> soldOutDate = JSON.parseObject(soldOutDateMap, Map.class);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_HYPHEN);
        Map<String, LocalDate> convertedSoldOutDateMap = soldOutDate.entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> LocalDate.parse(entry.getValue(), formatter)
                ));
        trialCalReplenishment.setFullLinkTheoreticalSoldOutDate(convertedSoldOutDateMap.getOrDefault(virtualSku, null));
        trialCalReplenishment.setSoldOutDateBeforeTheoryList(soldOutDateBeforeTheoryList);
        List<FactoryRemainInventoryDto> shippingInventoryList = Optional.ofNullable(shippingTrial.getShippingInventoryList()).orElse(new ArrayList<>());
        // 判断是否需要补货试算，如果需要的话，返回发货试算结果，如果不需要的话，保存发货结果，然后返回空值
        boolean needReplenishment = true;
        int transitDays = shippingRules.getTransitDays();
        List<FactoryFinishedInventoryDto> maybeShippingFinishedInventoryList = finishedInventoryList.stream()
                .filter(f -> Boolean.FALSE.equals(f.getNeedToPriorDelivery()))
                .filter(m -> maxSaleDate.offsetNew(DAY_OF_YEAR, -transitDays).compareTo(m.getFactoryFinishedDate()) >= 0)
                .map(ObjectUtil::clone)
                .toList();
        if (CollectionUtil.isNotEmpty(maybeShippingFinishedInventoryList)) {
            double totalShippingNum = shippingInventoryList.stream().mapToDouble(FactoryRemainInventoryDto::getStoreNum).sum();
            double totalFinishedNum = maybeShippingFinishedInventoryList.stream().mapToDouble(FactoryFinishedInventoryDto::getShippingNum).sum();
            needReplenishment = totalFinishedNum <= totalShippingNum;
        }

        // 1. 产品状态是正常，且工厂剩余数量<=发货量时需要补货
        // 2. 产品状态是测款，且没有工厂计划时需要补货
        if ((NORMAL.getCode().equals(productStatus) || NEW_ARRIVAL_NORMAL_SAMPLE.getCode().equals(productStatus)) && needReplenishment) {
            return trialCalReplenishment;
        }

        // 在既无计划，也无非撤销状态采购单的情况下，测款产品才需要补货
        boolean isTest = false;
        if (NEW_ARRIVAL_TEST_SAMPLE.getCode().equals(productStatus) && CollectionUtil.isEmpty(finishedInventoryList)) {
            String virtualSkuId = virtualSkuDO.getId();
            Integer notAbandonedCount = purchaseOrdersService.getNotAbandonedDetail(virtualSkuId);
            isTest = true;
            if (notAbandonedCount == null || notAbandonedCount == 0) {
                return trialCalReplenishment;
            }
        } else if (NEW_ARRIVAL_TEST_SAMPLE.getCode().equals(productStatus)) {
            isTest = true;
        }

        if (purchaseNumType.equals(THEORETICAL)) {
            ReplenishmentProjectSaveDto projectSaveDto = ReplenishmentProjectSaveDto.builder()
                    .replenishmentTrial(trialCalReplenishment)
                    .shippingTrial(shippingTrial)
                    .isTest(isTest)
                    .discountShippingTrial(JSON.to(TrialCalReplenishmentDto.class, JSON.toJSONString(shippingTrial)))
                    .everydayReplenishmentMap(new HashMap<>())
                    .discountEverydayReplenishmentMap(new HashMap<>())
                    .operatorEverydayReplenishmentMap(new HashMap<>())
                    .selfSku(selfProductInfo.getSku())
                    .selfProductInfo(selfProductInfo)
                    .shippingRules(shippingRules)
                    .virtualSku(virtualSku)
                    .targetSalesMap(targetSalesMap)
                    .virtualSkuAndOldSkuMap(virtualSkuAndOldSkuMap)
                    .build();
            if (ObjectUtil.isEmpty(virRepDO)){
                replenishmentProjectService.saveReplenishmentProjectInfo(projectSaveDto, virtualSkuDO, achievementRateDto);
            }else {
                projectSaveDto.setUpdatedTargetSalesMap(targetSalesMap);
                replenishmentProjectService.updateReCalReplenishmentProjectInfo(projectSaveDto, virtualSkuDO,
                        achievementRateDto, virRepDO);
            }
        }
        return null;
    }

    public double calSafeInventory(ShippingProjectBaseParamDto shippingRules, Map<String, Double> targetSalesMap,
                                   String warehouse, Map<String, DateTime> fastestArrivingDateMap, DateTime startDate,
                                   Map<String, Map<String, Double>> everydayRemainInventoryMap,
                                   List<FactoryRemainInventoryDto> onShippingInventoryList) {
        // 此处是在途数据
        List<FactoryRemainInventoryDto> newShippingInventoryList = onShippingInventoryList.stream().map(ObjectUtil::clone).collect(Collectors.toList());
        int safeDays = shippingRules.getSafeDays();
        Map<String, Double> shippingRatioMap = shippingRules.getShippingRatio();

        // 临时的每天模拟剩余库存
        Map<String, Map<String, Double>> tempEverydayRetainMap = new TreeMap<>() {{
            putAll(everydayRemainInventoryMap);
        }};

        double totalSum = 0;
        for (var tempEntry : tempEverydayRetainMap.entrySet()) {
            DateTime todayCalDate = DateUtil.parse(tempEntry.getKey());
            // 若当天不在安全天数区间范围内，跳过当天的计算
            if (todayCalDate.isAfterOrEquals(startDate.offsetNew(DAY_OF_YEAR, safeDays)) || todayCalDate.isBefore(startDate)) {
                continue;
            }

            Map<String, Double> tempRemainInventoryMap = tempEntry.getValue();
            double totalTargetSales = targetSalesMap.getOrDefault(tempEntry.getKey(), 0D);
            double totalRatio = getTotalRatio(shippingRatioMap, tempRemainInventoryMap, fastestArrivingDateMap, todayCalDate);
            if (totalRatio == 0) {
                continue;
            }

            // 先计算没有到货的仓库实际日销销售完成后还会不会有剩余库存需要到货仓去消耗
            String nextDay = todayCalDate.offsetNew(DAY_OF_YEAR, 1).toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
            Map<String, Double> tempNextdayRetainMap = new HashMap<>();

            // 先计算比例为0的仓库消耗情况
            BigDecimal everydayTotalSaleNumDec = calRemainInventoryService.consumeZeroRatioWarehouseInventory(shippingRatioMap, tempRemainInventoryMap,
                    new HashMap<>(), tempNextdayRetainMap, totalTargetSales);
            if (everydayTotalSaleNumDec.compareTo(BigDecimal.ZERO) == 0) {
                // 上述仓库消耗完以后，剩余目标日销为0，则直接进行下一天的计算
                shippingRatioMap.entrySet().stream()
                        .filter(entry1 -> entry1.getValue() > 0)
                        .map(Map.Entry::getKey)
                        .forEach(key -> {
                            double sum = calShippingInventorySum(newShippingInventoryList, key, nextDay);
                            tempNextdayRetainMap.compute(key, (k, v) -> sum + tempRemainInventoryMap.getOrDefault(key, 0D));
                        });
                tempEverydayRetainMap.put(nextDay, tempNextdayRetainMap);
                continue;
            }

            // 若还有库存，则需要有发货比例的仓去承担销售，并计算出安全库存
            totalTargetSales = everydayTotalSaleNumDec.setScale(3, HALF_UP).doubleValue();
            boolean needRepeat;
            do {
                Map<String, Double> warehouseSalesMap = new HashMap<>();
                needRepeat = false;
                totalRatio = getTotalRatio(shippingRatioMap, tempRemainInventoryMap, fastestArrivingDateMap, todayCalDate);

                for (Map.Entry<String, Double> entry1 : tempRemainInventoryMap.entrySet()) {
                    // 先计算无法到货的仓
                    DateTime fastestArrivingDate = fastestArrivingDateMap.get(entry1.getKey());
                    if (fastestArrivingDate == null || fastestArrivingDate.isBeforeOrEquals(todayCalDate)) {
                        continue;
                    }

                    double sum = calShippingInventorySum(newShippingInventoryList, entry1.getKey(), nextDay);
                    if (entry1.getValue() <= 0) {
                        tempNextdayRetainMap.put(entry1.getKey(), sum);
                        continue;
                    }

                    // 计算未到货仓库每天的消耗量
                    double retainValue = entry1.getValue();
                    double warehouseRealRatio = shippingRatioMap.getOrDefault(entry1.getKey(), 0D) / totalRatio;
                    warehouseRealRatio = roundUpToThreeDecimal(warehouseRealRatio);
                    double targetSales = roundUpToThreeDecimal(totalTargetSales * warehouseRealRatio);
                    if (retainValue < targetSales) {
                        entry1.setValue(0D);
                        totalTargetSales -= retainValue;
                        totalRatio -= warehouseRealRatio;
                        needRepeat = true;

                        tempNextdayRetainMap.put(entry1.getKey(), sum);
                    } else {
                        warehouseSalesMap.compute(entry1.getKey(), (k, v) -> Optional.ofNullable(v).orElse(0D) + targetSales);
                    }
                }
                if (!needRepeat) {
                    for (var entry1 : warehouseSalesMap.entrySet()) {
                        double sum = calShippingInventorySum(newShippingInventoryList, entry1.getKey(), nextDay);
                        totalTargetSales -= entry1.getValue();
                        double todayRemain = tempRemainInventoryMap.get(entry1.getKey()) - entry1.getValue();
                        tempNextdayRetainMap.put(entry1.getKey(), roundUpToThreeDecimal(sum + todayRemain));
                    }
                }
            } while (needRepeat);

            tempEverydayRetainMap.put(nextDay, tempNextdayRetainMap);
            // 计算到货仓的实际日销比例
            double totalArrivingRatio = shippingRatioMap.entrySet().stream()
                    .filter(f -> f.getValue() > 0)
                    .filter(entry1 -> {
                        String sWarehouse = entry1.getKey();
                        DateTime arrivingDate = fastestArrivingDateMap.get(sWarehouse);
                        return arrivingDate.isBeforeOrEquals(todayCalDate);
                    })
                    .mapToDouble(Map.Entry::getValue)
                    .sum();
            BigDecimal realRatio = BigDecimal.ZERO;
            if (totalArrivingRatio != 0) {
                realRatio = BigDecimal.valueOf(shippingRatioMap.get(warehouse)).divide(BigDecimal.valueOf(totalArrivingRatio), 3, HALF_UP);
            }
            totalSum += roundUpToThreeDecimal(totalTargetSales * realRatio.doubleValue());
        }
        return roundUpToThreeDecimal(totalSum);
    }

    @Override
    public void reCalReplenishment(ReplenishmentDetailForm form) {
        ReplenishmentVirtualSkuPurchaseDO virRepDO = virtualSkuPurchaseRepository.getById(form.getReplenishmentId());
        if (ObjectUtil.isEmpty(virRepDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "补货计划不存在");
        }

        ReplenishmentRecordDO record = replenishmentRecordRepository.selectOneByVirtualPurchaseId(form.getReplenishmentId());
        if (ObjectUtil.isEmpty(record) || record.getReplenishmentStatus().equals(ReplenishmentStatusEnum.SAVED.getCode()) ||
                record.getReplenishmentStatus().equals(ReplenishmentStatusEnum.ABROGATE.getCode())) {
            throw new IllegalArgumentException("该补货计划已完成或已作废，不允许重算");
        }

        ProductSnapshotDO snapshotDO = snapshotRepository.getById(virRepDO.getProductSnapshotId());

        SelfProductDO selfProductDO = JSON.parseObject(snapshotDO.getSelfData(), SelfProductDO.class);
        VirtualProductDO virtualProductDO = JSON.parseObject(snapshotDO.getVirtualData(), VirtualProductDO.class);

        // 规则
        ProductRulesDto replenishmentRules = replenishmentRulesService.getProductRulesFromDatabase(VirtualProductDO.builder().id(virRepDO.getVirtualSkuId()).build());

        var rules = ShippingProjectBaseParamDto.builder()
                .ruleId(replenishmentRules.getRulesId())
                .purchaseProjectDays(replenishmentRules.getPurchaseProjectDays())
                .purchaseCircle(replenishmentRules.getPurchaseCircle())
                .safeDays(replenishmentRules.getSafeDays())
                .transitDays(replenishmentRules.getTransitDays())
                .shippingFrequency(replenishmentRules.getShippingFrequency())
                .headShippingDays(JSON.parseObject(replenishmentRules.getHeadShippingDays(), new TypeReference<HashMap<String, Integer>>() {
                }))
                .shippingRatio(JSON.parseObject(replenishmentRules.getShippingRatio(), new TypeReference<HashMap<String, Double>>() {
                }))
                .build();

        // 日销
        String saleDestination = virRepDO.getSaleDestination();

        HashMap<String, Double> targetSalesMap = JSON.parseObject(saleDestination, new TypeReference<>() {
        });

        // 海外库存
        var foreignList = foreignInventoryRepository.getByReplenishmentVirtualPurchaseId(form.getReplenishmentId());
        Map<String, List<FactoryRemainInventoryDto>> stockQuantityMap = new HashMap<>();
        ArrayList<FactoryRemainInventoryDto> factoryRemainInventoryDto = new ArrayList<>();
        for (var foreign : foreignList) {
            factoryRemainInventoryDto.add(FactoryRemainInventoryDto.builder()
                    .shipmentCode(foreign.getShipmentCode())
                    .virtualSku(virtualProductDO.getVirtualSku())
                    .warehouse(foreign.getWarehouseId())
                    .storeNum(foreign.getStoreNum().doubleValue())
                    .enableUsingDate(foreign.getEnableUsingDate())
                    .startShippingTime(foreign.getStartShippingDate())
                    .build());
        }
        stockQuantityMap.put(virtualProductDO.getVirtualSku(), factoryRemainInventoryDto);

        ReplenishmentMapManager mapManager = ReplenishmentMapManager.getInstance();
        mapManager.putStockQuantityMap(virtualProductDO.getVirtualSku(), factoryRemainInventoryDto);
        List<FactoryFinishedInventoryDO> factoryFinishedInventoryDOList = factoryFinishedInventoryRepository.getListByShippingProjectId(Collections.singletonList(virRepDO.getId()), ShippingSourceTypeEnum.REPLENISH.getCode());
        ArrayList<FactoryFinishedInventoryDto> finishedInventoryList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(factoryFinishedInventoryDOList)) {
            for (var factory : factoryFinishedInventoryDOList) {
                finishedInventoryList.add(FactoryFinishedInventoryDto.builder()
                        .contractCode(factory.getContractCode())
                        .virtualSku(virtualProductDO.getVirtualSku())
                        .isOldStatus(factory.getIsOldStatus())
                        .virtualSkuId(virtualProductDO.getId())
                        .factoryFinishedDate(factory.getFactoryFinishedDate())
                        .shippingNum(Double.valueOf(factory.getFactoryShippingPackageNum()))
                        .remark(factory.getRemarks())
                        .build());
            }
        }
        mapManager.putDeliveryMap(virtualProductDO.getVirtualSku(), finishedInventoryList);
        mapManager.putTargetSalesMap(virtualProductDO.getVirtualSku(), targetSalesMap);

        // 将补货创建时间初始化
        ReplenishmentManager.getInstance().setCreateTime(DateUtil.beginOfDay(DateTime.of(record.getCreateDate())));

        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, Integer> headShippingDateMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getHeadShippingDate));
        // 保存并获取备货规则
        int redundantDate = getRedundantDateNum();
        String jsonShippingRules = JSON.toJSONString(rules);
        AchievementRateDto achievementRateDto = AchievementRateDto.builder().parentEntityRate(BigDecimal.valueOf(virRepDO.getParentEntityRate()))
                .subEntityRate(BigDecimal.valueOf(virRepDO.getSubEntityRate())).build();
        // 调用新提取的方法
        var result = calculateReplenishment(virtualProductDO, selfProductDO, rules, achievementRateDto,
                new HashMap<>(), stockQuantityMap, targetSalesMap, senboWarehouseList, headShippingDateMap,
                THEORETICAL, redundantDate, virRepDO);
        // 如果不需要补货的话，直接计算下一件产品
        if (result == null) {
            return;
        }
        // 打折后试算
        ReplenishmentCalculationResult discountResult;
        Map<String, Map<String, ReplenishDeliveryRangeAndNumDp>> everydayReplenishmentMap = result.everydayReplenishmentMap;
        ReplenishmentStatusReasonDto replenishmentStatusReason = replenishmentProjectService.getReplenishmentStatusReason(
                achievementRateDto, virtualProductDO, everydayReplenishmentMap, rules, result.isTest);
        // 折后发货数据
        TrialCalReplenishmentDto discountShippingTrial;
        // 折后补货数据
        Map<String, Map<String, ReplenishDeliveryRangeAndNumDp>> discountEverydayReplenishmentMap;
        // 折后目标日销
        Map<String, Double> updatedTargetSalesMap = new HashMap<>();
        switch (replenishmentStatusReason.getReasonEnum()) {
            case PARENT_STANDARD_CHILD_NOT, PARENT_NOT_CHILD_STANDARD -> {
                // 目标日销*达成率
                updatedTargetSalesMap = calculateUpdatedTargetSalesMap(targetSalesMap, achievementRateDto.getSubEntityRate().doubleValue());
                // 打折后试算
                ShippingProjectBaseParamDto baseParam = JSON.parseObject(jsonShippingRules, ShippingProjectBaseParamDto.class);
                discountResult = calculateReplenishment(
                        virtualProductDO, selfProductDO, baseParam, achievementRateDto, new HashMap<>(), stockQuantityMap,
                        updatedTargetSalesMap, senboWarehouseList, headShippingDateMap, ADVICE, redundantDate, virRepDO
                );
                if (discountResult != null) {
                    discountShippingTrial = JSON.to(TrialCalReplenishmentDto.class, JSON.toJSONString(discountResult.shippingTrial));
                    discountEverydayReplenishmentMap = discountResult.everydayReplenishmentMap;
                } else {
                    discountShippingTrial = JSON.to(TrialCalReplenishmentDto.class, JSON.toJSONString(result.shippingTrial));
                    discountEverydayReplenishmentMap = new HashMap<>();
                }
            }
            case NORMAL -> {
                discountEverydayReplenishmentMap = everydayReplenishmentMap;
                updatedTargetSalesMap = targetSalesMap;
                discountShippingTrial = JSON.to(TrialCalReplenishmentDto.class, JSON.toJSONString(result.shippingTrial));
            }
            default -> {
                discountEverydayReplenishmentMap = new HashMap<>();
                if (replenishmentStatusReason.getReasonEnum().equals(PARENT_NOT) || replenishmentStatusReason.getReasonEnum().equals(PARENT_CHILD_NOT)) {
                    // 目标日销*达成率
                    updatedTargetSalesMap = calculateUpdatedTargetSalesMap(targetSalesMap, achievementRateDto.getSubEntityRate().doubleValue());
                }
                discountShippingTrial = JSON.to(TrialCalReplenishmentDto.class, JSON.toJSONString(result.shippingTrial));
            }
        }

        Map<String, Map<String, ReplenishDeliveryRangeAndNumDp>> operatorEverydayReplenishmentMap = new HashMap<>();
        for (var entry : everydayReplenishmentMap.entrySet()) {
            String key = entry.getKey();
            Map<String, ReplenishDeliveryRangeAndNumDp> innerMap = new HashMap<>();
            for (String innerKey : entry.getValue().keySet()) {
                innerMap.put(innerKey, new ReplenishDeliveryRangeAndNumDp(0D));
            }
            operatorEverydayReplenishmentMap.put(key, innerMap);
        }

        for (var entry : discountEverydayReplenishmentMap.entrySet()) {
            String key = entry.getKey();
            Map<String, ReplenishDeliveryRangeAndNumDp> innerMap = operatorEverydayReplenishmentMap.getOrDefault(key, new HashMap<>());
            for (String innerKey : entry.getValue().keySet()) {
                innerMap.put(innerKey, new ReplenishDeliveryRangeAndNumDp(0D));
            }
            operatorEverydayReplenishmentMap.put(key, innerMap);
        }
        // 保存试算数据
        ReplenishmentProjectSaveDto projectSaveDto = ReplenishmentProjectSaveDto.builder()
                .replenishmentTrial(result.trialCalReplenishment)
                .shippingTrial(result.shippingTrial)
                .isTest(result.isTest)
                .discountShippingTrial(discountShippingTrial)
                .selfSku(selfProductDO.getSku())
                .selfProductInfo(selfProductDO)
                .everydayReplenishmentMap(everydayReplenishmentMap)
                .targetSalesMap(targetSalesMap)
                .discountEverydayReplenishmentMap(discountEverydayReplenishmentMap)
                .operatorEverydayReplenishmentMap(operatorEverydayReplenishmentMap)
                .shippingRules(rules)
                .virtualSku(virtualProductDO.getVirtualSku())
                .virtualSkuAndOldSkuMap(new HashMap<>())
                .updatedTargetSalesMap(updatedTargetSalesMap)
                .build();
        replenishmentProjectService.updateReCalReplenishmentProjectInfo(projectSaveDto, virtualProductDO,
                achievementRateDto, virRepDO);
    }

    private int getRedundantDateNum() {
        int redundantDate;
        try {
            redundantDate = Integer.parseInt(Objects.requireNonNull(stringRedisTemplate.opsForValue().get(PURCHASE_CAL_REDUNDANT_DATE_NUM)));
        } catch (Exception e) {
            redundantDate = 7;
            stringRedisTemplate.opsForValue().set(PURCHASE_CAL_REDUNDANT_DATE_NUM, String.valueOf(redundantDate));
        }
        return redundantDate;
    }

    private double getTotalRatio(Map<String, Double> shippingRatioMap, Map<String, Double> tempRemainInventoryMap,
                                 Map<String, DateTime> fastestArrivingDateMap, DateTime todayCalDate) {
        return shippingRatioMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 0)
                .filter(s -> {
                    String sWarehouse = s.getKey();
                    DateTime arrivingDate = fastestArrivingDateMap.get(sWarehouse);
                    return tempRemainInventoryMap.getOrDefault(sWarehouse, 0D) > 0 || arrivingDate.isBeforeOrEquals(todayCalDate);
                })
                .mapToDouble(Map.Entry::getValue)
                .sum();
    }

    private double calShippingInventorySum(List<FactoryRemainInventoryDto> newShippingInventoryList, String warehouse, String nextDay) {
        return newShippingInventoryList.stream()
                .filter(f -> f.getWarehouse().equals(warehouse) && format.format(f.getEnableUsingDate()).equals(nextDay))
                .mapToDouble(FactoryRemainInventoryDto::getStoreNum)
                .sum();
    }

    private double calGeneralSafeInventory(int safeDate, DateTime startDate, Map<String, Double> totalTargetSalesMap) {
        double totalSafeInventory = 0;
        for (DateTime calDate = DateUtil.dateNew(startDate);
             calDate.compareTo(startDate.offsetNew(DAY_OF_YEAR, safeDate)) < 0;
             calDate.offset(DAY_OF_YEAR, 1)) {
            String calDateStr = calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
            totalSafeInventory += totalTargetSalesMap.getOrDefault(calDateStr, 0D);
        }
        return roundUpToThreeDecimal(totalSafeInventory);
    }

    private double roundUpToThreeDecimal(double num) {
        return BigDecimal.valueOf(num).setScale(3, HALF_UP).doubleValue();
    }
}
