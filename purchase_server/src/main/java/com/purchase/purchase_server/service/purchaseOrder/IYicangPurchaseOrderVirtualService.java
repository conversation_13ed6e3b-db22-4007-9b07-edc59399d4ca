package com.purchase.purchase_server.service.purchaseOrder;

import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderVirtualDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.PurchaseOrderImportDTO;
import com.purchase.purchase_server.entity.form.PurchaseOrdersForm;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrderVirtualVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cm_yicang_purchase_order_virtual(易仓采购单虚拟sku表)】的数据库操作Service
 * @createDate 2024-11-01 14:30:43
 */
public interface IYicangPurchaseOrderVirtualService {

    /**
     * 根据id删除（物理）
     *
     * @param detailIds
     */
    void removeBySbPoDetailIds(List<String> detailIds);

    /**
     * 批量保存
     */
    void saveBatch(List<YicangPurchaseOrderVirtualDO> list);

    /**
     * 构建note数据
     */
    YicangPurchaseOrderVirtualDO createVirtualProduct(String sku, String number, String selfProductId, String detailId,
                                                      String poCode, List<String> failResult);

    /**
     * 采购单详情
     *
     * @param form 查询条件
     */
    List<YicangPurchaseOrderVirtualVO> getOrderVirtualDetail(PurchaseOrdersForm form);


    /**
     * 导入采购单详情
     *
     * @param dto 采购单详情
     */
    void saveOrderVirtualByImport(PurchaseOrderImportDTO dto, YicangPurchaseOrderVirtualDO poVirtualDO);
}
