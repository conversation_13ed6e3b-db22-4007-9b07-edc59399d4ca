package com.purchase.purchase_server.utils.easyExcelUtil.manager;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class DeliveryManager extends AbstractManager {
    private static final ThreadLocal<DeliveryManager> threadLocalInstance = new ThreadLocal<>();
    private String createBy;
    private Date createTime;
    // 私有构造函数，确保外部无法直接实例化
    private DeliveryManager() {
        successfulTimes = 0;
        failedTimes = 0;
        createBy = "admin";
        createTime = new Date();
        errorList = new ArrayList<>();
        stockingRulesStatus = false;
        targetSalesStatus = false;
        amStockQuantityStatus = false;
        lampStockQuantityStatus = false;
        stockQuantityStatus = false;
        deliveryStatus = false;
    }

    public static DeliveryManager getInstance() {
        DeliveryManager instance = threadLocalInstance.get();
        if (instance == null) {
            synchronized (DeliveryManager.class) {
                instance = threadLocalInstance.get();
                if (instance == null) {
                    instance = new DeliveryManager();
                    threadLocalInstance.set(instance);
                }
            }
        }
        return instance;
    }

    public synchronized void resetVariables() {
        // 清除当前线程的实例
        threadLocalInstance.remove();
    }
}
