package com.file.system.file_system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.file.system.file_system.entity.form.FileMissionForm;
import com.file.system.file_system.entity.form.FileMissionPageForm;
import com.file.system.file_system.entity.vo.MissionCenterVo;

/**
 * <AUTHOR>
 */
public interface IFileMissionCenterService {

    String saveFileMission(FileMissionForm form);

    IPage<MissionCenterVo> getMissionPage(FileMissionPageForm form);

    void deleteMission(FileMissionForm form);
}
