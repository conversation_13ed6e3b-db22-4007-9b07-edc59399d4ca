package com.inventory_server.model.warning.urgentPurchase.repository.dateRepository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crafts_mirror.common.security.dataPermission.DataPermission;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.inventory_server.model.product.entity.dos.ProductSnapshotDO;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.redundancy.entity.dos.RedundantInventoryDO;
import com.inventory_server.model.warning.unsalable.entity.form.UnsalablePageForm;
import com.inventory_server.model.warning.urgentPurchase.entity.dos.WarningUrgentPurchaseDO;
import com.inventory_server.model.warning.urgentPurchase.entity.enums.UrgentPurchaseDirectionAndSortEnum;
import com.inventory_server.model.warning.urgentPurchase.entity.vo.UrgentPurchasePageVo;
import com.inventory_server.model.warning.urgentPurchase.mapper.UrgentPurchaseMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.SecurityConstants.ROLE_DATA_PER_URGENT_PURCHASE;

/**
 * @Description 加急补货持久层
 * <AUTHOR>
 * @Date 2025/4/21 20:26
 **/
@Service
public class UrgentPurchaseRepository extends ServiceImpl<UrgentPurchaseMapper, WarningUrgentPurchaseDO> {

    public void saveOrUpdateByInventoryInfoId(WarningUrgentPurchaseDO urgentPurchase) {
        String inventoryInfoId = urgentPurchase.getInventoryInfoId();
        boolean exists = checkUrgentPurchase(inventoryInfoId);
        if (exists) {
            baseMapper.update(urgentPurchase, Wrappers.<WarningUrgentPurchaseDO>lambdaQuery()
                    .eq(WarningUrgentPurchaseDO::getInventoryInfoId, inventoryInfoId));
        } else {
            baseMapper.insert(urgentPurchase);
        }
    }

    public List<WarningUrgentPurchaseDO> getUrgentPurchaseListByInventoryIdCollection(Collection<String> inventoryInfoIdList) {
        if (CollectionUtil.isEmpty(inventoryInfoIdList)) {
            return new ArrayList<>();
        }
        return baseMapper.selectList(Wrappers.<WarningUrgentPurchaseDO>lambdaQuery()
                .in(WarningUrgentPurchaseDO::getInventoryInfoId, inventoryInfoIdList));
    }

    public List<UrgentPurchasePageVo> getUrgentPurchaseList(UnsalablePageForm form) {
        return baseMapper.selectJoinList(UrgentPurchasePageVo.class, buildQueryWrapper(form));
    }

    public IPage<UrgentPurchasePageVo> getUrgentPurchasePage(UnsalablePageForm form) {
        IPage<UrgentPurchasePageVo> iPage = new Page<>(form.getCurrent(), form.getSize());
        return baseMapper.selectJoinPage(iPage, UrgentPurchasePageVo.class, buildQueryWrapper(form));
    }

    private MPJLambdaWrapper<WarningUrgentPurchaseDO> buildQueryWrapper(UnsalablePageForm form) {
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_URGENT_PURCHASE, "t2");

        return new MPJLambdaWrapper<WarningUrgentPurchaseDO>()
                .selectAs(WarningUrgentPurchaseDO::getId, "urgentPurchaseId")
                .select(WarningUrgentPurchaseDO::getInventoryInfoId)
                .select(WarningUrgentPurchaseDO::getUpdateDate)
                .select(WarningUrgentPurchaseDO::getUrgentFactoryFinishedDate)
                .select(WarningUrgentPurchaseDO::getUrgentPurchaseDate)
                .selectAs(WarningUrgentPurchaseDO::getUrgentPurchaseDateRange, "urgentPurchaseRange")
                .select(WarningUrgentPurchaseDO::getUrgentPurchaseNum)
                .select("t3.self_data->>'$.image' as image")
                .select("t3.self_data->>'$.sku' as selfSku")
                .select("t3.self_data->>'$.id' as selfSkuId")
                .select("t3.self_data->>'$.categoryId' as category")
                .select("t3.self_data->>'$.productName' as productName")
                .select(VirtualProductDO::getOperator)
                .select("t3.virtual_data->>'$.virtualSku' as virtualSku")
                .select("t3.virtual_data->>'$.id' as virtualSkuId")
                .select("t3.virtual_data->>'$.channel' as channel")
                .select("t3.virtual_data->>'$.subType' as subType")
                .select("t3.virtual_data->>'$.productType' as productType")
                .select("t3.virtual_data->>'$.productStatus' as productStatus")
                .select(RedundantInventoryDO::getForeignTheoreticalSoldOutDate)
                .select(RedundantInventoryDO::getDaysBeforeSoldOut)
                .select(RedundantInventoryDO::getFullLinkTheoreticalSoldOutDate)
                .select(RedundantInventoryDO::getFullLinkDaysBeforeSoldOut)

                .leftJoin(RedundantInventoryDO.class, RedundantInventoryDO::getId, WarningUrgentPurchaseDO::getInventoryInfoId)
                .leftJoin(VirtualProductDO.class, VirtualProductDO::getId, RedundantInventoryDO::getVirtualSkuId)
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, WarningUrgentPurchaseDO::getSnapshotId)

                // 加急补货 > 0 的才需要展示出来
                .gt(WarningUrgentPurchaseDO::getUrgentPurchaseNum, 0)
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), ProductSnapshotDO::getVirtualSku, form.getVirtualSkuList())
                .in(CollectionUtil.isNotEmpty(form.getOldSkuList()), "t3.virtual_data->>'$.oldSku'", form.getOldSkuList())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), ProductSnapshotDO::getSelfSku, form.getSelfSkuList())
                .eq(StrUtil.isNotEmpty(form.getChannel()), "t3.virtual_data->>'$.channel'", form.getChannel())
                .eq(form.getSubType() != null, "t3.virtual_data->>'$.subType'", form.getSubType())
                .eq(form.getProductStatus() != null, "t3.virtual_data->>'$.productStatus'", form.getProductStatus())
                .in(CollectionUtil.isNotEmpty(form.getProductStatusList()), "t3.virtual_data->>'$.productStatus'", form.getProductStatusList())
                .eq(form.getProductType() != null, "t3.virtual_data->>'$.productType'", form.getProductType())
                .like(StrUtil.isNotEmpty(form.getProductName()), "t3.self_data->>'$.productName'", Optional.ofNullable(form.getProductName()).map(String::strip).orElse(null))
                .eq(StrUtil.isNotEmpty(form.getCategoryId()), "t3.self_data->>'$.categoryId'", form.getCategoryId())
                .apply(StrUtil.isNotBlank(form.getOperator()), "FIND_IN_SET('" + form.getOperator() + "',t2.operator)")
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
                .func(true,
                        i -> {
                            if (StrUtil.isNotBlank(form.getSort()) && StrUtil.isNotBlank(form.getDirection())) {
                                i.last("ORDER BY " + UrgentPurchaseDirectionAndSortEnum.ofCode(form.getSort()).getTableField() +
                                        " IS NULL ASC, " + UrgentPurchaseDirectionAndSortEnum.ofCode(form.getSort()).getTableField() +
                                        " " + form.getDirection() + " , t.update_date DESC, t2.virtual_sku ASC");
                            } else {
                                i.orderByDesc(WarningUrgentPurchaseDO::getUpdateDate).orderByAsc(VirtualProductDO::getVirtualSku);
                            }
                        }
                );
    }

    public boolean checkUrgentPurchase(String inventoryInfoId) {
        if (StrUtil.isBlank(inventoryInfoId)) {
            return false;
        }
        return baseMapper.exists(Wrappers.<WarningUrgentPurchaseDO>lambdaQuery()
                .eq(WarningUrgentPurchaseDO::getInventoryInfoId, inventoryInfoId)
        );
    }

    public void deleteByInventoryId(String inventoryId) {
        remove(Wrappers.<WarningUrgentPurchaseDO>lambdaQuery().eq(WarningUrgentPurchaseDO::getInventoryInfoId, inventoryId));
    }
}
