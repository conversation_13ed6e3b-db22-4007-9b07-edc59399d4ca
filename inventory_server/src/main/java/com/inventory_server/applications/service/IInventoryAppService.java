package com.inventory_server.applications.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inventory_server.model.inventory.entity.form.AsyncRealTimeInventoryForm;
import com.inventory_server.model.inventory.entity.form.RealTimeInventoryPageForm;
import com.inventory_server.model.inventory.entity.vo.NonFBARealTimeInventoryVo;

public interface IInventoryAppService {

    void fetchYcInventoryListAndSave();

    void asyncNonFBARealTimeInventory(AsyncRealTimeInventoryForm form);

    IPage<NonFBARealTimeInventoryVo> nonFBARealTimeInventoryPage(RealTimeInventoryPageForm form);
}
