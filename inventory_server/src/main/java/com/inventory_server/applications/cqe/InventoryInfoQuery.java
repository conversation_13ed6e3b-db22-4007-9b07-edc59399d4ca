package com.inventory_server.applications.cqe;

import com.crafts_mirror.utils.dp.BasePageForm;
import com.inventory_server.model.redundancy.entity.dto.RedundancySaleDestinationDto;
import com.inventory_server.model.redundancy.enums.DirectionAndSortEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/10
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class InventoryInfoQuery extends BasePageForm implements Serializable {
    @Serial
    private static final long serialVersionUID = -2004312285598073089L;

    private String inventoryInfoId;
    /**
     * 库存ids
     */
    private List<String> ids;

    /**
     * 虚拟skuIdList
     */
    private List<String> virtualSkuIdList;

    /**
     * 虚拟skuList
     */
    private List<String> virtualSkuList;

    /**
     * 自定义skuIdList
     */
    private List<String> selfSkuIdList;

    /**
     * 自定义skuList
     */
    private List<String> selfSkuList;

    /**
     * 老skuList
     */
    private List<String> oldSkuList;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 排序
     * @see DirectionAndSortEnum
     */
    private String direction;

    /**
     * 排序目标
     * @see DirectionAndSortEnum
     */
    private String sort;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 子体类型
     */
    private String subType;

    /**
     * 产品类型
     */
    private String productType;
    /**
     * 产品状态
     */
    private String productStatus;
    /**
     * 产品状态
     */
    private List<String> productStatusList;
    /**
     * 运营
     */
    private String operator;

    /**
     * 创建开始时间
     */
    private String createStartDate;

    /**
     * 创建开始时间
     */
    private String createEndDate;

    /**
     * 大类id
     */
    private String categoryId;

    // 目标日销年月
    private String saleDestinationYm;

    private Map<String, List<RedundancySaleDestinationDto>> saleDestinationMap;

}
