package com.purchase.purchase_server.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderDeliveryScheduleDO;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderDetailDO;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderVirtualDO;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrdersDO;
import com.purchase.purchase_server.entity.form.PurchaseOrdersForm;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrderVirtualVO;
import com.purchase.purchase_server.mapper.YicangPurchaseOrderVirtualMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/28 15:00
 **/
@Service
public class YicangPurchaseOrderVirtualRepositoryImpl extends ServiceImpl<YicangPurchaseOrderVirtualMapper, YicangPurchaseOrderVirtualDO> {

    /**
     * 根据采购单明细表id删除
     *
     * @param sbPoDetailIds 采购单id
     */
    public void removeBySbPoDetailIds(List<String> sbPoDetailIds) {
        if (CollectionUtil.isNotEmpty(sbPoDetailIds)) {
            baseMapper.removeBySbPoDetailIds(sbPoDetailIds);
        }

    }

    public List<YicangPurchaseOrderVirtualVO> getOrderDetail(PurchaseOrdersForm form) {
        return baseMapper.selectJoinList(YicangPurchaseOrderVirtualVO.class, new MPJLambdaWrapper<YicangPurchaseOrderVirtualDO>()
                .selectAll(YicangPurchaseOrderVirtualDO.class)
                .select(YicangPurchaseOrderDetailDO::getProductSku)
                .select(YicangPurchaseOrderDetailDO::getSbSelfSkuName)
                .selectAs(YicangPurchaseOrdersDO::getId, "sbPoId")
                .selectCollection(YicangPurchaseOrderDeliveryScheduleDO.class, YicangPurchaseOrderVirtualVO::getScheduleList)
                .leftJoin(YicangPurchaseOrderDetailDO.class, YicangPurchaseOrderDetailDO::getId, YicangPurchaseOrderVirtualDO::getSbPoDetailId)
                .leftJoin(YicangPurchaseOrdersDO.class, YicangPurchaseOrdersDO::getId, YicangPurchaseOrderDetailDO::getSbPoId)
                .leftJoin(YicangPurchaseOrderDeliveryScheduleDO.class, YicangPurchaseOrderDeliveryScheduleDO::getSbPoVirtualId, YicangPurchaseOrderVirtualDO::getId)
                .eq(StrUtil.isNotBlank(form.getSbPoId()), YicangPurchaseOrderDetailDO::getSbPoId, form.getSbPoId())
                .in(CollectionUtil.isNotEmpty(form.getSbPoIdList()), YicangPurchaseOrderDetailDO::getSbPoId, form.getSbPoIdList())
                .in(CollectionUtil.isNotEmpty(form.getRefNoList()), YicangPurchaseOrdersDO::getRefNo, form.getRefNoList())
                .eq(StrUtil.isNotBlank(form.getDestinationSku()), YicangPurchaseOrderVirtualDO::getDestinationSku, form.getDestinationSku())
                .eq(StrUtil.isNotBlank(form.getSbPoVirtualId()), YicangPurchaseOrderVirtualDO::getId, form.getSbPoVirtualId())
        );
    }

    public List<YicangPurchaseOrderVirtualDO> getYicangPurchaseOrderVirtualList() {
        return baseMapper.selectList(Wrappers.lambdaQuery());
    }
}