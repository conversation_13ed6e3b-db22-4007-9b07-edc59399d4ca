package com.inventory_server.model.warning.urgentPurchase.constants;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Date 2025/5/21 17:16
 **/
@Configuration
@RefreshScope
@Data
public class UrgentPurchaseConstants {
    @Value("${warning.purchase.redundantDays}")
    private String redundantDays;
}
