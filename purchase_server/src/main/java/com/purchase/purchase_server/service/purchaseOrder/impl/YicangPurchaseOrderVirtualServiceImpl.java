package com.purchase.purchase_server.service.purchaseOrder.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderVirtualDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.PurchaseOrderImportDTO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.YicangPurchaseOrderScheduleDto;
import com.purchase.purchase_server.entity.form.PurchaseOrdersForm;
import com.purchase.purchase_server.entity.form.VirtualProductSearchForm;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.UserInteriorVO;
import com.purchase.purchase_server.entity.vo.VirtualProductListVo;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrderVirtualVO;
import com.purchase.purchase_server.enums.IsOldStatusEnum;
import com.purchase.purchase_server.repository.dataRepository.YicangPurchaseOrderVirtualRepositoryImpl;
import com.purchase.purchase_server.service.purchaseOrder.IYicangPurchaseOrderVirtualService;
import com.purchase.purchase_server.service.replenishment.ISysUserInteriorService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;
import static com.crafts_mirror.utils.constant.SystemConstant.PRODUCTS_VIRTUAL_INFO_URL;

/**
 * <AUTHOR>
 * @description 针对表【cm_yicang_purchase_order_virtual(易仓采购单虚拟sku表)】的数据库操作Service实现
 * @createDate 2024-11-01 14:30:43
 */
@Service
@Slf4j
public class YicangPurchaseOrderVirtualServiceImpl implements IYicangPurchaseOrderVirtualService {

    @Resource
    private YicangPurchaseOrderVirtualRepositoryImpl purchaseOrderVirtualRepository;

    @Resource
    private ISysUserInteriorService sysUserInteriorService;

    @Resource
    protected RestTemplate restTemplate;

    @Override
    public void removeBySbPoDetailIds(List<String> detailIds) {
        purchaseOrderVirtualRepository.removeBySbPoDetailIds(detailIds);
    }

    @Override
    public void saveBatch(List<YicangPurchaseOrderVirtualDO> list) {
        purchaseOrderVirtualRepository.saveBatch(list);
    }

    @Override
    public YicangPurchaseOrderVirtualDO createVirtualProduct(String sku, String number, String selfProductId, String detailId,
                                                             String poCode, List<String> failResult) {
        var restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());

        var searchForm = VirtualProductSearchForm.builder()
                .destinationSku(sku)
                .build();

        var virtualProductListVo = restTemplateUtil.post(searchForm, ResultDTO.class, PRODUCTS_VIRTUAL_INFO_URL);
        var list = JSON.to(List.class, virtualProductListVo.getData());

        var poVirtualDO = new YicangPurchaseOrderVirtualDO();
        if (CollectionUtil.isNotEmpty(list)) {
            String virtualSku = sku.toUpperCase();
            var virtualInfo = JSON.to(VirtualProductListVo.class, list.get(0));
            if (!selfProductId.toUpperCase().equals(virtualInfo.getSelfProductId())) {
                failResult.add("采购单：" + poCode + ",虚拟sku：" + sku + "的自定产品不是当前产品");
                return null;
            }
            if (virtualInfo.getVirtualSku().equals(virtualSku)) {
                poVirtualDO.setIsOldStatus(IsOldStatusEnum.VIRTUAL_SKU.getCode());
            } else {
                poVirtualDO.setIsOldStatus(IsOldStatusEnum.OLD_SKU.getCode());
            }
            poVirtualDO.setDestinationSku(virtualSku);
            poVirtualDO.setVirtualSkuId(virtualInfo.getVirtualSkuId());
            poVirtualDO.setOperator(virtualInfo.getOperatorList());
            poVirtualDO.setQtyEta(Integer.valueOf(number));
            poVirtualDO.setSbPoDetailId(detailId);
            poVirtualDO.setChannel(virtualInfo.getChannel());

        } else {
            failResult.add("采购单：" + poCode + ",虚拟sku：" + sku + "不存在");
            return null;
        }
        return poVirtualDO;
    }


    @Override
    public List<YicangPurchaseOrderVirtualVO> getOrderVirtualDetail(PurchaseOrdersForm form) {
        List<YicangPurchaseOrderVirtualVO> orderDetail = purchaseOrderVirtualRepository.getOrderDetail(form);

        //运营
        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));


        orderDetail.forEach(item -> {
            String operator = item.getOperator();
            String operatorNames = "";
            if (StrUtil.isNotBlank(operator)) {
                // 使用String.split()方法的新重载,直接返回Stream
                operatorNames = Arrays.stream(operator.split(",", 0))
                        .map(op -> StrUtil.isNotBlank(collect.get(op)) ? collect.get(op) : op)
                        .collect(Collectors.joining(","));
            }
            item.setOperator(operatorNames);
            String buyer = item.getBuyer();
            item.setBuyer(StrUtil.isNotBlank(collect.get(buyer)) ? collect.get(buyer) : buyer);

            item.setScheduleList(item.getScheduleList().stream()
                    .sorted(Comparator.comparing(
                            YicangPurchaseOrderScheduleDto::getExpectedDeliveryDate,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .map(schedule -> {
                        if (schedule.getExpectedDeliveryDate() != null) {
                            String format = DateUtil.format(DateUtil.parse(schedule.getExpectedDeliveryDate()), YYYY_MM_DD_DATE_FORMAT_HYPHEN);
                            schedule.setExpectedDeliveryDate(format);
                        }
                        return schedule;
                    })
                    .toList());
        });

        return orderDetail;
    }

    @Override
    public void saveOrderVirtualByImport(PurchaseOrderImportDTO dto, YicangPurchaseOrderVirtualDO poVirtualDO) {
        List<YicangPurchaseOrderVirtualVO> orderDetail = getOrderVirtualDetail(PurchaseOrdersForm.builder()
                .sbPoId(dto.getSbPoId())
                .destinationSku(dto.getDestinationSku())
                .build());
        if (CollectionUtil.isNotEmpty(orderDetail)) {
            var yicangPurchaseOrderVirtualVO = orderDetail.get(0);

            poVirtualDO.setId(yicangPurchaseOrderVirtualVO.getId());
            poVirtualDO.setQtyEta(Integer.valueOf(dto.getQtyEta()));
            purchaseOrderVirtualRepository.updateById(poVirtualDO);
        } else {
            var restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
            var searchForm = VirtualProductSearchForm.builder()
                    .destinationSku(dto.getDestinationSku())
                    .build();

            var virtualProductListVo = restTemplateUtil.post(searchForm, ResultDTO.class, PRODUCTS_VIRTUAL_INFO_URL);
            var list = JSON.to(List.class, virtualProductListVo.getData());

            var virtualInfo = JSON.to(VirtualProductListVo.class, list.get(0));

            poVirtualDO.setVirtualSkuId(virtualInfo.getVirtualSkuId());
            poVirtualDO.setDestinationSku(dto.getDestinationSku());
            poVirtualDO.setIsOldStatus(dto.getIsOldStatus());
            poVirtualDO.setChannel(virtualInfo.getChannel());
            poVirtualDO.setOperator(virtualInfo.getOperatorList());
            poVirtualDO.setQtyEta(Integer.valueOf(dto.getQtyEta()));
            poVirtualDO.setSbPoDetailId(dto.getDetailId());
            purchaseOrderVirtualRepository.save(poVirtualDO);
        }
    }
}




