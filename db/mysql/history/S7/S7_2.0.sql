alter table cm_replenishment_virtual_sku_purchase
    add actual_daily_sales_num double null comment '实际日销' after sale_destination;
alter table cm_replenishment_virtual_sku_purchase
    add target_sales_num double null comment '试算当月目标日销' after actual_daily_sales_num;
alter table cm_replenishment_virtual_sku_purchase
    add sub_entity_rate double null comment '子体达成率' after target_sales_num;
alter table cm_replenishment_virtual_sku_purchase
    add parent_entity_rate double null comment '父体达成率' after sub_entity_rate;
alter table cm_replenishment_virtual_sku_purchase
    add reason tinyint not null comment '原因：0-正常，1-父体达标、子体未达标，按实际达标率补货，2-父体未达标、流量子体达标，按实际达标率补货，3-父体未达标，非流量子建议不补货，4-父体、流量子体均未达标，建议不补货，5-试算后无需补货，6-产品状态"停发"，7-产品状态"售完停售"，8-产品状态"停售"' after replenishment_status;
alter table cm_replenishment_virtual_sku_purchase
    add confirmed_reason varchar(255) null comment '运营确认量原因' after reason;


alter table cm_replenishment_trial_purchase_inventory
    modify advice_purchase_num int not null comment '理论补货量';
alter table cm_replenishment_trial_purchase_inventory
    add actual_replenishment_num int null comment '建议补货量' after advice_purchase_num;
alter table cm_replenishment_trial_purchase_inventory
    add operation_confirmed_num int null comment '运营确认量' after actual_replenishment_num;


ALTER TABLE `crafts_mirror`.`cm_mock_inventory_table`
    ADD INDEX `shipping_project_id`(`shipping_project_id`) USING BTREE