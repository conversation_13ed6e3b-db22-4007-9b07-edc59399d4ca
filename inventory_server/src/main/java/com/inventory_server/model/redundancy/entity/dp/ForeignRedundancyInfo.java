package com.inventory_server.model.redundancy.entity.dp;

import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 海外仓冗余信息
 * <AUTHOR>
 * @Date 2024/12/10 21:04
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class ForeignRedundancyInfo extends RedundancyInfo {

    @Builder.Default
    private List<ForeignWarehouseRedundancy> foreignWarehouseRedundancies = new ArrayList<>();

    public void addForeignWarehouseRedundancy(ForeignWarehouseRedundancy redundancy) {
        foreignWarehouseRedundancies.add(redundancy);
    }
}
