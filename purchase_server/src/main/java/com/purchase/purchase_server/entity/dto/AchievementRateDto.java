package com.purchase.purchase_server.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/6/20 16:49
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class AchievementRateDto {

    private BigDecimal subEntityRate;

    private BigDecimal parentEntityRate;
}
