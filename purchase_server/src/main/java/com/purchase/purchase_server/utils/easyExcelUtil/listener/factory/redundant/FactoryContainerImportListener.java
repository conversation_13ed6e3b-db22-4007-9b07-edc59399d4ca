package com.purchase.purchase_server.utils.easyExcelUtil.listener.factory.redundant;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.purchase.purchase_server.assembler.FactoryAssembler;
import com.purchase.purchase_server.entity.dto.factory.FactoryContainerDto;
import com.purchase.purchase_server.entity.dto.factory.FactoryImportDataDto;
import com.purchase.purchase_server.entity.excelObject.FactoryContainerExcel;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Description 导入补货计划-计划
 * <AUTHOR>
 * @Date 2024/1/15 15:49
 **/
@Slf4j
public class FactoryContainerImportListener extends AbstractFactoryImportListener<FactoryContainerExcel> {

    private final FactoryAssembler factoryAssembler = ApplicationContextProvider.getBean(FactoryAssembler.class);

    private final FactoryImportDataDto factoryImportDataDto;

    protected static final Integer TOTAL_ROW_NUMBER = 10000;

    public FactoryContainerImportListener(String fileName, List<String> errorList,
                                          FactoryImportDataDto factoryImportDataDto) {
        super(fileName, errorList);
        this.factoryImportDataDto = factoryImportDataDto;
    }

    @Override
    public void invoke(FactoryContainerExcel product, AnalysisContext analysisContext) {
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }
        boolean check = check(product, analysisContext);
        if (!check) {
            return;
        }
        if (StrUtil.isNotBlank(product.getFactoryCode()) && (StrUtil.isNotBlank(product.getProvince()) || StrUtil.isNotBlank(product.getCity()) ||
                StrUtil.isNotBlank(product.getAddressCode()) || StrUtil.isNotBlank(product.getDetailedAddress()))) {
            FactoryContainerDto factoryContainerDto = factoryAssembler.factoryContainerExcelToDto(product);

            Optional.ofNullable(factoryImportDataDto.getFactoryContainerDto())
                    .orElseGet(() -> {
                        List<FactoryContainerDto> list = new ArrayList<>();
                        factoryImportDataDto.setFactoryContainerDto(list);
                        return list;
                    })
                    .add(factoryContainerDto);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    /**
     * 这里会一行行的返回头
     *
     * @param headMap 表头map
     * @param context 上下文
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        //删除headMap中的空值
        headMap.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty());
        List<String> headMapList = new ArrayList<>(headMap.values());

        List<String> headList = new ArrayList<>();
        Field[] fields = FactoryContainerExcel.class.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ExcelProperty.class)) {
                ExcelProperty declaredAnnotation = field.getDeclaredAnnotation(ExcelProperty.class);
                String headValue = declaredAnnotation.value()[0];
                headList.add(headValue);
            }
        }
        if (!headMapList.equals(headList)) {
            throw new RuntimeException("导入表头不正确");
        }
    }
}
