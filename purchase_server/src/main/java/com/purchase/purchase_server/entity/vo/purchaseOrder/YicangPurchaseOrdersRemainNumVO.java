package com.purchase.purchase_server.entity.vo.purchaseOrder;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 易仓采购单未安排数量
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YicangPurchaseOrdersRemainNumVO implements Serializable {


    @Serial
    private static final long serialVersionUID = -1232246753305767121L;
    private String id;

    /**
     * 供应商代码
     */
    private String supplierCode;

    /**
     * 参考号
     */
    private String refNo;

    /**
     * 采购单号
     */
    private String poCode;
    /**
     * 图片
     */
    private String image;

    /**
     * 自定义sku
     */
    private String productSku;
    /**
     * 自定义sku名称
     */
    private String sbSelfSkuName;

    /**
     * senbo虚拟skuId
     */
    private String virtualSkuId;

    /**
     * senbo虚拟sku
     */
    private String destinationSku;

    /**
     * 跟单人
     */
    private String orderTracker;

    /**
     * 产品经理
     */
    private String buyer;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 运营
     */
    private String operator;


    /**
     * 采购量
     */
    private Integer qtyEta;
    /**
     * 工厂交期
     */
    private String expectedDeliveryDate;

    /**
     * 未安排数量
     */
    private Integer expectedDeliveryQuantity;

    /**
     * 采购单明细表id
     */
    private String sbPoDetailId;

    /**
     * 采购单id
     */
    private String sbPoId;

    private String sbPoVirtualId;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private String updateDate;

    /**
     * 实到总数
     */
    private Integer qtyRecevingAll;

    /**
     * 采购总数
     */
    private Integer qtyEtaAll;

    /**
     * 是否需要复审 0 不需要 1 需要
     */
    private Integer isReExamine;

    // 新增描述字段
    private Integer poStaus;

    // 新增描述字段
    private String poStatusDesc;
}