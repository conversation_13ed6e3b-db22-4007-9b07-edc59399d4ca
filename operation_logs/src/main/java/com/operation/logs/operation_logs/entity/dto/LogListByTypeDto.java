package com.operation.logs.operation_logs.entity.dto;

import com.crafts_mirror.utils.dp.BasePageForm;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/9
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class LogListByTypeDto extends BasePageForm implements Serializable {
    @Serial
    private static final long serialVersionUID = 819774925708376453L;
    private String trackNum;
    private String operationType;
}
