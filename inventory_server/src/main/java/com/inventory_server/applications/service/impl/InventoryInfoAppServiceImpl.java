package com.inventory_server.applications.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.inventory_server.applications.service.IInventoryAppService;
import com.inventory_server.infrastructures.entity.form.FileMissionForm;
import com.inventory_server.model.file.service.IFileCenterService;
import com.inventory_server.model.inventory.entity.dto.AsyncInventoryResultDto;
import com.inventory_server.model.inventory.entity.form.AsyncRealTimeInventoryForm;
import com.inventory_server.model.inventory.entity.form.RealTimeInventoryPageForm;
import com.inventory_server.model.inventory.entity.vo.NonFBARealTimeInventoryVo;
import com.inventory_server.model.inventory.repository.httpRepository.FetchYcInventoryInfoRepository;
import com.inventory_server.model.inventory.service.IFetchYcInventoryService;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/10 13:15
 **/
@Service
public class InventoryInfoAppServiceImpl implements IInventoryAppService {
    @Resource
    private IFetchYcInventoryService fetchYcInventoryService;

    @Resource
    private IFileCenterService fileCenterService;

    @Resource
    private FetchYcInventoryInfoRepository fetchYcInventoryInfoRepository;

    @Override
    public void fetchYcInventoryListAndSave() {
        fetchYcInventoryService.fetchAllYcInventory();
    }

    @Override
    @Async("asyncRealTimeInventoryThreadPool")
    public void asyncNonFBARealTimeInventory(AsyncRealTimeInventoryForm form) {
        // 增加同步非FBA仓实时库存同步记录
        String username = StrUtil.isNotBlank(SecurityUtils.getUsername()) ? SecurityUtils.getUsername() : "系统";
        String missionId = fileCenterService.uploadMissionStatus(FileMissionForm.builder()
                .importStatus("同步中").type("同步非FBA库存")
                .fileName("")
                .createBy(username)
                .build());
        AsyncInventoryResultDto dto = new AsyncInventoryResultDto(0);
        try {
            String startDate = StrUtil.isBlank(form.getStartDate()) ? null : form.getStartDate();
            String endDate = StrUtil.isBlank(form.getEndDate()) ? null : form.getEndDate();
            fetchYcInventoryInfoRepository.fetchYcNonFBAInventoryInfoAndSave(startDate, endDate, dto);
        } finally {
            fileCenterService.uploadMissionStatus(FileMissionForm.builder()
                    .importStatus("已完成")
                    .missionId(missionId)
                    .finishDate(new Date())
                    .failedResultList(dto.getFailedReasonList())
                    .importResult(String.format("成功：%d；失败：%d", dto.getSucceedCount(), dto.getFailedCount()))
                    .fileName("")
                    .createBy(username)
                    .build());
        }
    }

    @Override
    public IPage<NonFBARealTimeInventoryVo> nonFBARealTimeInventoryPage(RealTimeInventoryPageForm form) {
        return fetchYcInventoryService.nonFBARealTimeInventoryPage(form);
    }
}
