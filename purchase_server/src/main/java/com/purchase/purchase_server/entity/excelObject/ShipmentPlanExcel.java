package com.purchase.purchase_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelProperty;
import com.crafts_mirror.utils.aop.validator.PositiveDate;
import com.purchase.purchase_server.aop.validator.EnumValue;
import com.purchase.purchase_server.enums.FactoryFinancialSettementEnum;
import com.purchase.purchase_server.enums.shipment.ShipmentTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;

/**
 * @Description excel导入货件计划
 * <AUTHOR>
 * @Date 2024/11/11 11:39
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentPlanExcel extends BaseExcel implements Serializable {

    @NotBlank(message = "货件号不能为空")
    @Length(max = 30, message = "货件号长度超过30")
    @ExcelProperty("*货件号")
    private String shipmentCode;

    @ExcelProperty("*位置")
    @NotBlank(message = "位置不能为空")
    private String warehouseName;

    @ExcelProperty("实际装柜时间")
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_SLASH, message = "实际装柜时间格式不正确")
    private String actualLoadingTime;

    @ExcelProperty("开船")
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_SLASH, message = "开船时间格式不正确")
    private String shippingDate;

    @ExcelProperty("预计到港")
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_SLASH, message = "预计到港时间格式不正确")
    private String estimatedArrivalTime;

    @ExcelProperty("实际到港")
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_SLASH, message = "实际到港时间格式不正确")
    private String actualArrivalTime;

    @ExcelProperty("实际签收时间")
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_SLASH, message = "实际签收时间格式不正确")
    private String actualSignTime;

    @ExcelProperty("预计签收时间")
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_SLASH, message = "预计签收时间格式不正确")
    private String estimatedSignTime;

    @ExcelProperty("海外仓上架")
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_SLASH, message = "海外仓上架时间格式不正确")
    private String warehouseShelfTime;

    @ExcelProperty("实际签收")
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_SLASH, message = "实际签收格式不正确")
    private String actualSign;

    @ExcelProperty("*货品类型")
    @NotBlank(message = "货品类型不能为空")
    @EnumValue(enumClass = ShipmentTypeEnum.class, methodName = "ofDesc", message = "货品类型填写错误")
    private String shipmentType;
}
