package com.purchase.purchase_server.repository.dataRepository.Lcl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclShippingNumHistoryDO;
import com.purchase.purchase_server.entity.vo.Lcl.LclShippingNumHistoryVO;
import com.purchase.purchase_server.mapper.Lcl.LclShippingNumHistoryMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class LclShippingNumHistoryRepositoryImpl extends ServiceImpl<LclShippingNumHistoryMapper, LclShippingNumHistoryDO> {


    public List<LclShippingNumHistoryVO> list(String tsiId) {
        return baseMapper.selectJoinList(LclShippingNumHistoryVO.class, new MPJLambdaWrapper<LclShippingNumHistoryDO>()
                .selectAll(LclShippingNumHistoryDO.class)
                .eq(LclShippingNumHistoryDO::getLclConsolidationTsiId, tsiId)
                .orderByAsc(LclShippingNumHistoryDO::getId));
    };

    public int delete(List<String> lclConsolidationTsiIds){
        return baseMapper.deleteByLclConsolidationTsiIds(lclConsolidationTsiIds);
    }
}
