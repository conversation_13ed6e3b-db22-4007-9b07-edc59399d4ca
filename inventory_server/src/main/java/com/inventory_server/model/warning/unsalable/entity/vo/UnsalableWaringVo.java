package com.inventory_server.model.warning.unsalable.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN;
import static java.math.RoundingMode.HALF_UP;

/**
 * <AUTHOR>
 * @Date 2025/2/25 11:02
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UnsalableWaringVo {

    private String warningId;

    private String inventoryInfoId;

    private String productName;

    private String image;

    private String selfSku;

    private String virtualSku;

    private String oldSku;

    private String virtualSkuId;

    private String channel;

    private String operator;

    private Integer productStatus;

    private Integer productType;

    private String category;

    /**
     * 币种
     */
    private String currency;

    /**
     * 含税单价
     */
    private Double priceWithTaxes;

    /**
     * 无计划货值
     */
    private Double unsalablePrices;

    private Integer subType;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN)
    private String updateDate;

    /**
     * 无计划库存
     */
    private Integer totalUnsalableInventory;

    /**
     * 升级前虚拟sku
     */
    private String preUpgradeVirtualSku;

    /**
     * 借货策略
     */
    private Integer borrowingStrategy;

    public Double getUnsalablePrices() {
        return unsalablePrices == null ? null : BigDecimal.valueOf(unsalablePrices).setScale(3, HALF_UP).doubleValue();
    }
}
