package com.inventory_server.model.warning.urgentPurchase.entity.vo;

import com.inventory_server.model.redundancy.entity.dto.InterventionalTimeDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * @Description 加急补货列表页
 * <AUTHOR>
 * @Date 2025/4/22 16:24
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UrgentPurchasePageVo implements Serializable {

    private String urgentPurchaseId;

    private String inventoryInfoId;

    private String productName;

    private String image;

    private String selfSku;

    private String virtualSku;

    private String oldSku;

    private String virtualSkuId;

    private String channel;

    private String operator;

    private Integer productStatus;

    private Integer productType;

    private Integer subType;

    private String category;

    private String preUpgradeVirtualSku;

    /**
     * 加急补货仓库
     */
    private String urgentWarehouse;

    /**
     * 加急补货日
     */
    private LocalDate urgentPurchaseDate;

    /**
     * 可干预时间
     */
    private InterventionalTimeDto interventionalTimeDto;

    /**
     * 加急补货前断货天数
     */
    private Integer soldOutDaysBeforeUrgentPurchase;

    /**
     * 加急补货交期
     */
    private String urgentFactoryFinishedDate;

    /**
     * 加急补货量
     */
    private Integer urgentPurchaseNum;

    /**
     * 加急补货统计区间
     */
    private String urgentPurchaseRange;

    /**
     * 海外仓理论售罄时间
     */
    private String foreignTheoreticalSoldOutDate;

    /**
     * 售罄前断货天数
     */
    private Integer daysBeforeSoldOut;

    /**
     * 全链路理论售罄时间
     */
    private String fullLinkTheoreticalSoldOutDate;

    /**
     * 全链路售罄前断货天数
     */
    private Integer fullLinkDaysBeforeSoldOut;

    /**
     * 更新时间
     */
    private String updateDate;

}
