
package com.crafts_mirror.utils.enums.product;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum VirtualProductTypeEnum {
    REGULAR(0, "常规产品"),
    CH<PERSON><PERSON><PERSON>(1, "儿童产品"),
    SEASONAL(2, "季节性产品");

    private final Integer code;
    private final String desc;

    public static VirtualProductTypeEnum ofDesc(String value) {
        return Arrays.stream(VirtualProductTypeEnum.values())
                .filter(it -> it.getDesc().equals(value))
                .findFirst()
                .orElse(null);
    }

    public static VirtualProductTypeEnum ofCode(Integer code) {
        if (code == null){
            return null;
        }
        return Arrays.stream(VirtualProductTypeEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
