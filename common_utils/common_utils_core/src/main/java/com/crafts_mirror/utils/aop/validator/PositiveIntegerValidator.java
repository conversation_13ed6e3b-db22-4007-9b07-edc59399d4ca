package com.crafts_mirror.utils.aop.validator;

import cn.hutool.core.util.StrUtil;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @date 2024/6/7
 **/
public class PositiveIntegerValidator implements ConstraintValidator<PositiveInteger, Object> {

    @Override
    public void initialize(PositiveInteger constraintAnnotation) {
        // 初始化方法，可以留空
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null || StrUtil.isBlank(value.toString())) {
            return true;
        }
        try {
            String s = String.valueOf(value);
            int number = Integer.parseInt(s);
            return number > 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}