package com.purchase.purchase_server.entity.form;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 发货装柜
 * @date 2024/1/18
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LclShippingNumEditForm implements Serializable {

    @Serial
    private static final long serialVersionUID = -8895374137459917584L;

    private String id;
    /**
     * 发货数量
     */
    private Integer shippingNum;

    /**
     * 目标仓
     */
    private String destinationWarehouse;
}
