package com.inventory_server.infrastructures.interceptor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.config.CachedBodyHttpServletRequest;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.common.entity.LoginVo;
import com.crafts_mirror.utils.dp.VisitLogForm;
import com.crafts_mirror.utils.utils.UserAgentUtils;
import com.google.common.base.Stopwatch;
import com.inventory_server.model.visitLog.runnable.VisitLogRunnable;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.NamedThreadLocal;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import static com.crafts_mirror.common.security.utils.ServletUtils.getIpAddr;
import static com.crafts_mirror.common.security.utils.ServletUtils.getRequestBody;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.NO_NEED_TO_LOG_VISIT_LOG_SET;

/**
 * @Description 访问记录拦截器
 * <AUTHOR>
 * @Date 2024/8/9 15:50
 **/
@Configuration
@Slf4j
@NoArgsConstructor
public class VisitInfoInterceptor implements HandlerInterceptor {
    private static final ThreadLocal<Stopwatch> stopwatchThreadLocal = new NamedThreadLocal<>("LogInterceptor StartTime");

    private static final ThreadLocal<String> requestBodyThreadLocal = new NamedThreadLocal<>("LogInterceptor RequestBody");

    private static final ThreadLocal<String> requestParamThreadLocal = new NamedThreadLocal<>("LogInterceptor RequestParam");

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource(name = "visitLogThreadPool")
    private Executor visitLogThreadPool;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) throws IOException {
        CachedBodyHttpServletRequest wrappedRequest = new CachedBodyHttpServletRequest(request);

        // 从请求体中读取参数
        String contentType = wrappedRequest.getContentType();
        if (StrUtil.isBlank(contentType) || !contentType.startsWith("multipart/form-data")) {
            String requestBody = getRequestBody(wrappedRequest);
            requestBodyThreadLocal.set(requestBody);
        }
        requestParamThreadLocal.set(JSON.toJSONString(wrappedRequest.getParameterMap()));
        // 在请求处理之前启动 Stopwatch
        Stopwatch stopwatch = Stopwatch.createStarted();
        stopwatchThreadLocal.set(stopwatch);
        return true;
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler, Exception ex) {
        // 在请求处理之后记录并打印响应时间
        Stopwatch stopwatch = stopwatchThreadLocal.get();
        VisitLogForm visitLogForm = new VisitLogForm();
        visitLogForm.setOperatorTime(LocalDateTime.now());

        if (stopwatch != null) {
            stopwatch.stop();
            visitLogForm.setResponseTime(stopwatch.elapsed(TimeUnit.MILLISECONDS));
            // 清理 ThreadLocal 以防止内存泄漏
            stopwatchThreadLocal.remove();
        }

        String servletPath = "inventory" + request.getServletPath();
        visitLogForm.setRequestUrl(servletPath);
        visitLogForm.setCustomIp(getIpAddr(request));

        UserAgentUtils userAgent = new UserAgentUtils(request.getHeader("User-Agent"));
        visitLogForm.setDeviceName(userAgent.getDevice());
        visitLogForm.setBrowser(userAgent.getBrowser());

        String requestBody = requestBodyThreadLocal.get();
        visitLogForm.setRequestBody(StrUtil.isBlank(requestBody) ? null : requestBody);
        String requestParam = requestParamThreadLocal.get();
        visitLogForm.setRequestParam(StrUtil.isBlank(requestParam) ? null : requestParam);
        requestBodyThreadLocal.remove();
        requestParamThreadLocal.remove();
        visitLogForm.setRequestMethod(request.getMethod());

        if (Boolean.TRUE.equals(stringRedisTemplate.opsForSet().isMember(NO_NEED_TO_LOG_VISIT_LOG_SET, servletPath))) {
            return;
        }
        // 获取用户名
        LoginVo loginUser = SecurityUtils.getLoginUser();
        String userName = loginUser == null ? "unknown" : loginUser.getUserName();
        visitLogForm.setOperator(userName);

        visitLogThreadPool.execute(new VisitLogRunnable(visitLogForm, handler));

        log.info("接口请求记录{}", visitLogForm);
    }
}
