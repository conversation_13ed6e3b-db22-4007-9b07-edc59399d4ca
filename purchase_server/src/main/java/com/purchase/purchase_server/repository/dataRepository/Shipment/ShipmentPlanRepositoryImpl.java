package com.purchase.purchase_server.repository.dataRepository.Shipment;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.aop.OperationLog;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.dataObject.ShipmentDetailDO;
import com.purchase.purchase_server.entity.dataObject.ShipmentPlanDO;
import com.purchase.purchase_server.entity.dto.Shipment.ShipmentPlanDto;
import com.purchase.purchase_server.entity.form.ShipmentPlanForm;
import com.purchase.purchase_server.entity.vo.ShipmentPlan.ShipmentPlanVO;
import com.purchase.purchase_server.mapper.ShipmentPlanMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/11/18 15:00
 **/
@Service
public class ShipmentPlanRepositoryImpl extends ServiceImpl<ShipmentPlanMapper, ShipmentPlanDO> {


    public List<ShipmentPlanDO> list(ShipmentPlanDto val) {
        return list(Wrappers.<ShipmentPlanDO>lambdaQuery()
                .eq(StrUtil.isNotBlank(val.getWarehouseId()), ShipmentPlanDO::getWarehouseId, val.getWarehouseId())
                .eq(StrUtil.isNotBlank(val.getShipmentCode()), ShipmentPlanDO::getShipmentCode, val.getShipmentCode())
        );
    }

    @OperationLog(content = "货件计划创建或更新", operationType = "货件计划管理")
    public void saveOrUpdatePlan(ShipmentPlanDO val, LogTrackNumDto logTrackNumDto) {
        String id = val.getId();
        baseMapper.insertOrUpdate(val);
        Map<String, String> logMap = new HashMap<>();
        logMap.put(val.getId(), StrUtil.isBlank(id) ? "创建货件" : "编辑计划时间");
        logTrackNumDto.setLogMap(logMap);
        logTrackNumDto.setAuthorization(SecurityUtils.getToken());
    }

    public IPage<ShipmentPlanVO> page(ShipmentPlanForm form) {
        IPage<ShipmentPlanVO> page = new Page<>(form.getCurrent(), form.getSize());
        return baseMapper.selectJoinPage(page, ShipmentPlanVO.class, new MPJLambdaWrapper<ShipmentPlanDO>()
                .distinct()
                .selectAll(ShipmentPlanDO.class)
                .leftJoin(ShipmentDetailDO.class, ShipmentDetailDO::getShipmentPlanId, ShipmentPlanDO::getId)
                .eq(StrUtil.isNotBlank(form.getPlanId()), ShipmentPlanDO::getId, form.getPlanId())
                .like(StrUtil.isNotBlank(form.getProductName()), ShipmentDetailDO::getSelfProductName, form.getProductName())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), ShipmentDetailDO::getSelfSku, form.getSelfSkuList())
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), ShipmentDetailDO::getDestinationSku, form.getVirtualSkuList())
                .like(StrUtil.isNotBlank(form.getShipmentCode()), ShipmentPlanDO::getShipmentCode, form.getShipmentCode())
                .eq(StrUtil.isNotBlank(form.getWarehouseId()), ShipmentPlanDO::getWarehouseId, form.getWarehouseId())
                .like(StrUtil.isNotBlank(form.getContractNo()), ShipmentDetailDO::getContractNo, form.getContractNo())
                .orderByDesc(ShipmentPlanDO::getUpdateDate)
        );
    }

    public void deleteById(String planId) {
        baseMapper.deleteById(planId);
    }

    public void updatePlanUpdateDate(List<String> ids) {
        if (CollectionUtil.isNotEmpty(ids)) {
            baseMapper.update(new UpdateWrapper<ShipmentPlanDO>().lambda()
                    .set(ShipmentPlanDO::getUpdateBy, SecurityUtils.getUsername())
                    .set(ShipmentPlanDO::getUpdateDate, new Date())
                    .in(ShipmentPlanDO::getId, ids));
        }
    }
}
