package com.auth.authorization_server.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class PostVO implements Serializable {


    @Serial
    private static final long serialVersionUID = 8102868719969572910L;
    /**
     * 职位id
     */
    private String id;

    /**
     * 职位名称
     */
    private String postName;

}
