package com.purchase.purchase_server.enums;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentProjectDO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum RepProjectDirectionAndSortEnum {

    FULL_LINK_THEORETICAL_SOLD_OUT_DATE("fullLinkTheoreticalSoldOutDate","全链路海外仓理论售罄时间"),
    FULL_LINK_DAYS_BEFORE_SOLD_OUT("fullLinkDaysBeforeSoldOut","全链路售罄前断货天数"),

    ASC("asc","升序"),
    DESC("desc","降序");

    private final String code;
    private final String desc;
    public static RepProjectDirectionAndSortEnum ofCode(String code) {
        return Arrays.stream(RepProjectDirectionAndSortEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
    public static List<String> getCodeList() {
        return Arrays.stream(RepProjectDirectionAndSortEnum.values())
                .map(RepProjectDirectionAndSortEnum::getCode).collect(Collectors.toList());
    }

    public static final Map<String, SFunction<ReplenishmentProjectDO, ?>> sortMap;
    static {
        sortMap = Map.of(
                FULL_LINK_THEORETICAL_SOLD_OUT_DATE.getCode(), ReplenishmentProjectDO::getFullLinkTheoreticalSoldOutDate,
                FULL_LINK_DAYS_BEFORE_SOLD_OUT.getCode(), ReplenishmentProjectDO::getFullLinkDaysBeforeSoldOut
        );
    }
}
