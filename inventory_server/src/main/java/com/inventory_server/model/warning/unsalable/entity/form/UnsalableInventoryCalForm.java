package com.inventory_server.model.warning.unsalable.entity.form;

import com.inventory_server.model.redundancy.entity.dos.InventoryForeignStoreDO;
import com.inventory_server.model.redundancy.entity.dto.DeliveryCalResultDto;
import com.inventory_server.model.redundancy.entity.dto.FactoryFinishedInventoryDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/24 13:31
 **/
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class UnsalableInventoryCalForm implements Serializable {
    private String inventoryInfoId;

    private String virtualSkuId;

    private DeliveryCalResultDto normalDeliveryResultDto;

    private List<InventoryForeignStoreDO> remainInventoryList;

    /**
     * 工厂计划
     */
    private List<FactoryFinishedInventoryDto> factoryPlanInfoList;
}
