package com.inventory_server.interfaces.scheduled;

import com.inventory_server.model.redundancy.entity.dos.RedundantInventoryDO;
import com.inventory_server.model.redundancy.repository.dataRepository.RedundantInventoryRepository;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 冗余库存定时任务
 * <AUTHOR>
 * @Date 2024/7/29 17:49
 **/
@Service
@Slf4j
public class RedundancyJob {

    @Resource
    private RedundantInventoryRepository redundantInventoryRepository;

    @XxlJob("setSnapshotId")
    public void setSnapshotId() {
        List<RedundantInventoryDO> allRedundancyList = redundantInventoryRepository.getAllRedundancyList();
    }
}
