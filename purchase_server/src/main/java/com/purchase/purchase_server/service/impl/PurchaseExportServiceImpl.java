package com.purchase.purchase_server.service.impl;


import com.purchase.purchase_server.service.IPurchaseExportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.TreeMap;

import static com.crafts_mirror.utils.utils.PatternUtils.UPPERCASE_LOWERCASE_PATTERN;

@Service
@Slf4j
public class PurchaseExportServiceImpl implements IPurchaseExportService {
    @Override
    public List<List<String>> getExcelHeadList(List<String> skuHeadList) {
        List<List<String>> excelHeadList = new ArrayList<>();
        excelHeadList.add(Arrays.asList("状态", "状态"));
        excelHeadList.add(Arrays.asList("时间", "时间"));
        excelHeadList.add(Arrays.asList("天数", "天数"));
        String[] titles = {"模拟在途库存", "模拟剩余库存", "模拟日销"};
        for (String title : titles) {
            for (String s : skuHeadList) {
                excelHeadList.add(Arrays.asList(title, s));
            }
        }
        return excelHeadList;
    }

    @Override
    public List<List<String>> excelDataList(Boolean isDelivery, TreeMap<String, TreeMap<String, Double>> onshippingInventory,
                                            TreeMap<String, TreeMap<String, Double>> remainingInventory,
                                            TreeMap<String, TreeMap<String, Double>> dailySales) {

        List<String> dateList = new ArrayList<>(onshippingInventory.keySet());
        List<String> skuHeadList = onshippingInventory.get(dateList.get(0)).keySet().stream()
                .filter(s -> !UPPERCASE_LOWERCASE_PATTERN.matcher(s).matches())
                .toList();
        List<List<String>> excelDataList = new ArrayList<>();
        int i = 1;
        StringBuffer status = new StringBuffer();
        for (String date : dateList) {
            List<String> data = new ArrayList<>();
            if (isDelivery){
                if (onshippingInventory.get(date).containsKey("startShipping")) {
                    status.append("发");
                }
                if (remainingInventory.get(date).containsKey("startSale")) {
                    status.append("到");
                }
            }else {
                if (onshippingInventory.get(date).containsKey("purchaseStart")) {
                    status.append("采");
                }
                if (onshippingInventory.get(date).containsKey("purchaseStartShipping")) {
                    status.append("发");
                }
                if (remainingInventory.get(date).containsKey("purchaseStartSale")) {
                    status.append("到");
                }
            }
            data.add(status.toString());
            status.setLength(0);
            data.add(date);
            data.add(String.valueOf(i++));
            for (String s : skuHeadList) {
                data.add(String.valueOf(onshippingInventory.getOrDefault(date, new TreeMap<>()).getOrDefault(s, 0D)));
            }
            for (String s : skuHeadList) {
                data.add(String.valueOf(remainingInventory.getOrDefault(date, new TreeMap<>()).getOrDefault(s, 0D)));
            }
            for (String s : skuHeadList) {
                data.add(String.valueOf(dailySales.getOrDefault(date, new TreeMap<>()).getOrDefault(s, 0D)));
            }
            excelDataList.add(data);
        }
        return excelDataList;
    }
}
