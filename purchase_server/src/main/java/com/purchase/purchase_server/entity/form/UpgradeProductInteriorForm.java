package com.purchase.purchase_server.entity.form;

import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 升级款查询
 * <AUTHOR>
 * @Date 2023/12/6 11:07
 **/
@Data
@Builder
public class UpgradeProductInteriorForm implements Serializable {


    @Serial
    private static final long serialVersionUID = 267435432260392146L;
    private String id;

    /**
     * 原skuId
     */
    private String originalId;

    /**
     * 升级款对应skuId
     */
    private String upgradeSkuId;

}
