package com.purchase.purchase_server.repository.interiorRepository.impl;

import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.common.entity.ChannelSearchVo;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.purchase.purchase_server.repository.interiorRepository.IChannelInteriorRepository;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import static com.crafts_mirror.utils.constant.SystemConstant.ALL_NICHE_CHANNEL_URL;

/**
 * <AUTHOR>
 * @Date 2025/5/6 11:12
 **/
@Service
public class ChannelInteriorRepositoryImpl implements IChannelInteriorRepository {

    @Resource
    private RestTemplate restTemplate;

    @Override
    public ChannelSearchVo getAllChannel() {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO resultDTO = restTemplateUtil.get(ALL_NICHE_CHANNEL_URL, ResultDTO.class);

        return JSON.to(ChannelSearchVo.class, resultDTO.getData());
    }
}
