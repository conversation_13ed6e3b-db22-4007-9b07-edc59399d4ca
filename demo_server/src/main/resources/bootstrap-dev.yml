spring:
  cloud:
    nacos:
      config:
        server-addr: http://192.168.8.18:8848
        file-extension: yml
        group: DEFAULT_GROUP
        namespace: 4abef59b-ac24-4dd7-abe9-11adf57f9de1
        username: nacos
        password: nacos
        prefix: ${spring.application.name}
        refresh-enabled: true
      discovery:
        server-addr: http://192.168.8.18:8848
        group: DEFAULT_GROUP
        namespace: 4abef59b-ac24-4dd7-abe9-11adf57f9de1
        username: nacos
        password: nacos