package com.purchase.purchase_server.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderDetailDO;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【cm_yicang_purchase_order_detail(易仓采购单明细)】的数据库操作Mapper
* @createDate 2024-11-01 14:30:43
*/
public interface YicangPurchaseOrderDetailMapper extends MPJBaseMapper<YicangPurchaseOrderDetailDO> {

    /**
     * 根据采购单表id删除
     * @param sbPoId 采购单id
     */
    void removeBySbPoId(@Param("sbPoId") String sbPoId);
}




