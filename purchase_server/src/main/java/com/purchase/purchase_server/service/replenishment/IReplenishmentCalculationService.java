package com.purchase.purchase_server.service.replenishment;

import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.form.ReplenishmentDetailForm;

import java.util.List;

public interface IReplenishmentCalculationService {
    /**
     * 计算补货
     */
    void calReplenishment(List<SenboWarehouseDto> senboWarehouseList);

    /**
     *  重新计算补货
     */
    void reCalReplenishment(ReplenishmentDetailForm form);

}
