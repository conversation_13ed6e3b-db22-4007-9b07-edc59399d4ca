package com.purchase.purchase_server.entity.form;

import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentSaleDestinationDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description 补货详情表单
 * <AUTHOR>
 * @Date 2024/1/22 14:38
 **/
@Data
public class ReplenishmentDetailForm implements Serializable {
    private String replenishmentId;

    private String purchaseNumType;

    /**
     * 建议采购日开始时间
     */
    private String advicePurchaseStartDate;

    /**
     * 建议采购日结束时间
     */
    private String advicePurchaseEndDate;

    private String trialPurchaseId;

    /**
     * 运营确认状态
     */
    private String operationConfirmedStatus;

    private String destinationWarehouse;

    private String advicePurchaseDate;

    // 目标日销年月
    private List<String> saleDestinationYmList;

    private String saleDestinationYm;

    private Map<String, List<ReplenishmentSaleDestinationDto>> saleDestinationMap;
}
