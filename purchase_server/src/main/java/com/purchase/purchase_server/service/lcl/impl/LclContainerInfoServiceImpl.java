package com.purchase.purchase_server.service.lcl.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.enums.YesOrNoEnum;
import com.purchase.purchase_server.assembler.LclConsolidationAssembler;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclConsolidationRecordDO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclConsolidationTrialShippingInventoryDO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclContainerDetailDO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclContainerInfoDO;
import com.purchase.purchase_server.entity.dataObject.ProductSnapshotDO;
import com.purchase.purchase_server.entity.dataObject.SelfProductDO;
import com.purchase.purchase_server.entity.dto.FactoryInfoDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclConsolidationNonFinishedInventoryDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclContainerDetailExportDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclContainerInfoDto;
import com.purchase.purchase_server.entity.excelObject.Lcl.LclConsolidationNonFinishedExcel;
import com.purchase.purchase_server.entity.excelObject.Lcl.LclContainerInfoExcel;
import com.purchase.purchase_server.entity.form.LclContainerDetailDeleteForm;
import com.purchase.purchase_server.entity.form.LclContainerMoveForm;
import com.purchase.purchase_server.entity.form.LclSearchPageForm;
import com.purchase.purchase_server.entity.vo.Lcl.LclContainerInfoPage;
import com.purchase.purchase_server.entity.vo.Lcl.LclContainerInfoVo;
import com.purchase.purchase_server.entity.vo.UserInteriorVO;
import com.purchase.purchase_server.enums.lcl.IsPackageFullEnum;
import com.purchase.purchase_server.enums.lcl.MoveOrSplitTypeEnum;
import com.purchase.purchase_server.enums.lcl.PCSTypeEnum;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.repository.dataRepository.Lcl.*;
import com.purchase.purchase_server.repository.dataRepository.ProductSnapRepositoryImpl;
import com.purchase.purchase_server.service.ISysUserService;
import com.purchase.purchase_server.service.channel.IChannelInfoService;
import com.purchase.purchase_server.service.lcl.ILclCalContainerLoadingService;
import com.purchase.purchase_server.service.lcl.ILclContainerInfoService;
import com.purchase.purchase_server.service.lcl.ILclContainerLoadingDetailService;
import com.purchase.purchase_server.service.lcl.ILclFinishedInventoryService;
import com.purchase.purchase_server.utils.commonUtils.CompositeKey;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YY_MM_DD_DATE_FORMAT_DOT;

/**
 * <AUTHOR>
 * @description 针对表【cm_lcl_consolidation_record(拼柜装柜记录表)】的数据库操作Service实现
 * @createDate 2024-12-04 16:47:06
 */
@Service
@Slf4j
public class LclContainerInfoServiceImpl implements ILclContainerInfoService {

    @Resource
    private LclConsolidationRecordRepositoryImpl recordRepository;

    @Resource
    private LclConsolidationFinishedInventoryRepositoryImpl lclConFinRepositoryImpl;

    @Resource
    private LclConsolidationTrialShippingInventoryRepositoryImpl lclConTrialFinRepositoryImpl;

    @Resource
    private LclContainerInfoRepositoryImpl lclContainerInfoRepository;

    @Resource
    private LclContainerDetailRepositoryImpl lclContainerDetailRepository;

    @Resource
    private ILclFinishedInventoryService lclFinishedInventoryService;

    @Resource
    private LclConsolidationAssembler lclConsolidationAssembler;

    @Resource
    private ILclCalContainerLoadingService lclCalContainerLoadingService;

    @Resource
    private ILclContainerLoadingDetailService lclContainerLoadingDetailService;

    @Resource
    private IChannelInfoService channelInfoService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private ProductSnapRepositoryImpl productSnapRepository;

    @Override
    public void exportLclContainerLoadingInfo(LclSearchPageForm form, HttpServletResponse response) {
        LclConsolidationRecordDO recordDO = recordRepository.getById(form.getRecordId());

        if (ObjectUtil.isEmpty(recordDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "装柜计划不存在");
        }
        List<LclConsolidationNonFinishedExcel> nonFinishedExcels = new ArrayList<>();

        var nonFinishedInventoryDTOS = lclFinishedInventoryService.listByRecordId(form.getRecordId());
        if (CollectionUtil.isNotEmpty(nonFinishedInventoryDTOS)) {
            nonFinishedInventoryDTOS.forEach(i -> {
                i.setFactoryRemainNum(i.getFactoryShippingPackageNum());
            });
            nonFinishedExcels = lclConsolidationAssembler.lclNonContainerListDTOToExcel(nonFinishedInventoryDTOS);
        }

        List<List<LclContainerInfoExcel>> lclContainerInfoExcels = new ArrayList<>();
        LclSearchPageForm infoPageForm = new LclSearchPageForm();
        infoPageForm.setCurrent(1);
        infoPageForm.setSize(-1);
        infoPageForm.setRecordId(recordDO.getId());
        LclContainerInfoPage<LclContainerInfoVo> lclContainerInfoPage = lclCalContainerLoadingService.getLclContainerInfoPage(infoPageForm);
        List<LclContainerInfoVo> lclContainerInfoList = lclContainerInfoPage.getRecords();

        List<LclContainerDetailExportDTO> lclContainerSheet1ExportDTOS = new ArrayList<>();
        List<LclConsolidationNonFinishedExcel> lclConNonFinishedExcels = new ArrayList<>();
        List<LclContainerDetailExportDTO> detailList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(lclContainerInfoList)) {
            List<String> infoListIds = lclContainerInfoList.stream().map(LclContainerInfoVo::getContainerId).collect(Collectors.toList());

            detailList = lclContainerDetailRepository.listByLclInfoIds(infoListIds);

            Map<String, List<LclContainerDetailExportDTO>> detailGroupByInfoId = detailList.stream().collect(Collectors.groupingBy(LclContainerDetailExportDTO::getContainerInfoId));

            lclContainerInfoExcels = processContainerInfoData(lclContainerInfoList, detailGroupByInfoId);

            Map<String, List<LclContainerDetailExportDTO>> noneList = detailList.stream().collect(Collectors.groupingBy(LclContainerDetailExportDTO::getConsolidationFactoryFinishedId));

            Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();
            List<UserInteriorVO> userList = sysUserService.getUserList();
            Map<String, String> userMap = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));
            for (var nonSheet : noneList.entrySet()) {
                List<LclContainerDetailExportDTO> nonList = nonSheet.getValue();
                int sum = nonList.stream().mapToInt(LclContainerDetailExportDTO::getLclShippingNum).sum();
                LclContainerDetailExportDTO first = lclConsolidationAssembler.lclNonContainerDetailExportToExcel(nonList.getFirst());
                first.setFactoryRemainNum(first.getFactoryShippingPackageNum() - sum);
                first.setChannel(channelIdNameMap.getOrDefault(first.getChannel(), first.getChannel()));
                first.setPurchaser(userMap.getOrDefault(first.getPurchaser(), first.getPurchaser()));
                first.setOrderTracker(userMap.getOrDefault(first.getOrderTracker(), first.getOrderTracker()));
                first.setBuyer(userMap.getOrDefault(first.getBuyer(), first.getBuyer()));
                first.setOperator(StrUtil.isNotBlank(first.getOperator()) ?
                        Arrays.stream(first.getOperator().split(",", 0))
                                .map(op -> StrUtil.isNotBlank(userMap.get(op)) ? userMap.get(op) : op)
                                .collect(Collectors.joining(",")) : first.getOperator());
                BigDecimal caseVolume = BigDecimal.valueOf(first.getCaseLength() * first.getCaseWidth() * first.getCaseHeight() / 1000000)
                        .setScale(4, RoundingMode.HALF_UP);
                first.setCaseVolume(caseVolume.doubleValue());
                PCSTypeEnum.ofCode(first.getPcsType())
                        .ifPresent(pCSTypeEnum -> first.setPcsType(pCSTypeEnum.getDesc()));
                YesOrNoEnum.ofCode(first.getCommodityInspection())
                        .ifPresent(orNoEnum -> first.setCommodityInspection(orNoEnum.getDesc()));

                lclContainerSheet1ExportDTOS.add(first);
            }
            if (CollectionUtil.isNotEmpty(lclContainerSheet1ExportDTOS)) {
                lclConNonFinishedExcels.addAll(lclConsolidationAssembler.lclNonContainerDetailExportDtoToExcel(lclContainerSheet1ExportDTOS));
            }
        }

        // 获取所有已完成的装柜信息中的组合键
        Set<CompositeKey> completedKeys = new HashSet<>();

        detailList.forEach(excel -> {
                    CompositeKey key = new CompositeKey(
                            excel.getContractCode(),
                            excel.getShipmentCode(),
                            excel.getFactoryFinishedDate(),
                            excel.getDestinationSku(),
                            excel.getProductName(),
                            excel.getRemarks()
                    );
                    completedKeys.add(key);
                }
        );

        // 过滤掉nonFinishedExcels中与已完成装柜信息完全匹配的数据
        List<LclConsolidationNonFinishedExcel> filteredNonFinishedExcels = nonFinishedExcels.stream()
                .filter(excel -> {
                    CompositeKey key = new CompositeKey(
                            excel.getContractCode(),
                            excel.getShipmentCode(),
                            excel.getFactoryFinishedDate(),
                            excel.getDestinationSku(),
                            excel.getProductName(),
                            excel.getRemarks()
                    );
                    return !completedKeys.contains(key);
                })
                .collect(Collectors.toList());

        // 获取数据处理后 从在途拆分出来没有还回去的计划
        var lclConFinished = lclConFinRepositoryImpl.listByLclRecordId(form.getRecordId());
        List<LclConsolidationNonFinishedInventoryDTO> lclConNonFinishedInventoryDTOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(lclConFinished)) {
            List<String> lclConFinishedIds = lclConFinished.stream().map(LclConsolidationNonFinishedInventoryDTO::getId).collect(Collectors.toList());

            List<LclConsolidationTrialShippingInventoryDO> lclConTrialList = lclConTrialFinRepositoryImpl.list(Wrappers.<LclConsolidationTrialShippingInventoryDO>lambdaQuery()
                    .in(LclConsolidationTrialShippingInventoryDO::getConsolidationFactoryFinishedId, lclConFinishedIds));

            var lclConTrialMap = lclConTrialList.stream().collect(Collectors.groupingBy(LclConsolidationTrialShippingInventoryDO::getConsolidationFactoryFinishedId));

            lclConFinished.forEach(item -> {
                String id = item.getId();
                var lclConTrials = lclConTrialMap.getOrDefault(id, new ArrayList<>());
                if (CollectionUtil.isNotEmpty(lclConTrials)) {
                    boolean flag = false;
                    for (var lclConTrial : lclConTrials) {
                        flag = lclConTrial.getLclShippingNum() > 0;
                        break;
                    }
                    if (!flag) {
                        item.setFactoryRemainNum(item.getFactoryShippingPackageNum());
                        lclConNonFinishedInventoryDTOS.add(item);
                    }
                } else {
                    item.setFactoryRemainNum(item.getFactoryShippingPackageNum());
                    lclConNonFinishedInventoryDTOS.add(item);
                }
            });
            if (CollectionUtil.isNotEmpty(lclConNonFinishedInventoryDTOS)) {
                filteredNonFinishedExcels.addAll(lclConsolidationAssembler.lclNonContainerListDTOToExcel(lclConNonFinishedInventoryDTOS));
            }
        }
        filteredNonFinishedExcels.addAll(lclConNonFinishedExcels);

        List<LclConsolidationNonFinishedExcel> thanZeroList = filteredNonFinishedExcels.stream().filter(item -> item.getFactoryRemainNum() > 0).toList();

        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String dateStr = DateUtil.format(DateUtil.date(), YY_MM_DD_DATE_FORMAT_DOT);
            String fileName = URLEncoder.encode("订柜计划" + dateStr, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();

            WriteSheet writeSheet = EasyExcel.writerSheet(1, "剩余计划数量")
                    .head(LclConsolidationNonFinishedExcel.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            excelWriter.write(thanZeroList, writeSheet);
            int i = 2;
            for (var result : lclContainerInfoExcels) {
                String shipmentCode = result.getFirst().getShipmentCode();
                WriteSheet writeSheet2 = EasyExcel.writerSheet(i, shipmentCode)
                        .head(LclContainerInfoExcel.class)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .build();
                excelWriter.write(result, writeSheet2);
                i++;
            }
        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 处理装柜信息数据，将LclContainerInfoVo和LclContainerDetailDO组合成LclContainerInfoExcel列表
     *
     * @param lclContainerInfoList 装柜信息列表
     * @param detailGroupByInfoId  装柜详情Map
     * @return 组合后的Excel数据列表
     */
    private List<List<LclContainerInfoExcel>> processContainerInfoData(List<LclContainerInfoVo> lclContainerInfoList,
                                                                       Map<String, List<LclContainerDetailExportDTO>> detailGroupByInfoId) {

        List<List<LclContainerInfoExcel>> resultLclContainerInfoExcelList = new ArrayList<>();
        Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();
        List<UserInteriorVO> userList = sysUserService.getUserList();
        Map<String, String> userMap = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        lclContainerInfoList.forEach(info -> {
            List<LclContainerInfoExcel> lclContainerInfoExcelList = new ArrayList<>();
            List<LclContainerDetailExportDTO> details = detailGroupByInfoId.getOrDefault(info.getContainerId(), new ArrayList<>());
            String destinationWarehouse = info.getDestinationWarehouse();

            details.forEach(detail -> {
                BigDecimal caseVolume = BigDecimal.valueOf(detail.getCaseLength() * detail.getCaseWidth() * detail.getCaseHeight() / 1000000)
                        .setScale(4, RoundingMode.HALF_UP);
                LclContainerInfoExcel excel = LclContainerInfoExcel.builder()
                        // 基本信息
                        .shipmentCode(info.getShipmentCode())
                        .shippingStartDate(info.getShippingStartDate())
                        .destinationWarehouse(destinationWarehouse)
                        .addressCode(info.getAddressCode())
                        .shippingQuantity(info.getShippingQuantity())
                        .shippingVolume(String.valueOf(info.getShippingVolume()))
                        .containerLoadingRate(String.valueOf(info.getContainerLoadingRate()))
                        .surplusVolume(String.valueOf(info.getSurplusVolume()))
                        .inspectionVolumeRatio(String.valueOf(info.getInspectionVolumeRatio()))
                        .shipmentCategory(info.getShipmentCategory())

                        // 从详情中获取的数据
                        .contractCode(detail.getContractCode())
                        .destinationSku(detail.getDestinationSku())
                        .productName(detail.getProductName())
                        .channel(channelIdNameMap.getOrDefault(detail.getChannel(), detail.getChannel()))
                        .selfSku(detail.getSelfSku())
                        .containerLoad(detail.getContainerLoad())
                        .caseLength(detail.getCaseLength())
                        .caseWidth(detail.getCaseWidth())
                        .detailAddressCode(detail.getAddressCode())
                        .caseHeight(detail.getCaseHeight())
                        .caseVolume(caseVolume.doubleValue())
                        .singleCaseGrossWeight(detail.getSingleCaseGrossWeight())
                        .purchaser(userMap.getOrDefault(detail.getPurchaser(), detail.getPurchaser()))
                        .orderTracker(userMap.getOrDefault(detail.getOrderTracker(), detail.getOrderTracker()))
                        .buyer(userMap.getOrDefault(detail.getBuyer(), detail.getBuyer()))
                        .operator(StrUtil.isNotBlank(detail.getOperator()) ?
                                Arrays.stream(detail.getOperator().split(",", 0))
                                        .map(op -> StrUtil.isNotBlank(userMap.get(op)) ? userMap.get(op) : op)
                                        .collect(Collectors.joining(",")) : detail.getOperator())
                        .currency(detail.getCurrency())
                        .unitOrContractPrice(detail.getUnitOrContractPrice())
                        .factoryFinishedDate(detail.getFactoryFinishedDate())
                        .remarks(detail.getRemarks())
                        .unassignedQuantity(detail.getFactoryRemainNum())
                        .remarkVolume(detail.getRemarks())
                        .build();
                PCSTypeEnum.ofCode(detail.getPcsType())
                        .ifPresent(pCSTypeEnum -> excel.setPcsType(pCSTypeEnum.getDesc()));
                YesOrNoEnum.ofCode(detail.getCommodityInspection())
                        .ifPresent(orNoEnum -> excel.setCommodityInspection(orNoEnum.getDesc()));
                // 根据仓库名称判断并设置对应字段
                Integer lclShippingNum = detail.getLclShippingNum();
                double volume = caseVolume.multiply(new BigDecimal(detail.getLclShippingNum())).doubleValue();
                if (destinationWarehouse != null) {
                    if (destinationWarehouse.contains("洛杉矶")) {
                        excel.setLosAngeles(lclShippingNum);
                        excel.setWestCoastWarehouseVolume(volume);
                    } else if (destinationWarehouse.contains("萨凡纳")) {
                        excel.setSavannah(lclShippingNum);
                        excel.setSoutheastWarehouseVolume(volume);
                    } else if (destinationWarehouse.contains("休斯顿")) {
                        excel.setHouston(lclShippingNum);
                    } else if (destinationWarehouse.contains("新泽西")) {
                        excel.setNewJersey(lclShippingNum);
                        excel.setSouthWarehouseVolume(volume);
                    } else {
                        excel.setAuxiliaryColumn(lclShippingNum);
                        excel.setAuxiliaryVolume(volume);
                    }
                } else {
                    excel.setAuxiliaryColumn(lclShippingNum);
                    excel.setAuxiliaryVolume(volume);
                }
                excel.setUnassignedVolume(caseVolume.multiply(new BigDecimal(detail.getFactoryRemainNum())).doubleValue());
                lclContainerInfoExcelList.add(excel);
            });
            resultLclContainerInfoExcelList.add(lclContainerInfoExcelList);
        });
        return resultLclContainerInfoExcelList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean moveOrSplitContainerData(LclContainerMoveForm form) {
        return splitContainerData(form);
    }

    @Override
    public String checkMoveOrSplitContainerData(LclContainerMoveForm form) {

        String result = null;
        if (StrUtil.isNotBlank(form.getTargetContainerId())) {
            LclContainerInfoDO infoDO = lclContainerInfoRepository.getById(form.getTargetContainerId());
            if (ObjectUtil.isEmpty(infoDO)) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "目标货件不存在");
            }
            LclSearchPageForm lclSearchPageForm = new LclSearchPageForm();
            lclSearchPageForm.setContainerId(infoDO.getId());

            List<LclContainerDetailDO> detailDO = lclContainerDetailRepository.listByIds(form.getSourceDetailIds());

            List<ProductSnapshotDO> productSnapshotList = productSnapRepository.selectListByIdList(detailDO.stream().map(LclContainerDetailDO::getProductSnapshotId).toList());
            var snapshotDOMap = productSnapshotList.stream().collect(Collectors.toMap(ProductSnapshotDO::getId, c -> c));

            BigDecimal totalVolume = BigDecimal.ZERO;
            BigDecimal totalWeight = BigDecimal.ZERO;
            Date lastShippingDate = null;
            Set<String> addressCode = new HashSet<>();
            for (var detail : detailDO) {
                int lclShippingNum = detail.getLclShippingNum();
                if (MoveOrSplitTypeEnum.SPLIT.getCode().equals(form.getOperationType())) {
                    lclShippingNum = form.getLclShippingNum();
                }
                ProductSnapshotDO productSnapshotDO = snapshotDOMap.get(detail.getProductSnapshotId());
                String selfData = productSnapshotDO.getSelfData();
                SelfProductDO selfProductDO = JSON.parseObject(selfData, SelfProductDO.class);
                BigDecimal volume = BigDecimal.valueOf(selfProductDO.getCaseHeight() * selfProductDO.getCaseLength() * selfProductDO.getCaseWidth() / 1000000)
                        .setScale(4, RoundingMode.HALF_UP);
                int boxCount = (int) Math.ceil((double) lclShippingNum / selfProductDO.getContainerLoad());
                totalVolume = totalVolume.add(volume.multiply(BigDecimal.valueOf(boxCount)));
                totalWeight = totalWeight.add(BigDecimal.valueOf(selfProductDO.getSingleCaseGrossWeight())
                        .multiply(BigDecimal.valueOf(lclShippingNum)));

                if (lastShippingDate == null || detail.getShippingStartDate().after(lastShippingDate)) {
                    lastShippingDate = detail.getShippingStartDate();
                }
                String factoryData = productSnapshotDO.getFactoryData();
                FactoryInfoDTO factoryInfoDO = JSON.parseObject(factoryData, FactoryInfoDTO.class);
                addressCode.add(factoryInfoDO.getAddressCode());
            }
            ;
            if (infoDO.getShippingVolume() + totalVolume.doubleValue() > 72) {
                result = "体积超" + (infoDO.getShippingVolume() + totalVolume.doubleValue() - 72) + "m³";
            }

            if (infoDO.getShippingWeight() + totalWeight.doubleValue() > 190000) {
                result = StrUtil.isNotBlank(result) ? result + "、" : "";
                result = result + "重量超" + (infoDO.getShippingWeight() + totalWeight.doubleValue() - 190000) + "kg";
            }

            if (lastShippingDate.after(infoDO.getShippingStartDate())) {
                result = StrUtil.isNotBlank(result) ? result + "、" : "";
                result = result + "货件装柜时间需要延顺";
            }
            if (addressCode.size() > 1 || !addressCode.contains(infoDO.getAddressCode())) {
                result = StrUtil.isNotBlank(result) ? result + "、" : "";
                result = result + "有多个地址代号";
            }
        }
        return result;

    }

    @Override
    public String getShipmentCode(LclContainerMoveForm form) {
        List<LclContainerInfoDO> infoDOList = lclContainerInfoRepository.list(Wrappers.<LclContainerInfoDO>lambdaQuery()
                .likeLeft(LclContainerInfoDO::getShipmentCode, "货件")
                .eq(LclContainerInfoDO::getLclRecordId, form.getRecordId())
                .orderByAsc(LclContainerInfoDO::getShipmentCode));
        if (CollectionUtil.isNotEmpty(infoDOList)) {
            int num = Integer.parseInt(infoDOList.getLast().getShipmentCode().substring(2)) + 1;
            return "货件" + num;
        } else {
            return "货件1";
        }
    }

    @Override
    public List<LclContainerInfoDto> shipmentCodeList(LclContainerMoveForm form) {
        List<LclContainerDetailDO> detailDO = lclContainerDetailRepository.listByIds(form.getSourceDetailIds());
        String containerInfoId = detailDO.getFirst().getContainerInfoId();
        List<LclContainerInfoDto> shipmentCodeList = new ArrayList<>();
        lclContainerInfoRepository.list(Wrappers.<LclContainerInfoDO>lambdaQuery()
                        .eq(LclContainerInfoDO::getLclRecordId, form.getRecordId())
                        .orderByAsc(LclContainerInfoDO::getShipmentCode))
                .forEach(i -> {
                    if (!containerInfoId.equals(i.getId())) {
                        LclContainerInfoDto dto = LclContainerInfoDto.builder()
                                .containerInfoId(i.getId())
                                .shipmentCode(i.getShipmentCode())
                                .build();
                        shipmentCodeList.add(dto);
                    }
                });
        return shipmentCodeList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteLclContainerDetail(LclContainerDetailDeleteForm form) {
        LclContainerDetailDO containerDetailDO = lclContainerDetailRepository.getById(form.getId());
        if (ObjectUtil.isEmpty(containerDetailDO)) {
            return true;
        }
        lclContainerDetailRepository.removeById(form.getId());
        lclContainerLoadingDetailService.updateContainerInfo(containerDetailDO.getContainerInfoId());
        return true;
    }

    public boolean splitContainerData(LclContainerMoveForm form) {
        boolean split = MoveOrSplitTypeEnum.SPLIT.getCode().equals(form.getOperationType());
        List<LclContainerDetailDO> detailDO = lclContainerDetailRepository.listByIds(form.getSourceDetailIds());
        if (split && form.getLclShippingNum() > detailDO.getFirst().getLclShippingNum()) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "拆分数量超过原发货数量");
        }
        if (StrUtil.isNotBlank(form.getTargetContainerId())) {
            LclContainerInfoDO targetInfo = lclContainerInfoRepository.getById(form.getTargetContainerId());
            if (ObjectUtil.isEmpty(targetInfo)) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "目标货件不存在");
            }
            String sourceContainerId = detailDO.getFirst().getContainerInfoId();
            if (split) {
                LclContainerDetailDO splitDO = detailDO.getFirst();
                LclContainerDetailDO newDetailDO = ObjectUtil.clone(splitDO);
                newDetailDO.setContainerInfoId(targetInfo.getId());
                newDetailDO.setLclShippingNum(form.getLclShippingNum());
                // 是否装满
                List<ProductSnapshotDO> productSnapshotList = productSnapRepository.selectListByIdList(Collections.singletonList(newDetailDO.getProductSnapshotId()));
                ProductSnapshotDO productSnapshotDO = productSnapshotList.getFirst();
                String selfData = productSnapshotDO.getSelfData();
                SelfProductDO selfProductDO = JSON.parseObject(selfData, SelfProductDO.class);
                newDetailDO.setIsPackageFull(Integer.valueOf(form.getLclShippingNum() % selfProductDO.getContainerLoad() == 0 ? IsPackageFullEnum.FULL.getCode() : IsPackageFullEnum.NOT_FULL.getCode()));
                lclContainerDetailRepository.updateById(newDetailDO);
            } else {
                detailDO.forEach(item -> {
                    item.setContainerInfoId(targetInfo.getId());
                });
                lclContainerDetailRepository.updateBatchById(detailDO);
            }
            lclContainerLoadingDetailService.updateContainerInfo(sourceContainerId);
            lclContainerLoadingDetailService.updateContainerInfo(targetInfo.getId());
        } else {

            LclContainerInfoDO one = lclContainerInfoRepository.getOne(Wrappers.<LclContainerInfoDO>lambdaQuery()
                    .eq(LclContainerInfoDO::getLclRecordId, form.getRecordId())
                    .eq(LclContainerInfoDO::getShipmentCode, form.getTargetContainerName()));

            if (ObjectUtil.isNotEmpty(one)) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "货件号已存在");
            }
            List<ProductSnapshotDO> productSnapshotList = productSnapRepository.selectListByIdList(detailDO.stream().map(LclContainerDetailDO::getProductSnapshotId).toList());
            var snapshotDOMap = productSnapshotList.stream().collect(Collectors.toMap(ProductSnapshotDO::getId, c -> c));
            LclContainerInfoDO sourceInfo = lclContainerInfoRepository.getById(detailDO.getFirst().getContainerInfoId());

            LclContainerInfoDO lclContainerInfoDO = LclContainerInfoDO.builder()
                    .lclRecordId(form.getRecordId())
                    .shipmentCode(form.getTargetContainerName())
                    .sourceType("排柜新生成")
                    .destinationWarehouse(sourceInfo.getDestinationWarehouse())
                    .addressCode(sourceInfo.getAddressCode())
                    .build();
            lclContainerInfoRepository.save(lclContainerInfoDO);

            if (split) {
                LclContainerDetailDO newDetailDO = ObjectUtil.clone(detailDO.getFirst());
                newDetailDO.setContainerInfoId(lclContainerInfoDO.getId());
                newDetailDO.setLclShippingNum(form.getLclShippingNum());

                // 是否装满
                String selfData = snapshotDOMap.get(newDetailDO.getProductSnapshotId()).getSelfData();
                SelfProductDO selfProductDO = JSON.parseObject(selfData, SelfProductDO.class);
                newDetailDO.setIsPackageFull(Integer.valueOf(form.getLclShippingNum() % selfProductDO.getContainerLoad() == 0 ? IsPackageFullEnum.FULL.getCode() : IsPackageFullEnum.NOT_FULL.getCode()));
                lclContainerDetailRepository.updateById(newDetailDO);
            } else {
                detailDO.forEach(item -> {
                    item.setContainerInfoId(lclContainerInfoDO.getId());
                });
                lclContainerDetailRepository.updateBatchById(detailDO);
            }
            lclContainerLoadingDetailService.updateContainerInfo(lclContainerInfoDO.getId());
            lclContainerLoadingDetailService.updateContainerInfo(sourceInfo.getId());
        }
        return Boolean.TRUE;
    }
}




