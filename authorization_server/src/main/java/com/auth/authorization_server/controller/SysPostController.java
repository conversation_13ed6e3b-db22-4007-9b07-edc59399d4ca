package com.auth.authorization_server.controller;


import com.auth.authorization_server.domain.vo.PostVO;
import com.auth.authorization_server.service.ISysPostService;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 职位 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/position")
@Slf4j
public class SysPostController {


    @Resource
    private ISysPostService postService;
    /**
     * 职位列表
     */
    @GetMapping("/list")
    public ResultDTO<List<PostVO>> profile() {
        List<PostVO> postVOS = postService.selectPostList();
        //UserDTO userDTO = userAssembler.SysUserToDto(user);
        return ResultDTO.success(postVOS);
    }

}
