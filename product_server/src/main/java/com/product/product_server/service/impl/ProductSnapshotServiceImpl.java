package com.product.product_server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.product.product_server.entity.dataObject.*;
import com.product.product_server.entity.dto.FactoryInfoDTO;
import com.product.product_server.entity.form.VirtualProductSearchForm;
import com.product.product_server.entity.vo.ProductSnapshotVo;
import com.product.product_server.enums.UpgradeTypeEnum;
import com.product.product_server.repository.*;
import com.product.product_server.service.IChannelService;
import com.product.product_server.service.IProductSnapshotService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 快照表
 * <AUTHOR>
 * @Date 2024/3/5 17:19
 **/
@Service
@Slf4j
public class ProductSnapshotServiceImpl implements IProductSnapshotService {

    @Resource
    private SelfProductRepositoryImpl selfProductRepository;

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepository;

    @Resource
    private SpuProductRepositoryImpl spuProductRepository;

    @Resource
    private ProductSnapshotRepositoryImpl productSnapshotRepository;

    @Resource
    private FactoryInfoRepositoryImpl factoryInfoRepository;

    @Resource
    private VirtualUpgradeRelationRepositoryImpl virtualUpgradeRelationRepository;

    @Resource
    private IChannelService channelService;

    @Override
    public void saveProductSnapshot(String virtualProductId) {
        if (StrUtil.isBlank(virtualProductId)) {
            return;
        }
        this.saveProductSnapshotList(Collections.singletonList(virtualProductId));
    }

    @Override
    public void saveProductSnapshotList(List<String> virtualProductIdList) {
        if (virtualProductIdList.isEmpty()) {
            return;
        }

        Map<String, String> channelNameIdMap = channelService.getChannelNameIdMap();

        List<ProductSnapshotDO> productSnapshotList = new ArrayList<>();
        List<VirtualProductDO> virtualList = virtualProductRepository.list(Wrappers.<VirtualProductDO>lambdaQuery()
                .in(VirtualProductDO::getId, virtualProductIdList));
        List<SelfProductDO> selfList = selfProductRepository.list(Wrappers.<SelfProductDO>lambdaQuery()
                .in(SelfProductDO::getId, virtualList.stream().map(VirtualProductDO::getSelfProductSkuId).collect(Collectors.toSet())));
        Map<String, SelfProductDO> selfProductMap = selfList.stream().collect(Collectors.toMap(SelfProductDO::getId, selfProductDO -> selfProductDO));

        List<FactoryInfoDTO> factoryList = factoryInfoRepository.selectFactoryInfoList(selfList.stream().distinct().map(SelfProductDO::getFactoryId).collect(Collectors.toList()));
        Map<String, FactoryInfoDTO> factoryMap = factoryList.stream().collect(Collectors.toMap(FactoryInfoDTO::getId, factoryInfoDTO -> factoryInfoDTO));

        List<SpuProductDO> spuProductList = spuProductRepository.list(Wrappers.<SpuProductDO>lambdaQuery()
                .in(SpuProductDO::getId, virtualList.stream().map(VirtualProductDO::getSpuId).collect(Collectors.toSet())));
        Map<String, SpuProductDO> spuProductMap = spuProductList.stream().collect(Collectors.toMap(SpuProductDO::getId, spuProductDO -> spuProductDO));

        for (VirtualProductDO virtualProductDO : virtualList) {
            SelfProductDO selfProductDO = selfProductMap.get(virtualProductDO.getSelfProductSkuId());
            if (ObjectUtil.isNotEmpty(selfProductDO)) {
                String upgradeId = virtualProductDO.getUpgradeId();
                String upgradeVirtualId = null;
                String upgradeType = null;
                if (StrUtil.isNotBlank(upgradeId)) {
                    VirtualUpgradeRelationDO upgradeDO = virtualUpgradeRelationRepository.getById(upgradeId);
                    boolean flag = upgradeDO.getOriginalId().equals(virtualProductDO.getId());
                    upgradeType = flag ? UpgradeTypeEnum.ORIGINAL.getCode() : UpgradeTypeEnum.UPGRADE.getCode();
                    upgradeVirtualId = flag ? upgradeDO.getUpgradeId() : upgradeDO.getOriginalId();
                }
                // 处理渠道
                virtualProductDO.setChannel(channelNameIdMap.getOrDefault(virtualProductDO.getChannel().toUpperCase(Locale.ROOT), virtualProductDO.getChannel()));

                ProductSnapshotDO productSnapshotDO = ProductSnapshotDO.builder()
                        .selfSkuId(selfProductDO.getId())
                        .selfSku(selfProductDO.getSku())
                        .virtualSkuId(virtualProductDO.getId())
                        .virtualSku(virtualProductDO.getVirtualSku())
                        .selfData(JSON.toJSONString(selfProductDO))
                        .virtualData(JSON.toJSONString(virtualProductDO))
                        .factoryData(JSON.toJSONString(factoryMap.get(selfProductDO.getFactoryId())))
                        .spuData(JSON.toJSONString(spuProductMap.get(virtualProductDO.getSpuId())))
                        .upgradeType(upgradeType)
                        .upgradeVirtualId(upgradeVirtualId)
                        .buyer(selfProductDO.getBuyer())
                        .operator(virtualProductDO.getOperator())
                        .build();
                productSnapshotList.add(productSnapshotDO);
            }
        }

        productSnapshotRepository.saveBatch(productSnapshotList);
    }

    @Override
    public void saveProductSnapshotListBySelfList(List<String> selfProductIdList) {
        if (CollectionUtil.isEmpty(selfProductIdList)) {
            return;
        }
        List<VirtualProductDO> virtualList = virtualProductRepository.list(Wrappers.<VirtualProductDO>lambdaQuery()
                .in(VirtualProductDO::getSelfProductSkuId, selfProductIdList));
        List<String> virtualProductIdList = virtualList.stream().distinct().map(VirtualProductDO::getId).toList();
        saveProductSnapshotList(virtualProductIdList);
    }

    @Override
    public void saveProductSnapshotByFactory(List<String> factoryInfoIds) {
        if (CollectionUtil.isEmpty(factoryInfoIds)) {
            return;
        }

        VirtualProductSearchForm form = new VirtualProductSearchForm();
        form.setFactoryInfoIds(factoryInfoIds);
        List<VirtualProductDO> virtualList = virtualProductRepository.getVirtualProductList(form);
        List<String> virtualProductIdList = virtualList.stream().distinct().map(VirtualProductDO::getId).toList();
        saveProductSnapshotList(virtualProductIdList);
    }

    @Override
    public ProductSnapshotVo getProductSnapshotList(List<String> snapshotIdList) {
        List<ProductSnapshotDO> productSnapshotDOS = productSnapshotRepository.listByIds(snapshotIdList);
        return ProductSnapshotVo.builder().productSnapshotList(productSnapshotDOS).build();
    }
}
