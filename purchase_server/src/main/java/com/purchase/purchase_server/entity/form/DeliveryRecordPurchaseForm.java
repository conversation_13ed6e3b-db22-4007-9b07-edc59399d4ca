package com.purchase.purchase_server.entity.form;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 发货计划记录查询表单
 * <AUTHOR>
 * @Date 2024/4/7 11:07
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class DeliveryRecordPurchaseForm extends BasePageForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 5286237672104269454L;
    /**
     * 发货计划id
     */
    private String recordId;

    /**
     * 状态
     */
    private String trialStatus;


    /**
     * 发货装柜时间
     */
    private String shippingStartDate;
    private String shippingEndDate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createStartDate;
    private String createEndDate;

    /**
     * 自定义sku
     */
    private List<String> selfSkuList;

    /**
     * 虚拟sku
     */
    private List<String> virtualSkuList;

    /**
     * 老skuList
     */
    private List<String> oldSkuList;

    /**
     * 产品名称
     */
    private String productName;

    private List<String> recordIds;
}
