package com.inventory_server.model.redundancy.entity.dp;

import com.inventory_server.model.redundancy.entity.dto.WarehouseTotalRedundancyDto;
import com.inventory_server.model.redundancy.enums.RedundantTypeEnum;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.util.*;

/**
 * @Description 冗余库存详细信息
 * <AUTHOR>
 * @Date 2024/5/11 11:05
 **/
@Getter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class RedundancyInfo {

    private LocalDate arrivingDate;

    /**
     * 判断时间点
     */
    private LocalDate judgeDate;

    /**
     * 判断日期初库存
     */
    private Double initInventory;

    /**
     * 期初安全库存
     */
    private Double safeInventory;

    /**
     * 到货量
     */
    private Integer arrivingNum;

    /**
     * 期初海外仓总冗余
     */
    private Integer totalRedundancy;

    /**
     * 海外仓冗余、在途冗余或本地仓冗余
     */
    @Setter
    private Integer redundantNum;

    /**
     * 海外仓总冗余类型
     */
    private RedundantTypeEnum totalRedundantTypeEnum;

    /**
     * 冗余类型
     */
    private RedundantTypeEnum redundantTypeEnum;

    private RedundantType type;

    private List<WarehouseTotalRedundancyDto> warehouseRedundantInventoryList = new ArrayList<>();

    @Getter
    public enum RedundantType {
        REAL_ARRIVING("0", "真实在途到货", 1),

        PLAN_ARRIVING("1", "模拟工厂生产计划到货", 2),

        TODAY_PURCHASE_ARRIVING("2", "模拟今日下补货单到货", 3),

        N_DAYS_PURCHASE_ARRIVING("3", "模拟下次再下补货单到货", 4),

        NO_REMAIN_INVENTORY("4", "海外仓无库存，冗余0件", 0),

        NO_FACTORY_PLAN("5", "本地仓无库存，冗余0件", 5),

        NO_NEED_TO_DELIVERY("6", "未触发发货，本地仓全部冗余", 6),

        SELLING_OUT("7", "停售商品", 7),

        TEST_SAMPLE_INVENTORY("8", "测款商品", 8);

        private final String code;

        private final String desc;

        private final Integer order;

        RedundantType(String code, String desc, Integer order) {
            this.code = code;
            this.desc = desc;
            this.order = order;
        }

    }

    /**
     * 此处需要判断发货时间、到货时间、到货仓库是否一致，如果一致的话，就认为是计划里同一批次的发货所对应的海外仓冗余库存以及工厂冗余库存
     * @param o obj
     * @return 相等则返回true
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RedundancyInfo that = (RedundancyInfo) o;
        return
//                Objects.equals(shippingDate, that.shippingDate) &&
                        Objects.equals(arrivingDate, that.arrivingDate) &&
//                Objects.equals(arrivingWarehouse, that.arrivingWarehouse) &&
                        Objects.equals(type, that.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
//                shippingDate,
                arrivingDate,
//                arrivingWarehouse,
                type);
    }
}
