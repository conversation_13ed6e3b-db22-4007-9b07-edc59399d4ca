<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.purchase.purchase_server.mapper.MockInventoryMapper">


    <!-- 批量删除 -->
    <delete id="deleteByShippingProjectIds">
        DELETE FROM cm_mock_inventory_table WHERE shipping_project_id IN
        <foreach collection="shippingProjectIds" item="shippingProjectId" open="(" separator="," close=")">
            #{shippingProjectId}
        </foreach>
    </delete>
</mapper>
