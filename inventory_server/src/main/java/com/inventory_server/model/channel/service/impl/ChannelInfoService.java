package com.inventory_server.model.channel.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.crafts_mirror.utils.common.entity.ChannelSearchDto;
import com.crafts_mirror.utils.common.entity.ChannelSearchVo;
import com.inventory_server.model.channel.repository.interior.IChannelInteriorRepository;
import com.inventory_server.model.channel.service.IChannelInfoService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/6 11:55
 **/
@Service
public class ChannelInfoService implements IChannelInfoService {

    @Resource
    private IChannelInteriorRepository channelInteriorRepository;

    @Override
    public ChannelSearchVo getAllChannel() {
        return channelInteriorRepository.getAllChannel();
    }

    @Override
    public Map<String, String> getChannelIdNameMap() {
        ChannelSearchVo channelSearchVo = getAllChannel();
        List<ChannelSearchDto> channelSearchList = channelSearchVo.getChannelSearchDtoList();
        if (CollectionUtil.isEmpty(channelSearchList)) {
            return new HashMap<>();
        }
        return channelSearchList.stream().collect(Collectors.toMap(ChannelSearchDto::getChannelId, ChannelSearchDto::getChannelName));
    }

    @Override
    public Map<String, String> getChannelNameIdMap() {
        ChannelSearchVo channelSearchVo = getAllChannel();
        List<ChannelSearchDto> channelSearchList = channelSearchVo.getChannelSearchDtoList();
        if (CollectionUtil.isEmpty(channelSearchList)) {
            return new HashMap<>();
        }
        return channelSearchList.stream().collect(Collectors.toMap(c -> c.getChannelName().toUpperCase(Locale.ROOT), ChannelSearchDto::getChannelId));
    }
}
