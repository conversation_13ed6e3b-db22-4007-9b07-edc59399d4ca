package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * 货件计划表
 * @TableName cm_shipment_plan
 */

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value ="cm_shipment_plan")
public class ShipmentPlanDO extends BaseEntity {


    /**
     * 货件计划编号
     */
    private String shipmentCode;

    /**
     * 仓库id
     */
    private String warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 实际装柜时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date actualLoadingTime;

    /**
     * 开船时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date shippingDate;

    /**
     * 预计到港时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date estimatedArrivalTime;

    /**
     * 实际到港时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date actualArrivalTime;

    /**
     * 实际签收时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date actualSignTime;

    /**
     * 预计签收时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date estimatedSignTime;

    /**
     * 海外仓上架时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date warehouseShelfTime;

    /**
     * 实际签收
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date actualSign;

    /**
     * 货件类型 0-灯具，1-家具，2-灯具+家具，3-家具+灯具
     */
    private String shipmentType;
}