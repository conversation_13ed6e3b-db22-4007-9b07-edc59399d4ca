package com.inventory_server.applications.dto;

import com.inventory_server.model.redundancy.entity.bo.InventoryOnShippingRedundantBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryAndForeignInfoMapDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -4549730035869230977L;
    private Map<String, String> virtualSkuByInventoryInfoId;
    private Map<String, String> channelByInventoryInfoId;
    private Map<String, Integer> borrowingStrategyByInventoryInfoId;
    private List<InventoryFactoryRedundantInfoDto> factoryRedundantInfo;
    private List<InventoryOnShippingRedundantBO> onShippingRedundantInfo;
    private List<InventoryForeignRedundantInfoDto> foreignRedundantInfo;
}
