package com.purchase.purchase_server.enums.purchaseOrders;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum PoStatusEnum {
    PENDING_CONFIRM(1, "待确认"),
    UNDER_REVIEW(2, "审核中"),
    APPROVED_IN_TRANSIT(3, "已审批"),
    UNDER_REVIEW_AGAIN(4, "复审中"),
    ARRIVAL_EXCEPTION(5, "到货异常"),
    COMPLETED(8, "已完成"),
    CANCELLED(9, "撤销");

    private final int code;
    private final String desc;

    public static PoStatusEnum ofCode(int code) {
        return Arrays.stream(PoStatusEnum.values())
                .filter(it -> it.getCode() == code)
                .findFirst()
                .orElse(null);
    }
}
