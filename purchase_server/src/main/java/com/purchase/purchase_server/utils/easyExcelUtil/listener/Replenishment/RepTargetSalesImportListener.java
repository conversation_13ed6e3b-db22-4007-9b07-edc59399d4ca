package com.purchase.purchase_server.utils.easyExcelUtil.listener.Replenishment;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.util.ConverterUtils;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.ActualDailySalesDto;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.vo.UpgradeInfoInteriorVo;
import com.purchase.purchase_server.model.purchase.RepTargetSalesInfoDp;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentManager;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentMapManager;
import com.purchase.purchase_server.utils.easyExcelUtil.template.Replenishment.ExcelConstants;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.IntStream;

/**
 * @Description 导入补货计划-目标日销
 * <AUTHOR>
 * @Date 2024/1/16 15:49
 **/
@Slf4j
public class RepTargetSalesImportListener extends AbstractRepImportListener<HashMap<Integer, String>> {

    public RepTargetSalesImportListener(List<SenboWarehouseDto> senboWarehouseList) {
        super(senboWarehouseList);
    }

    @Override
    public void invoke(HashMap<Integer, String> data, AnalysisContext analysisContext) {
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }
        ReplenishmentMapManager replenishmentMapManager = ReplenishmentMapManager.getInstance();

        //动态读取 excel逻辑删除造成数据null问题
        data.entrySet().removeIf(entry -> entry.getKey() > replenishmentMapManager.getTargetSalesHeadList().size() + 1);

        String virtualSku = data.get(0);
        VirtualProductDO virtualProductDO = replenishmentMapManager.getSkuVirtualProductMap().get(virtualSku);
        if (virtualProductDO == null) {
            virtualProductDO = replenishmentMapManager.getOldSkuVirtualProductMap().get(virtualSku);
            if (virtualProductDO != null && virtualSku.equals(virtualProductDO.getOldSku())) {
                throw new IllegalArgumentException("不可以填写老sku");
            }
            throw new IllegalArgumentException(String.format("虚拟sku：%s不存在", virtualSku));
        }

        if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())) {
            UpgradeInfoInteriorVo upgradeInfoInteriorVo = selectUpgradeInfo(virtualProductDO.getUpgradeId());
            if (ObjectUtil.isNotEmpty(upgradeInfoInteriorVo)) {
                if (upgradeInfoInteriorVo.getOriginalSkuId().equals(virtualProductDO.getId())) {
                    replenishmentMapManager.putTargetSalesUpgradeMap(upgradeInfoInteriorVo.getOriginalSku(), upgradeInfoInteriorVo.getUpgradeSku());
                    return;
                }
            }
        }

        new RepTargetSalesInfoDp(data);
        ReplenishmentManager manager = ReplenishmentManager.getInstance();

        if (!replenishmentMapManager.addTargetSalesSet(virtualSku)) {
            throw new IllegalArgumentException("虚拟sku重复");
        }
        //数据组装
        HashMap<String, Double> sumDaysMap = IntStream.range(0, replenishmentMapManager.getTargetSalesHeadList().size())
                .boxed()
                .collect(HashMap::new,
                        (map, i) -> map.put(replenishmentMapManager.getTargetSalesHeadList().get(i), Double.parseDouble(data.getOrDefault((i + 2), "0.0"))),
                        HashMap::putAll);

        replenishmentMapManager.putTargetSalesMap(data.get(0), sumDaysMap);

        replenishmentMapManager.putActualDailySalesMap(data.get(0), ActualDailySalesDto.builder()
                .actualDailySalesNum(Double.parseDouble(data.get(1)))
                .build());
        manager.incrementSuccessfulTimes();
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        Map<Integer, String> map = ConverterUtils.convertToStringMap(headMap, context);
        //删除map中的空值
        map.values().removeIf(Objects::isNull);
        List<String> fixedColumns = ExcelConstants.Sheet2.FIXED_COLUMNS;
        for (Map.Entry<Integer, String> entry : map.entrySet()) {
            Integer key = entry.getKey();
            String value = entry.getValue();

            if (key <= 1) {
                if (!fixedColumns.get(key).equals(value)) {
                    throw new RuntimeException("目标日销表头错误，请检查表头是否正确");
                }
            }
        }
    }
}
