package com.gateway.apigateway.filter;

import com.crafts_mirror.utils.common.entity.LoginVo;
import jakarta.annotation.Resource;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import static com.crafts_mirror.utils.constant.Constants.AUTHORIZATION;
import static com.gateway.apigateway.consts.GatewayConst.NO_NEED_LOGIN_URL_SET;

/**
 * @Description Url权限控制过滤器
 * <AUTHOR>
 * @Date 2024/2/3 14:53
 **/
@Component
@Order(2)
public class UrlAuthorizationFilter implements GlobalFilter {

    @Resource
    private RedisTemplate<String, LoginVo> redisTemplate;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 获取用户token
        ServerHttpRequest request = exchange.getRequest();

        // 获取用户访问的url
        String url = request.getPath().value();
        if(NO_NEED_LOGIN_URL_SET.contains(url)) {
            return chain.filter(exchange.mutate().request(request).build());
        }
        String authorization = request.getHeaders().get(AUTHORIZATION).get(0);
        // 根据token获取到用户的权限
        //LoginVo loginVo = redisTemplate.opsForValue().get(RedisKeyConstant.USER_TOKEN_KEY + authorization);
        //Set<String> urlSet = loginVo.getUrlPermSet().stream().map(u -> StrUtil.subAfter(u, URL_PERMISSION_PREFIX, false)).collect(Collectors.toSet());
        //if(loginVo.getUrlPermSet() == null || !urlSet.contains(url)) {
            // 鉴权功能暂未完全实现，此处统一暂时不处理
//            return Mono.error(new CustomizedException(HttpStatus.UNAUTHORIZED, "401", "没有访问该功能的权限，请联系研发人员"));
//        }
        return chain.filter(exchange.mutate().request(request).build());
    }
}
