package com.product.product_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.product.product_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @Description 日志实体类
 * <AUTHOR>
 * @Date 2023/12/13 14:46
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_operation_log")
public class OperationLogDO extends BaseEntity {
    private String operator;

    private String content;

    private String operationType;

    private String trackNumber;
}
