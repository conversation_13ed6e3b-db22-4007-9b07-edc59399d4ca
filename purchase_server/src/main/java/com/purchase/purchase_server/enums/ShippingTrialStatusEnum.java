package com.purchase.purchase_server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ShippingTrialStatusEnum {
    ABROGATE("-2", "作废"),
    CALCULATION_FAILED("-1", "试算失败"),
    CALCULATING("0", "试算中"),
    UNSAVED("1", "待保存"),
    SAVED("2", "已保存");

    private final String code;
    private final String desc;

    // 静态方法，检查是否存在某个值
    public static boolean contains(String codeToCheck) {
        for (ShippingTrialStatusEnum status : ShippingTrialStatusEnum.values()) {
            if (status.code.equals(codeToCheck)) {
                return true;
            }
        }
        return false;
    }
}
