package com.purchase.purchase_server.model.purchase;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 节点
 *
 * @author: 毛豆
 * @date: 2024-01-04
 */
@Data
public class Node<k, v> {
    /**
     * key
     */
    private k k;
    /**
     * 值
     */
    private v v;
    /**
     * 扩展字段
     */
    private Object ext;
    /**
     * 子集
     */
    private List<Node<k, v>> children;

    public static <k, v> Node<k, v> build(k k) {
        return build(k, null);
    }

    public static <k, v> Node<k, v> build(k k, v v) {
        Node<k, v> node = new Node<>();
        node.setK(k);
        node.setV(v);
        return node;
    }

    public static <k, v> Node<k, v> build(k k, v v, Serializable ext) {
        Node<k, v> node = new Node<>();
        node.setK(k);
        node.setV(v);
        node.setExt(ext);
        return node;
    }
}
