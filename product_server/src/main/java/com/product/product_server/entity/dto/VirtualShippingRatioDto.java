package com.product.product_server.entity.dto;

import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/7/4 17:01
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VirtualShippingRatioDto {

    private String virtualSkuId;

    private Integer warehouseId;

    private String senboWarehouse;

    private Integer headShippingDate;

    private Integer sort;

    @Min(value = 0, message = "发货比例不能小于0")
    @Digits(integer = 3, fraction = 1, message = "发货比例最多1位小数")
    private Double shippingRatio;
}
