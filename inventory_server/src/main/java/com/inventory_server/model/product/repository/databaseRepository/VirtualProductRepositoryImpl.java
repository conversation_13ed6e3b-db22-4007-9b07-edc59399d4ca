package com.inventory_server.model.product.repository.databaseRepository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.product.entity.dos.VirtualUpgradeRelationDO;
import com.inventory_server.model.product.mapper.VirtualProductMapper;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @Description 虚拟sku相关repository类
 * <AUTHOR>
 * @Date 2023/12/6 14:55
 **/
@Service
public class VirtualProductRepositoryImpl extends ServiceImpl<VirtualProductMapper, VirtualProductDO> {

    public VirtualProductDO getOneByVirtualSkuOrOldSku(String destinationSku) {
        return getOne(Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getVirtualSku,destinationSku).or().eq(VirtualProductDO::getOldSku,destinationSku));
    }

    public List<VirtualProductDO> getListByVirtualSkuList(List<String> virtualSkuList) {
        return list(Wrappers.<VirtualProductDO>lambdaQuery().in(VirtualProductDO::getVirtualSku,virtualSkuList));
    }

    public String getProduceDaysByVirtualSku(String virtualSku) {
        return baseMapper.getProduceDaysByVirtualSku(virtualSku);
    }

    public String getProduceDaysByVirtualSkuId(String virtualSkuId) {
        return baseMapper.getProduceDaysByVirtualSkuId(virtualSkuId);
    }

    public String getVirtualSkuByVirtualId(String virtualId) {
        return getOne(Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getId,virtualId)).getVirtualSku();
    }

    public String getPreUpgradeSkuByUpgradeSkuId(String virtualId) {
        return baseMapper.selectJoinOne(String.class, new MPJLambdaWrapper<VirtualProductDO>()
                .select(VirtualProductDO::getVirtualSku)
                .leftJoin(VirtualUpgradeRelationDO.class, VirtualUpgradeRelationDO::getOriginalId, VirtualProductDO::getId)
                .eq(VirtualUpgradeRelationDO::getUpgradeId, virtualId)
        );
    }
}
