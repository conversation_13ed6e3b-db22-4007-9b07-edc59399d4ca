package com.purchase.purchase_server.entity.form;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 发货装柜
 * @date 2024/1/18
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LclShippingNumEditListNewForm implements Serializable {


    private List<LclShippingNumEditListForm> lclShippingNumEditNewList;

    private String id;

    /**
     * 发货装柜开始时间
     */
    private String shippingStartDate;

    /**
     * 发货装柜结束时间
     */
    private String shippingEndDate;

    /**
     * 发货装柜时间
     */
    private String shippingDate;


    /**
     * 是否整理
     * @see com.purchase.purchase_server.enums.lcl.LclConsolidationEnum
     */
    private String isLclConsolidation;
}
