package com.purchase.purchase_server.service;

import cn.hutool.core.date.DateTime;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.entity.dto.delivery.MockTableCalDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectBaseParamDto;
import com.purchase.purchase_server.entity.dto.delivery.TrialCalReplenishmentDto;
import com.purchase.purchase_server.model.purchase.PurchaseDayNeedNumDp;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

public interface ICommonConsumptionService {

    DateTime calDeliveryNumStartDate(DateTime startDate, List<FactoryRemainInventoryDto> onShippingInventoryList,
                                     String warehouse, Map<String, DateTime> fastestArrivingDateMap,
                                     Map<String, Double> targetSalesMap, DateTime maxSaleDate, Map<String, Double> remainMap,
                                     DateTime replenishmentCreateDate, ShippingProjectBaseParamDto shippingRules,
                                     List<String> sortedWarehouseList, List<FactoryRemainInventoryDto> priorDeliveryList);

    TrialCalReplenishmentDto calDeliveryResults(MockTableCalDto mockTableCalDto);

    DateTime calReplenishmentOrDeliveryNumStartDate(DateTime startDate, List<FactoryRemainInventoryDto> onShippingInventoryList,
                                                    String warehouse, Map<String, DateTime> fastestArrivingDateMap,
                                                    Map<String, Double> targetSalesMap, DateTime maxSaleDate, Map<String, Double> remainMap,
                                                    DateTime replenishmentCreateDate, ShippingProjectBaseParamDto shippingRules,
                                                    List<String> sortedWarehouseList, List<FactoryRemainInventoryDto> priorDeliveryList);


    PurchaseDayNeedNumDp calNeedReplenishmentNum(Map<String, Map<String, Double>> everyRetainInventoryMap, Map<String, Double> targetSalesMap,
                                                 int rangeDays, Map<String, Double> shippingRatioMap, String warehouse, DateTime calStartDate,
                                                 Map<String, DateTime> fastestArrivingDateMap, List<FactoryRemainInventoryDto> onShippingInventoryList,
                                                 List<FactoryRemainInventoryDto> priorDeliveryList);

    PurchaseDayNeedNumDp calNeedDeliveryNum(Map<String, Map<String, Double>> everyRetainInventoryMap,
                                            Map<String, Double> targetSalesMap, int rangeDays, Map<String, Double> shippingRatioMap,
                                            String warehouse, DateTime calStartDate, double factorySum,
                                            Map<String, DateTime> fastestArrivingDateMap, List<FactoryRemainInventoryDto> onShippingInventoryList,
                                            List<FactoryRemainInventoryDto> priorDeliveryList);

    TreeMap<String, Double> calTargetSalesMap(String saleDestination, LocalDate startDate);
}
