package com.purchase.purchase_server.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crafts_mirror.common.security.dataPermission.DataPermission;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.bo.ArrivalDetailsBO;
import com.purchase.purchase_server.entity.bo.DeliveryRecordPurchaseListBO;
import com.purchase.purchase_server.entity.bo.ShipCountBO;
import com.purchase.purchase_server.entity.bo.TrialShippingInventoryBO;
import com.purchase.purchase_server.entity.dataObject.*;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclConsolidationRecordDO;
import com.purchase.purchase_server.entity.form.DeliveryPurchaseForm;
import com.purchase.purchase_server.entity.form.DeliveryRecordPurchaseForm;
import com.purchase.purchase_server.mapper.ShippingRecordMapper;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.SecurityConstants.ROLE_DATA_PER_DELIVERY;


/**
 * @Description 发货记录
 * <AUTHOR>
 * @Date 2024/4/1 14:42
 **/
@Service
public class ShippingRecordRepositoryImpl extends ServiceImpl<ShippingRecordMapper, ShippingRecordDO> {
    public IPage<DeliveryRecordPurchaseListBO> getRecordPageList(DeliveryRecordPurchaseForm form) {
        IPage<DeliveryRecordPurchaseListBO> iPage = new Page<>(form.getCurrent(), form.getSize());
        return this.baseMapper.selectJoinPage(iPage, DeliveryRecordPurchaseListBO.class, buildFlowPageQueryWrapper(form));
    }

    public DeliveryRecordPurchaseListBO getRecordByRecordId(String recordId) {
        if (StrUtil.isBlank(recordId)) {
            return new DeliveryRecordPurchaseListBO();
        }
        DeliveryRecordPurchaseForm recordForm = DeliveryRecordPurchaseForm.builder().recordId(recordId).build();
        recordForm.setCurrent(1);
        recordForm.setSize(1);
        return getRecordPageList(recordForm).getRecords().getFirst();
    }

    public ShippingRecordDO getByProjectId(String projectId) {
        return this.baseMapper.selectJoinOne(ShippingRecordDO.class, new MPJLambdaWrapper<ShippingRecordDO>()
                .selectAll(ShippingRecordDO.class)
                .leftJoin(ShippingProjectDO.class, ShippingProjectDO::getShippingRecordId, ShippingRecordDO::getId)
                .eq(ShippingProjectDO::getId, projectId));
    }

    public static MPJLambdaWrapper<ShippingRecordDO> buildFlowPageQueryWrapper(DeliveryRecordPurchaseForm query) {
        MPJLambdaWrapper<ShippingRecordDO> LambdaWrapper = new MPJLambdaWrapper<>();
        LambdaWrapper.distinct()
                .selectAs(ShippingRecordDO::getId, "record_id")
                .select(ShippingRecordDO::getTrialStatus)
                .select(ShippingRecordDO::getShippingStartDate)
                .select(ShippingRecordDO::getShippingEndDate)
                .select(ShippingRecordDO::getCreateBy)
                .select(ShippingRecordDO::getCreateDate)
                .select(FileMissionCenterDO::getFileName)
                .select(ShippingRecordDO::getExportFilePath)
                .selectAs(LclConsolidationRecordDO::getId, "lcl_record_id")
                .leftJoin(FileMissionCenterDO.class, FileMissionCenterDO::getId, ShippingRecordDO::getDataSourceId)
                .leftJoin(LclConsolidationRecordDO.class, LclConsolidationRecordDO::getShippingRecordId, ShippingRecordDO::getId)
                .in(CollectionUtil.isNotEmpty(query.getRecordIds()), ShippingRecordDO::getId, query.getRecordIds())
                .eq(StrUtil.isNotBlank(query.getRecordId()), ShippingRecordDO::getId, query.getRecordId())
                .eq(StrUtil.isNotBlank(query.getTrialStatus()), ShippingRecordDO::getTrialStatus, query.getTrialStatus())
                .eq(StrUtil.isNotBlank(query.getCreateBy()), ShippingRecordDO::getCreateBy, query.getCreateBy())
                .between(StrUtil.isNotBlank(query.getCreateStartDate()) && StrUtil.isNotBlank(query.getCreateEndDate()),
                        ShippingRecordDO::getCreateDate, query.getCreateStartDate(), query.getCreateEndDate())
                .and(StrUtil.isNotBlank(query.getShippingStartDate()) && StrUtil.isNotBlank(query.getShippingEndDate()),
                        i -> i.ge(ShippingRecordDO::getShippingEndDate, query.getShippingStartDate())
                                .le(ShippingRecordDO::getShippingStartDate, query.getShippingEndDate()))
                .orderByDesc(ShippingRecordDO::getCreateDate);
        return LambdaWrapper;
    }

    public List<String> getProjectIdList(DeliveryPurchaseForm form) {
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_DELIVERY, "t3");

        return this.baseMapper.selectJoinList(String.class, new MPJLambdaWrapper<ShippingRecordDO>()
                .distinct()
                .select(ShippingProjectDO::getId)
                .leftJoin(ShippingProjectDO.class, ShippingProjectDO::getShippingRecordId, ShippingRecordDO::getId)
                .leftJoin(FactoryFinishedInventoryDO.class, FactoryFinishedInventoryDO::getShippingProjectId, ShippingProjectDO::getId)
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, FactoryFinishedInventoryDO::getProductSnapshotId)
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, ProductSnapshotDO::getSelfSkuId)
                .leftJoin(FactoryInfoDO.class, FactoryInfoDO::getId, SelfProductDO::getFactoryId)
                .leftJoin(TrialShippingInventoryDO.class, TrialShippingInventoryDO::getFactoryFinishedId, FactoryFinishedInventoryDO::getId)
                .eq(ShippingRecordDO::getId, form.getRecordId())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), ProductSnapshotDO::getSelfSku, form.getSelfSkuList())
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), ProductSnapshotDO::getVirtualSku, form.getVirtualSkuList())
                .in(CollectionUtil.isNotEmpty(form.getOldSkuList()), "t3.virtual_data->>'$.oldSku'", form.getOldSkuList())
                .apply(StrUtil.isNotBlank(form.getProductName()), "t3.self_data->>'$.productName' like {0}", StrUtil.isBlank(form.getProductName()) ? form.getProductName() : "%" + form.getProductName().strip() + "%")
                .eq(StrUtil.isNotBlank(form.getFactoryCode()), FactoryInfoDO::getFactoryCode, Optional.ofNullable(form.getFactoryCode()).map(String::strip).orElse(null))
                .apply(StrUtil.isNotBlank(form.getOperator()), "FIND_IN_SET('" + form.getOperator() + "',t3.virtual_data->>'$.operator')")
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
        );
    }

    public IPage<TrialShippingInventoryBO> needShipList(DeliveryPurchaseForm form, Collection<String> projectIdList, Collection<String> factoryIdList) {
        return this.baseMapper.selectJoinPage(new Page<>(form.getCurrent(), form.getSize()), TrialShippingInventoryBO.class,
                new MPJLambdaWrapper<ShippingRecordDO>()
                        .selectAs(ShippingProjectDO::getId, "shipping_project_id")
                        .select(FactoryInfoDO::getFactoryCode)
                        .select(ProductSnapshotDO::getVirtualSku)
                        .select("t3.self_data->>'$.productName' as product_name")
                        .select("t3.virtual_data->>'$.channel' as channel")
                        .select("t3.virtual_data->>'$.operator' as operator")
                        .selectAs(ProductSnapshotDO::getSelfSku, "self_product_sku")
                        .select(ShippingRecordDO::getShippingStartDate)
                        .select(FactoryFinishedInventoryDO::getFactoryFinishedDate)
                        .selectAs(FactoryFinishedInventoryDO::getId, "factory_finished_id")
                        .select(FactoryFinishedInventoryDO::getContractCode)
                        .leftJoin(ShippingProjectDO.class, ShippingProjectDO::getShippingRecordId, ShippingRecordDO::getId)
                        .leftJoin(FactoryFinishedInventoryDO.class, FactoryFinishedInventoryDO::getShippingProjectId, ShippingProjectDO::getId)
                        .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, FactoryFinishedInventoryDO::getProductSnapshotId)
                        .leftJoin(PrepareProductsRulesDO.class, PrepareProductsRulesDO::getDeliveryProjectId, ShippingProjectDO::getId)
                        .leftJoin(SelfProductDO.class, SelfProductDO::getId, ProductSnapshotDO::getSelfSkuId)
                        .leftJoin(FactoryInfoDO.class, FactoryInfoDO::getId, SelfProductDO::getFactoryId)
                        .in(ShippingProjectDO::getId, projectIdList)
                        .in(FactoryFinishedInventoryDO::getId, factoryIdList)
                        .orderByDesc(ProductSnapshotDO::getVirtualSku)
                        .orderByAsc(FactoryFinishedInventoryDO::getFactoryFinishedDate)
        );
    }

    public IPage<TrialShippingInventoryBO> notYetShippingList(DeliveryPurchaseForm form, List<String> projectIdList) {
        if (CollectionUtil.isEmpty(projectIdList)) {
            return new Page<>(form.getCurrent(), form.getSize());
        }
        return this.baseMapper.selectJoinPage(new Page<>(form.getCurrent(), form.getSize()), TrialShippingInventoryBO.class,
                new MPJLambdaWrapper<ShippingRecordDO>()
                        .selectAs(ShippingProjectDO::getId, "shipping_project_id")
                        .select(FactoryInfoDO::getFactoryCode)
                        .select(ProductSnapshotDO::getVirtualSku)
                        .select("t3.self_data->>'$.productName' as product_name")
                        .select("t3.virtual_data->>'$.channel' as channel")
                        .select("t3.virtual_data->>'$.operator' as operator")
                        .selectAs(ProductSnapshotDO::getSelfSku, "self_product_sku")
                        .select(ShippingRecordDO::getShippingStartDate)
                        .select(FactoryFinishedInventoryDO::getFactoryFinishedDate)
                        .selectAs(FactoryFinishedInventoryDO::getFactoryShippingPackageNum, "factory_shipping_num")
                        .selectAs(FactoryFinishedInventoryDO::getId, "factory_finished_id")
                        .select(FactoryFinishedInventoryDO::getContractCode)
                        .leftJoin(ShippingProjectDO.class, ShippingProjectDO::getShippingRecordId, ShippingRecordDO::getId)
                        .leftJoin(FactoryFinishedInventoryDO.class, FactoryFinishedInventoryDO::getShippingProjectId, ShippingProjectDO::getId)
                        .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, FactoryFinishedInventoryDO::getProductSnapshotId)
                        .leftJoin(PrepareProductsRulesDO.class, PrepareProductsRulesDO::getDeliveryProjectId, ShippingProjectDO::getId)
                        .leftJoin(SelfProductDO.class, SelfProductDO::getId, ProductSnapshotDO::getSelfSkuId)
                        .leftJoin(FactoryInfoDO.class, FactoryInfoDO::getId, SelfProductDO::getFactoryId)
                        .in(ShippingProjectDO::getId, projectIdList)
                        .apply(String.format("t2.factory_finished_date >= DATE_SUB(%s,INTERVAL IF (t2.delivery_type = 0, 0, t4.transit_days) DAY)",
                                "'" + form.getShippingEndDate() + "'"))
                        .orderByDesc(ProductSnapshotDO::getVirtualSku)
                        .orderByAsc(FactoryFinishedInventoryDO::getFactoryFinishedDate)
        );
    }


    public ShipCountBO selectShippingCountMap(String recordId) {
        return this.baseMapper.selectJoinOne(ShipCountBO.class, new MPJLambdaWrapper<ShippingRecordDO>()
                .select("COALESCE(sum(t2.factory_shipping_package_num-t2.factory_remain_num),0) AS shipping_num")
                .leftJoin(ShippingProjectDO.class, ShippingProjectDO::getShippingRecordId, ShippingRecordDO::getId)
                .leftJoin(FactoryFinishedInventoryDO.class, FactoryFinishedInventoryDO::getShippingProjectId, ShippingProjectDO::getId)
                .leftJoin(PrepareProductsRulesDO.class, PrepareProductsRulesDO::getDeliveryProjectId, ShippingProjectDO::getId)
                .eq(ShippingRecordDO::getId, recordId)
                .apply("(t2.factory_shipping_package_num-t2.factory_remain_num > 0" +
                        " AND t2.factory_finished_date <= DATE_SUB(t.shipping_end_date,INTERVAL t3.transit_days DAY))")
        );
    }

    public ShipCountBO selectRemainCountMap(String recordId) {
        return this.baseMapper.selectJoinOne(ShipCountBO.class, new MPJLambdaWrapper<ShippingRecordDO>()
                .select("COALESCE(sum(t2.factory_remain_num),0) AS factory_remain_num")
                .leftJoin(ShippingProjectDO.class, ShippingProjectDO::getShippingRecordId, ShippingRecordDO::getId)
                .leftJoin(FactoryFinishedInventoryDO.class, FactoryFinishedInventoryDO::getShippingProjectId, ShippingProjectDO::getId)
                .leftJoin(PrepareProductsRulesDO.class, PrepareProductsRulesDO::getDeliveryProjectId, ShippingProjectDO::getId)
                .eq(ShippingRecordDO::getId, recordId)
                .apply("(t2.factory_remain_num > 0" +
                        " AND t2.factory_finished_date <= DATE_SUB(t.shipping_end_date,INTERVAL t3.transit_days DAY))")
        );
    }

    public ShipCountBO selectNotYetCountMap(String recordId) {
        return this.baseMapper.selectJoinOne(ShipCountBO.class, new MPJLambdaWrapper<ShippingRecordDO>()
                .select("COALESCE(sum(t2.factory_shipping_package_num),0) AS not_yet_shipping_period_num")
                .leftJoin(ShippingProjectDO.class, ShippingProjectDO::getShippingRecordId, ShippingRecordDO::getId)
                .leftJoin(FactoryFinishedInventoryDO.class, FactoryFinishedInventoryDO::getShippingProjectId, ShippingProjectDO::getId)
                .leftJoin(PrepareProductsRulesDO.class, PrepareProductsRulesDO::getDeliveryProjectId, ShippingProjectDO::getId)
                .eq(ShippingRecordDO::getId, recordId)
                .apply("(t2.factory_remain_num > 0" +
                        " AND t2.factory_finished_date > DATE_SUB(t.shipping_end_date,INTERVAL t3.transit_days DAY))")
        );
    }

    public List<TrialShippingInventoryBO> selectExportList(DeliveryPurchaseForm form) {
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_DELIVERY, "t3");
        return this.baseMapper.selectJoinList(TrialShippingInventoryBO.class, new MPJLambdaWrapper<ShippingRecordDO>()
                        .select(FactoryFinishedInventoryDO::getContractCode)
                        .selectAs(ShippingProjectDO::getId, "shippingProjectId")
                        .selectAs(ShippingProjectDO::getIsTestUnsalableInventory, "isTestUnsalableInventory")
                        .selectAs(FactoryFinishedInventoryDO::getId, "factoryFinishedId")
                        .select("DATE_FORMAT( t.shipping_start_date, '%Y/%c/%e') AS shipping_start_date")
                        .select("DATE_FORMAT( t2.factory_finished_date, '%Y/%c/%e') AS factory_finished_date")
                        .select("t2.factory_shipping_package_num AS factory_shipping_num")
                        .select("CASE t2.is_old_status" +
                                " WHEN '1' THEN t3.virtual_data->>'$.oldSku'" +
                                " WHEN '0' THEN t3.virtual_sku" +
                                " END as virtual_sku")
                        .select("t3.virtual_data->>'$.productStatus' AS reason")
                        .select(FactoryFinishedInventoryDO::getRemarks)
                        .leftJoin(ShippingProjectDO.class, ShippingProjectDO::getShippingRecordId, ShippingRecordDO::getId)
                        .leftJoin(FactoryFinishedInventoryDO.class, FactoryFinishedInventoryDO::getShippingProjectId, ShippingProjectDO::getId)
                        .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, FactoryFinishedInventoryDO::getProductSnapshotId)
                        .leftJoin(PrepareProductsRulesDO.class, PrepareProductsRulesDO::getDeliveryProjectId, ShippingProjectDO::getId)
                        .leftJoin(SelfProductDO.class, SelfProductDO::getId, ProductSnapshotDO::getSelfSkuId)
                        .leftJoin(FactoryInfoDO.class, FactoryInfoDO::getId, SelfProductDO::getFactoryId)
                        .eq(ShippingRecordDO::getId, form.getRecordId())
                        .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), ProductSnapshotDO::getSelfSku, form.getSelfSkuList())
                        .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), ProductSnapshotDO::getVirtualSku, form.getVirtualSkuList())
                        .in(CollectionUtil.isNotEmpty(form.getOldSkuList()), "t3.virtual_data->>'$.oldSku'", form.getOldSkuList())
                        .apply(StrUtil.isNotBlank(form.getProductName()), "t3.self_data->>'$.productName' like {0}", StrUtil.isBlank(form.getProductName()) ? form.getProductName() : "%" + form.getProductName().strip() + "%")
                        .eq(StrUtil.isNotBlank(form.getFactoryCode()), FactoryInfoDO::getFactoryCode, Optional.ofNullable(form.getFactoryCode()).map(String::strip).orElse(null))
                        .apply(StrUtil.isNotBlank(form.getOperator()), "FIND_IN_SET('" + form.getOperator() + "',t3.virtual_data->>'$.operator')")
//                .apply(ShippingTrialStatusEnum.SAVED.getCode().equals(form.getTrialStatus()), "t2.factory_shipping_package_num - t2.factory_remain_num > 0")
                        .apply(String.format(" t2.factory_finished_date <= DATE_SUB(%s,INTERVAL t4.transit_days DAY)", "'" + form.getShippingEndDate() + "'"))
                        .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
                        .orderByDesc(FactoryFinishedInventoryDO::getVirtualSkuId)
        );
    }

    public List<TrialShippingInventoryBO> selectFactoryNotYetList(DeliveryPurchaseForm form) {
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_DELIVERY, "t3");
        return this.baseMapper.selectJoinList(TrialShippingInventoryBO.class, new MPJLambdaWrapper<ShippingRecordDO>()
                .select(ProductSnapshotDO::getVirtualSku)
                .select("t3.self_data->>'$.productName' as product_name")
                .select("t3.virtual_data->>'$.channel' as channel")
                .select("DATE_FORMAT(t2.factory_finished_date, '%Y-%m-%d') as factory_finished_date")
                .selectAs(FactoryFinishedInventoryDO::getFactoryShippingPackageNum, "factory_shipping_num")
                .select(FactoryFinishedInventoryDO::getContractCode)
                .select(FactoryFinishedInventoryDO::getRemarks)
                .leftJoin(ShippingProjectDO.class, ShippingProjectDO::getShippingRecordId, ShippingRecordDO::getId)
                .leftJoin(FactoryFinishedInventoryDO.class, FactoryFinishedInventoryDO::getShippingProjectId, ShippingProjectDO::getId)
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, FactoryFinishedInventoryDO::getProductSnapshotId)
                .leftJoin(PrepareProductsRulesDO.class, PrepareProductsRulesDO::getDeliveryProjectId, ShippingProjectDO::getId)
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, ProductSnapshotDO::getSelfSkuId)
                .leftJoin(FactoryInfoDO.class, FactoryInfoDO::getId, SelfProductDO::getFactoryId)
                .eq(ShippingRecordDO::getId, form.getRecordId())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), ProductSnapshotDO::getSelfSku, form.getSelfSkuList())
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), ProductSnapshotDO::getVirtualSku, form.getVirtualSkuList())
                .apply(StrUtil.isNotBlank(form.getProductName()), "t3.self_data->>'$.productName' like {0}", StrUtil.isBlank(form.getProductName()) ? form.getProductName() : "%" + form.getProductName().strip() + "%")
                .eq(StrUtil.isNotBlank(form.getFactoryCode()), FactoryInfoDO::getFactoryCode, Optional.ofNullable(form.getFactoryCode()).map(String::strip).orElse(null))
                .apply(StrUtil.isNotBlank(form.getOperator()), "FIND_IN_SET('" + form.getOperator() + "',t3.virtual_data->>'$.operator')")
                .apply(String.format("t2.factory_finished_date > DATE_SUB(%s,INTERVAL t4.transit_days DAY)", "'" + form.getShippingEndDate() + "'"))
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
                .orderByDesc(FactoryFinishedInventoryDO::getVirtualSkuId)
        );
    }

    public List<ArrivalDetailsBO> selectShipDetailsList(DeliveryPurchaseForm form) {
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_DELIVERY, "t4");

        return this.baseMapper.selectJoinList(ArrivalDetailsBO.class, new MPJLambdaWrapper<ShippingRecordDO>()
                .selectAs(ShippingProjectDO::getId, "deliveryProjectId")
                .select(FactoryFinishedInventoryDO::getContractCode)
                .select(ShippingRecordDO::getShippingStartDate)
                .select("t4.self_data->>'$.productName' as product_name")
                .select("t4.virtual_data->>'$.channel' as channel")
                .selectAs(ProductSnapshotDO::getSelfSku, "self_product_sku")
                .select("t4.self_data->>'$.containerLoad' as container_load")
                .select("t4.self_data->>'$.caseLength' as case_length")
                .select("t4.self_data->>'$.caseWidth' as case_width")
                .select("t4.self_data->>'$.caseHeight' as case_height")
                .select("ROUND(t4.self_data->>'$.caseLength'*t4.self_data->>'$.caseWidth'*t4.self_data->>'$.caseHeight',3) as case_volume")
                .select("t4.self_data->>'$.singleCaseGrossWeight' as single_case_gross_weight")
                .select("t4.self_data->>'$.buyer' as buyer")
                .select("DATE_FORMAT( t3.real_shipping_start_date, '%Y/%c/%e') AS real_shipping_start_date")
                .select("DATE_FORMAT( t2.factory_finished_date, '%Y/%c/%e') AS factory_finished_date")
                .select(FactoryFinishedInventoryDO::getRemarks)
                .select(TrialShippingInventoryDO::getDestinationWarehouse)
                .select(TrialShippingInventoryDO::getShippingNum)
                .select("ROUND(t4.self_data->>'$.caseLength'*t4.self_data->>'$.caseWidth'*t4.self_data->>'$.caseHeight'*(t3.shipping_num)/1000000,3) AS factory_shipping_volume")
                .select("ROUND(t4.self_data->>'$.singleCaseGrossWeight'*t3.package_num,3) as factory_gross_weight")
                .select("CASE t2.is_old_status" +
                        " WHEN '1' THEN t4.virtual_data->>'$.oldSku'" +
                        " WHEN '0' THEN t4.virtual_sku" +
                        " END as destination_sku")
                .leftJoin(ShippingProjectDO.class, ShippingProjectDO::getShippingRecordId, ShippingRecordDO::getId)
                .leftJoin(FactoryFinishedInventoryDO.class, FactoryFinishedInventoryDO::getShippingProjectId, ShippingProjectDO::getId)
                .leftJoin(TrialShippingInventoryDO.class, TrialShippingInventoryDO::getFactoryFinishedId, FactoryFinishedInventoryDO::getId)
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, FactoryFinishedInventoryDO::getProductSnapshotId)
                .leftJoin(PrepareProductsRulesDO.class, PrepareProductsRulesDO::getDeliveryProjectId, ShippingProjectDO::getId)
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, ProductSnapshotDO::getSelfSkuId)
                .leftJoin(FactoryInfoDO.class, FactoryInfoDO::getId, SelfProductDO::getFactoryId)
                .eq(ShippingRecordDO::getId, form.getRecordId())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), ProductSnapshotDO::getSelfSku, form.getSelfSkuList())
                .between(TrialShippingInventoryDO::getRealShippingStartDate, form.getShippingStartDate(), form.getShippingEndDate())
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), ProductSnapshotDO::getVirtualSku, form.getVirtualSkuList())
                .in(CollectionUtil.isNotEmpty(form.getOldSkuList()), "t4.virtual_data->>'$.oldSku'", form.getOldSkuList())
                .apply(StrUtil.isNotBlank(form.getProductName()), "t4.self_data->>'$.productName' like {0}", StrUtil.isBlank(form.getProductName()) ? form.getProductName() : "%" + form.getProductName().strip() + "%")
                .eq(StrUtil.isNotBlank(form.getFactoryCode()), FactoryInfoDO::getFactoryCode, Optional.ofNullable(form.getFactoryCode()).map(String::strip).orElse(null))
                .apply(StrUtil.isNotBlank(form.getOperator()), "FIND_IN_SET('" + form.getOperator() + "',t4.virtual_data->>'$.operator')")
                .apply("t2.factory_shipping_package_num-t2.factory_remain_num > 0 AND " +
                        String.format("t2.factory_finished_date <= DATE_SUB(%s,INTERVAL t5.transit_days DAY)", "'" + form.getShippingEndDate() + "'"))
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
                .orderByDesc(TrialShippingInventoryDO::getRealShippingStartDate)
        );
    }

    public FileMissionCenterDO selectFailResultList(String recordId) {
        return this.baseMapper.selectJoinOne(FileMissionCenterDO.class, new MPJLambdaWrapper<ShippingRecordDO>()
                .select(FileMissionCenterDO::getFileName)
                .select(FileMissionCenterDO::getFilePath)
                .select(ShippingRecordDO::getFailedResult)
                .leftJoin(FileMissionCenterDO.class, FileMissionCenterDO::getId, ShippingRecordDO::getDataSourceId)
                .eq(ShippingRecordDO::getId, recordId)
        );
    }

    public void deleteById(String recordId) {
        baseMapper.deleteById(recordId);
    }
}
