package com.crafts_mirror.utils.aop.validator;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @date 2024/6/7
 **/
public class PositiveDateValidator implements ConstraintValidator<PositiveDate, String> {
    private String format;
    @Override
    public void initialize(PositiveDate constraintAnnotation) {
        // 初始化方法，可以留空
        this.format = constraintAnnotation.pattern();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (StrUtil.isBlank(value)) {
            return true;
        }
        try {
            DateUtil.parse(value, format);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}