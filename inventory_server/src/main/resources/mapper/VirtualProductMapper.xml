<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.inventory_server.model.product.mapper.VirtualProductMapper">


    <select id="getProduceDaysByVirtualSku" resultType="java.lang.String">
        SELECT purchase_date FROM cm_self_product A LEFT JOIN cm_virtual_product B ON A.id = B.self_product_sku_id
        WHERE B.virtual_sku = #{virtualSku} AND A.status = "0" AND B.status = "0"
    </select>
    <select id="getProduceDaysByVirtualSkuId" resultType="java.lang.String">
        SELECT purchase_date FROM cm_self_product A LEFT JOIN cm_virtual_product B ON A.id = B.self_product_sku_id
        WHERE B.id = #{virtualSkuId} AND A.status = '0' AND B.status = '0'
    </select>
</mapper>