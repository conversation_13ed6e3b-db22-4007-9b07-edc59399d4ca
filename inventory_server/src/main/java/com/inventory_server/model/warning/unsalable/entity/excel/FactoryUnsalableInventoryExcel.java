package com.inventory_server.model.warning.unsalable.entity.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.inventory_server.model.warning.unsalable.entity.dto.FactoryUnsalableDto;
import com.inventory_server.model.warning.unsalable.entity.vo.UnsalableWaringVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.utils.DateUtils.YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT;
import static java.math.RoundingMode.HALF_UP;

/**
 * @Description 本地无计划明细
 * <AUTHOR>
 * @Date 2025/4/17 17:14
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@ContentStyle(wrapped = BooleanEnum.TRUE,
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER)
public class FactoryUnsalableInventoryExcel extends AbstractUnsalableInventoryExcel {
    @ExcelProperty(value = "合同号", order = 10)
    private String contractCode;

    @JsonFormat(pattern = YYYY_MM_DD_DATE_FORMAT_SLASH)
    @ExcelProperty(value = "工厂交期", order = 11)
    private String factoryFinishedDate;

    @ExcelProperty(value = "备注", order = 1000)
    private String remarks;

    public FactoryUnsalableInventoryExcel(UnsalableWaringVo vo, FactoryUnsalableDto factoryUnsalableDto) {
        super(vo);
        this.contractCode = factoryUnsalableDto.getContractCode();
        this.factoryFinishedDate = YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT.format(factoryUnsalableDto.getFactoryFinishedDate());
        this.remarks = factoryUnsalableDto.getRemarks();
        this.setTotalUnsalableInventory(factoryUnsalableDto.getUnsalableNum());
        this.setUnsalablePrices(BigDecimal.valueOf(factoryUnsalableDto.getUnsalableNum() * vo.getPriceWithTaxes()).setScale(4, HALF_UP).doubleValue());

        if (factoryUnsalableDto.getVirtualSkuId().equals(vo.getVirtualSkuId())) {
            this.setVirtualSku(factoryUnsalableDto.getIsOld().equals("1") ? vo.getOldSku() : vo.getVirtualSku());
        } else {
            this.setVirtualSku(vo.getPreUpgradeVirtualSku());
        }
    }
}
