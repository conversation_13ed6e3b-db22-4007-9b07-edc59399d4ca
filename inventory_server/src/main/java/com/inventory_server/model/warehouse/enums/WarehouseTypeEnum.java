package com.inventory_server.model.warehouse.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Description 仓库类型：0标准，1中转
 * <AUTHOR>
 * @Date 2024/7/3 10:46
 **/
@Getter
public enum WarehouseTypeEnum {
    STANDARD(0, "标准"),
    TRANSIT(1, "中转")
    ;
    private final Integer type;

    private final String desc;

    private static final Map<Integer, String> map = new HashMap<>(4);

    static {
        map.put(STANDARD.type, STANDARD.desc);
        map.put(TRANSIT.type, TRANSIT.desc);
    }

    WarehouseTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getDescByType(int type) {
        return Optional.ofNullable(map.get(type)).orElseThrow(() -> new NullPointerException("没有对应的仓库类型"));
    }
}
