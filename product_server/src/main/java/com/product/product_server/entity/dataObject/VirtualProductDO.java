package com.product.product_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.product.product_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.JdbcType;

/**
 * @Description 虚拟sku产品
 * <AUTHOR>
 * @Date 2023/12/6 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_virtual_product")
public class VirtualProductDO extends BaseEntity {
    private String selfProductSkuId;

    private String channel;

    @TableField(jdbcType = JdbcType.BINARY)
    private String virtualSku;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String oldSku;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String spuId;

    /**
     * 子体类型
     * @see com.product.product_server.enums.VirtualSubTypeEnum
     */
    private Integer subType;

    /**
     * 产品状态
     * @see com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum
     */
    private Integer productStatus;

    /**
     * 产品类型
     * @see com.product.product_server.enums.VirtualProductTypeEnum
     */
    private Integer productType;
    /**
     * 借货策略
     * @see com.product.product_server.enums.BorrowingStrategyEnum
     */
    private Integer borrowingStrategy;

    /**
     * 运营人员
     */
    private String operator;

    /**
     * 升级款关系id
     */
    private String upgradeId;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remarks;
}
