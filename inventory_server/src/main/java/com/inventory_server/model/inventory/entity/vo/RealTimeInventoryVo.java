package com.inventory_server.model.inventory.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN;

/**
 * @Description 易仓实时库存父类
 * <AUTHOR>
 * @Date 2025/2/19 11:04
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public abstract class RealTimeInventoryVo {
    private String realTimeInventoryId;

    private String image;

    private String productName;

    private String selfSku;

    private String virtualSku;

    private String channel;

    private String operator;

    private Integer productStatus;

    private Integer productType;

    private Integer subType;

    private String warehouse;

    /**
     * 可售
     */
    private Double sellableQty;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN)
    private String updateDate;
}
