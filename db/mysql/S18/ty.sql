CREATE TABLE `cm_warning_unsalable_inventory`
(
    `id`                        varchar(50)                                              NOT NULL,
    `create_date`               datetime                                                          DEFAULT NULL,
    `update_date`               datetime                                                          DEFAULT NULL,
    `create_by`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci      DEFAULT NULL,
    `update_by`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci      DEFAULT NULL,
    `status`                    char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0',
    `total_unsalable_inventory` int                                                      NOT NULL COMMENT '总无计划库存',
    `inventory_info_id`         varchar(50)                                              NOT NULL COMMENT '冗余库存id',
    PRIMARY KEY (`id`),
    KEY `inventoryInfoId` (`inventory_info_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

INSERT INTO `crafts_mirror`.`cm_sys_module` (`id`, `module_name`, `function_name`, `remark`) VALUES ('400', '库存', '库存预警', '运营(虚拟sku)');