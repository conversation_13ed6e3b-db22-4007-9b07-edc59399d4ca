package com.purchase.purchase_server.service.purchaseOrder;

import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderSystemTrackDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrdersSummaryDto;

/**
* <AUTHOR>
* @description 针对表【cm_yicang_purchase_order_system_track(易仓系统跟单状态表)】的数据库操作Service
* @createDate 2024-11-01 14:30:43
*/
public interface IYicangPurchaseOrderSystemTrackService {

    /**
     * 根据采购单表id删除
     * @param sbPoId 采购单表id
     */
    void removeBySbPoId(String sbPoId);

    /**
     * 保存易仓系统跟单状态
     * @param systemTrackDO 易仓系统跟单状态
     */
    void save(YicangPurchaseOrderSystemTrackDO systemTrackDO);

    /**
     * 更新采购单系统跟单状态
     */
    void updateTrackingInfo(OrdersSummaryDto order, String sbPoId);
}
