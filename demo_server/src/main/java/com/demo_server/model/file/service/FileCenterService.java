package com.demo_server.model.file.service;

import com.crafts_mirror.utils.utils.RestTemplateUtils;
import org.springframework.util.MultiValueMap;

/**
 * @Description 文件中心服务
 * <AUTHOR>
 * @Date 2024/5/9 10:36
 **/
public interface FileCenterService {

    MultiValueMap<String, Object> putFile(byte[] byteArrayResource, String fileName, String key);

    void uploadFile(MultiValueMap<String, Object> httpEntity, RestTemplateUtils restTemplateUtil);
}
