package com.inventory_server.model.redundancy.entity.form;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 加急头程天数的修改与回溯
 * <AUTHOR>
 * @Date 2025/4/15 14:18
 **/
@Data
public class UrgentHeadShipDateForm implements Serializable {
    @Serial
    private static final long serialVersionUID = -665465476938693L;
    @NotBlank(message = "规则id不能为空")
    private String saleRuleId;

    @PositiveOrZero(message = "头程时间只能整数")
    @NotNull(message = "头程时间只能为整数")
    private Integer urgentHeadShipDate;

    private Boolean isBackTracking;
}
