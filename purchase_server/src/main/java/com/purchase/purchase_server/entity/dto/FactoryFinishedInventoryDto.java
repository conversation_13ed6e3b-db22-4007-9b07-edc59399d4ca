package com.purchase.purchase_server.entity.dto;

import com.purchase.purchase_server.enums.DeliveryTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * @Description 工厂交货数量
 * <AUTHOR>
 * @Date 2024/1/2 10:24
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryFinishedInventoryDto implements Serializable {
    /**
     * 合同号
     */
    private String contractCode;
    /**
     * 虚拟sku
     */
    private String virtualSku;

    private String isOldStatus;

    private String virtualSkuId;

    /**
     * 工厂交期
     */
    private Date factoryFinishedDate;

    /**
     * 工厂交货数量
     */
    private Double shippingNum;
    /**
     * 备注
     */
    private String remark;

    /**
     * 标识该计划是否需要发货
     */
    private Boolean needToPriorDelivery;

    private DeliveryTypeEnum deliveryType;

    public boolean checkIfTheSamePartFactoryOrder(FactoryFinishedInventoryDto inventoryDto) {
        return equals(inventoryDto);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FactoryFinishedInventoryDto dto = (FactoryFinishedInventoryDto) o;
        return Objects.equals(contractCode, dto.contractCode) && Objects.equals(virtualSku, dto.virtualSku) &&
                Objects.equals(factoryFinishedDate, dto.factoryFinishedDate) && Objects.equals(remark, dto.remark);
    }

    @Override
    public int hashCode() {
        return Objects.hash(contractCode, virtualSku, factoryFinishedDate, remark);
    }
}
