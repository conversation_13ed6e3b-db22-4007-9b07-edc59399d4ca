package com.inventory_server.model.redundancy.entity.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/11
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryForeignStoreBO implements Serializable {

    @Serial
    private static final long serialVersionUID = 2405177589150251807L;

    private String warehouse;
    private Date enableUsingDate;
    private Integer inventoryNum;
    private String inventoryInfoId;
    private String virtualSku;
    private String shipmentCode;
    private String enableUsingDateString;
    private String startShippingDate;
    private String remarks;
}
