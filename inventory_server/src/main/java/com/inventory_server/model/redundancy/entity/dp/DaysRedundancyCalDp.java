package com.inventory_server.model.redundancy.entity.dp;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import com.inventory_server.applications.dto.FactoryRemainInventoryDto;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static java.math.RoundingMode.DOWN;
import static java.math.RoundingMode.HALF_UP;

/**
 * @Description 每天的冗余库存情况
 * <AUTHOR>
 * @Date 2024/5/25 9:57
 **/
@Getter
public class DaysRedundancyCalDp {
    private  LocalDate arrivingDate;

    private  List<FactoryRemainInventoryDto> arrivingInventoryList;

    private  Map<String, Double> remainInventoryMap;

    private  int redundancyDays;

    private int totalRedundancy;

    /**
     * 安全库存
     */
    private BigDecimal safeInventory;

    /**
     * 剩余库存
     */
    private BigDecimal remainInventory;

    /**
     * 到货量
     */
    private BigDecimal arrivingInventory;

    public DaysRedundancyCalDp(LocalDate arrivingDate, Map<String, Double> remainInventoryMap, int redundancyDays,
                               List<FactoryRemainInventoryDto> arrivingInventoryList, Map<String, BigDecimal> targetSalesMap) {
        this.arrivingDate = arrivingDate;
        this.arrivingInventoryList = arrivingInventoryList;
        this.remainInventoryMap = remainInventoryMap;
        this.redundancyDays = redundancyDays;
        this.safeInventory = calSafeInventory(arrivingDate, targetSalesMap);
        // 分别计算在途到货数量、当天总剩余库存、当天总冗余库存
        calArrivingNum();
        calTotalRemain();
        calTotalRedundantInventory();
    }

    public DaysRedundancyCalDp(BigDecimal safeInventory, int totalRedundancy) {
        this.safeInventory = safeInventory;
        this.totalRedundancy = totalRedundancy;
    }

    public double calInitInventory() {
        return remainInventory.subtract(arrivingInventory).setScale(3, HALF_UP).doubleValue();
    }

    private void calArrivingNum() {
        double arrivingSum = arrivingInventoryList.stream().mapToDouble(FactoryRemainInventoryDto::getStoreNum).sum();
        arrivingInventory = BigDecimal.valueOf(arrivingSum).setScale(3, HALF_UP);
    }

    private void calTotalRemain() {
        double totalRemainNum = remainInventoryMap.entrySet().stream()
                .filter(f -> !f.getKey().toLowerCase(Locale.ROOT).contains("start"))
                .mapToDouble(Map.Entry::getValue)
                .sum();
        remainInventory = BigDecimal.valueOf(totalRemainNum).setScale(3, HALF_UP);
    }

    /**
     * 根据目标日销计算安全库存
     * @param arrivingDate
     * @return
     */
    private BigDecimal calSafeInventory(LocalDate arrivingDate, Map<String, BigDecimal> targetSalesMap) {
        BigDecimal totalSales = BigDecimal.ZERO;
        DateTime calDate = new DateTime(arrivingDate);
        int calDays = redundancyDays;
        DateTime endDate = calDate.offsetNew(DateField.DAY_OF_YEAR, calDays);

        for (; calDate.isBefore(endDate); calDate.offset(DateField.DAY_OF_YEAR, 1)) {
            totalSales = totalSales.add(targetSalesMap.getOrDefault(calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH), BigDecimal.ZERO));
        }

        return totalSales;
    }

    /**
     * 计算到货日的总冗余量
     */
    private void calTotalRedundantInventory() {
        totalRedundancy = remainInventory.subtract(arrivingInventory).subtract(safeInventory)
                .setScale(0, DOWN).intValue();
        totalRedundancy = Math.max(0, totalRedundancy);
    }
}
