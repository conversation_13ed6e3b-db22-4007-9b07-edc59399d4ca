package com.product.product_server.model.products;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Locale;
import java.util.Objects;

public record CaseWeightInfoDp(String weightUnit, Double caseGrossWeight, Integer containerLoad) {

    public CaseWeightInfoDp(String weightUnit, Double caseGrossWeight, Integer containerLoad) {
        if(weightUnit == null || !"kg".equals(weightUnit.toLowerCase(Locale.ROOT))) {
            throw new IllegalArgumentException("单箱重量单位异常，暂时只允许以kg作单位");
        }
        this.weightUnit = weightUnit.toLowerCase(Locale.ROOT);
        Objects.requireNonNull(caseGrossWeight, "单箱毛重不能为空");
        this.caseGrossWeight = BigDecimal.valueOf(caseGrossWeight).setScale(2, RoundingMode.UP).doubleValue();
        this.containerLoad = Objects.requireNonNull(containerLoad, "单箱数量不能为空");
        if(containerLoad <= 0) {
            throw new IllegalArgumentException("单箱数量必须是正整数");
        }
    }
}
