package com.purchase.purchase_server.utils.easyExcelUtil.listener.Deliver;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.entity.dataObject.ShippingRecordDO;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.form.UpgradeProductInteriorForm;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.UpgradeInfoInteriorVo;
import com.purchase.purchase_server.enums.DeliveryImportSheetNameEnum;
import com.purchase.purchase_server.repository.dataRepository.ShippingRecordRepositoryImpl;
import com.purchase.purchase_server.utils.easyExcelUtil.listener.AbstractImportListener;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.AbstractManager;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.AbstractMapManager;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.DeliveryManager;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.DeliveryMapManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static com.crafts_mirror.utils.constant.SystemConstant.PRODUCTS_VIRTUAL_UPGRADE_INFO_URL;

/**
 * @Description 导入产品抽象类
 * <AUTHOR>
 * @Date 2023/12/21 11:35
 **/
@Slf4j
public abstract class AbstractPurchaseImportListener<T> extends AbstractImportListener<T> {

    protected final ShippingRecordRepositoryImpl shippingRecordRepositoryImpl = ApplicationContextProvider.getBean(ShippingRecordRepositoryImpl.class);

    protected final RestTemplate restTemplate = ApplicationContextProvider.getBean(RestTemplate.class);

    public AbstractPurchaseImportListener(List<SenboWarehouseDto> senboWarehouseList) {
        super(senboWarehouseList);
    }

    public UpgradeInfoInteriorVo selectUpgradeInfo(String upgradeId) {
        var restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        var virtualProductListVo = restTemplateUtil.post(UpgradeProductInteriorForm.builder().id(upgradeId).build(), ResultDTO.class, PRODUCTS_VIRTUAL_UPGRADE_INFO_URL);
        return JSON.to(UpgradeInfoInteriorVo.class, virtualProductListVo.getData());
    }

    @Override
    protected String getCalculationType() {
        return "发货计划导入";
    }

    @Override
    protected AbstractManager getManager() {
        return DeliveryManager.getInstance();
    }

    @Override
    protected AbstractMapManager getMapManager() {
        return DeliveryMapManager.getInstance();
    }

    @Override
    protected void validRulesSkuMatchRequired() {
        AbstractMapManager deliveryMapManager = getMapManager();
        var rulesMap = deliveryMapManager.getStockingRulesMap();
        Set<String> deliveryVirtualSkuSet = deliveryMapManager.getDeliveryVirtualSkuSet();
        var targetSalesMap = deliveryMapManager.getTargetSalesMap();
        StringBuilder error = new StringBuilder();

        Set<String> rulesVirtualSkuSet = rulesMap.keySet();
        HashMap<String, String> stockingRulesUpgradeMap = deliveryMapManager.getStockingRulesUpgradeMap();

        HashMap<String, String> stockingReversedMap = new HashMap<>();
        stockingRulesUpgradeMap.forEach((key, value) -> stockingReversedMap.put(value, key));
        if (!rulesVirtualSkuSet.containsAll(deliveryVirtualSkuSet)) {
            deliveryVirtualSkuSet.removeAll(rulesVirtualSkuSet);
            for (String virtualSku : deliveryVirtualSkuSet) {
                String upgradeSku = stockingReversedMap.getOrDefault(virtualSku, "");
                if (StrUtil.isNotBlank(upgradeSku)) {
                    error.append(String.format("计划里虚拟sku %s 对应升级款 %s 在备货规则中不存在；\n", upgradeSku, virtualSku));
                } else {
                    error.append(String.format("计划里下列商品没有备货规则：%s；\n", virtualSku));
                }
            }
        }

        for (var upgrade : stockingRulesUpgradeMap.entrySet()) {
            if (!rulesVirtualSkuSet.contains(upgrade.getValue())) {
                error.append(String.format("备货规则中未找到被升级款：%s 对应的升级款：%s；\n", upgrade.getKey(), upgrade.getValue()));
            }
        }

//        Set<String> targetSalesSet = targetSalesMap.keySet();
//        // 创建一个新的 HashMap 来存储互换后的键值对
//        HashMap<String, String> targetSalesUpgradeMap = deliveryMapManager.getTargetSalesUpgradeMap();
//        HashMap<String, String> targetSalesReversedMap = new HashMap<>();
//        targetSalesUpgradeMap.forEach((key, value) -> targetSalesReversedMap.put(value, key));
//        if (!targetSalesSet.containsAll(deliveryVirtualSkuSet)) {
//            deliveryVirtualSkuSet.removeAll(targetSalesSet);
//            for (String virtualSku : deliveryVirtualSkuSet) {
//                String upgradeSku = targetSalesReversedMap.getOrDefault(virtualSku, "");
//                if (StrUtil.isNotBlank(upgradeSku)) {
//                    error.append(String.format("计划里虚拟sku %s 对应升级款 %s 在目标日销中不存在；\n", upgradeSku, virtualSku));
//                } else {
//                    error.append(String.format("计划里下列商品没有目标日销：%s；\n", virtualSku));
//                }
//            }
//        }
//
//        for (var upgrade : targetSalesUpgradeMap.entrySet()) {
//            if (!rulesVirtualSkuSet.contains(upgrade.getValue())) {
//                error.append(String.format("目标日销中未找到被升级款：%s 对应的升级款：%s；\n", upgrade.getKey(), upgrade.getValue()));
//            }
//        }

        if (StrUtil.isNotBlank(error.toString())) {
            throw new IllegalArgumentException(error.toString());
        }
    }

    @Override
    protected void updateRecordResult() {
        AbstractManager manager = getManager();

        shippingRecordRepositoryImpl.updateById(ShippingRecordDO.builder()
                .id(manager.getRecordId())
                .taskResult(String.format("成功：%d；失败：%d", manager.getSuccessfulTimes(), manager.getFailedTimes()))
                .failedResult(String.join(";", Optional.ofNullable(manager.getErrorList()).orElse(new ArrayList<>()))).build());
    }

    protected Boolean caseAllSheetParsed(String sheetName, AbstractManager manager) {
        DeliveryImportSheetNameEnum code = DeliveryImportSheetNameEnum.ofCode(sheetName);
        switch (code) {
            case STOCKING_RULES -> manager.setStockingRulesStatus(true);
            case AM_STOCK_QUANTITY -> manager.setAmStockQuantityStatus(true);
            case LAMP_STOCK_QUANTITY -> manager.setLampStockQuantityStatus(true);
            case STOCK_QUANTITY -> manager.setStockQuantityStatus(true);
            case DELIVERY -> manager.setDeliveryStatus(true);
        }
        return manager.getStockingRulesStatus() && manager.getAmStockQuantityStatus()
                && manager.getLampStockQuantityStatus()
                && manager.getStockQuantityStatus() && manager.getDeliveryStatus();
    }
}
