package com.product.product_server.entity.form;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/10
 **/
@Data
@AllArgsConstructor
public class InteriorReplenishmentInfoQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 4875859731829291626L;
    /**
     * 创建开始时间
     */
    private String createStartDate;

    /**
     * 创建结束时间
     */
    private String createEndDate;
}
