package com.purchase.purchase_server.service.replenishment;

import com.purchase.purchase_server.entity.dataObject.ReplenishmentSoldOutDaysDO;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentProjectSaveDto;

import java.util.List;

public interface ISoldOutDaysService {

    void saveSoldOutDays(ReplenishmentProjectSaveDto projectSaveDto, String projectId);

    void deleteByReplenishmentProjectId(List<String> replenishmentProjectIds);

    List<ReplenishmentSoldOutDaysDO> getBeforeSoldOutList(String replenishmentProjectId);
}
