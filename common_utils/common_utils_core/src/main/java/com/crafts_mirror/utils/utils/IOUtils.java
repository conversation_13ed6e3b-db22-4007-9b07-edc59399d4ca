package com.crafts_mirror.utils.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.Closeable;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2022/7/15 10:27
 */
@Slf4j
public class IOUtils {

    @SafeVarargs
    public static <T extends Closeable> void closeIOStream(T... t1){
        try {
            for (T t : t1) {
                if(t == null) {
                    continue;
                }
                t.close();
            }
        } catch (IOException e) {
            log.error("关闭流异常", e);
            throw new RuntimeException("关闭流异常", e);
        }
    }
}
