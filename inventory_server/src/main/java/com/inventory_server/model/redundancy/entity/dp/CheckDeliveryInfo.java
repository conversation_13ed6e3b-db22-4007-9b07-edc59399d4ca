package com.inventory_server.model.redundancy.entity.dp;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_M_D_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.utils.DateUtils.YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT;

public interface CheckDeliveryInfo {

    default void checkContractCode(String contractCode) {
        if(StrUtil.isBlank(contractCode)) {
            throw new IllegalArgumentException("合同号不能为空");
        }

        if (contractCode.length() > 50) {
            throw new IllegalArgumentException("合同号长度超过限制");
        }

        //if(!PatternUtils.UPPERCASE_LOWERCASE_NUMBER_DOT_PATTERN.matcher(contractCode).matches()) {
        //    throw new IllegalArgumentException("合同号只能英文、数字和符号的组合");
        //}
    }

    default void checkRemark(String remarks) {
        if (StrUtil.isNotBlank(remarks) && remarks.length() > 100){
            throw new IllegalArgumentException("备注-请填写小于100个字符");
        }
    }

    default void getTime(String daysDuration) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(YYYY_M_D_DATE_FORMAT_SLASH);
        try {
            if (StrUtil.isBlank(daysDuration)){
                throw new IllegalArgumentException("工厂交期-不可以为空");
            }
            // 尝试解析日期字符串
            LocalDate date = LocalDate.parse(daysDuration, inputFormatter);
            // 将日期格式化为所需的格式
            date.format(YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("工厂交期-请正确填写日期");
        }
    }

    default void checkNum(String shipments){
        if (!(NumberUtil.isInteger(shipments) && Integer.parseInt(shipments) >= 0)){
            throw new IllegalArgumentException("数量-请正确填写数字");
        }
    }
}
