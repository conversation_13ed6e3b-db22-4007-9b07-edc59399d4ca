package com.product.product_server.model.products;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN;

/**
 * @Description 创建人、创建时间、修改人、修改时间领域模型
 * <AUTHOR>
 * @Date 2023/12/6 11:50
 **/
public record CreatorEditorDp(String creator, String editor, Date createDate, Date editDate) {
    private static final ThreadLocal<SimpleDateFormat> THREAD_LOCAL = ThreadLocal.withInitial(() -> new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN));

    public CreatorEditorDp(String creator, String editor, Date createDate, Date editDate) {
        this.creator = creator;
        this.editor = editor;
        try {
            SimpleDateFormat simpleDateFormat = THREAD_LOCAL.get();
            this.createDate = simpleDateFormat.parse(simpleDateFormat.format(createDate));
            this.editDate = simpleDateFormat.parse(simpleDateFormat.format(editDate));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
}
