package com.purchase.purchase_server.entity.form.interior;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_M_D_DATE_FORMAT_SLASH;

/**
 * @Description 售罄前断货天数
 * <AUTHOR>
 * @Date 2024/5/17 11:46
 **/
@Getter
@AllArgsConstructor
public class SoldOutDateBeforeTheoryDto {
    private final LocalDate daysBegin;

    private LocalDate daysEnd;

    private Integer soldOutDays;

    private String daysGap;

    private Double lackNum;

    private String inventoryInfoId;

    public SoldOutDateBeforeTheoryDto(LocalDate daysBegin, Double lackNum) {
        this.daysBegin = daysBegin;
        this.daysEnd = daysBegin;
        this.lackNum = lackNum;
        soldOutDays = 1;
        setDaysGap();
    }

    public boolean addDaysEnd(LocalDate end, double lackNum) {
        if(this.daysEnd.until(end, ChronoUnit.DAYS) != 1) {
            return false;
        }

        this.daysEnd = end;
        this.lackNum += lackNum;
        soldOutDays++;
        setDaysGap();
        return true;
    }

    private void setDaysGap() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_M_D_DATE_FORMAT_SLASH);
        daysGap = daysBegin.isEqual(daysEnd) ? daysBegin.format(formatter) : daysBegin.format(formatter) + " ~ " + daysEnd.format(formatter);
    }
    private void setInventoryInfoId(String inventoryInfoId) {
        this.inventoryInfoId = inventoryInfoId;
    }
}
