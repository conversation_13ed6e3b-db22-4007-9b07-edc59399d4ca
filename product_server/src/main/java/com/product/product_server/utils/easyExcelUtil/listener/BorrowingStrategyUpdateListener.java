package com.product.product_server.utils.easyExcelUtil.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.util.ConverterUtils;
import com.product.product_server.entity.dataObject.VirtualProductDO;
import com.product.product_server.entity.excelObject.BorrowingStrategyExcel;
import com.product.product_server.enums.BorrowingStrategyEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;

/**
 * @Description 更新借货策略
 * <AUTHOR>
 * @Date 2025/4/15 11:15
 **/
@Slf4j
public class BorrowingStrategyUpdateListener extends AbstractUpdateVirtualProductInfoListener<BorrowingStrategyExcel> {
    public BorrowingStrategyUpdateListener(String fileName, String importExcelId) {
        super(fileName, importExcelId);
    }

    @Override
    public void invoke(BorrowingStrategyExcel data, AnalysisContext context) {
        log.warn("更新借货策略-------------导入excel进行中，成功解析excel");
        Integer approximateRowNumber = context.readSheetHolder().getApproximateTotalRowNumber();
        if(approximateRowNumber != null && approximateRowNumber > 50000) {
            throw new RuntimeException("导入数据超过50000行，请缩小导入数据量");
        }

        VirtualProductDO info = getVirtualProductInfo(data.getVirtualSku());
        BorrowingStrategyEnum borrowingStrategy = BorrowingStrategyEnum.ofDesc(data.getBorrowingStrategy());
        if (borrowingStrategy == null) {
            throw new IllegalArgumentException("借货策略只能填直接借调/人工确认");
        }

        updateBorrowingStrategy(info.getId(), borrowingStrategy.getCode());
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        Map<Integer,String> map = ConverterUtils.convertToStringMap(headMap, context);
        for (Map.Entry<Integer, String> entry : map.entrySet()) {
            if (map.size() == 2){
                String value = entry.getValue();
                if (!Objects.equals(value, "*虚拟SKU") && !Objects.equals(value, "*借货策略")) {
                    throw new RuntimeException("表头错误，请检查表头是否正确");
                }
            }else {
                throw new RuntimeException("表头错误，请检查表头是否正确");
            }
        }
        super.invokeHead(headMap, context);
    }
}
