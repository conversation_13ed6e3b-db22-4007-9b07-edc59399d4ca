package com.product.product_server.utils.easyExcelUtil.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.util.ConverterUtils;
import com.product.product_server.entity.dataObject.VirtualProductDO;
import com.product.product_server.entity.excelObject.UpgradeExcel;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description 更新子体类型
 * <AUTHOR>
 * @Date 2024/8/8 11:15
 **/
@Slf4j
public class UpgradeUpdateListener extends AbstractUpdateVirtualProductInfoListener<UpgradeExcel> {
    public UpgradeUpdateListener(String fileName, String importExcelId) {
        super(fileName, importExcelId);
    }

    @Override
    public void invoke(UpgradeExcel data, AnalysisContext context) {
        log.warn("更新升级产品-------------导入excel进行中，成功解析excel");
        Integer approximateRowNumber = context.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > 10000) {
            throw new RuntimeException("导入数据超过10000行，请缩小导入数据量");
        }
        // 校验并获取原始SKU信息
        String originalSkuStr = data.getOriginalSku();
        if (StrUtil.isBlank(originalSkuStr)) {
            throw new IllegalArgumentException("老款虚拟SKU为空");
        }
        VirtualProductDO originalSku = Optional.ofNullable(getVirtualProductInfo(originalSkuStr))
                .orElseThrow(() -> new IllegalArgumentException("老款虚拟SKU不存在"));

        // 校验并获取升级SKU信息 
        String upgradeSkuStr = data.getUpgradeSku();
        if (StrUtil.isBlank(upgradeSkuStr)) {
            throw new IllegalArgumentException("代替为升级款虚拟SKU为空");
        }
        VirtualProductDO upgradeSku = Optional.ofNullable(getVirtualProductInfo(upgradeSkuStr))
                .orElseThrow(() -> new IllegalArgumentException("代替为升级款虚拟SKU不存在"));

        // 校验SKU升级状态
        if (StrUtil.isNotBlank(originalSku.getUpgradeId()) || StrUtil.isNotBlank(upgradeSku.getUpgradeId())) {
            throw new IllegalArgumentException("已存在升级款/被升级款");
        }

        updateVirtualProductUpgrade(originalSku.getId(), upgradeSku.getId());
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        Map<Integer, String> map = ConverterUtils.convertToStringMap(headMap, context);
        for (Map.Entry<Integer, String> entry : map.entrySet()) {
            if (map.size() == 2) {
                String value = entry.getValue();
                if (!Objects.equals(value, "老款虚拟SKU") && !Objects.equals(value, "代替为升级款SKU")) {
                    throw new RuntimeException("表头错误，请检查表头是否正确");
                }
            } else {
                throw new RuntimeException("表头错误，请检查表头是否正确");
            }
        }
        super.invokeHead(headMap, context);
    }
}
