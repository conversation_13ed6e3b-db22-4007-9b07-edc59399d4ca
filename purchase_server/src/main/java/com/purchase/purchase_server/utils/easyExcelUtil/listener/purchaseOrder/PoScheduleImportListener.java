package com.purchase.purchase_server.utils.easyExcelUtil.listener.purchaseOrder;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.purchase.purchase_server.assembler.FactoryAssembler;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderDetailDO;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrdersDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrdersSummaryDto;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.PurchaseOrderImportDTO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.YicangPurchaseOrderDetailDTO;
import com.purchase.purchase_server.entity.excelObject.PoDeliveryScheduleExcel;
import com.purchase.purchase_server.enums.IsOldStatusEnum;
import com.purchase.purchase_server.enums.purchaseOrders.PoStatusEnum;
import com.purchase.purchase_server.model.purchaseOrder.PoDeliveryInfoDp;
import com.purchase.purchase_server.repository.dataRepository.VirtualProductRepositoryImpl;
import com.purchase.purchase_server.service.purchaseOrder.IPurchaseOrdersService;
import com.purchase.purchase_server.service.purchaseOrder.IYicangPurchaseOrderDetailService;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description 导入补货计划-计划
 * <AUTHOR>
 * @Date 2024/1/15 15:49
 **/
@Slf4j
public class PoScheduleImportListener extends AbstractImportListener<PoDeliveryScheduleExcel> {

    private final FactoryAssembler factoryAssembler = ApplicationContextProvider.getBean(FactoryAssembler.class);

    private final IPurchaseOrdersService purchaseOrdersService = ApplicationContextProvider.getBean(IPurchaseOrdersService.class);

    private final VirtualProductRepositoryImpl virtualProductRepository = ApplicationContextProvider.getBean(VirtualProductRepositoryImpl.class);

    private final IYicangPurchaseOrderDetailService purchaseOrderDetailService = ApplicationContextProvider.getBean(IYicangPurchaseOrderDetailService.class);


    private final List<PurchaseOrderImportDTO> purchaseOrderImportDto;

    protected static final Integer TOTAL_ROW_NUMBER = 10000;

    public PoScheduleImportListener(List<String> errorList,
                                    List<PurchaseOrderImportDTO> purchaseOrderImportDto) {
        super(errorList);
        this.purchaseOrderImportDto = purchaseOrderImportDto;
    }

    @Override
    public void invoke(PoDeliveryScheduleExcel product, AnalysisContext analysisContext) {
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }
        // 判空
        check(product);
        // 填写情况校验
        new PoDeliveryInfoDp(product);

        List<YicangPurchaseOrdersDO> poList = purchaseOrdersService.select(OrdersSummaryDto.builder().refNo(product.getRefNo()).build());
        if (CollectionUtil.isEmpty(poList)) {
            throw new IllegalArgumentException("参考号不存在");
        }

        YicangPurchaseOrdersDO yicangPurchaseOrdersDO = poList.stream()
                .filter(po -> po.getPoStaus() != PoStatusEnum.CANCELLED.getCode())  // 过滤出PoStatus不为9的记录
                .findFirst()                          // 获取第一个匹配的记录
                .orElseGet(() -> poList.isEmpty() ? null : poList.get(0));  // 如果没有匹配的，则返回列表第一个元素


        String destinationSku = product.getDestinationSku();
        String isOldStatus = null;
        String detailId = null;
        if (StrUtil.isNotBlank(destinationSku)) {
            String upperCase = destinationSku.toUpperCase();
            VirtualProductDO virtualProductDO = virtualProductRepository.getOneByVirtualSkuOrOldSku(upperCase);

            if (ObjectUtil.isEmpty(virtualProductDO)) {
                throw new IllegalArgumentException("虚拟SKU/老SKU不存在");
            }
            YicangPurchaseOrderDetailDTO dto = new YicangPurchaseOrderDetailDTO();
            dto.setSbPoId(yicangPurchaseOrdersDO.getId());
            dto.setSbSelfSkuId(virtualProductDO.getSelfProductSkuId());
            List<YicangPurchaseOrderDetailDO> orderDetailList = purchaseOrderDetailService.select(dto);
            if (CollectionUtil.isEmpty(orderDetailList)) {
                throw new IllegalArgumentException("采购订单内不存在" + destinationSku + "对应的自定义sku");
            }
            detailId = orderDetailList.get(0).getId();
            if (upperCase.equals(virtualProductDO.getVirtualSku())) {
                isOldStatus = IsOldStatusEnum.VIRTUAL_SKU.getCode();
            } else {
                isOldStatus = IsOldStatusEnum.OLD_SKU.getCode();
            }
            destinationSku = upperCase;
        }
        purchaseOrderImportDto.add(
                PurchaseOrderImportDTO.builder()
                        .refNo(product.getRefNo())
                        .sbPoId(yicangPurchaseOrdersDO.getId())
                        .detailId(detailId)
                        .destinationSku(destinationSku)
                        .isOldStatus(isOldStatus)
                        .qtyEta(product.getQtyEta())
                        .deliveryDate(product.getDeliveryDate())
                        .deliveryQuantity(product.getDeliveryQuantity()).build());
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    /**
     * 这里会一行行的返回头
     *
     * @param headMap 表头map
     * @param context 上下文
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        //删除headMap中的空值
        headMap.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty());
        List<String> headMapList = new ArrayList<>(headMap.values());

        List<String> headList = new ArrayList<>();
        Field[] fields = PoDeliveryScheduleExcel.class.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ExcelProperty.class)) {
                ExcelProperty declaredAnnotation = field.getDeclaredAnnotation(ExcelProperty.class);
                String headValue = declaredAnnotation.value()[0];
                headList.add(headValue);
            }
        }
        if (!headMapList.equals(headList)) {
            throw new RuntimeException("导入表头不正确");
        }
    }
}
