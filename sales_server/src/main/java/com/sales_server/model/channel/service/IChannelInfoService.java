package com.sales_server.model.channel.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sales_server.model.channel.entity.form.ChannelSearchPageForm;
import com.sales_server.model.channel.entity.form.SaveChannelForm;
import com.sales_server.model.channel.entity.vo.ChannelPageVo;
import com.sales_server.model.channel.entity.vo.ChannelSearchVo;
import com.sales_server.model.channel.entity.vo.NicheChannelDetailVo;

import java.util.Map;

public interface IChannelInfoService {
    void saveSaleChannel(SaveChannelForm form);

    void saveNicheChannel(SaveChannelForm form);

    void checkSaleChannelNameExisted(String saleChannel);

    void checkNicheChannelNameExisted(SaveChannelForm form);

    void checkNicheChannelSortExisted(SaveChannelForm form);

    void checkSaleChannelIdExisted(String parentId);

    ChannelSearchVo getChannelList(int isLeaf);

    IPage<ChannelPageVo> getChannelPage(ChannelSearchPageForm form);

    NicheChannelDetailVo getNicheChannelDetail(String nicheChannelId);

    Map<String, String> getChannelIdNameMap(int isLeaf);

    Map<String, String> getChildParentNameMap();

}
