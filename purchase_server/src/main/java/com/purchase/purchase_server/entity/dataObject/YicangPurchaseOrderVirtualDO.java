package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;

/**
 * 易仓采购单虚拟sku表
 * @TableName cm_yicang_purchase_order_virtual
 */
@TableName(value ="cm_yicang_purchase_order_virtual")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class YicangPurchaseOrderVirtualDO extends BaseEntity {
    @Serial
    private static final long serialVersionUID = -917513346550617788L;
    /**
     * senbo虚拟skuId
     */
    private String virtualSkuId;

    /**
     * senbo虚拟sku
     */
    private String destinationSku;

    /**
     * 运营
     */
    private String operator;

    /**
     * 采购量
     */
    private Integer qtyEta;

    /**
     * 采购单明细表id
     */
    private String sbPoDetailId;

    /**
     * sku类型(0-虚拟sku 1-老sku)
     */
    private String isOldStatus;

    /**
     * 渠道
     */
    private String channel;
}