package com.purchase.purchase_server.entity.vo.purchaseOrder;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 易仓采购单vo
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YicangPurchaseOrdersVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 8722015892263425396L;

    private String id;
    /**
     * 采购单Id
     */
    private Integer poId;

    /**
     * 采购单号
     */
    private String poCode;

    /**
     * 仓库Id
     */
    private Integer warehouseId;

    /**
     * 头程运输方式
     */
    private Integer shippingMethodIdHead;

    /**
     * 跟踪号
     */
    private String trackingNo;

    /**
     * 参考号
     */
    private String refNo;

    /**
     * 供应商Id
     */
    private Integer suppilerId;

    /**
     * 总应付金额
     */
    private Float payableAmount;

    /**
     * 总实付金额
     */
    private Float actuallyAmount;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 付款状态：
     * 0未申请付款，
     * 3已申请未付款，1已付款 ，
     * 2未付清
     */
    private String payStatus;

    /**
     * 采购单状态：
     * 10已确认(供应商反馈)，
     * 1待确认，
     * 2审核中，
     * 3已审批(入库在途)，
     * 5到货异常，
     * 8已完成，
     * 9撤销，
     * 11已取消(供应商反馈) ('10'、'11' 这2种状态未开通供应商系统忽略)
     */
    private Integer poStaus;

    // 新增描述字段
    private String poStatusDesc;

    /**
     * 创建时间
     */
    private String dateCreate;

    /**
     * 预计到货时间
     */
    private String dateEta;

    /**
     * 审核时间
     */
    private String dateRelease;

    /**
     * 采购单完成时间
     */
    private String poCompletionTime;

    /**
     * 更新时间
     * （以下业务场景下会更新：
     * 1，采购单，操作 ： 编辑、提交审核、撤销提交审核 、 采购单撤销、强制完成采购单、批量导入跟踪号信息
     * 2，收货、
     * 3，质检（以及操作未到货）
     * 4，取消上架
     * 5，出纳付款，操作“确认付款”
     * 6，采购变更，变更单， 操作：创建变更单、作废、驳回、审核通过
     * 7，收货异常处理（触发更新：payable_amount“应付金额”、receiving_exception_status “收货异常状态” 或者 po_staus “采购单状态”时）
     * 8，QC异常处理（触发更新：qc_exception_status “QC异常状态” 或者 po_staus“采购单状态”时）
     * 9，类型为“退货”、“良品换货”、“次品换良品”出库单，使用的采购单批次，确认出库）
     */
    private String poUpdateTime;

    /**
     * 中转仓库Id
     */
    private Integer toWarehouseId;

    /**
     * 收货异常：
     * 0没有异常、
     * 1收货异常、
     * 2QC异常
     * 3收货&质检异常
     */
    private Integer receivingException;

    /**
     * 采购员（用户ID）
     */
    private Integer operatorPurchase;

    /**
     * 收货异常是否已经处理
     * 0无、
     * 1没有处理、
     * 2已经处理
     */
    private Integer receivingExceptionHandle;

    /**
     * 采购单是否通过审批，未通过或通过
     */
    private String returnVerify;

    /**
     * 运输金额
     */
    private Float payShipAmount;

    /**
     * 建立方式，
     * 0系统生成，
     * 1人工建立
     */
    private Integer createType;

    /**
     * 跟单状态序号
     */
    private String ptsStatusSort;

    /**
     * 结算方式
     */
    private Integer accountType;

    /**
     * 跟单员（用户ID）
     */
    private Integer ptsOprater;

    /**
     * 交易支付单号，如淘宝交易号
     */
    private String transactionNo;

    /**
     * 采购单承运商ID
     */
    private Integer psId;

    /**
     * 备注
     */
    private String poRemark;

    /**
     * 收货异常状态
     */
    private Integer receivingExceptionStatus;

    /**
     * 质检异常状态
     */
    private Integer qcExceptionStatus;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商代码
     */
    private String supplierCode;

    /**
     * 仓库代码
     */
    private String warehouseCode;

    /**
     * 仓库描述
     */
    private String warehouseDesc;

    /**
     * 入库单号
     */
    private String receivingCode;

    /**
     * 是否是审核未通过，回退的采购单
     * 0否
     * 1是
     */
    private Integer verify;

    /**
     * 到货（
     * 1今日到
     * 2晚到
     * ）
     */
    private Integer markEta;

    /**
     * 跟单员
     */
    private String ptsName;

    /**
     * 承运商名称（ps_id为0时不返回）
     */
    private String psName;

    /**
     * 承运商网址（ps_id为0时不返回）
     */
    private String psUrl;

    /**
     * 最新的采购单跟踪备注
     */
    private String ptNote;

    /**
     * 最新的采购单跟踪备注时间
     */
    private String ptAddTime;

    /**
     * 预期总数
     */
    private Integer qtyExpectedAll;

    /**
     * 实到总数
     */
    private Integer qtyRecevingAll;

    /**
     * 采购总数
     */
    private Integer qtyEtaAll;

    /**
     * 跟踪号数量
     */
    private Integer trackings;

    /**
     * 是否网采：
     * 0否，
     * 1是
     */
    private Integer poIsNet;

    /**
     * 支付方式，
     * 1现金、
     * 2在线，
     * 3银行账号
     */
    private Integer payType;

    /**
     * 银行名称，支付方式为银行账号时返回
     */
    private String bankName;

    /**
     * 支付账户
     */
    private String payAccount;

    /**
     * 预期总金额
     */
    private Float sumAmount;

    /**
     * 预计出厂时间
     */
    private String dateExpected;

    /**
     * 补货方式：
     * 0缺货入库，
     * 1警报入库，
     * 2特采入库，
     * 3正常入库，
     * 4样品采购入库，
     * 5备货采购，
     * 6试销采购，
     * 7返修入库
     */
    private Integer poType;

    /**
     * 网采单号（多个）格式：["1111111","222222"]
     */
    private String singleNetNumber;

    /**
     * 税费
     */
    private Float totalTaxFee;

    /**
     * 采购审核备注
     */
    private String paymentNote;

    /**
     * 采购公司
     */
    private String company;

    /**
     * 创建人
     */
    private String operatorCreate;

    /**
     * 跟踪号集合（所有跟踪号，格式：["123","234"]）beta版未上线
     */
    private String trackingNoSet;

    /**
     * 组织机构id
     */
    private Integer userOrganizationId;

    /**
     * 是否退税（0-否 1-是）
     */
    private Integer isRebateTax;

    /**
     * 组织机构名称
     */
    private String userOrganizationName;

    /**
     * 预付比例
     */
    private Integer accountProportion;

    /**
     * 支付周期
     */
    private String paymentCycleType;

    /**
     * 是否需要复审 0 不需要 1 需要
     */
    private Integer isReExamine;

    /**
     * 最近收货时间
     */
    private String latestReceivingTime;

    /**
     * 是否导入过，0：未导入过，1：导入过
     * @see com.purchase.purchase_server.enums.purchaseOrders.IsImportedEnum
     */
    private String isImported;

    /**
     * 待到货量
     */
    private Integer pendingDeliveryQty;

    private String sbSelfSkuName;

    private String createBy;

    private String createDate;

    /**
     * 采购人员
     */
    private String purchaser;

    /**
     * 跟单人
     */
    private String orderTracker;
}