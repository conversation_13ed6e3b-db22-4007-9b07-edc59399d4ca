package com.inventory_server.model.redundancy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum StockQuantityImportHeadEnum {

    SHIPMENT_CODE("*货件号", "shipmentCode"),
    VIRTUAL_SKU("*虚拟SKU","virtualSku"),
    STOCK_QUANTITY("*库存数","stockQuantity"),
    INVENTORY_TIME("*库存可用时间","inventoryTime"),
    WAREHOUSE("*仓库","warehouse"),
    START_SHIPPING_TIME("*出货时间","startShippingTime"),
    REMARKS("备注","remarks"),
    ;



    private final String code;
    private final String desc;
    public static StockQuantityImportHeadEnum ofCode(String code) {
        return Arrays.stream(StockQuantityImportHeadEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
    public static List<String> getCodeList() {
        return Arrays.stream(StockQuantityImportHeadEnum.values())
                .map(StockQuantityImportHeadEnum::getCode).collect(Collectors.toList());
    }
}
