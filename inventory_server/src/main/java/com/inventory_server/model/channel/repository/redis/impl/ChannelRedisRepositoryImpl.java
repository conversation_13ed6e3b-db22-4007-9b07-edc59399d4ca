package com.inventory_server.model.channel.repository.redis.impl;

import com.inventory_server.model.channel.repository.redis.IChannelRedisRepository;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * @Description 渠道redis层
 * <AUTHOR>
 * @Date 2025/2/18 11:52
 **/
@Service
public class ChannelRedisRepositoryImpl implements IChannelRedisRepository {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Deprecated
    public Set<String> getChannelSet() {
//        Set<String> members = stringRedisTemplate.opsForSet().members(CHANNEL_SET);
        return new HashSet<>();
    }

    @Override
    @Deprecated
    public boolean checkChannelExists(String channelName) {
        return false;
    }
}
