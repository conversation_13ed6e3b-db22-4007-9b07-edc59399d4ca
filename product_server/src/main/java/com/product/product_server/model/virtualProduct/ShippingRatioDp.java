package com.product.product_server.model.virtualProduct;

import cn.hutool.core.util.StrUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.math.RoundingMode.HALF_UP;


/**
 * @Description 发货比例
 * <AUTHOR>
 * @Date 2023/12/7 10:56
 **/
public record ShippingRatioDp(HashMap<Integer, String> keyReplacementMap,
                              Map<Integer, String> senboWarehouseKeyMap) {
    public ShippingRatioDp(HashMap<Integer, String> keyReplacementMap, Map<Integer, String> senboWarehouseKeyMap) {
        this.keyReplacementMap = keyReplacementMap;
        this.senboWarehouseKeyMap = senboWarehouseKeyMap;
        //发货比例校验
        this.deliveryRatio(keyReplacementMap, senboWarehouseKeyMap);

    }
    //发货比例校验

    //校验各仓库的value是否为百分比，如果是百分比则校验value总和是否为100%，如果不是百分比则抛出异常，如果是百分比但是总和不为100%则抛出异常
    private void deliveryRatio(HashMap<Integer, String> map, Map<Integer, String> senboWarehouseKeyMap) {

        Set<Integer> integers = map.keySet();
        double sum = integers.stream()
                .mapToDouble(info -> {
                    String percent = map.get(info);
                    if (StrUtil.isBlank(percent)) {
                        throw new IllegalArgumentException(senboWarehouseKeyMap.get(info) + "-发货比例不能为空");
                    }
                    return isPercent(senboWarehouseKeyMap.get(info), percent);
                }).sum();
        if (roundUpToThreeDecimalPlaces(sum) != 1.0) {
            throw new IllegalArgumentException("发货比例总和不为100%");
        }
    }


    /*判断字符串是否为百分比格式的的辅助方法例：90%*/
    private static double isPercent(String key, String str) {
        Pattern pattern = Pattern.compile("^[0-9]+(.[0-9]{1})?%$");
        Matcher matcher = pattern.matcher(str);
        if (matcher.matches()) {
            return  new BigDecimal(str.replace("%", ""))
                    .divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP).doubleValue();
        } else {
            throw new IllegalArgumentException(key + "-请正确填写百分比，最多只能有1位小数。");
        }
    }

    private double roundUpToThreeDecimalPlaces(double num) {
        if (Double.isNaN(num)) {
            return 0;
        }

        return BigDecimal.valueOf(num).setScale(3, HALF_UP).doubleValue();
    }
}
