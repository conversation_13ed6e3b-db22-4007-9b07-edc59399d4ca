package com.sales_server.infrastructures.aspect;

import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.utils.constant.RedisKeyConstant;
import com.crafts_mirror.utils.utils.JwtUtil;
import com.sales_server.infrastructures.exception.RequestTooFrequentlyException;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.crafts_mirror.utils.constant.Constants.AUTHORIZATION;

/**
 * @Description 防重处理切面类
 * <AUTHOR>
 * @Date 2024/1/20 17:30
 **/
@Component
@Aspect
@Slf4j
public class PreventReSubmitAspect {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final int INVALID_NUMBER = 3;

    @Pointcut("@annotation(com.sales_server.infrastructures.aop.PreventReSubmit)")
    public void preventReSubmitCut() {
    }

    private final byte[] lock = new byte[0];

    @Around("preventReSubmitCut()")
    public Object preventReSubmitAspect(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature)joinPoint.getSignature();
        Method method = signature.getMethod();
        // 获取方法名
        String methodName = method.getName();
        // 获取方法
        Object[] args = joinPoint.getArgs();
        // 获取用户名
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String authorization = request.getHeader(AUTHORIZATION);
        String username = (String) JwtUtil.getClaimsByToken(authorization).get("username");

        // 进行防重校验
        String submitKey = methodName + Arrays.toString(args) + username;
        int submitKeyHashCode = submitKey.hashCode();
        log.info("防重校验———\n" + submitKeyHashCode);
        synchronized (lock) {
            String flag = stringRedisTemplate.opsForValue().get(RedisKeyConstant.PREVENT_RESUBMIT_REQUEST_PREFIX + submitKeyHashCode);
            if (StrUtil.isNotBlank(flag)) {
                log.error(String.format("用户%s提交次数太频繁，已阻止部分请求", username));
                stringRedisTemplate.expire(RedisKeyConstant.PREVENT_RESUBMIT_REQUEST_PREFIX + submitKeyHashCode, INVALID_NUMBER, TimeUnit.SECONDS);
                throw new RequestTooFrequentlyException("提交次数太频繁，请稍后再试");
            }
        }
        stringRedisTemplate.opsForValue().set(RedisKeyConstant.PREVENT_RESUBMIT_REQUEST_PREFIX + submitKeyHashCode, String.valueOf(submitKeyHashCode), INVALID_NUMBER, TimeUnit.SECONDS);

        // 执行目标方法
        return joinPoint.proceed();
    }
}
