package com.purchase.purchase_server.utils.easyExcelUtil.manager;

import lombok.Data;
import lombok.Getter;

import java.util.List;

/**
 * @Description 导入抽象manager类
 * <AUTHOR>
 * @Date 2024/3/20 13:44
 **/
@Getter
@Data
public abstract class AbstractManager {
    protected Integer successfulTimes;
    protected Integer failedTimes;
    protected String recordId;
    //备货规则读取状态
    protected Boolean stockingRulesStatus;
    //目标日销量读取状态
    protected Boolean targetSalesStatus;
    //库存读取状态
    protected Boolean amStockQuantityStatus;
    protected Boolean lampStockQuantityStatus;

    protected Boolean stockQuantityStatus;
    //发货计划读取状态
    protected Boolean deliveryStatus;
    protected List<String> errorList;

    public synchronized void incrementSuccessfulTimes() {
        successfulTimes++;
    }

    public synchronized void incrementFailedTimes() {
        failedTimes++;
    }

    public synchronized void addToErrorList(String error) {
        errorList.add(error);
    }
    public synchronized void setStockingRulesStatus(Boolean status) {
        stockingRulesStatus = status;
    }
    public synchronized void setTargetSalesStatus(Boolean status) {
        targetSalesStatus = status;
    }
    public synchronized void setAmStockQuantityStatus(Boolean status) {
        amStockQuantityStatus = status;
    }
    public synchronized void setLampStockQuantityStatus(Boolean status) {
        lampStockQuantityStatus = status;
    }
    public synchronized void setStockQuantityStatus(Boolean status) {
        stockQuantityStatus = status;
    }
    public synchronized void setDeliveryStatus(Boolean status) {
        deliveryStatus = status;
    }
}
