package com.purchase.purchase_server.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderDeliveryScheduleDO;
import com.purchase.purchase_server.entity.form.PurchaseOrdersScheduleForm;
import com.purchase.purchase_server.mapper.YicangPurchaseOrderDeliveryScheduleMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/28 15:00
 **/
@Service
public class YicangPurchaseOrderDeliveryScheduleRepositoryImpl extends ServiceImpl<YicangPurchaseOrderDeliveryScheduleMapper, YicangPurchaseOrderDeliveryScheduleDO> {


    public List<YicangPurchaseOrderDeliveryScheduleDO> select(PurchaseOrdersScheduleForm form){
        return baseMapper.selectList(new MPJLambdaWrapper<YicangPurchaseOrderDeliveryScheduleDO>()
                .eq(StrUtil.isNotBlank(form.getSbPoVirtualId()), YicangPurchaseOrderDeliveryScheduleDO::getSbPoVirtualId, form.getSbPoVirtualId())
                .eq(StrUtil.isNotBlank(form.getDeliveryDate()), YicangPurchaseOrderDeliveryScheduleDO::getExpectedDeliveryDate, form.getDeliveryDate())
                .orderByAsc(YicangPurchaseOrderDeliveryScheduleDO::getExpectedDeliveryDate)
        );
    }

    /**
     * 根据采购单虚拟表Id删除
     *
     * @param sbPoVirtualIds 采购单虚拟表id
     */
    public void removeBySbPoVirtualIds(List<String> sbPoVirtualIds) {
        if (CollectionUtil.isNotEmpty(sbPoVirtualIds)) {
            baseMapper.removeBySbPoVirtualIds(sbPoVirtualIds);
        }
    }

}




