package com.purchase.purchase_server.model.purchase;


import cn.hutool.core.util.StrUtil;

import java.util.HashMap;

/**
 * @Description 发货计划导入-目标日销数据校验
 * <AUTHOR>
 * @Date 2023/12/29 10:56
 **/
public record TargetSalesInfoDp(HashMap<Integer,String> keyReplacementMap) {
    public TargetSalesInfoDp(HashMap<Integer,String> keyReplacementMap) {
        this.keyReplacementMap = keyReplacementMap;
        new ImportVirtualSkuInfoDp(keyReplacementMap.get(0));
        //发货比例校验
        this.checkNum(keyReplacementMap);

    }
    private void checkNum(HashMap<Integer,String> map){
        map.forEach((key, value) -> {
            if (key != 0 && !isValidNumber(key,value)){
                throw new IllegalArgumentException("第"+(key+1)+"列请正确填写数字");
            }
        });
    }
    // 检查值是否为数字且大于0的辅助方法
    private static boolean isValidNumber(Integer key,String value) {
        try {
            //数量可以为空
            if (StrUtil.isNotBlank(value)){
                return Double.parseDouble(value)>= 0;
            }else {
                return false;
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("第"+(key+1)+"请正确填写数字");
        }
    }

}
