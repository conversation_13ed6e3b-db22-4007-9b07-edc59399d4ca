CREATE TABLE `cm_shipping_record`
(
    `id`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `shipping_start_date` datetime                                                     NOT NULL COMMENT '最早发货装柜时间',
    `shipping_end_date`   datetime                                                     NOT NULL COMMENT '最晚发货装柜时间',
    `trial_status`        char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL COMMENT '试算状态（-2：作废；-1：试算失败；0：试算中；1：待保存；2：已保存）',
    `data_source_type`    char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL COMMENT '数据来源类型（0：导入；）',
    `data_source_id`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '数据来源ID',
    `failed_result`       longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '失败原因',
    `task_result`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '任务结果',
    `create_date`         datetime                                                     DEFAULT NULL,
    `update_date`         datetime                                                     DEFAULT NULL,
    `create_by`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `update_by`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `status`              char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

ALTER TABLE `cm_shipping_project` DROP COLUMN `shipping_start_date`;
ALTER TABLE `cm_shipping_project` DROP COLUMN `shipping_end_date`;
ALTER TABLE `cm_shipping_project` DROP COLUMN `trial_status`;
ALTER TABLE `cm_shipping_project` ADD COLUMN `shipping_record_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '发货记录ID' AFTER `status`;


CREATE TABLE `cm_replenishment_record`
(
    `id`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `replenishment_status` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL COMMENT '试算状态（-2：作废；-1：试算失败；0：试算中；1：待保存；2：已保存）',
    `data_source_type`     char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL COMMENT '数据来源类型（0：导入；）',
    `data_source_id`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '数据来源ID',
    `failed_result`        longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '失败原因',
    `task_result`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '任务结果',
    `create_date`          datetime                                                     DEFAULT NULL,
    `update_date`          datetime                                                     DEFAULT NULL,
    `create_by`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `update_by`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `status`               char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


ALTER TABLE `cm_replenishment_project` DROP COLUMN `replenishment_status`;
ALTER TABLE `cm_replenishment_project` DROP COLUMN `advice_purchase_num`;
ALTER TABLE `cm_replenishment_project` DROP COLUMN `accepted_purchase_num`;
ALTER TABLE `cm_replenishment_project` ADD COLUMN `replenishment_record_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '补货记录ID' AFTER `status`;

ALTER TABLE `cm_replenishment_virtual_sku_purchase` DROP COLUMN `advice_purchase_num`;
ALTER TABLE `cm_replenishment_virtual_sku_purchase` DROP COLUMN `accepted_purchase_num`;

ALTER TABLE `cm_file_mission_center` ADD COLUMN `file_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '源文件地址' AFTER `file_name`;

# 统一数据库编码
ALTER TABLE cm_sys_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE cm_sys_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE cm_sys_role_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE cm_sys_user CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE cm_sys_user_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE cm_user CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE t_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE t_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE t_role_permission CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE t_user_role CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

ALTER TABLE js_sys_dict_data CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE js_sys_dict_type CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

