package com.inventory_server.model.warning.urgentPurchase.entity.enums;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.inventory_server.model.redundancy.entity.dos.RedundantInventoryDO;
import com.inventory_server.model.warning.urgentPurchase.entity.dos.WarningUrgentPurchaseDO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum UrgentPurchaseDirectionAndSortEnum {

    URGENT_PURCHASE_DAYS_BEFORE_SOLD_OUT("urgentPurchaseDaysBeforeSoldOut", "加急补货前断货天数", ""),
    URGENT_PURCHASE_DAYS("urgentPurchaseDays","加急补货日", "urgent_purchase_date"),
    URGENT_PURCHASE_FACTORY_FINISHED_DATE("urgentPurchaseFactoryFinishedDate","加急补货交期", "urgent_factory_finished_date"),
    URGENT_PURCHASE_NUM("urgentPurchaseNum","加急补货数量", "urgent_purchase_num"),
    DAYS_BEFORE_SOLD_OUT("soldOut", "售罄前断货天数", "days_before_sold_out"),
    SOLD_OUT_DATE("soldOutDate","海外仓理论售罄时间", "foreign_theoretical_sold_out_date"),
    FULL_LINK_SOLD_OUT_DATE("fullLinkSoldOutDate","全链路海外仓理论售罄时间", "full_link_theoretical_sold_out_date"),
    FULL_LINK_DAYS_BEFORE_SOLD_OUT("fullLinkSoldOut", "全链售罄前断货天数", "full_link_days_before_sold_out"),
    FULL_LINK_SALABLE_DAYS("fullLinkSalableDays", "全链路海外仓可售天数", "full_link_salable_days"),
    SALABLE_DAYS("salableDays", "海外仓可售天数", "salable_days"),

    ASC("asc","升序", null),
    DESC("desc","降序", null);

    private final String code;
    private final String desc;
    private final String tableField;
    public static UrgentPurchaseDirectionAndSortEnum ofCode(String code) {
        return Arrays.stream(UrgentPurchaseDirectionAndSortEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
    public static List<String> getCodeList() {
        return Arrays.stream(UrgentPurchaseDirectionAndSortEnum.values())
                .map(UrgentPurchaseDirectionAndSortEnum::getCode).collect(Collectors.toList());
    }

    public static final Map<String, SFunction<RedundantInventoryDO, ?>> URGENT_PURCHASE_SORT_MAP1;
    public static final Map<String, SFunction<WarningUrgentPurchaseDO, ?>> URGENT_PURCHASE_SORT_MAP2;
    static {
        URGENT_PURCHASE_SORT_MAP1 = Map.of(
                DAYS_BEFORE_SOLD_OUT.getCode(), RedundantInventoryDO::getDaysBeforeSoldOut,
                SOLD_OUT_DATE.getCode(), RedundantInventoryDO::getForeignTheoreticalSoldOutDate,
                FULL_LINK_DAYS_BEFORE_SOLD_OUT.getCode(), RedundantInventoryDO::getFullLinkDaysBeforeSoldOut,
                FULL_LINK_SOLD_OUT_DATE.getCode(), RedundantInventoryDO::getFullLinkTheoreticalSoldOutDate,
                FULL_LINK_SALABLE_DAYS.getCode(), RedundantInventoryDO::getFullLinkSalableDays,
                SALABLE_DAYS.getCode(), RedundantInventoryDO::getSalableDays
        );
        URGENT_PURCHASE_SORT_MAP2 = Map.of(
                URGENT_PURCHASE_DAYS.getCode(), WarningUrgentPurchaseDO::getUrgentPurchaseDate,
                URGENT_PURCHASE_FACTORY_FINISHED_DATE.getCode(), WarningUrgentPurchaseDO::getUrgentFactoryFinishedDate,
                URGENT_PURCHASE_NUM.getCode(), WarningUrgentPurchaseDO::getUrgentPurchaseNum
        );
    }
}
