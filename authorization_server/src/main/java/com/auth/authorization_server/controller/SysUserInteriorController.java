package com.auth.authorization_server.controller;

import com.auth.authorization_server.domain.vo.UserVO;
import com.auth.authorization_server.req.UserParams;
import com.auth.authorization_server.service.ISysUserInteriorService;
import com.crafts_mirror.common.security.dataPermission.vo.RoleDataPerVO;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(path = "/interior/user")
@Slf4j
public class SysUserInteriorController {

    @Resource
    private ISysUserInteriorService userService;

    /**
     * 获取用户列表
     */
    @GetMapping("/list")
    public ResultDTO<List<UserVO>> list() {
        return ResultDTO.success(userService.selectUserList());
    }

    @PostMapping("/info")
    public ResultDTO<List<UserVO>> getInfo(@RequestBody UserParams params) {
        return ResultDTO.success(userService.selectUserInfo(params));
    }

    @GetMapping("/dataPermissionUser/{moduleId}")
    public ResultDTO<RoleDataPerVO> getDataPermissionUser(@PathVariable(value = "moduleId") String moduleId){
        return ResultDTO.success(userService.getDataPermissionUser(moduleId));
    }
}