# LCL 拼柜装柜接口文档

## 接口概述

**接口路径**: `/insert/lclContainerLoading`  
**请求方法**: POST  
**接口功能**: 执行拼柜装柜算法，将货物按照最优策略分配到集装箱中  
**权限要求**: `purchase:lclConsolidation:list`

## 请求参数

### LclConsolidationForm

```json
{
    "lclRecordId": "装柜记录ID",
    "shippingRecordId": "发货计划记录ID", 
    "shippingStartDate": "发货开始时间 (yyyy-MM-dd)",
    "shippingEndDate": "发货结束时间 (yyyy-MM-dd)",
    "minVolume": "最小体积阈值(立方米)",
    "isLimit": true,
    "isConsolidation": "是否整理标识",
    "shippingDate": "当前采购日时间"
}
```

## 响应结果

```json
{
    "code": 200,
    "message": "success",
    "data": null
}
```

## 核心算法逻辑

### 1. 数据预处理

#### 1.1 数据获取与验证
- 从数据库获取待装柜的货物清单 (`LclContainerLoadingBO`)
- 验证供应商地址代号完整性
- 按发货类型分离加急和普通货物

#### 1.2 数据分组策略
```java
// 按发货类型分组
Map<Boolean, List<LclContainerLoadingBO>> collect = lclContainerLoadingBOS.stream()
    .collect(Collectors.partitioningBy(i -> {
        boolean equals = DeliveryTypeEnum.URGENT.getCode().equals(i.getDeliveryType());
        return StrUtil.isNotBlank(i.getTrialId()) && !equals;
    }));
```

### 2. 体积和重量阈值设置

#### 2.1 体积阈值计算规则
```java
private double setVolumeThreshold(String addressCode, String factoryCode, String warehouse, Map<String, String> warehouseMap) {
    if (warehouseMap.get(warehouse).contains("FBA")) {
        return 68; // FBA仓库固定68立方
    }
    
    if (addressCode.equals("YN")) {
        return 70; // 云南地区70立方
    } else {
        if (factoryCode.equals("GDSS001") || factoryCode.equals("GDWS001")) {
            return 72; // 特定供应商72立方
        } else {
            return 71; // 其他供应商71立方
        }
    }
}
```

#### 2.2 重量阈值
- 根据目标仓库类型动态设置
- 0表示不考虑重量限制

### 3. 装柜算法核心流程

#### 3.1 按地址代码分组装柜
```
数据流程：
原始货物数据 → 按地址代码分组 → 按供应商分组 → 按SPU分组 → 装柜优化
```

#### 3.2 单供应商装柜策略

**第一阶段：按渠道装柜**
- 计算每个渠道的总体积和重量
- 体积超过阈值的渠道单独装柜
- 小体积渠道合并装柜

**第二阶段：大货物拆分装柜**
```java
// 大货物拆分逻辑
if (lclPavilionSummary.getTotalVolume() > volumeThresholds) {
    double ratio = weightThreshold == 0 ? volumeThresholds / totalVolume :
                   Math.min(volumeThresholds / totalVolume, totalWeight / weightThreshold);
    
    // 按比例分配货物到新柜子
    int lclMinNum = Math.min(boxLoadingCount * item.getContainerLoad(), item.getLclShippingNum());
}
```

#### 3.3 跨供应商拼柜优化

**优化策略分类：**

1. **无重量限制优化** (`optimizeZeroWeightContainers`)
   - 云南地区：目标65立方，最多2个容器组合
   - 其他地区：
     - 第一轮：目标68立方，少于4个容器组合
     - 第二轮：目标67立方，4-7个容器组合

2. **有重量限制优化** (`optimizeWithWeightLimits`)
   - 云南地区：目标65立方，2个容器组合
   - 其他地区：
     - 第一轮：目标71立方，2个容器组合
     - 第二轮：目标69立方，3个容器组合  
     - 第三轮：目标67立方，3个容器组合

### 4. 组合优化算法

#### 4.1 贪心算法寻找最优组合
```java
public static List<List<Integer>> findAllCombinationsWithIndices(
    double[] originalArray, double targetSum, double[] weights, double weightThreshold) {
    
    // 寻找满足条件的所有组合：
    // 1. 体积和在 [targetSum-1, targetSum] 范围内
    // 2. 重量不超过 weightThreshold（如果设置）
    // 3. 元素个数最少
}
```

#### 4.2 箱子装载优化算法
```java
public static Map<String, Integer> optimizeBoxLoading(
    Map<String, Double> boxVolumes,      // 每种货物的单箱体积
    Map<String, Integer> initialBoxCounts, // 每种货物的可用物品总数
    Map<String, Double> boxItemWeights,   // 每种货物的单箱重量
    Map<String, Integer> itemsPerBoxMap,  // 每箱可放入的物品数量
    double remainingSpace,                // 剩余空间体积
    double remainingWeight) {             // 剩余重量限制
    
    // 递归寻找最佳装载组合，最大化空间利用率
}
```

### 5. 体积和重量计算

#### 5.1 单个货物体积计算
```java
private double calculateItemVolume(LclContainerLoadingBO item) {
    // 计算单箱体积 (长×宽×高，单位转换为米)
    double length = item.getCaseLength() / 100;
    double width = item.getCaseWidth() / 100; 
    double height = item.getCaseHeight() / 100;
    double singleBoxVolume = length * width * height;
    
    // 计算所需箱数（向上取整）
    int boxCount = (int) Math.ceil((double) item.getLclShippingNum() / item.getContainerLoad());
    
    return singleBoxVolume * boxCount;
}
```

#### 5.2 单个货物重量计算
```java
private double calculateItemWeight(LclContainerLoadingBO item) {
    int boxCount = (int) Math.ceil((double) item.getLclShippingNum() / item.getContainerLoad());
    return item.getSingleCaseGrossWeight() * boxCount / 1000; // 转换为吨
}
```

### 6. 数据合并与整理

#### 6.1 相同试算ID合并
```java
private List<LclContainerLoadingBO> mergeByTrialId(List<LclContainerLoadingBO> items) {
    // 将相同trialId的货物合并，lclShippingNum相加
    Map<String, LclContainerLoadingBO> mergedMap = new LinkedHashMap<>();
    // ... 合并逻辑
}
```

### 7. 容器达标判断

#### 7.1 达标容器分离
```java
private Map<Boolean, List<LclContainerBO>> splitContainersByThreshold(
    List<LclContainerBO> containers, String addressCode, double weightThreshold) {
    
    return containers.stream().collect(Collectors.partitioningBy(container -> {
        if (weightThreshold != 0) {
            return container.getUsedVolume() >= 67;
        }
        if (addressCode.equals("YN")) {
            return container.getUsedVolume() >= 69;
        } else {
            return container.getUsedVolume() >= 70;
        }
    }));
}
```

## 算法特点

### 优势
1. **多层次优化**：从单供应商到跨供应商的递进式优化
2. **智能拆分**：大货物按比例拆分，提高装载效率
3. **动态阈值**：根据地区、供应商、仓库类型动态调整阈值
4. **贪心算法**：寻找最少容器数量的最优组合
5. **重量约束**：同时考虑体积和重量限制

### 适用场景
- 多供应商货物拼柜
- 不同地区的装柜策略差异化
- FBA和非FBA仓库的区别处理
- 加急货物的特殊处理

## 注意事项

1. **并发控制**：使用Redis锁防止重复提交
2. **事务管理**：装柜过程中异常会回滚状态
3. **异步处理**：装柜算法异步执行，避免长时间阻塞
4. **数据验证**：装柜前验证供应商地址代号完整性

## 错误处理

- 装柜处理中：返回"装柜处理中，请稍后再试"
- 地址代号为空：抛出业务异常，包含供应商代码
- 算法异常：回滚装柜状态为"待装柜"
