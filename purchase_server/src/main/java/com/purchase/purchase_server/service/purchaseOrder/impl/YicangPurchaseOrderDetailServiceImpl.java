package com.purchase.purchase_server.service.purchaseOrder.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.assembler.YiCangPurchaseOrdersAssembler;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderDetailDO;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderVirtualDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrdersSummaryDto;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.YicangPurchaseOrderDetailDTO;
import com.purchase.purchase_server.entity.form.PurchaseOrdersForm;
import com.purchase.purchase_server.entity.form.VirtualProductSearchForm;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.VirtualProductListVo;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrderVirtualVO;
import com.purchase.purchase_server.repository.dataRepository.YicangPurchaseOrderDetailRepositoryImpl;
import com.purchase.purchase_server.service.purchaseOrder.IYicangPurchaseOrderDetailService;
import com.purchase.purchase_server.service.purchaseOrder.IYicangPurchaseOrderVirtualService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SystemConstant.PRODUCTS_VIRTUAL_INFO_URL;

/**
 * <AUTHOR>
 * @description 针对表【cm_yicang_purchase_order_detail(易仓采购单明细)】的数据库操作Service实现
 * @createDate 2024-11-01 14:30:43
 */
@Service
@Slf4j
public class YicangPurchaseOrderDetailServiceImpl implements IYicangPurchaseOrderDetailService {

    @Resource
    private YicangPurchaseOrderDetailRepositoryImpl purchaseOrderDetailRepository;

    @Resource
    private IYicangPurchaseOrderVirtualService purchaseOrderVirtualService;

    @Resource
    private YiCangPurchaseOrdersAssembler purchaseOrdersAssembler;

    @Resource
    protected RestTemplate restTemplate;

    @Override
    public List<YicangPurchaseOrderDetailDO> select(YicangPurchaseOrderDetailDTO dto) {
        return purchaseOrderDetailRepository.select(dto);
    }

    @Override
    public void removeBySbPoId(String sbPoId) {
        purchaseOrderDetailRepository.removeBySbPoId(sbPoId);
    }

    @Override
    public void saveBatch(List<YicangPurchaseOrderDetailDO> detailDOS) {
        purchaseOrderDetailRepository.saveBatch(detailDOS);
    }

    @Override
    public void updateOrderDetails(OrdersSummaryDto order, String sbPoId, List<String> failResult) {
        // 获取现有明细
        YicangPurchaseOrderDetailDTO dto = new YicangPurchaseOrderDetailDTO();
        dto.setSbPoId(sbPoId);
        var existingDetails = purchaseOrderDetailRepository.select(dto);

        // 删除相关数据
        var detailIds = existingDetails.stream()
                .map(YicangPurchaseOrderDetailDO::getId)
                .toList();
        purchaseOrderVirtualService.removeBySbPoDetailIds(detailIds);
        purchaseOrderDetailRepository.removeBySbPoId(sbPoId);

        // 保存新的明细和虚拟SKU信息
        saveOrderDetails(order, sbPoId, order.getPoCode(), failResult);
    }

    @Override
    public void saveOrderDetails(OrdersSummaryDto order, String sbPoId, String poCode, List<String> failResult) {
        var detailDTOList = order.getDetail();
        var orderDetails = purchaseOrdersAssembler.detailListDTOtoDO(detailDTOList);
        orderDetails.forEach(detail -> detail.setSbPoId(sbPoId));

        var virtualProducts = orderDetails.stream()
                .map(detail -> processDetailVirtualProducts(detail, poCode, failResult))
                .flatMap(List::stream)
                .toList();

        if (!virtualProducts.isEmpty()) {
            purchaseOrderVirtualService.saveBatch(virtualProducts);
        }
    }

    @Override
    public List<YicangPurchaseOrderVirtualVO> getOrderDetail(PurchaseOrdersForm form) {
        return purchaseOrderDetailRepository.getOrderDetail(form);
    }

    private List<YicangPurchaseOrderVirtualDO> processDetailVirtualProducts(YicangPurchaseOrderDetailDO detail,
                                                                            String poCode, List<String> failResult) {
        var restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());

        var searchForm = VirtualProductSearchForm.builder()
                .selfSkuList(Collections.singletonList(detail.getProductSku()))
                .build();

        var virtualProductListVo = restTemplateUtil.post(searchForm, ResultDTO.class, PRODUCTS_VIRTUAL_INFO_URL);
        var list = JSON.to(List.class, virtualProductListVo.getData());

        if (CollectionUtil.isEmpty(list)) {
            failResult.add("采购单：" + poCode + ",自定义产品：" + detail.getProductSku() + "不存在");
            return Collections.emptyList();
        }
        var virtualInfo = JSON.to(VirtualProductListVo.class, list.get(0));
        detail.setSbSelfSkuId(virtualInfo.getSelfProductId());
        detail.setSbSelfSkuName(virtualInfo.getProductName());
        detail.setImage(virtualInfo.getImage());
        detail.setBuyer(virtualInfo.getBuyer());
        purchaseOrderDetailRepository.save(detail);

        var virtualMap = parseNoteToNumberMap(detail.getNote());
        // 使用Map来合并相同virtualSkuId的数据
        Map<String, YicangPurchaseOrderVirtualDO> mergedProducts = virtualMap.entrySet().stream()
                .map(entry -> purchaseOrderVirtualService.createVirtualProduct(entry.getKey(), entry.getValue(), virtualInfo.getSelfProductId(), detail.getId(), poCode, failResult))
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                        YicangPurchaseOrderVirtualDO::getVirtualSkuId,
                        Collectors.reducing(null, (prod1, prod2) -> {
                            if (prod1 == null) return prod2;
                            // 合并qtyEta
                            prod1.setQtyEta(prod1.getQtyEta() + prod2.getQtyEta());
                            return prod1;
                        })
                ));

        return new ArrayList<>(mergedProducts.values());
    }

    /**
     * 解析note字段，获取sku和数量
     *
     * @param note
     * @return
     */
    private Map<String, String> parseNoteToNumberMap(String note) {
        return note != null ?
                Arrays.stream(note.split(";"))
                        .map(String::trim)
                        .filter(s -> s.matches("[^,]+,\\d+"))
                        .map(s -> s.split(","))
                        .collect(Collectors.toMap(
                                arr -> arr[0],
                                arr -> arr[1],
                                (existing, replacement) -> existing,
                                HashMap::new
                        )) :
                new HashMap<>();
    }
}




