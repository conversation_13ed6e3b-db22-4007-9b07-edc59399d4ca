package com.purchase.purchase_server.entity.form;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 更新发货计划表单
 * <AUTHOR>
 * @Date 2025/5/28 11:07
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LclUpdateShippingNumForm implements Serializable {

    /**
     * 详情id
     */
    private String detailId;

    /**
     * 发货数量
     */
    private Integer lclShippingNum;

}
