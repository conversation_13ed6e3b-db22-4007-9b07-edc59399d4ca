package com.inventory_server.model.redundancy.service;

import com.inventory_server.model.redundancy.entity.dos.InventorySoldOutDaysDO;
import com.inventory_server.model.redundancy.entity.dto.SoldOutDateBeforeTheoryDto;

import java.util.List;
import java.util.Map;

public interface ISoldOutDaysService {

    void insertSoldOutDays(List<SoldOutDateBeforeTheoryDto> soldOutDetailMap,
                           List<SoldOutDateBeforeTheoryDto> fullLinkSoldOutDateDtoList, String inventoryInfoId);

    void batchSaveSoldOutDays(List<InventorySoldOutDaysDO> list);

    List<InventorySoldOutDaysDO> getBeforeSoldOutList(List<String> inventoryInfoIds, String type);

    void deleteByInventoryInfoId(String inventoryInfoId);

    double sumLackNumByInventoryId(String inventoryInfoId, String type);

    Map<String, Double> sumLackNumByInventoryIdList(List<String> inventoryInfoIdList, String type);

}
