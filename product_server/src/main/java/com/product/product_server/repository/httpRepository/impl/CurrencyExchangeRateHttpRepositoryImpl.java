package com.product.product_server.repository.httpRepository.impl;

import com.crafts_mirror.common.security.annotation.Retryable;
import com.crafts_mirror.utils.common.entity.HttpRequestDetail;
import com.crafts_mirror.utils.utils.HttpClientPool;
import com.product.product_server.model.currency.dos.AllRatesResponseEntity;
import com.product.product_server.model.currency.dos.OfficialMERResponse;
import com.product.product_server.model.currency.dto.AllRatesResponseDto;
import com.product.product_server.model.currency.dto.ExchangeRateDto;
import com.product.product_server.model.currency.dto.OfficialMERDto;
import com.product.product_server.repository.httpRepository.ICurrencyExchangeRateHttpRepository;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_H_M_DATE_FORMAT_HYPHEN;
import static com.product.product_server.consts.NetworkUrl.EXCHANGE_RATE_FROM_USD_TO_OTHERS;
import static com.product.product_server.consts.NetworkUrl.OFFICIAL_MIDDLE_EXCHANGE_RATE_PREFIX;

/**
 * <AUTHOR>
 * @Date 2025/4/9 22:04
 **/
@Service
public class CurrencyExchangeRateHttpRepositoryImpl implements ICurrencyExchangeRateHttpRepository {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(YYYY_MM_DD_H_M_DATE_FORMAT_HYPHEN);

    @Override
    public AllRatesResponseDto getExchangeRateFromNetworkFromUSD(String toCurrency) {
        HttpClientPool<AllRatesResponseEntity> httpClientPool = new HttpClientPool<>();

        URI uri = URI.create(EXCHANGE_RATE_FROM_USD_TO_OTHERS);
        HttpRequestDetail<AllRatesResponseEntity> detail = new HttpRequestDetail<>(HttpRequestDetail.HttpMethod.GET, uri, null, AllRatesResponseEntity.class);
        AllRatesResponseEntity allRatesResponse = httpClientPool.sendRequest(detail);
        if (allRatesResponse == null || allRatesResponse.getCode() == null || allRatesResponse.getCode() != 200) {
            String error = allRatesResponse != null ? allRatesResponse.getMsg() : "未知";
            throw new IllegalArgumentException("获取实时汇率异常，异常原因：" + error);
        }

        return allRatesResponse.getData();
    }

    @Override
    @Retryable
    public ExchangeRateDto getOfficialMiddleExchangeRate(String fromCurrency) {
        HttpClientPool<OfficialMERResponse> httpClientPool = new HttpClientPool<>();
        URI uri = URI.create(OFFICIAL_MIDDLE_EXCHANGE_RATE_PREFIX + new Date().getTime());
        HttpRequestDetail<OfficialMERResponse> detail = new HttpRequestDetail<>(HttpRequestDetail.HttpMethod.GET, uri, null, OfficialMERResponse.class);
        OfficialMERResponse response = httpClientPool.sendRequest(detail);
        if (response == null || response.getData() == null) {
            throw new IllegalArgumentException("官方渠道获取中间价汇率异常");
        }

        Object lastDate = response.getData().get("lastDate");
        OfficialMERDto officialMERDto = response.getRecords().stream()
                .filter(f -> f.getForeignCName().equals(fromCurrency))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("缺少美元兑人民币汇率"));

        return ExchangeRateDto.builder()
                .isMiddleExchange(1)
                .fromCurrency(fromCurrency)
                .toCurrency("CNY")
                .exchangeUpdateDate(LocalDateTime.parse(lastDate.toString(), DATE_TIME_FORMATTER))
                .rate(officialMERDto.getPrice())
                .build();
    }

}
