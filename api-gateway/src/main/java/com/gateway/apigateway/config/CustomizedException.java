package com.gateway.apigateway.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.http.HttpStatus;

/**
 * @author: 雪竹
 * @description: 自定义异常处理类
 * @dateTime: 2023/11/24 16:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class CustomizedException extends Exception{
    private HttpStatus httpStatus;

    private String status;

    private String message;
}
