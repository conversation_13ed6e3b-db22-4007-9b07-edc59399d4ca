package com.purchase.purchase_server.entity.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description 供应商表
 * <AUTHOR>
 * @Date 2025/5/6 17:16
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryInfoDTO implements Serializable {
    /**
     * 供应商代码
     */
    private String factoryCode;
    /**
     * 供应商名称
     */
    private String factoryName;
    /**
     * 简称
     */
    private String shortName;
    /**
     * 跟单人
     */
    private String orderTracker;
    /**
     * 采购员
     */
    private String purchaser;
    /**
     * 币种
     */
    private String currency;
    /**
     * 备注
     */
    private String remark;
    /**
     * 禁用状态
     */
    private String disableStatus;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 地址代号
     */
    private String addressCode;

    private String id;

    private String status;

    private String createBy;

    private String updateBy;

    private Date createDate;

    private Date updateDate;
}
