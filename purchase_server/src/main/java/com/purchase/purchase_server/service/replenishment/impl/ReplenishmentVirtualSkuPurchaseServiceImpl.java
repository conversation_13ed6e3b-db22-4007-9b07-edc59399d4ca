package com.purchase.purchase_server.service.replenishment.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.DateUtils;
import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.bo.WatchBoardBO;
import com.purchase.purchase_server.entity.dataObject.*;
import com.purchase.purchase_server.entity.dto.ForeignInventoryDto;
import com.purchase.purchase_server.entity.dto.RepProjectBaseParamDto;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingDetailPlainDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentSaleDestinationDto;
import com.purchase.purchase_server.entity.form.InteriorInfoQuery;
import com.purchase.purchase_server.entity.form.ReplenishmentDetailForm;
import com.purchase.purchase_server.entity.vo.OperatorSearchVo;
import com.purchase.purchase_server.entity.vo.ReplenishmentInfoDetailVo;
import com.purchase.purchase_server.enums.ReplenishmentStatusEnum;
import com.purchase.purchase_server.enums.ShippingSourceTypeEnum;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.repository.dataRepository.*;
import com.purchase.purchase_server.repository.interiorRepository.WarehouseRepository;
import com.purchase.purchase_server.service.ICommonConsumptionService;
import com.purchase.purchase_server.service.IFactoryFinishedInventoryService;
import com.purchase.purchase_server.service.ISysUserService;
import com.purchase.purchase_server.service.replenishment.IReplenishmentVirtualSkuPurchaseService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.*;
import static com.purchase.purchase_server.enums.PurchaseNumTypeEnum.ADVICE;
import static com.purchase.purchase_server.enums.PurchaseNumTypeEnum.THEORETICAL;
import static com.purchase.purchase_server.enums.ShippingSourceTypeEnum.REPLENISH;

/**
 * <AUTHOR>
 * @description 针对表【cm_replenishment_virtual_sku_purchase】的数据库操作Service实现
 * @createDate 2024-01-17 10:45:51
 */
@Service
@Slf4j
public class ReplenishmentVirtualSkuPurchaseServiceImpl implements IReplenishmentVirtualSkuPurchaseService {
    @Resource
    private ReplenishmentVirtualSkuPurchaseRepositoryImpl repVirtualSkuPurchaseRepository;

    @Resource
    private ReplenishmentRulesRepositoryImpl repRulesRepositoryImpl;

    @Resource
    private ReplenishmentProjectRepositoryImpl replenishmentProjectRepository;

    @Resource
    private ReplenishmentForeignInventoryRepositoryImpl replenishmentForeignInventoryRepository;

    @Resource
    private ProductSnapshotRepositoryImpl productSnapshotRepositoryImpl;

    @Resource
    private IFactoryFinishedInventoryService factoryFinishedInventoryService;

    @Resource
    private ReplenishmentRecordRepositoryImpl replenishmentRecordRepositoryImpl;

    @Resource
    private WarehouseRepository warehouseRepository;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private ICommonConsumptionService commonConsumptionService;

    @Resource
    private ReplenishmentRecordRepositoryImpl replenishmentRecordRepository;

    @Override
    public ReplenishmentInfoDetailVo getReplenishmentInfoDetail(String repVirtualSkuId, String purchaseType) {
        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

        ReplenishmentVirtualSkuPurchaseDO repVirtualSkuDO = repVirtualSkuPurchaseRepository.getById(repVirtualSkuId);
        // 获取发货计划详情基础数据
        ReplenishmentRulesDO replenishmentRules = repRulesRepositoryImpl.getById(repVirtualSkuDO.getRulesId());
        var headShippingDaysMap = JSON.parseObject(replenishmentRules.getHeadShippingDays(), new TypeReference<TreeMap<String, Integer>>() {
        });
        var shippingRatioMap = JSON.parseObject(replenishmentRules.getShippingRatio(), new TypeReference<TreeMap<String, Double>>() {
        });

        // 排序
        Map<String, Integer> sortedHeadShippingDays = new LinkedHashMap<>();
        Map<String, Double> sortedShippingRatioDays = new LinkedHashMap<>();
        // 创建一个映射，用于快速查找仓库在列表中的索引
        Map<String, Integer> warehouseIndexMap = new HashMap<>();
        for (int i = 0; i < senboWarehouseList.size(); i++) {
            warehouseIndexMap.put(String.valueOf(senboWarehouseList.get(i).getSenboWarehouseId()), i);
        }

        // 分组并排序
        Map<String, Double> sortedEntries = shippingRatioMap.entrySet().stream()
                .collect(Collectors.partitioningBy(e -> e.getValue() > 0))
                .entrySet().stream()
                .sorted((e1, e2) -> Boolean.compare(e2.getKey(), e1.getKey())) // true (> 0) 在前
                .flatMap(entry -> entry.getValue().stream()
                        .sorted((e1, e2) -> {
                            Integer index1 = warehouseIndexMap.get(e1.getKey());
                            Integer index2 = warehouseIndexMap.get(e2.getKey());
                            return Integer.compare(
                                    index1 != null ? index1 : Integer.MAX_VALUE,
                                    index2 != null ? index2 : Integer.MAX_VALUE
                            );
                        }))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));

        // 填充排序后的映射
        for (Map.Entry<String, Double> entry : sortedEntries.entrySet()) {
            String warehouseId = entry.getKey();
            String warehouseName = idNameMap.get(warehouseId);
            sortedShippingRatioDays.put(warehouseName, entry.getValue());

            if (headShippingDaysMap.containsKey(warehouseId)) {
                sortedHeadShippingDays.put(warehouseName, headShippingDaysMap.get(warehouseId));
            }
        }

        ProductSnapshotDO productSnapshotDO = productSnapshotRepositoryImpl.getById(repVirtualSkuDO.getProductSnapshotId());
        SelfProductDO selfProductDO = JSON.parseObject(productSnapshotDO.getSelfData(), SelfProductDO.class);
        RepProjectBaseParamDto baseParamDto = RepProjectBaseParamDto.builder()
                .headShippingDays(sortedHeadShippingDays)
                .shippingRatio(sortedShippingRatioDays)
                .purchaseCircle(replenishmentRules.getPurchaseCircle())
                .shippingCircle(replenishmentRules.getShippingCircle())
                .purchaseDate(selfProductDO.getPurchaseDate())
                .safeDays(replenishmentRules.getSafeDays())
                .changeableSafeDays(replenishmentRules.getChangeableSafeDays())
                .purchaseDays(replenishmentRules.getPurchaseDays())
                .transitDays(replenishmentRules.getTransitDays())
                .build();

        // 海外仓库存
        List<ReplenishmentForeignInventoryDO> foreignInventoryList = replenishmentForeignInventoryRepository.getByReplenishmentVirtualPurchaseId(repVirtualSkuId);

        List<ForeignInventoryDto> enableUsingList = new ArrayList<>();
        List<ForeignInventoryDto> onShippingList = new ArrayList<>();
        Set<String> containsInventorySet = new HashSet<>();
        for (var foreignInventory : foreignInventoryList) {
            DateTime enableUsingDate = DateTime.of(foreignInventory.getEnableUsingDate());
            String startShippingDate = "";
            if (foreignInventory.getStartShippingDate() != null) {
                startShippingDate = DateTime.of(foreignInventory.getStartShippingDate()).toString(YYYY_MM_DD_DATE_FORMAT_HYPHEN);
            }
            ForeignInventoryDto inventoryDto = ForeignInventoryDto.builder()
                    .warehouse(idNameMap.get(foreignInventory.getWarehouseId()))
                    .storeNum(foreignInventory.getStoreNum().doubleValue())
                    .shipmentCode(foreignInventory.getShipmentCode())
                    .virtualSku(foreignInventory.getVirtualSku())
                    .enableUsingDate(enableUsingDate)
                    .enableUsingDateString(enableUsingDate.toString(YYYY_MM_DD_DATE_FORMAT_HYPHEN))
                    .startShippingDate(startShippingDate)
                    .remarks(foreignInventory.getRemarks())
                    .build();
            if (enableUsingDate.compareTo(repVirtualSkuDO.getCreateDate()) <= 0) {
                enableUsingList.add(inventoryDto);
            } else {
                onShippingList.add(inventoryDto);
            }
            containsInventorySet.add(foreignInventory.getWarehouseId());
        }

        // 仓库排序
        Set<String> keysToRemove = shippingRatioMap.entrySet().stream()
                .filter(entry -> entry.getValue() == 0)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
        List<WatchBoardBO> trialWatchBoardInfo = repVirtualSkuPurchaseRepository.getTrialWatchBoardInfoByReplenishmentId(repVirtualSkuId, REPLENISH.getCode());
        Set<String> deliveryWarehouseSet = trialWatchBoardInfo.stream().map(WatchBoardBO::getDestinationWarehouse).collect(Collectors.toSet());
        keysToRemove.removeAll(deliveryWarehouseSet);
        keysToRemove.removeAll(containsInventorySet);

        List<String> sortedWarehouseList = sortedEntries.keySet().stream()
                .filter(aDouble -> !keysToRemove.contains(aDouble))
                .map(idNameMap::get)
                .toList();
        // 目标日销
        String targetSales;
        if (purchaseType.equals(THEORETICAL.getCode())) {
            targetSales = repVirtualSkuDO.getSaleDestination();
        } else if (purchaseType.equals(ADVICE.getCode())) {
            targetSales = repVirtualSkuDO.getDiscountSaleDestination();
        } else {
            targetSales = repVirtualSkuDO.getOperatorSaleDestination();
        }

        // 获取目标日销
        LocalDate startDate = DateUtils.convertToLocalDate(replenishmentRules.getCreateDate());
        LocalDate localDate = startDate.minusMonths(2).withDayOfMonth(1);
        var targetSalesMap = commonConsumptionService.calTargetSalesMap(targetSales, localDate);

        // 工厂计划
        String sourceType;
        if (purchaseType.equals(ADVICE.getCode())) {
            sourceType = ShippingSourceTypeEnum.DISCOUNT_REPLENISH.getCode();
        } else {
            sourceType = REPLENISH.getCode();
        }

        List<ShippingDetailPlainDto> shippingDetailPlainDtoList = factoryFinishedInventoryService.getShippingDetailPlainDtoList(
                repVirtualSkuId, new ArrayList<>(), sourceType
        );

        ReplenishmentProjectDO replenishmentProject = replenishmentProjectRepository.getById(repVirtualSkuDO.getReplenishmentProjectId());
        ReplenishmentRecordDO byId = replenishmentRecordRepositoryImpl.getById(replenishmentProject.getReplenishmentRecordId());
        return ReplenishmentInfoDetailVo.builder()
                .virtualSku(productSnapshotDO.getVirtualSku())
                .productName(selfProductDO.getProductName())
                .baseParamDto(baseParamDto)
                .shippingDetailPlainDtoList(shippingDetailPlainDtoList)
                .destinationEverydaySale(targetSalesMap)
                .onShippingInventoryList(sortForeignInventoryDtoList(onShippingList))
                .enableUseInventoryList(sortForeignInventoryDtoList(enableUsingList))
                .replenishmentStatus(byId.getReplenishmentStatus())
                .projectCreateDate(DateUtil.format(repVirtualSkuDO.getCreateDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN))
                .warehouseSortList(sortedWarehouseList)
                .build();
    }

    private List<ForeignInventoryDto> sortForeignInventoryDtoList(List<ForeignInventoryDto> inventoryDtoList) {
        return inventoryDtoList.stream()
                .sorted(Comparator.comparing(ForeignInventoryDto::getEnableUsingDate)
                        .thenComparing(ForeignInventoryDto::getWarehouse))
                .toList();
    }

    @Override
    public List<OperatorSearchVo> getOperator() {
        return sysUserService.getOperator();
    }

    @Override
    public Set<String> getSnapIds(InteriorInfoQuery info) {
        return repVirtualSkuPurchaseRepository.getSnapIds(info);
    }

    @Override
    public List<ReplenishmentSaleDestinationDto> getSaleDestination(ReplenishmentDetailForm form) {
        ReplenishmentVirtualSkuPurchaseDO repVirtualSkuPurchaseDO = repVirtualSkuPurchaseRepository.getById(form.getReplenishmentId());
        if (ObjectUtil.isEmpty(repVirtualSkuPurchaseDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "补货数据不存在");
        }
        String operatorSaleDestination;
        if (ADVICE.getCode().equals(form.getPurchaseNumType())) {
            operatorSaleDestination = repVirtualSkuPurchaseDO.getDiscountSaleDestination();
        } else {
            operatorSaleDestination = repVirtualSkuPurchaseDO.getOperatorSaleDestination();
        }

        Map<String, BigDecimal> saleDestinationMap = JSONObject.parseObject(operatorSaleDestination, Map.class);
        List<ReplenishmentSaleDestinationDto> result = new ArrayList<>();

        String saleDestinationYm = form.getSaleDestinationYm();
        // 获取指定年月的数据
        Map<String, BigDecimal> filteredMap = saleDestinationMap.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(saleDestinationYm))
                .collect(Collectors.toMap(
                        Map.Entry::getKey, // 将 2024/01/01 转换为 2024-01-01
                        Map.Entry::getValue));

        // 解析年月，计算该月的天数
        YearMonth ym = YearMonth.parse(saleDestinationYm, DateTimeFormatter.ofPattern(YYYY_MM_DATE_FORMAT_SLASH));
        int daysInMonth = ym.lengthOfMonth();

        // 为该月的每一天创建记录
        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate date = ym.atDay(day);
            String dateStr = LocalDateTimeUtil.format(date, YYYY_MM_DD_DATE_FORMAT_SLASH);
            // 如果日期存在于原始数据中，使用原始值，否则设为0
            BigDecimal value = filteredMap.getOrDefault(dateStr, new BigDecimal(0));
            result.add(new ReplenishmentSaleDestinationDto(dateStr, value.doubleValue()));
        }
        // 按日期排序
        result.sort(Comparator.comparing(ReplenishmentSaleDestinationDto::getSaleDestinationDate));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(content = "修改目标日销", operationType = "补货计划操作")
    public void updateSaleDestination(ReplenishmentDetailForm form, LogTrackNumDto logDto) {
        var newSaleDestinationMap = form.getSaleDestinationMap();
        if (CollectionUtil.isNotEmpty(newSaleDestinationMap)) {
            ReplenishmentVirtualSkuPurchaseDO repVirtualSkuPurchaseDO = repVirtualSkuPurchaseRepository.getById(form.getReplenishmentId());
            if (ObjectUtil.isEmpty(repVirtualSkuPurchaseDO)) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "补货数据不存在");
            }

            ReplenishmentRecordDO record = replenishmentRecordRepository.selectOneByVirtualPurchaseId(form.getReplenishmentId());
            if (ObjectUtil.isEmpty(record) || record.getReplenishmentStatus().equals(ReplenishmentStatusEnum.SAVED.getCode()) ||
                    record.getReplenishmentStatus().equals(ReplenishmentStatusEnum.ABROGATE.getCode())) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "该补货计划已完成或已作废，不允许修改目标日销");
            }
            String operatorTargetSalesMap = repVirtualSkuPurchaseDO.getOperatorSaleDestination();
            // 折后日销
            String discountTargetSalesMap = repVirtualSkuPurchaseDO.getDiscountSaleDestination();
            Double subEntityRate = repVirtualSkuPurchaseDO.getSubEntityRate();
            BigDecimal rate = BigDecimal.valueOf(subEntityRate);
            Map<String, BigDecimal> saleDestinationMap = JSONObject.parseObject(operatorTargetSalesMap, new TypeReference<>() {
            });
            Map<String, BigDecimal> discountSaleDestinationMap = JSONObject.parseObject(discountTargetSalesMap, new TypeReference<>() {
            });

            List<String> saleDestinationYmList = new ArrayList<>(newSaleDestinationMap.keySet());
            TreeMap<String, BigDecimal> filteredMap = saleDestinationMap.entrySet().stream()
                    .filter(entry -> saleDestinationYmList.stream()
                            .noneMatch(prefix -> entry.getKey().startsWith(prefix)))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (oldValue, newValue) -> newValue,
                            TreeMap::new
                    ));

            TreeMap<String, BigDecimal> discountFilteredMap = discountSaleDestinationMap.entrySet().stream()
                    .filter(entry -> saleDestinationYmList.stream()
                            .noneMatch(prefix -> entry.getKey().startsWith(prefix)))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (oldValue, newValue) -> newValue,
                            TreeMap::new
                    ));

            List<ReplenishmentSaleDestinationDto> saleDestinationList = newSaleDestinationMap.values().stream()
                    .flatMap(List::stream)
                    .toList();
            for (var saleDestination : saleDestinationList) {
                String saleDestinationDate = DateUtil.format(DateUtil.parse(saleDestination.getSaleDestinationDate()), YYYY_MM_DD_DATE_FORMAT_SLASH);
                Double saleDestinationNum = saleDestination.getSaleDestinationNum();
                BigDecimal num = BigDecimal.valueOf(saleDestinationNum).setScale(3, RoundingMode.HALF_UP);

                if (num.compareTo(new BigDecimal(9999999)) > 0) {
                    throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "日销最多填写7位数");
                }
                filteredMap.put(DateUtil.format(DateUtil.parse(saleDestinationDate), YYYY_MM_DD_DATE_FORMAT_SLASH), BigDecimal.valueOf(saleDestinationNum));
                BigDecimal result = num.multiply(rate).setScale(3, RoundingMode.HALF_UP);
                discountFilteredMap.put(saleDestinationDate, BigDecimal.valueOf(result.doubleValue()));
            }

            String logStr = formatSaleDestinationLog(saleDestinationYmList);
            Map<String, String> logMap = new HashMap<>();
            logMap.put(form.getReplenishmentId(), logStr);
            logDto.setLogMap(logMap);
            logDto.setAuthorization(SecurityUtils.getToken());
            repVirtualSkuPurchaseRepository.updateById(ReplenishmentVirtualSkuPurchaseDO.builder()
                    .id(form.getReplenishmentId())
                    .saleDestination(JSON.toJSONString(filteredMap))
                    .discountSaleDestination(JSON.toJSONString(discountFilteredMap))
                    .operatorSaleDestination(JSON.toJSONString(filteredMap)).build());
        }
    }

    private String formatSaleDestinationLog(List<String> saleDestinationYmList) {
        if (saleDestinationYmList == null || saleDestinationYmList.isEmpty()) {
            return "修改目标日销"; // 或者根据需要返回空字符串或抛出异常
        }

        // 1. 解析与分组 (使用 TreeMap 保证年份自动排序)
        Map<Integer, List<Integer>> yearMonthMap = new TreeMap<>();
        for (String ym : saleDestinationYmList) {
            try {
                String[] parts = ym.split("/");
                if (parts.length == 2) {
                    int year = Integer.parseInt(parts[0]);
                    int month = Integer.parseInt(parts[1]);
                    yearMonthMap.computeIfAbsent(year, k -> new ArrayList<>()).add(month);
                } else {
                    // 处理格式不正确的字符串，可以选择忽略或记录错误
                    System.err.println("Skipping invalid format: " + ym);
                }
            } catch (NumberFormatException e) {
                // 处理无法解析为数字的情况
                System.err.println("Skipping invalid number format: " + ym);
            }
        }

        // 2. 对每个年份的月份进行排序
        yearMonthMap.values().forEach(Collections::sort);

        // 3. 构建字符串
        StringBuilder logMessage = new StringBuilder("修改");
        List<String> yearParts = new ArrayList<>();

        for (Map.Entry<Integer, List<Integer>> entry : yearMonthMap.entrySet()) {
            int year = entry.getKey();
            List<Integer> months = entry.getValue();

            // 将月份列表转换为逗号分隔的字符串
            String monthsStr = months.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining("，")); // 使用中文逗号

            yearParts.add(year + "年" + monthsStr + "月");
        }

        logMessage.append(String.join("，", yearParts)); // 使用中文逗号连接不同年份的部分
        logMessage.append("目标日销");

        return logMessage.toString();
    }
}




