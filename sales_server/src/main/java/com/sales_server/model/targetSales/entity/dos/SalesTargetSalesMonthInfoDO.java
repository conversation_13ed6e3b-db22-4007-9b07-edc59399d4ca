package com.sales_server.model.targetSales.entity.dos;

import com.sales_server.infrastructures.entity.PhysicalBaseEntity;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.experimental.SuperBuilder;


/**
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_sales_target_sales_month_info")
public class SalesTargetSalesMonthInfoDO extends PhysicalBaseEntity {

  private String virtualSkuId;
  private LocalDate targetDate;
  private Double targetSales;

}
