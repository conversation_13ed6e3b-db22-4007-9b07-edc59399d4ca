package com.purchase.purchase_server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.purchase.purchase_server.entity.dataObject.ProductSnapshotDO;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.repository.dataRepository.ProductSnapshotRepositoryImpl;
import com.purchase.purchase_server.service.IProductSnapshotService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 快照表
 * <AUTHOR>
 * @Date 2024/3/5 17:19
 **/
@Service
@Slf4j
public class ProductSnapshotServiceImpl implements IProductSnapshotService {

    @Resource
    private ProductSnapshotRepositoryImpl productSnapshotRepository;

    @Override
    public String selecetAndSaveSnapList(String virtualProductId, String username) {
        List<ProductSnapshotDO> snapshotList = productSnapshotRepository.list(
                Wrappers.<ProductSnapshotDO>lambdaQuery()
                        .eq(ProductSnapshotDO::getVirtualSkuId, virtualProductId)
                        .orderByDesc(ProductSnapshotDO::getCreateDate)
                        .orderByDesc(ProductSnapshotDO::getId));
        if (CollectionUtil.isEmpty(snapshotList)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "获取快照数据错误：虚拟skuId{}", virtualProductId);
        }
        return snapshotList.getFirst().getId();
    }
}
