package com.purchase.purchase_server.entity.dto.Lcl;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;

/**
 * @Description 货件详情vo类
 * <AUTHOR>
 * @Date 2025/5/27 13:59
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LclContainerDetailInfoDto implements Serializable {
    private String detailId;

    private String containerInfoId;

    private String factoryCode;

    private String contractCode;

    private String image;

    private String sku;

    private String skuId;

    private String productName;

    private String destinationSku;

    private String spuId;

    private String spuName;

    private Integer productStatus;

    @JsonFormat(pattern = YYYY_MM_DD_DATE_FORMAT_SLASH, timezone = "GMT+8")
    private Date shipmentStartDate;

    private String deliveryType;

    private Integer commodityInspection;

    private Integer pcsType;

    @JsonFormat(pattern = YYYY_MM_DD_DATE_FORMAT_SLASH, timezone = "GMT+8")
    private Date factoryFinishedDate;

    private Integer containerLoad;

    /**
     * 可安排总量（工厂交货数量）
     */
    private Integer factoryShippingPackageNum;

    private String factoryPlanRemarks;

    private Integer lclShippingNum;

    private Integer factoryRemainNum;

    private String  isPackageFull;

    private Double shippingVolume;

    private Double shippingWeight;

    private Integer unShippingNum;

    private Double unShippingVolume;

    private String shipmentCategory;
}
