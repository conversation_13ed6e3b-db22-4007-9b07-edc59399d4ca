package com.inventory_server.model.inventory.repository.httpRepository;

import com.crafts_mirror.utils.common.entity.HttpRequestDetail;
import com.inventory_server.model.inventory.entity.dto.AsyncInventoryResultDto;
import com.inventory_server.model.warehouse.entity.dos.YCResponseEntity;

public interface FetchYcInventoryInfoRepository {

    Integer fetchYcInventoryInfoCount(HttpRequestDetail<YCResponseEntity> detail, AsyncInventoryResultDto asyncInventoryResultDto);

    void fetchYcNonFBAInventoryInfoAndSave(String startDate, String endDate, AsyncInventoryResultDto asyncInventoryResultDto);
}
