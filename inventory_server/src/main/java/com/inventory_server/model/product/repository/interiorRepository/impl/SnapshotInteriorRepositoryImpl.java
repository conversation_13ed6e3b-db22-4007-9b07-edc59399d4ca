package com.inventory_server.model.product.repository.interiorRepository.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.inventory_server.applications.vo.response.ResultDTO;
import com.inventory_server.model.product.entity.dos.SelfProductDO;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.product.entity.dto.ProductSnapshotDto;
import com.inventory_server.model.product.entity.form.ProductSnapshotForm;
import com.inventory_server.model.product.entity.vo.ProductSnapshotVo;
import com.inventory_server.model.product.repository.interiorRepository.ISnapshotRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SystemConstant.SNAPSHOT_PRODUCTS_INFO_URL;

/**
 * @Description 获取产品快照的微服务调用类
 * <AUTHOR>
 * @Date 2024/7/29 11:35
 **/
@Slf4j
@Service
public class SnapshotInteriorRepositoryImpl implements ISnapshotRepository {

    @Resource
    protected RestTemplate restTemplate;

    @Override
    public ProductSnapshotVo getProductSnapshotListBySnapshotIdList(List<String> snapshotIdList) {
        ProductSnapshotForm snapshotForm = ProductSnapshotForm.builder().snapshotList(snapshotIdList).build();
        ResultDTO postResult = sendRequest(snapshotForm);
        return JSON.to(ProductSnapshotVo.class, postResult.getData());
    }

    @Override
    public ProductSnapshotDto getProductSnapshotBySnapshotId(String snapshotId) {
        ProductSnapshotForm snapshotForm = ProductSnapshotForm.builder().snapshotList(Collections.singletonList(snapshotId)).build();
        ResultDTO postResult = sendRequest(snapshotForm);
        ProductSnapshotVo productSnapshotVo = JSON.to(ProductSnapshotVo.class, postResult.getData());
        return productSnapshotVo.getProductSnapshotList().getFirst();
    }

    private ResultDTO sendRequest(ProductSnapshotForm snapshotForm) {
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        return restTemplateUtils.post(snapshotForm, ResultDTO.class, SNAPSHOT_PRODUCTS_INFO_URL);
    }

    @Override
    public List<SelfProductDO> getSelfProductSnapshotListBySnapshotIdList(List<String> snapshotIdList) {
        ProductSnapshotVo productSnapshotVo = getProductSnapshotListBySnapshotIdList(snapshotIdList);
        return productSnapshotVo.getProductSnapshotList().stream()
                .map(m -> JSON.parseObject(m.getSelfData(), SelfProductDO.class))
                .collect(Collectors.toList());
    }

    @Override
    public SelfProductDO getSelfProductSnapshotBySnapshotId(String snapshotId) {
        ProductSnapshotVo productSnapshotVo = getProductSnapshotListBySnapshotIdList(Collections.singletonList(snapshotId));
        List<ProductSnapshotDto> productSnapshotList = productSnapshotVo.getProductSnapshotList();

        if (CollectionUtil.isEmpty(productSnapshotList)) {
            return null;
        }
        ProductSnapshotDto productSnapshotDto = productSnapshotList.get(0);
        return JSON.parseObject(productSnapshotDto.getSelfData(), SelfProductDO.class);
    }

    @Override
    public List<VirtualProductDO> getVirtualProductSnapshotListBySnapshotIdList(List<String> snapshotIdList) {
        ProductSnapshotVo productSnapshotVo = getProductSnapshotListBySnapshotIdList(snapshotIdList);
        return productSnapshotVo.getProductSnapshotList().stream()
                .map(m -> JSON.parseObject(m.getVirtualData(), VirtualProductDO.class))
                .collect(Collectors.toList());
    }

    @Override
    public ProductSnapshotVo getProductSnapshotListByVirtualSkuId(List<String> virtualSkuId) {
        ProductSnapshotForm snapshotForm = ProductSnapshotForm.builder().virtualSkuIdList(virtualSkuId).build();
        ResultDTO postResult = sendRequest(snapshotForm);
        return JSON.to(ProductSnapshotVo.class, postResult.getData());
    }

    @Override
    public VirtualProductDO getVirtualProductSnapshotBySnapshotId(String snapshotId) {
        if (StrUtil.isBlank(snapshotId)) {
            throw new IllegalArgumentException("该冗余库存详情缺少商品快照信息，请重新试算冗余库存");
        }
        return getVirtualProductSnapshotListBySnapshotIdList(Collections.singletonList(snapshotId)).get(0);
    }
}
