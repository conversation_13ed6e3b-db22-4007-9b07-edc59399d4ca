package com.purchase.purchase_server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum FactoryFinancialSettementEnum {
    REGULAR("0", "常规结算"),
     ACCEPTANCE("1", "承兑汇票");

    private final String code;
    private final String desc;

    public static FactoryFinancialSettementEnum ofCode(String code) {
        return Arrays.stream(FactoryFinancialSettementEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
    public static FactoryFinancialSettementEnum ofDesc(String desc) {
        return Arrays.stream(FactoryFinancialSettementEnum.values())
                .filter(it -> it.getDesc().equals(desc))
                .findFirst()
                .orElse(null);
    }
}
