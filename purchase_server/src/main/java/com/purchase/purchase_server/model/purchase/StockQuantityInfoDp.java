package com.purchase.purchase_server.model.purchase;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.purchase.purchase_server.entity.excelObject.StockQuantityInfoExcel;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Map;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_M_D_DATE_FORMAT_SLASH;

/**
 * @Description 发货计划导入-在途数据校验
 * <AUTHOR>
 * @Date 2023/12/29 10:56
 **/
public record StockQuantityInfoDp(StockQuantityInfoExcel stockQuantityInfoExcel, Map<String, String> nameIdMap) {

    public StockQuantityInfoDp(StockQuantityInfoExcel stockQuantityInfoExcel, Map<String, String> nameIdMap) {
        this.stockQuantityInfoExcel = stockQuantityInfoExcel;
        this.nameIdMap = nameIdMap;
        if (StrUtil.isBlank(stockQuantityInfoExcel.getStartShippingTime())) {
            throw new IllegalArgumentException("出货时间为空");
        }
        if (Optional.ofNullable(stockQuantityInfoExcel.getStoreHouse()).isPresent() &&
                Optional.ofNullable(stockQuantityInfoExcel.getStockQuantity()).isPresent() &&
                Optional.ofNullable(stockQuantityInfoExcel.getVirtualSku()).isPresent() &&
                Optional.ofNullable(stockQuantityInfoExcel.getInventoryTime()).isPresent() &&
                Optional.ofNullable(stockQuantityInfoExcel.getStartShippingTime()).isPresent()
        ){
            new ImportVirtualSkuInfoDp(stockQuantityInfoExcel.getVirtualSku());
            this.checkDate(stockQuantityInfoExcel);
            this.checkNum(stockQuantityInfoExcel);
            //检查仓库是否存在
            if (!nameIdMap.containsKey(stockQuantityInfoExcel.getStoreHouse())){
                throw new IllegalArgumentException("请填写正确仓库");
            }
        }else{
            throw new IllegalArgumentException("请填写内容");
        }

    }
    private void checkNum(StockQuantityInfoExcel stockQuantityInfoExcel){
        if (!(NumberUtil.isInteger(stockQuantityInfoExcel.getStockQuantity()) && Integer.parseInt(stockQuantityInfoExcel.getStockQuantity()) >= 0)){
            throw new IllegalArgumentException("请正确填写数字");
        }
    }
  //判断日期格式是否正确
    private void checkDate(StockQuantityInfoExcel stockQuantityInfoExcel){
        String inventoryTime = stockQuantityInfoExcel.getInventoryTime();
        String startShippingTime = stockQuantityInfoExcel.getStartShippingTime();
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(YYYY_M_D_DATE_FORMAT_SLASH);
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);

        try {
            // 尝试解析日期字符串
            LocalDate inventoryDate = LocalDate.parse(inventoryTime, inputFormatter);
            //判断日期是否大于当前日期，使用java8的日期格式
            if (!inventoryDate.isAfter(LocalDate.now())){
                throw new IllegalArgumentException("请填写大于当前日期");
            }
            // 将日期格式化为所需的格式
            inventoryDate.format(outputFormatter);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("请正确填写日期");
        }

        try {
            LocalDate startShippingDate = LocalDate.parse(startShippingTime, inputFormatter);
            startShippingDate.format(outputFormatter);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("出货时间格式必须是YYYY/MM/DD");
        }
    }
}
