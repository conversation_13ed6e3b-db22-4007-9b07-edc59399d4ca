package com.auth.authorization_server.repository;

import com.auth.authorization_server.mapper.DeptMapper;
import com.auth.authorization_server.model.dataObject.DeptDO;
import com.auth.authorization_server.model.dataObject.RoleDeptDO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class DeptRepositoryImpl extends ServiceImpl<DeptMapper, DeptDO> {

    /**
     * 查询部门管理数据
     *
     * @return 部门信息集合
     */
    public List<DeptDO> selectDeptList() {
        return baseMapper.selectList(Wrappers.<DeptDO>lambdaQuery()
                .orderByAsc(DeptDO::getParentId)
                .orderByDesc(DeptDO::getDeptSort)
                .orderByAsc(DeptDO::getDeptName));
    }

    ;

    /**
     * 校验部门名称是否唯一
     *
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @return 结果
     */
    public DeptDO checkDeptNameUnique(String deptName, String parentId) {
        return baseMapper.selectOne(Wrappers.<DeptDO>lambdaQuery()
                .eq(DeptDO::getDeptName, deptName)
                .eq(DeptDO::getParentId, parentId)
                .orderByAsc(DeptDO::getDeptName)
                .last("limit 1"));
    }

    ;

    /**
     * 根据ID查询所有子部门
     *
     * @param deptId 部门ID
     * @return 部门列表
     */
    public List<DeptDO> selectChildrenDeptById(String deptId) {
        return baseMapper.selectList(Wrappers.<DeptDO>lambdaQuery()
                .apply("find_in_set({0}, ancestors)", deptId));
    }

    ;

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public Long hasChildByDeptId(String deptId) {
        return baseMapper.selectCount(Wrappers.<DeptDO>lambdaQuery()
                .eq(DeptDO::getParentId, deptId)
                .last("limit 1"));
    }

    ;

    ///**
    // * 根据角色ID查询部门树信息
    // *
    // * @param roleId 角色ID
    // * @return 选中部门列表
    // */
    //public List<String> selectDeptListByRoleId(String roleId) {
    //    return baseMapper.selectJoinList(String.class, new MPJLambdaWrapper<DeptDO>()
    //            .select(DeptDO::getId)
    //            .leftJoin(RoleDeptDO.class, RoleDeptDO::getDeptId, DeptDO::getId)
    //            .eq(RoleDeptDO::getRoleId, roleId)
    //            .orderByAsc(DeptDO::getParentId)
    //            .orderByDesc(DeptDO::getDeptSort));
    //}
    //
    //;


}
