package com.auth.authorization_server.domain.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Optional;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -1L;

    private String id;
    /**
     * 用户账号
     */
    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 20, message = "用户账号长度不能超过20个字符")
    private String userName;
    /**
     * 用户昵称
     */
    @Size(max = 10, message = "用户昵称长度不能超过10个字符")
    private String nickName;

    /**
     * 手机号码
     */

    @Size(max = 11, message = "手机号码格式不正确！")
    private String phoneNumber;
    /**
     * 密码
     */
    private String passWord;
    /**
     * 盐
     */
    private String salt;
    /**
     * 帐号状态（0正常 1停用）
     */
    private String userStatus;

    /**
     * 角色对象
     */
    private List<RoleDTO> roles;

    /**
     * 部门对象
     */
    private List<DeptDTO> depts;

    /**
     * 角色ID
     */
    private String roleId;
    /**
     * 角色组
     */
    private String[] roleIds;

    /**
     * 角色组
     */
    private String[] deptIds;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 职位id
     */
    private String postId;

    public String getUserName() {
        return Optional.ofNullable(userName)
                .map(String::strip)
                .orElse(null);
    }

    public String getPhoneNumber() {
        return Optional.ofNullable(phoneNumber)
                .map(String::strip)
                .orElse(null);
    }
}
