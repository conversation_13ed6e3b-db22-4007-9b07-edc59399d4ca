package com.crafts_mirror.common.security.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.METHOD, ElementType.TYPE })
public @interface Retryable {
    int retryTimes() default 3;

    int retryInterval() default 3000;

    TimeUnit retryTimeUnit() default TimeUnit.MILLISECONDS;
}
