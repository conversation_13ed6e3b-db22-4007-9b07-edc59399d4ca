package com.purchase.purchase_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description excel导出发货计划-到仓明细 转换成的实体列
 * <AUTHOR>
 * @Date 2024/1/3 17:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER,verticalAlignment = VerticalAlignmentEnum.CENTER)
//@ContentFontStyle(fontHeightInPoints = 15)
public class DeliveryPurchaseNotExportInfoExcel extends BaseExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = -1439415841202434L;

    @ExcelProperty("合同号")
    private String contractCode;

    @ExcelProperty("虚拟SKU")
    private String destinationSku;

    @ExcelProperty("产品名称")
    private String productName;

    @ExcelProperty("渠道")
    private String channel;

    @ExcelProperty("自定义SKU")
    private String selfProductSku;

    @ExcelProperty("每箱个数")
    private Integer containerLoad;

    @ExcelProperty("外箱尺寸长")
    private Double caseLength;

    @ExcelProperty("外箱尺寸宽")
    private Double caseWidth;

    @ExcelProperty("外箱尺寸高")
    private Double caseHeight;

    @ExcelProperty("省份")
    private String province;

    @ExcelProperty("单箱体积")
    private Double caseVolume;

    @ExcelProperty("单箱毛重")
    private Double singleCaseGrossWeight;

    @ExcelProperty("采购人员")
    private String buyer;

    @ExcelProperty("发货装柜时间")
    private String realShippingStartDate;

    @ExcelProperty("工厂交期")
    private String factoryFinishedDate;

    @ExcelProperty("备注")
    private String remarks;

    @ExcelProperty("FBA仓")
    private Integer fba;

    @ExcelProperty("洛杉矶")
    private Integer westernAmerica;

    @ExcelProperty("萨凡纳")
    private Integer southeasternUnitedStates;

    @ExcelProperty("芝加哥")
    private Integer centralAmerica;

    @ExcelProperty("新泽西")
    private Integer easternUnitedStates;

    @ExcelProperty("休斯顿")
    private Integer southernUnitedStates;

    @ExcelProperty("CG仓")
    private Integer cg;

    @ExcelProperty("FBA仓体积")
    private Double fbaVolume;

    @ExcelProperty("洛杉矶体积")
    private Double westernAmericaVolume;

    @ExcelProperty("萨凡纳体积")
    private Double southeasternUnitedStatesVolume;

    @ExcelProperty("芝加哥体积")
    private Double centralAmericaVolume;

    @ExcelProperty("新泽西体积")
    private Double easternUnitedStatesVolume;

    @ExcelProperty("休斯顿体积")
    private Double southernUnitedStatesVolume;

    @ExcelProperty("CG仓体积")
    private Double cgVolume;

    @ExcelProperty("发货重量")
    private Double factoryGrossWeight;

    @ExcelProperty("预计售罄时间")
    private String expectedSellOutDate;
}
