package com.product.product_server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.product.product_server.entity.dataObject.ProductSnapshotDO;
import com.product.product_server.entity.dataObject.SelfProductDO;
import com.product.product_server.entity.dataObject.VirtualProductDO;
import com.product.product_server.entity.form.InteriorDeliveryInfoQuery;
import com.product.product_server.entity.form.InteriorLclConsolidationInfoQuery;
import com.product.product_server.entity.form.InteriorReplenishmentInfoQuery;
import com.product.product_server.entity.form.InventoryInfoQuery;
import com.product.product_server.entity.response.ResultDTO;
import com.product.product_server.enums.PCSTypeEnum;
import com.product.product_server.exception.BusinessException;
import com.product.product_server.model.products.CalPCSTypeDp;
import com.product.product_server.model.products.CaseWeightInfoDp;
import com.product.product_server.model.products.SpecificationDp;
import com.product.product_server.repository.ProductSnapshotRepositoryImpl;
import com.product.product_server.service.IChannelService;
import com.product.product_server.service.ISnapJobService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN;
import static com.crafts_mirror.utils.constant.SystemConstant.*;

/**
 * @Description 虚拟sku商品业务编排层
 * <AUTHOR>
 * @Date 2023/12/12 19:46
 **/
@Service
@Slf4j
public class SnapJobServiceImpl implements ISnapJobService {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ProductSnapshotRepositoryImpl snapshotRepository;

    @Resource
    private IChannelService channelService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cleanUnusedSnapshots(String jobParam) {
        try {
            // 处理日期参数
            String now = processDateParam(jobParam);
            String lastMonth = processLastMonthParam(now, jobParam);

            // 收集所有需要保留的快照ID
            Set<String> retainedSnapIds = collectAllRetainedSnapIds(now, lastMonth);

            Set<String> lastSnapIds;
            int count = 0;
            do {
                lastSnapIds = snapshotRepository.selectLatestSnapshots(lastMonth, now, count);
                retainedSnapIds.addAll(lastSnapIds);
                count += 1000;
            } while (CollectionUtil.isNotEmpty(lastSnapIds));

            // 删除未使用的快照
            if (CollectionUtil.isNotEmpty(retainedSnapIds)) {
                deleteSnapshots(retainedSnapIds, now, lastMonth);
            }
        } catch (Exception e) {
            log.error("清理未使用快照失败", e);
            throw e;
        }
    }

    @Override
    public void refreshSnapshots() {
        List<String> allSnapshotList = snapshotRepository.getAllSnapshotIdList();
        List<List<String>> partitionList = ListUtil.partition(allSnapshotList, 500);

        Map<String, String> channelNameIdMap = channelService.getChannelNameIdMap();

        for (var page : partitionList) {
            List<ProductSnapshotDO> productSnapshotList = snapshotRepository.listByIds(page);
            for (var info : productSnapshotList) {
                // 处理自定义商品
                SelfProductDO product = JSON.parseObject(info.getSelfData(), SelfProductDO.class);
                SpecificationDp specificationDp = new SpecificationDp(product.getSingleLength(), product.getSingleWidth(), product.getSingleHeight());
                CaseWeightInfoDp caseWeightInfoDp = new CaseWeightInfoDp(product.getWeightUnit(), product.getSingleCaseGrossWeight(), product.getContainerLoad());
                CalPCSTypeDp calPCSTypeDp = new CalPCSTypeDp(product.getProductName(), specificationDp, caseWeightInfoDp, null);
                PCSTypeEnum pcsTypeEnum = PCSTypeEnum.getByProductInfo(calPCSTypeDp);
                product.setPcsType(pcsTypeEnum.getCode());

                // 处理虚拟商品
                VirtualProductDO virtualProduct = JSON.parseObject(info.getVirtualData(), VirtualProductDO.class);
                String channelName = virtualProduct.getChannel();

                if (StrUtil.isNotBlank(channelName)) {
                    virtualProduct.setChannel(channelNameIdMap.getOrDefault(channelName.toUpperCase(Locale.ROOT), channelName));
                }
                info.setSelfData(JSON.toJSONString(product));
                info.setVirtualData(JSON.toJSONString(virtualProduct));

                info.setUpdateDate(null);
            }
            snapshotRepository.updateBatchById(productSnapshotList);
        }

    }

    private String processDateParam(String jobParam) {
        return StrUtil.isNotBlank(jobParam) && jobParam.equals("-1") ? null : DateUtil.now();
    }

    private String processLastMonthParam(String now, String jobParam) {
        return StrUtil.isNotBlank(jobParam) && jobParam.equals("-1") ? null :
                DateUtil.format(DateUtil.offsetWeek(DateUtil.parse(now), -2), YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN);
    }

    private Set<String> collectAllRetainedSnapIds(String now, String lastMonth) {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        Set<String> retainedSnapIds = new HashSet<>();

        // 收集库存系统快照ID
        Set<String> inventorySnapIds = getSnapIdsFromSystem(
                new InventoryInfoQuery(lastMonth, now),
                INVENTORY_SYSTEM_SELECT_SNAP_IDS,
                restTemplateUtil,
                "库存"
        );
        retainedSnapIds.addAll(inventorySnapIds);

        // 收集发货系统快照ID
        Set<String> deliverySnapIds = getSnapIdsFromSystem(
                new InteriorDeliveryInfoQuery(lastMonth, now),
                PURCHASE_SYSTEM_DELIVERY_SELECT_SNAP_IDS,
                restTemplateUtil,
                "发货"
        );
        retainedSnapIds.addAll(deliverySnapIds);

        // 收集补货系统快照ID
        Set<String> replenishmentSnapIds = getSnapIdsFromSystem(
                new InteriorReplenishmentInfoQuery(lastMonth, now),
                PURCHASE_SYSTEM_REPLENISHMENT_SELECT_SNAP_IDS,
                restTemplateUtil,
                "补货"
        );
        retainedSnapIds.addAll(replenishmentSnapIds);

        // 收集装柜系统快照ID
        Set<String> lclSnapIds = getSnapIdsFromSystem(
                new InteriorLclConsolidationInfoQuery(lastMonth, now),
                PURCHASE_SYSTEM_LCL_CONSOLIDATION_SELECT_SNAP_IDS,
                restTemplateUtil,
                "装柜"
        );
        retainedSnapIds.addAll(lclSnapIds);

        return retainedSnapIds;
    }

    private Set<String> getSnapIdsFromSystem(Object query, String url, RestTemplateUtils restTemplateUtil, String systemName) {
        ResultDTO resultDTO = restTemplateUtil.post(query, ResultDTO.class, url);
        if (resultDTO.getStatus().equals(ResponseCodeEnum.ERROR.getCode())) {
            log.error("从{}获取快照ID失败: {}", systemName, resultDTO.getMessage());
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "从{}获取快照ID失败: {}", systemName, resultDTO.getMessage());
        }
        return JSON.to(Set.class, resultDTO.getData());
    }

    private void deleteSnapshots(Set<String> retainedSnapIds, String now, String lastMonth) {
        int deletedCount = 0;
        int batchSize;
        do {
            batchSize = snapshotRepository.deleteUnusedSnapshots(retainedSnapIds, lastMonth, now);
            deletedCount += batchSize;
            log.info("已删除快照数据：{}", deletedCount);
        } while (batchSize > 0);

        log.info("清理未使用快照完成，共删除{}条数据", deletedCount);
    }
}
