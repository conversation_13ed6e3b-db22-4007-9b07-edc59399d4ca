package com.purchase.purchase_server.enums.lcl;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum MoveOrSplitTypeEnum {
    MOVE(0, "移动"),
    SPLIT(1, "拆分");

    private final Integer code;

    private final String desc;

    public static MoveOrSplitTypeEnum ofCode(Integer code) {
        return Arrays.stream(MoveOrSplitTypeEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
