<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.auth.authorization_server.mapper.MenuMapper">

    <select id="selectMenuTreeListByUrlAndPermission" parameterType="java.lang.String" resultType="com.auth.authorization_server.model.dataObject.MenuDO">
        WITH RECURSIVE MenuHierarchy AS (
            -- 第一个CTE查找初始的菜单项
            SELECT
                id,
                parent_id,
                menu_name
            FROM
                cm_sys_menu
            WHERE
                url = #{url} AND perms = #{permission}  -- 替换为实际的path条件

            UNION ALL

            -- 递归部分查找父级菜单项，直到parent_id = 0
            SELECT
                t.id,
                t.parent_id,
                t.menu_name
            FROM
                cm_sys_menu t
                    INNER JOIN
                MenuHierarchy mh ON t.id = mh.parent_id
        )
        SELECT
            *
        FROM
            MenuHierarchy;
    </select>

</mapper>