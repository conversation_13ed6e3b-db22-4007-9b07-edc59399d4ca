package com.purchase.purchase_server.service.shipping;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.purchase_server.entity.dto.delivery.DeliverySaleDestinationDto;
import com.purchase.purchase_server.entity.form.*;
import com.purchase.purchase_server.entity.vo.*;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

public interface IDeliveryPurchaseService {

    void importExcelToDeliveryPurchase(InputStream file, String fileName, byte[] byteArrayResource,
                                       String shipmentStartDate, String shipmentEndDate) throws IOException;

    void exportTemplate(HttpServletResponse response);

    IPage<DeliveryRecordPurchaseListVo> recordPageList(DeliveryRecordPurchaseForm form);

    IPage<DeliveryPurchaseListVo> pageList(DeliveryPurchaseForm form);

    MissionCenterVo selectFileInfo(String recordId);

    void exportDeliveryInfo(DeliveryPurchaseForm form);

    TrialShippingImportInfoDetailVo getTrialImportInfoDetail(String shippingProjectId);

    ShippingFactoryPlainInfoVo getShippingFactoryPlainInfoVo(String shippingProjectId);

    TrialWatchBoardVo getTriaWatchBoard(DeliveryWatchBoardForm form);

    MockInventoryVo calTrialMockInventory(DeliveryCalMockInventoryForm form);

    DeliveryPurchaseCountVo shippingCount(DeliveryPurchaseForm form);

    Boolean updateTrialStatus(String recordId, String status);


    List<UserInteriorVO> getUserList();

    /**
     * 获取运营名单
     *
     */
    List<OperatorSearchVo> getOperator();

    Boolean deleteRecord(String recordId);

    DeliveryRecordPurchaseListVo downloadExport(DeliveryPurchaseForm form);

    List<DeliverySaleDestinationDto> getSaleDestination(DeliveryPurchaseForm form);

}
