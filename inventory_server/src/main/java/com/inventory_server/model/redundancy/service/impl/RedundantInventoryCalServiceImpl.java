package com.inventory_server.model.redundancy.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum;
import com.crafts_mirror.utils.utils.DateUtils;
import com.inventory_server.applications.dto.FactoryFinishedInventoryDto;
import com.inventory_server.applications.dto.FactoryRemainInventoryDto;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.redundancy.entity.dp.*;
import com.inventory_server.model.redundancy.entity.dto.*;
import com.inventory_server.model.redundancy.entity.vo.DeliveryCalculationVo;
import com.inventory_server.model.redundancy.service.IRedundancyEndCalService;
import com.inventory_server.model.redundancy.service.IRedundantInventoryCalService;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.service.IWarehouseService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.utils.DateUtils.YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT;
import static com.inventory_server.model.redundancy.entity.dp.RedundancyInfo.RedundantType.SELLING_OUT;
import static com.inventory_server.model.redundancy.entity.dp.RedundancyInfo.RedundantType.TEST_SAMPLE_INVENTORY;
import static com.inventory_server.model.redundancy.enums.RedundantTypeEnum.FOREIGN_INVENTORY;
import static com.inventory_server.model.redundancy.enums.RedundantTypeEnum.SHIPPING_INVENTORY;
import static java.math.RoundingMode.HALF_UP;

/**
 * @Description 负责库存管理算法的service层
 * <AUTHOR>
 * @Date 2024/5/7 16:36
 **/
@Service
@Slf4j
public class RedundantInventoryCalServiceImpl implements IRedundantInventoryCalService {

    @Resource
    private IWarehouseService warehouseService;

    @Resource(name = "normalRedundancyEndCalServiceImpl")
    private IRedundancyEndCalService redundancyEndCalService;

    private static final DateTimeFormatter formatter = YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT;

    @Override
    public void calFactoryRedundantInventory(List<InventoryCalDp> inventoryCalDpList, DeliveryCalculationVo shippingResult,
                                             RedundancyResultDto redundantInventoryResult, Map<String, Integer> headShippingDateMap,
                                             int solidDays) {
        List<DeliveryCalResultDto> deliveryCalResultList = shippingResult.getDeliveryCalResultList();
        if (inventoryCalDpList.size() != deliveryCalResultList.size()) {
            String importVirtualSku = inventoryCalDpList.stream().map(m -> m.virtualProduct().getVirtualSku()).collect(Collectors.joining(","));
            String deliveryVirtualSku = deliveryCalResultList.stream()
                    .map(DeliveryCalResultDto::getVirtualSku).collect(Collectors.joining(","));
            log.error("导入数据的虚拟sku数量与发货试算结果虚拟sku数量不一致；导入数据虚拟sku：{}，发货试算结果中的虚拟sku：{}", importVirtualSku, deliveryVirtualSku);
            throw new RuntimeException("导入数据的虚拟sku数量与发货试算结果虚拟sku数量不一致，请联系研发人员处理");
        }

        var deliveryResultMap = deliveryCalResultList.stream().collect(Collectors.toMap(DeliveryCalResultDto::getVirtualSku, item -> item));
        for (var inventoryCal : inventoryCalDpList) {
            VirtualProductDO virtualProduct = inventoryCal.virtualProduct();
            String virtualSku = virtualProduct.getVirtualSku();
            DeliveryCalResultDto deliveryCalResultDto = deliveryResultMap.get(virtualSku);
            int productStatus = virtualProduct.getProductStatus();
            VirtualProductStatusEnum statusEnum = VirtualProductStatusEnum.ofCode(productStatus);

            switch (statusEnum) {
                case STOP_SELLING, SOLD_OUT_STOP_SELLING ->
                        calStopSellingProductsFactoryRedundancy(deliveryCalResultDto, virtualSku, redundantInventoryResult);
                case NEW_ARRIVAL_TEST_SAMPLE, NEW_ARRIVAL_NORMAL_SAMPLE -> {
                    RedundancyDp factoryRedundancyDp = new RedundancyDp(virtualSku);
                    factoryRedundancyDp.addRedundancy(FactoryRedundancyInfo.builder().type(TEST_SAMPLE_INVENTORY).redundantNum(0).build());
                    redundantInventoryResult.addFactoryRedundancyDp(factoryRedundancyDp);
                }
                default -> calNormalProductsFactoryRedundancy(inventoryCal, deliveryCalResultDto, redundantInventoryResult,
                                headShippingDateMap, solidDays);
            }
        }
    }

    private void calStopSellingProductsFactoryRedundancy(DeliveryCalResultDto deliveryCalResultDto, String virtualSku,
                                                         RedundancyResultDto redundantInventoryResult) {
        int totalFactoryInventory = deliveryCalResultDto.getFactoryFinishedResultList().stream()
                .mapToInt(DeliveryCalResultFactoryFinishedDto::getFactoryShippingPackageNum)
                .sum();
        RedundancyDp factoryRedundantInventory = new RedundancyDp(virtualSku);
        FactoryRedundancyInfo build = FactoryRedundancyInfo.builder()
                .redundantNum(totalFactoryInventory)
                .type(SELLING_OUT)
                .build();
        for (var dto : deliveryCalResultDto.getFactoryFinishedResultList()) {
            build.addRedundancy(FactoryFinishedRedundancy.builder()
                    .redundancy(dto.getFactoryShippingPackageNum())
                    .virtualSku(dto.getDestinationSku())
                    .contractCode(dto.getContractCode())
                    .factoryFinishedDate(DateUtils.convertToLocalDate(dto.getFactoryFinishedDate()))
                    .build());
        }
        factoryRedundantInventory.addRedundancy(build);
        redundantInventoryResult.addFactoryRedundancyDp(factoryRedundantInventory);
    }

    private void calNormalProductsFactoryRedundancy(InventoryCalDp inventoryCal, DeliveryCalResultDto deliveryCalResultDto,
                                                    RedundancyResultDto redundancyResult, Map<String, Integer> headShippingDateMap,
                                                    int solidDays) {
        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();

        Map<String, Double> shippingRatioMap = inventoryCal.inventoryRules().shippingRulesDp().shippingRatioMap();
        var everydayRemainInventoryMap = deliveryCalResultDto.getEverydayRemainInventoryMap();

        // 先根据excel数据、发货结果数据计算出各到货时间的到货类型
        InvOrRepDaysRulesDp daysRulesDp = inventoryCal.inventoryRules().daysRulesDp();
        int safeDays = daysRulesDp.safeDays();
        int redundancyDays = safeDays + solidDays;
        String virtualSku = inventoryCal.virtualProduct().getVirtualSku();
        Map<String, BigDecimal> targetSalesMap = inventoryCal.targetSales().targetSalesMap();
        LocalDate calEndDate = redundancyEndCalService.calRedundancyEndDate(inventoryCal, LocalDate.now(), headShippingDateMap, solidDays);

        // 计算海外仓冗余和在途冗余的和
        int foreignRedundancy = redundancyResult.getForeignRedundancyDpList().stream()
                .filter(f -> f.getVirtualSku().equals(virtualSku))
                .map(m -> m.getFirstRedundantInventoryInfo().getRedundantNum())
                .findFirst().orElse(0);
        double onShippingRedundancy = redundancyResult.getOnShippingRedundancyDpList().stream()
                .filter(f -> f.getVirtualSku().equals(virtualSku))
                .flatMap(m -> m.getRedundancyList().stream())
                .mapToInt(m -> Optional.ofNullable(m.getRedundantNum()).orElse(0))
                .sum();
        int totalRedundancy = (int) roundUpToThreeDecimalPlaces(foreignRedundancy + onShippingRedundancy);

        var factoryFinishedResultList = deliveryCalResultDto.getFactoryFinishedResultList();
        // 获取未安排数量
        DeliveryAllRedundancyDto allRedundancyDto = new DeliveryAllRedundancyDto(virtualSku, everydayRemainInventoryMap, targetSalesMap,
                redundancyDays, shippingRatioMap, senboWarehouseList, deliveryCalResultDto.getShippingInventoryList(), calEndDate);

        List<FactoryFinishedInventoryDto> finishedInventoryList = inventoryCal.factoryPlan().factoryFinishedInventoryDtos();
        RedundancyDp factoryRedundancyDp = new RedundancyDp(virtualSku);
        allRedundancyDto.calFactoryRedundancy(factoryRedundancyDp, totalRedundancy, finishedInventoryList, factoryFinishedResultList);
        redundancyResult.addFactoryRedundancyDp(factoryRedundancyDp);
    }

    @Override
    public void calForeignRedundantInventory(List<InventoryCalDp> inventoryCalDpList, RedundancyResultDto resultDto,
                                             DeliveryCalculationVo deliveryCalculationVo, Map<String, Integer> headShippingDateMap,
                                             int solidDays) {
        List<DeliveryCalResultDto> deliveryCalResultList = deliveryCalculationVo.getDeliveryCalResultList();
        var deliveryResultMap = deliveryCalResultList.stream().collect(Collectors.toMap(DeliveryCalResultDto::getVirtualSku, item -> item));
        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();

        for (InventoryCalDp inventoryCal : inventoryCalDpList) {
            VirtualProductDO virtualProduct = inventoryCal.virtualProduct();
            String virtualSku = virtualProduct.getVirtualSku();
            int productStatus = virtualProduct.getProductStatus();
            VirtualProductStatusEnum statusEnum = VirtualProductStatusEnum.ofCode(productStatus);

            switch (statusEnum) {
                case STOP_SELLING, SOLD_OUT_STOP_SELLING ->
                        calStopSellingForeignAndShipRedundancy(resultDto, inventoryCal, deliveryResultMap.get(virtualSku));
                case NEW_ARRIVAL_TEST_SAMPLE, NEW_ARRIVAL_NORMAL_SAMPLE -> {
                    RedundancyDp foreignRedundancy = new RedundancyDp(virtualSku);
                    foreignRedundancy.addRedundancy(
                            ForeignRedundancyInfo.builder()
                                    .type(TEST_SAMPLE_INVENTORY)
                                    .redundantTypeEnum(FOREIGN_INVENTORY)
                                    .redundantNum(0).build()
                    );
                    resultDto.addForeignRedundancyDp(foreignRedundancy);
                }
                default ->
                        calNormalStatusForeignRedundancy(inventoryCal, deliveryResultMap.get(virtualSku), senboWarehouseList,
                                resultDto, headShippingDateMap, solidDays);
            }
        }
    }

    private void calStopSellingForeignAndShipRedundancy(RedundancyResultDto resultDto, InventoryCalDp inventoryCal, DeliveryCalResultDto deliveryResult) {
        String virtualSku = inventoryCal.virtualProduct().getVirtualSku();
        RedundancyDp foreignRedundancyDp = new RedundancyDp(virtualSku);
        RedundancyDp onShippingRedundancyDp = new RedundancyDp(virtualSku);

        // 计算海外仓冗余
        if (deliveryResult.getEverydayRemainInventoryMap().isEmpty()) {
            foreignRedundancyDp.addRedundancy(ForeignRedundancyInfo.builder()
                    .arrivingDate(LocalDate.now())
                    .redundantNum(0)
                    .redundantTypeEnum(FOREIGN_INVENTORY)
                    .build());
        } else {
            var entry = deliveryResult.getEverydayRemainInventoryMap().firstEntry();
            int sumRedundancy = entry.getValue().values().stream()
                    .filter(aDouble -> aDouble > 0)
                    .mapToInt(Double::intValue)
                    .sum();

            ForeignRedundancyInfo build = ForeignRedundancyInfo.builder()
                    .arrivingDate(LocalDate.parse(entry.getKey(), formatter))
                    .redundantNum(sumRedundancy)
                    .redundantTypeEnum(FOREIGN_INVENTORY)
                    .build();

            for (var entry1 : entry.getValue().entrySet()) {
                if (entry1.getValue() <= 0) {
                    continue;
                }

                ForeignWarehouseRedundancy foreignWarehouseRedundancy = ForeignWarehouseRedundancy.builder()
                        .warehouse(entry1.getKey())
                        .redundantInventory(entry1.getValue().intValue())
                        .build();
                build.addForeignWarehouseRedundancy(foreignWarehouseRedundancy);
            }
            foreignRedundancyDp.addRedundancy(build);
        }
        resultDto.addForeignRedundancyDp(foreignRedundancyDp);

        // 计算在途冗余
        List<FactoryRemainInventoryDto> onShippingInventoryList = inventoryCal.stockQuantity().list();
        if (CollUtil.isNotEmpty(onShippingInventoryList)) {

            OnShippingRedundancyInfo onShippingRedundancyInfo = OnShippingRedundancyInfo.builder()
                    .redundantTypeEnum(SHIPPING_INVENTORY)
                    .build();
            for (var dto : onShippingInventoryList) {
                if (dto.getEnableUsingDate().equals(DateUtils.convertToDate(LocalDate.now()))) {
                    continue;
                }
                OnShippingWarehouseRedundancy build = OnShippingWarehouseRedundancy.builder()
                        .warehouse(dto.getWarehouse())
                        .shipmentCode(dto.getShipmentCode())
                        .redundantInventory(dto.getStoreNum().intValue())
                        .enableUsingDate(DateUtils.convertToLocalDate(dto.getEnableUsingDate()))
                        .startShippingDate(DateUtils.convertToLocalDate(dto.getStartShippingTime()))
                        .build();
                onShippingRedundancyInfo.addShippingWarehouseRedundancy(build);
            }
            onShippingRedundancyDp.addRedundancy(onShippingRedundancyInfo);
            resultDto.addOnShippingRedundancyDp(onShippingRedundancyDp);
        }
    }

    private void calNormalStatusForeignRedundancy(InventoryCalDp inventoryCal, DeliveryCalResultDto deliveryResult,
                                                  List<SenboWarehouseDto> senboWarehouseList, RedundancyResultDto resultDto,
                                                  Map<String, Integer> headShippingDateMap, int solidDays) {
        // 有发货比例的仓库的最短头程时间
        Map<String, Double> shippingRatioMap = inventoryCal.inventoryRules().shippingRulesDp().shippingRatioMap();
        InvOrRepDaysRulesDp daysRulesDp = inventoryCal.inventoryRules().daysRulesDp();

        // 获取计算冗余库存的必要参数
        TreeMap<String, Map<String, Double>> everydayRemainInventoryMap = deliveryResult.getEverydayRemainInventoryMap();
        Map<String, BigDecimal> targetSalesMap = inventoryCal.targetSales().targetSalesMap();
        int safeDays = daysRulesDp.safeDays();
        int redundancyDays = safeDays + solidDays;
        String virtualSku = inventoryCal.virtualProduct().getVirtualSku();

        // 冗余库存计算截止时间
        LocalDate calEndDate = redundancyEndCalService.calRedundancyEndDate(inventoryCal, LocalDate.now(), headShippingDateMap, solidDays);

        List<FactoryRemainInventoryDto> remainInventoryList = inventoryCal.stockQuantity().list();
        DeliveryAllRedundancyDto allRedundancyDto = new DeliveryAllRedundancyDto(virtualSku, everydayRemainInventoryMap,
                targetSalesMap, redundancyDays, shippingRatioMap, senboWarehouseList, remainInventoryList, calEndDate);

        RedundancyDp foreignRedundancyDp = new RedundancyDp(virtualSku);
        RedundancyDp onShippingRedundancyDp = new RedundancyDp(virtualSku);
        allRedundancyDto.calForeignAndOnShippingRedundancy(foreignRedundancyDp, onShippingRedundancyDp);
        resultDto.addForeignRedundancyDp(foreignRedundancyDp);
        resultDto.addOnShippingRedundancyDp(onShippingRedundancyDp);
    }

    @Override
    public Map<String, LocalDate> calTheoreticalSoldOutDate(DeliveryCalculationVo shippingResult, List<InventoryCalDp> inventoryCalDps) {
        List<DeliveryCalResultDto> deliveryCalResultList = shippingResult.getDeliveryCalResultList();
        Map<String, Map<String, BigDecimal>> virtualSkuTargetSalesMap = inventoryCalDps.stream()
                .map(InventoryCalDp::targetSales)
                .collect(Collectors.toMap(TargetSalesDp::virtualSku, TargetSalesDp::targetSalesMap));

        Map<String, LocalDate> soldOutDateMap = new HashMap<>();
        for (var dto : deliveryCalResultList) {
            String virtualSku = dto.getVirtualSku();
            var targetSalesMap = virtualSkuTargetSalesMap.get(virtualSku);
            var everydayRemainInventoryMap = dto.getEverydayRemainInventoryMap();
            var everydayOnShippingInventoryMap = dto.getEverydayOnShippingInventoryMap();

            LocalDate maxDate = targetSalesMap.keySet().stream()
                    .map(m -> LocalDate.parse(m, formatter))
                    .max(Comparator.comparing(c -> c))
                    .orElse(LocalDate.now().plusMonths(1));

            LocalDate lastDayArriving = everydayOnShippingInventoryMap.entrySet().stream()
                    .filter(t -> calDaysSum(t.getValue()) > 0)
                    .map(m -> DateUtils.convertToLocalDate(m.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH))
                    .max(Comparator.comparing(c -> c))
                    .orElse(LocalDate.now());

            LocalDate first = targetSalesMap.entrySet().stream()
                    .filter(entry -> {
                        LocalDate localDate = DateUtils.convertToLocalDate(entry.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH);
//                        LocalDate temp = lastDayArriving.with(TemporalAdjusters.firstDayOfMonth());
                        return !localDate.isBefore(lastDayArriving);
                    })
                    .filter(entry -> entry.getValue().compareTo(BigDecimal.ZERO) > 0)
                    .map(entry -> DateUtils.convertToLocalDate(entry.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH))
                    .min(Comparator.comparing(c -> c))
                    .orElse(maxDate);
            if (first.isBefore(lastDayArriving)) {
                first = lastDayArriving;
            }

            LocalDate finalFirst = first;
            LocalDate soldOutDate = everydayRemainInventoryMap.entrySet().stream()
                    .filter(entry -> !DateUtils.convertToLocalDate(entry.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH).isBefore(finalFirst))
                    // 取第一个目标日销大于0的日期开始计算
                    .dropWhile(entry -> {
                        BigDecimal targetSales = targetSalesMap.getOrDefault(entry.getKey(), BigDecimal.ZERO).setScale(3, HALF_UP);
                        return targetSales.compareTo(BigDecimal.ZERO) <= 0;
                    })
                    .map(entry -> {
                        // 当天剩余总库存
                        double remainSum = calDaysSum(entry.getValue());
                        // 当天在途总库存
                        double shippingSum = calDaysSum(everydayOnShippingInventoryMap.getOrDefault(entry.getKey(), new HashMap<>()));
                        double totalNum = roundUpToThreeDecimalPlaces(remainSum + shippingSum);

                        BigDecimal targetSales = targetSalesMap.getOrDefault(entry.getKey(), BigDecimal.ZERO).setScale(3, HALF_UP);
                        if (totalNum <= targetSales.doubleValue()) {
                            return finalFirst;
                        }

                        return LocalDateTimeUtil.parseDate(entry.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH).plusDays(1);
                    })
                    .max(Comparator.comparing((LocalDate a) -> a))
                    .orElse(null);

            if (soldOutDate != null && soldOutDate.isBefore(maxDate)) {
                soldOutDateMap.put(virtualSku, soldOutDate);
            }
        }
        return soldOutDateMap;
    }

    private double calDaysSum(Map<String, Double> map) {
        return roundUpToThreeDecimalPlaces(map.entrySet().stream()
                .filter(f -> !f.getKey().contains("start"))
                .mapToDouble(Map.Entry::getValue)
                .sum());
    }

    @Override
    public Map<String, List<SoldOutDateBeforeTheoryDto>> calAllVirtualSkuRealSoldOutDate(List<DeliveryCalResultDto> deliveryCalResultList,
                                                                                         List<InventoryCalDp> inventoryCalDps,
                                                                                         Map<String, LocalDate> soldOutDateMap) {
        Map<String, List<SoldOutDateBeforeTheoryDto>> soldOutDateBeforeMap = new HashMap<>(deliveryCalResultList.size());
        var virtualSkuTargetSalesMap = inventoryCalDps.stream()
                .map(InventoryCalDp::targetSales)
                .collect(Collectors.toMap(TargetSalesDp::virtualSku, TargetSalesDp::targetSalesMap));
        for (var dto : deliveryCalResultList) {
            var everydayRemainInventoryMap = dto.getEverydayRemainInventoryMap();
            var targetSalesMap = virtualSkuTargetSalesMap.getOrDefault(dto.getVirtualSku(), new HashMap<>());
            LocalDate soldOutDate = soldOutDateMap.getOrDefault(dto.getVirtualSku(), targetSalesMap.keySet().stream()
                    .map(m -> LocalDate.parse(m, formatter))
                    .max(Comparator.comparing(c -> c))
                    .orElse(null));

            var soldOutDateList = calRealSoldOutDate(everydayRemainInventoryMap, targetSalesMap, soldOutDate);
            soldOutDateBeforeMap.put(dto.getVirtualSku(), soldOutDateList);
        }
        return soldOutDateBeforeMap;
    }

    @Override
    public Map<String, SalableDateDto> calAllVirtualSkuRealSoldOutDate(List<DeliveryCalResultDto> deliveryResultList) {
        Map<String, SalableDateDto> map = new HashMap<>(deliveryResultList.size());
        for (var dto : deliveryResultList) {
            String virtualSku = dto.getVirtualSku();
            SalableDateDto salableDateDto = new SalableDateDto();
            var everydaySaleProductMap = dto.getEverydaySaleProductMap();
            long count = everydaySaleProductMap.entrySet().stream()
                    .filter(f -> f.getValue().values().stream().anyMatch(m -> m > 0))
                    .count();
            salableDateDto.addSalableDays((int)count);
            map.put(virtualSku, salableDateDto);
        }
        return map;
    }

    @Override
    //剩余库存 目标日销 理论售罄时间
    public List<SoldOutDateBeforeTheoryDto> calRealSoldOutDate(Map<String, Map<String, Double>> everydayRemainMap,
                                                               Map<String, BigDecimal> targetSalesMap, LocalDate soldOutDate) {
        if (soldOutDate == null) {
            return new ArrayList<>();
        }

        List<SoldOutDateBeforeTheoryDto> dtoList = new ArrayList<>();
        everydayRemainMap.entrySet().stream()
                .takeWhile(entry -> LocalDateTimeUtil.parseDate(entry.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH).isBefore(soldOutDate))
                .forEach(entry -> {
                    LocalDate calDate = LocalDateTimeUtil.parseDate(entry.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH);
                    double remainNum = entry.getValue().entrySet().stream()
                            .filter(f -> !f.getKey().toLowerCase(Locale.ROOT).contains("start"))
                            .mapToDouble(Map.Entry::getValue)
                            .sum();
                    BigDecimal targetSales = targetSalesMap.getOrDefault(formatter.format(calDate), BigDecimal.ZERO).setScale(3, HALF_UP);
                    double targetSalesDouble = targetSales.doubleValue();
                    double lackNum = roundUpToThreeDecimalPlaces(targetSalesDouble - remainNum);
                    if (lackNum > 0 && dtoList.stream().noneMatch(dto -> dto.addDaysEnd(calDate, lackNum))) {
                        dtoList.add(new SoldOutDateBeforeTheoryDto(calDate, roundUpToThreeDecimalPlaces(lackNum)));
                    }
                });
        return dtoList;
    }

    private double roundUpToThreeDecimalPlaces(double num) {
        if (Double.isNaN(num)) {
            return 0;
        }

        return BigDecimal.valueOf(num).setScale(3, HALF_UP).doubleValue();
    }
}
