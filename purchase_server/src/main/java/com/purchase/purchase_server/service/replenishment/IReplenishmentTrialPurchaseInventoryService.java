package com.purchase.purchase_server.service.replenishment;

import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentProjectSaveDto;
import com.purchase.purchase_server.entity.form.*;
import com.purchase.purchase_server.entity.vo.ReplenishmentDetailWatchBoardReplenishmentVo;
import com.purchase.purchase_server.entity.vo.ReplenishmentDetailWatchBoardShippingVo;
import com.purchase.purchase_server.entity.vo.ReplenishmentTrialConfirmQuantityVo;
import com.purchase.purchase_server.entity.vo.TrialMockInventoryAddSoldVo;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description 针对表【cm_replenishment_trial_purchase_inventory】的数据库操作Service
 * @createDate 2024-01-17 10:45:51
 */
public interface IReplenishmentTrialPurchaseInventoryService {
    //List<AdvicePurchaseDateListVo> advicePurchaseList(ReplenishmentProjectForm form);

    //Boolean updateAdvicePurchase(AdvicePurchaseForm form);

    Boolean saveAdvicePurchase(String recordId, String replenishmentStatus);

    void checkRecordData(String recordId, String replenishmentStatus);

    void exportRepInfo(ReplenishmentProjectForm form, HttpServletResponse response);

    void exportNotRestockRepInfo(ReplenishmentProjectForm form, HttpServletResponse response);

    ReplenishmentDetailWatchBoardShippingVo getWatchBoardShippingInfoOpertor(String replenishmentId, String purchaseNumType);
    // 编辑确认量-获取到仓数据
    ReplenishmentTrialConfirmQuantityVo getConfirmEditQuantity(ReplenishmentDetailForm form);

    ReplenishmentDetailWatchBoardReplenishmentVo getWatchBoardReplenishmentInfo(ReplenishmentDetailForm form);

    Boolean saveReplenishmentPurchaseNumChange(RetrialReplenishmentInventoryForm form);

    TrialMockInventoryAddSoldVo calDelivery(RetrialReplenishmentInventoryForm form);

    Boolean delete(RetrialReplenishmentInventoryForm form);

    Boolean updateOperation(OperationConfirmedNumForm form, LogTrackNumDto logTrackNumDto);

    Boolean batchUpdateOperation(OperationConfirmedNumForm form, LogTrackNumDto logTrackNumDto);

    void saveTrialPurchaseInventoryInfo(ReplenishmentProjectSaveDto projectSaveDto, VirtualProductDO virtualProductDO,
                                        String skuPurchaseDOId);

    void refreshData();

    Boolean checkConfirmedReason(OperationConfirmedNumForm form);

}
