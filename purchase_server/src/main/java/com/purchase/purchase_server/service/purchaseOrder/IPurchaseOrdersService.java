package com.purchase.purchase_server.service.purchaseOrder;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrdersDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrderSaveScheduleDto;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrdersSummaryDto;
import com.purchase.purchase_server.entity.form.PurchaseOrdersForm;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrderVirtualVO;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrdersRemainNumVO;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrdersVO;
import jakarta.servlet.http.HttpServletResponse;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cm_yicang_purchase_orders(易仓采购单表)】的数据库操作Service
 * @createDate 2024-11-01 14:30:43
 */
public interface IPurchaseOrdersService {
    /**
     * 根据易仓采购单id查询数据
     *
     * @param dto 易仓采购单
     * @return 易仓采购单数据
     */
    List<YicangPurchaseOrdersDO> select(OrdersSummaryDto dto);

    /**
     * 更新数据
     */
    void updateById(YicangPurchaseOrdersDO yicangPurchaseOrdersDO);

    /**
     * 保存易仓采购单
     *
     * @param ordersDO 易仓采购单
     */
    void save(YicangPurchaseOrdersDO ordersDO);

    /**
     * 分页查询
     *
     * @param form 查询条件
     */
    IPage<YicangPurchaseOrdersVO> pageList(PurchaseOrdersForm form);

    /**
     * 分页查询-未安排数量
     *
     * @param form 查询条件
     */
    IPage<YicangPurchaseOrdersRemainNumVO> pageListByRemainNum(PurchaseOrdersForm form);

    /**
     * 采购单详情
     *
     * @param form 查询条件
     */
    List<YicangPurchaseOrderVirtualVO> getOrderDetail(PurchaseOrdersForm form);

    /**
     * 导入采购交货计划
     *
     */
    void importPurchaseOrderInfo(InputStream file, byte[] fileBytes, String fileName);

    void exportOrderInfo(PurchaseOrdersForm form, HttpServletResponse response);

    /**
     * @param val 交期计划
     */
    boolean saveOrderSchedule(OrderSaveScheduleDto val);

    Integer getNotAbandonedDetail(String virtualSku);

    List<YicangPurchaseOrdersDO> getNotAbandonedDetailList(String virtualSku);
}
