package com.purchase.purchase_server.entity.dto.replenishment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ReplenishmentSaleDestinationDto implements Serializable {


    @Serial
    private static final long serialVersionUID = 1586800759196592326L;
    private String saleDestinationDate;

   private Double saleDestinationNum;
}