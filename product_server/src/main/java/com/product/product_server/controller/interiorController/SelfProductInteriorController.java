package com.product.product_server.controller.interiorController;

import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.product.product_server.entity.dto.ProductCategoryDTO;
import com.product.product_server.service.IProductCategoryService;
import com.product.product_server.service.ISelfProductService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Description 自定义商品信息内部调用类
 * <AUTHOR>
 * @Date 2024/11/8 15:57
 **/
@Slf4j
@RestController
@RequestMapping("/interior/selfProduct")
public class SelfProductInteriorController {

    @Resource
    private ISelfProductService selfProductService;

    @Resource
    private IProductCategoryService productCategoryService;

    @GetMapping("/category")
    public ResultDTO<List<ProductCategoryDTO>> category() {
        return ResultDTO.success(selfProductService.category());
    }

    @GetMapping("/category/leafTree")
    public ResultDTO<Map<String, ProductCategoryDTO>> categoryLeafTree() {
        return ResultDTO.success(productCategoryService.selectCategoryLeafTree());
    }

}
