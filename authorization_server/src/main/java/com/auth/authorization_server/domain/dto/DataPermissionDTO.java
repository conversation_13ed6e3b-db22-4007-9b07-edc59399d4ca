package com.auth.authorization_server.domain.dto;

import com.auth.authorization_server.domain.vo.UserVO;
import com.auth.authorization_server.model.dataObject.ModuleDO;
import com.auth.authorization_server.model.dataObject.RoleDataPermissionDO;
import com.auth.authorization_server.model.vo.TreeSelect;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DataPermissionDTO implements Serializable {

    /**
     * 用户列表
     */
    List<UserVO> userList;

    /**
     * 选中用户
     */
    List<String> checkedUserList;

    /**
     * 部门列表
     */
    List<TreeSelect> deptTree;

    /**
     * 选中部门
     */
    List<TreeSelect> checkedDeptList;

    /**
     * 模块功能
     */
    List<ModuleDO> moduleList;

    /**
     * 选中模块
     */
    List<String> checkedModuleList;

}
