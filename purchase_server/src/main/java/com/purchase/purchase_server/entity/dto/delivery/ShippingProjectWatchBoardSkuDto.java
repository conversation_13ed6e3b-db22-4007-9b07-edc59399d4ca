package com.purchase.purchase_server.entity.dto.delivery;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 试算看板虚拟sku
 * <AUTHOR>
 * @Date 2024/1/10 13:53
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShippingProjectWatchBoardSkuDto implements Serializable {
    private String destinationSku;

    private List<ShippingProjectWatchBoardFactoryDto> finishDateList;
}
