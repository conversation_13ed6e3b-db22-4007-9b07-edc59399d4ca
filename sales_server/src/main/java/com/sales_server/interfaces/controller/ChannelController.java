package com.sales_server.interfaces.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.sales_server.infrastructures.aop.PreventReSubmit;
import com.sales_server.model.channel.entity.form.ChannelSearchPageForm;
import com.sales_server.model.channel.entity.form.SaveChannelForm;
import com.sales_server.model.channel.entity.vo.ChannelPageVo;
import com.sales_server.model.channel.entity.vo.ChannelSearchVo;
import com.sales_server.model.channel.entity.vo.NicheChannelDetailVo;
import com.sales_server.model.channel.service.IChannelInfoService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * @Description 渠道controller层
 * <AUTHOR>
 * @Date 2025/4/30 14:03
 **/
@RestController
@RequestMapping(value = "/channel")
@Slf4j
public class ChannelController {

    @Resource
    private IChannelInfoService channelInfoService;

    @PostMapping("/save/saleChannel")
    @RequiresPermissions("channel:channelInfo:info")
    @PreventReSubmit
    public ResultDTO<Void> saveSaleChannel(@RequestBody SaveChannelForm form) {
        channelInfoService.saveSaleChannel(form);
        return ResultDTO.success();
    }

    @PostMapping("/save/nicheChannel")
    @RequiresPermissions("channel:channelInfo:info")
    @PreventReSubmit
    public ResultDTO<Void> saveNicheChannel(@Valid @RequestBody SaveChannelForm form) {
        channelInfoService.saveNicheChannel(form);
        return ResultDTO.success();
    }

    @GetMapping("/check/saleChannel")
    @RequiresPermissions("channel:channelInfo:info")
    public ResultDTO<Boolean> checkSaleChannelNameExisted(@RequestParam("saleChannel") String saleChannel) {
        channelInfoService.checkSaleChannelNameExisted(saleChannel);
        return ResultDTO.success(true);
    }

    @PostMapping("/check/nicheChannel/name")
    @RequiresPermissions("channel:channelInfo:info")
    public ResultDTO<Boolean> checkNicheChannelNameExisted(@RequestBody SaveChannelForm form) {
        channelInfoService.checkNicheChannelNameExisted(form);
        return ResultDTO.success(true);
    }

    @PostMapping("/check/nicheChannel/sort")
    @RequiresPermissions("channel:channelInfo:info")
    public ResultDTO<Boolean> checkNicheChannelSortExisted(@RequestBody SaveChannelForm form) {
        channelInfoService.checkNicheChannelSortExisted(form);
        return ResultDTO.success(true);
    }

    @GetMapping("/list/channel/all")
    public ResultDTO<ChannelSearchVo> getChannelList(@RequestParam("isLeaf") Integer isLeaf) {
        ChannelSearchVo channelList = channelInfoService.getChannelList(isLeaf);
        return ResultDTO.success(channelList);
    }

    @PostMapping("/page/nicheChannel")
    @RequiresPermissions("channel:channelInfo:info")
    public ResultDTO<IPage<ChannelPageVo>> getChannelPage(@RequestBody ChannelSearchPageForm form) {
        IPage<ChannelPageVo> channelPage = channelInfoService.getChannelPage(form);
        return ResultDTO.success(channelPage);
    }

    @GetMapping("/info/nicheChannel")
    @RequiresPermissions("channel:channelInfo:info")
    public ResultDTO<NicheChannelDetailVo> getChannelDetailInfo(@RequestParam("nicheChannelId") String nicheChannelId) {
        NicheChannelDetailVo nicheChannelDetail = channelInfoService.getNicheChannelDetail(nicheChannelId);
        return ResultDTO.success(nicheChannelDetail);
    }
}
