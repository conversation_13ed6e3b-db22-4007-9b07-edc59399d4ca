package com.inventory_server.model.product.entity.vo;

import com.inventory_server.model.product.entity.dto.ProductSnapshotDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/29 11:41
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class ProductSnapshotVo implements Serializable {
    private List<ProductSnapshotDto> productSnapshotList;
}
