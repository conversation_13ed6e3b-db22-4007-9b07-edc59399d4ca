package com.inventory_server.model.redundancy.service;

import com.inventory_server.model.redundancy.entity.dp.InventoryCalDp;
import com.inventory_server.model.redundancy.entity.dto.SalableDateDto;

import java.time.LocalDate;
import java.util.Map;

/**
 * 冗余库存计算截止日期服务层
 */
public interface IRedundancyEndCalService {

    LocalDate calRedundancyEndDate(InventoryCalDp inventoryCalDp, LocalDate startDate, Map<String, Integer> headShippingDateMap,
                                   int redundantDateNum);

    String getEndDateDesc(InventoryCalDp inventoryCalDp, LocalDate startDate, Map<String, Integer> headShippingDateMap,
                          int redundantDateNum);
}
