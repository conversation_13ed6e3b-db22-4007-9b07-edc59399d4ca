CREATE TABLE `cm_replenishment_mock_inventory_table`
(
    `id`                                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `onshipping_inventory`              json                                                         NOT NULL COMMENT '模拟在途库存',
    `remain_inventory`                  json                                                         NOT NULL COMMENT '模拟剩余库存',
    `everyday_sale`                     json                                                         NOT NULL COMMENT '模拟日销',
    `create_date`                       datetime                                                     DEFAULT NULL COMMENT '创建时间',
    `update_date`                       datetime                                                     DEFAULT NULL,
    `create_by`                         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `update_by`                         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `status`                            char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL,
    `virtual_sku_purchase_inventory_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '补货计划id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
CREATE TABLE `cm_replenishment_project`
(
    `id`                    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `self_sku`              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '自定义sku',
    `overseas_inventory`    json                                                         NOT NULL COMMENT '海外仓库存',
    `overseas_shipping`     json                                                         NOT NULL COMMENT '海外仓在途',
    `local_inventory`       json                                                         NOT NULL COMMENT '本地库存',
    `advice_purchase_num`   int                                                          NOT NULL COMMENT '建议采购量',
    `accepted_purchase_num` int                                                          NOT NULL COMMENT '已采购采购量',
    `create_date`           datetime                                                     DEFAULT NULL,
    `update_date`           datetime                                                     DEFAULT NULL,
    `create_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `update_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `replenishment_status`  char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL COMMENT '补货保存状态（0：未保存；1：已保存）',
    `status`                char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
CREATE TABLE `cm_replenishment_rules`
(
    `id`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `virtual_sku`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `shipping_ratio`     json                                                         NOT NULL COMMENT '发货比例',
    `head_shipping_days` json                                                         DEFAULT NULL COMMENT '头程时间',
    `purchase_circle`    int                                                          NOT NULL COMMENT '采购周期',
    `shipping_circle`    int                                                          NOT NULL COMMENT '发货周期',
    `safe_days`          int                                                          NOT NULL COMMENT '安全天数',
    `purchase_days`      int                                                          NOT NULL COMMENT '采购下单天数',
    `transit_days`       int                                                          NOT NULL COMMENT '中转天数',
    `create_date`        datetime                                                     DEFAULT NULL,
    `update_date`        datetime                                                     DEFAULT NULL,
    `create_by`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `update_by`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `status`             char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
CREATE TABLE `cm_replenishment_trial_purchase_inventory`
(
    `id`                                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `advice_purchase_num`               int                                                          NOT NULL COMMENT '建议采购量',
    `destination_warehouse`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '目标仓',
    `advice_purchase_date`              datetime                                                     NOT NULL COMMENT '建议采购日',
    `expected_factory_finished_date`    datetime                                                     NOT NULL COMMENT '预计交货时间',
    `expected_shipping_start_date`      datetime                                                     NOT NULL COMMENT '预计装柜时间',
    `expected_arriving_date`            datetime                                                     NOT NULL COMMENT '预计到仓时间',
    `package_num`                       int                                                          NOT NULL COMMENT '箱数',
    `remarks`                           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
    `replenishment_virtual_purchase_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '按虚拟sku展示采购计划的id',
    `create_date`                       datetime                                                      DEFAULT NULL,
    `update_date`                       datetime                                                      DEFAULT NULL,
    `create_by`                         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL,
    `update_by`                         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL,
    `trial_status`                      char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL COMMENT '采纳状态（0-未采纳 1-已采纳）',
    `status`                            char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
CREATE TABLE `cm_replenishment_virtual_sku_purchase`
(
    `id`                       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `destination_sku`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '虚拟sku或老sku',
    `overseas_original_data`   json                                                         NOT NULL COMMENT '海外仓库存原始数据',
    `overseas_inventory`       json                                                         NOT NULL COMMENT '海外仓库存',
    `overseas_shipping`        json                                                         NOT NULL COMMENT '海外仓在途',
    `sale_destination`         json                                                         NOT NULL COMMENT '目标日销',
    `local_inventory`          json                                                         NOT NULL COMMENT '本地库存',
    `sold_out_date`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '断货日',
    `replenishment_status`     char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL COMMENT '补货状态（0：正常；1：需补货）',
    `advice_purchase_num`      int unsigned NOT NULL COMMENT '建议采购量',
    `accepted_purchase_num`    int                                                          NOT NULL COMMENT '已采纳采购量',
    `replenishment_project_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '采购计划id',
    `create_date`              datetime                                                      DEFAULT NULL,
    `update_date`              datetime                                                      DEFAULT NULL,
    `create_by`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL,
    `update_by`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL,
    `status`                   char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL,
    `rules_id`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '补货规则id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;