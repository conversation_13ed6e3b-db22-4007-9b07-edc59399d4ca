package com.inventory_server.model.product.bo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/10
 **/
@Data
public class SelfAndVirtualBO {
    private String VirtualSku;

    private String sku;

    private String image;
    /**
     * 品名
     */
    private String productName;
    /**
     * 采购人员
     */
    private String buyer;
    /**
     * 供应商代码
     */
    private String factoryId;
    /**
     * 单品规格长
     */
    private Double singleLength;
    /**
     * 单品规格宽
     */
    private Double singleWidth;
    /**
     * 单品规格高
     */
    private Double singleHeight;
    /**
     * 包装规格长
     */
    private Double packageLength;
    /**
     * 包装规格宽
     */
    private Double packageWidth;
    /**
     * 包装规格高
     */
    private Double packageHeight;
    /**
     * 外箱规格长
     */
    private Double caseLength;
    /**
     * 外箱规格宽
     */
    private Double caseWidth;
    /**
     * 外箱规格高
     */
    private Double caseHeight;
    /**
     * 单品净重
     */
    private Double singleProductNetWeight;
    /**
     * 单品毛重
     */
    private Double singleProductGrossWeight;
    /**
     * 重量单位
     */
    private String weightUnit;
    /**
     * 单箱毛重
     */
    private Double singleCaseGrossWeight;
    /**
     * 装箱量
     */
    private Integer containerLoad;
    /**
     * 币种，暂时只有人民币和美元，以后可能需要建字典维护
     */
    private String currency;
    /**
     * 采购交期
     */
    private String purchaseDate;
    /**
     * 是否含税
     */
    private Integer taxes;
    /**
     * 单价
     */
    private Double price;
    /**
     * 含税单价
     */
    private Double priceWithTaxes;
    /**
     * 备注
     */
    private String remarks;

    private String id;

    private String status;

    private String createBy;

    private String updateBy;

    private Date createDate;

    private Date updateDate;
}
