package com.purchase.purchase_server.mq;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

@Service
@RocketMQMessageListener(
        topic = "TestTopic",
        consumerGroup = "my-consumer-group",
        consumeMode = ConsumeMode.CONCURRENTLY // 或者 ORDERLY
)
@Slf4j
public class TestMqConsumer implements RocketMQListener<String> {

    @Override
    public void onMessage(String message) {
        // TODO: 业务处理
        log.info("Receive message: " + message);
        // TODO: 业务处理
    }
}

