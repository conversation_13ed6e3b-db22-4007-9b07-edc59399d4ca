package com.product.product_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.product.product_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @Description Spu相关商品信息
 * <AUTHOR>
 * @Date 2023/12/15 20:47
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_spu_product")
public class SpuProductDO extends BaseEntity {

    private String spu;

    private String id;

    private String spuProductName;

    private String remarks;
}
