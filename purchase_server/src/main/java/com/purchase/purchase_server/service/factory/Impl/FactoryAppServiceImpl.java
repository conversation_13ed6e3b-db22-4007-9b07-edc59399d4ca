package com.purchase.purchase_server.service.factory.Impl;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.enums.UserPostEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.consts.FactoryDefaultConstants;
import com.purchase.purchase_server.entity.dto.factory.*;
import com.purchase.purchase_server.entity.form.UserParams;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.UserInteriorVO;
import com.purchase.purchase_server.entity.vo.UserSearchVo;
import com.purchase.purchase_server.service.factory.IFactoryAppService;
import com.purchase.purchase_server.service.factory.IFactoryContainerService;
import com.purchase.purchase_server.service.factory.IFactoryInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import static com.crafts_mirror.utils.constant.SystemConstant.SYS_USER_INFO;

/**
 * @Description 应用服务，负责领域的组合、编排、转发、转换和传递
 * <AUTHOR>
 * @Date 2024/6/6 17:15
 **/
@Service
@Slf4j
public class FactoryAppServiceImpl implements IFactoryAppService {


    @Resource
    private IFactoryInfoService factoryInfoService;

    @Resource
    private IFactoryContainerService factoryContainerService;

    @Resource
    private FactoryDefaultConstants factoryDefaultConstants;

    @Resource
    protected RestTemplate restTemplate;

    @Override
    public void importFactoryApp(InputStream file, byte[] fileBytes, String fileName) {
        factoryInfoService.importFactoryExcel(file, fileBytes, fileName, new LogTrackNumDto());
    }

    @Override
    public FactoryDetailDataDto queryDetail(FactoryInfoQuery query) {
        return factoryInfoService.queryDetail(query);
    }

    @Override
    public IPage<FactoryInfoIPageDto> pageList(FactoryDataPageQuery query) {
        return factoryInfoService.pageList(query);
    }

    @Override
    public Boolean insertFactory(FactoryDataCommand factoryDataCommand) {
        return factoryInfoService.insertFactoryData(factoryDataCommand, new LogTrackNumDto());
    }

    @Override
    public Boolean deleteInfo(String factoryInfoId) {
        return factoryInfoService.deleteInfo(factoryInfoId);
    }

    @Override
    public Boolean deleteFactoryContainer(String val) {
        return factoryContainerService.deleteByFactoryInfoId(FactoryContainerDto.builder().id(val).build());
    }

    @Override
    public List<UserSearchVo> getUserSet(UserParams params) {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO resultDTO = restTemplateUtil.post(params, ResultDTO.class, SYS_USER_INFO);
        List userList = JSON.to(List.class, resultDTO.getData());
        List<UserSearchVo> buyerSearchVoList = new ArrayList<>();

        userList.forEach(i -> {
            UserInteriorVO userInteriorVO = JSON.to(UserInteriorVO.class, i);
            buyerSearchVoList.add(UserSearchVo.builder().userName(userInteriorVO.getUserName()).nickName(userInteriorVO.getNickName()).build());
        });

        // 当postId等于PURCHASER时，如果没有userName等于purchaser的数据，将其加到列表第一个位置
        if (UserPostEnum.PURCHASER.getCode().equals(params.getPostId())) {
            String purchaser = factoryDefaultConstants.getPurchaser();
            String purchaserName = factoryDefaultConstants.getPurchaserName();
            boolean hasPurchaser = buyerSearchVoList.stream()
                    .anyMatch(item -> item.getUserName().equals(purchaser));
            if (!hasPurchaser && StrUtil.isNotBlank(purchaser) && StrUtil.isNotBlank(purchaserName)) {
                buyerSearchVoList.addFirst(UserSearchVo.builder()
                        .userName(purchaser)
                        .nickName(purchaserName)
                        .build());
            }
        }
        // 当postId等于MERCHANDISER时，如果没有userName等于orderTracker的数据，将其加到列表第一个位置
        if (UserPostEnum.MERCHANDISER.getCode().equals(params.getPostId())) {
            String orderTracker = factoryDefaultConstants.getOrderTracker();
            String orderTrackerName = factoryDefaultConstants.getOrderTrackerName();
            boolean hasOrderTracker = buyerSearchVoList.stream()
                    .anyMatch(item -> item.getUserName().equals(orderTracker));
            if (!hasOrderTracker && StrUtil.isNotBlank(orderTracker) && StrUtil.isNotBlank(orderTrackerName)) {
                buyerSearchVoList.addFirst(UserSearchVo.builder()
                        .userName(orderTracker)
                        .nickName(orderTrackerName)
                        .build());
            }
        }
        return buyerSearchVoList;
    }

    @Override
    public List<UserSearchVo> getBuyerSet() {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        UserParams params = new UserParams();
        params.setPostId(UserPostEnum.PRODUCT_MANAGER.getCode());
        ResultDTO resultDTO = restTemplateUtil.post(params, ResultDTO.class, SYS_USER_INFO);
        List userList = JSON.to(List.class, resultDTO.getData());
        List<UserSearchVo> buyerSearchVoList = new ArrayList<>();
        userList.forEach(i -> {
            UserInteriorVO userInteriorVO = JSON.to(UserInteriorVO.class, i);
            buyerSearchVoList.add(UserSearchVo.builder().userName(userInteriorVO.getUserName()).nickName(userInteriorVO.getNickName()).build());
        });
        return buyerSearchVoList;
    }

    @Override
    public Boolean updateBuyerFromSelfProduct() {
        factoryInfoService.updateBuyerFromSelfProduct();
        return Boolean.TRUE;
    }
}
