package com.purchase.purchase_server.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Description 补货计划
 * <AUTHOR>
 * @Date 2024/1/18 11:56
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepProjectDto {

    private String virtualPurchaseId;
    private List<String> trialPurchaseList;
}
