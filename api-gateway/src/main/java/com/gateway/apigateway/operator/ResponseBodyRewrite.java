package com.gateway.apigateway.operator;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gateway.apigateway.config.CustomizedException;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import org.springframework.cloud.gateway.filter.factory.rewrite.RewriteFunction;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * @author: 雪竹
 * @description: 重写返回体类
 * @dateTime: 2023/11/24 15:23
 **/
@Slf4j
public class ResponseBodyRewrite implements RewriteFunction<String, String> {

    private final ObjectMapper objectMapper;

    public ResponseBodyRewrite(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public Publisher<String> apply(ServerWebExchange exchange, String body) {
        try {
            Map<String, Object> map = objectMapper.readValue(body, Map.class);

            // 取得id
            int userId = Integer.parseInt(map.get("user-id").toString());

            // 添加一个key/value
            map.put("gateway-response-tag", userId + "-" + System.currentTimeMillis());

            return Mono.just(objectMapper.writeValueAsString(map));
        } catch (Exception ex) {
            log.error("2. json process fail", ex);
            return Mono.error(new CustomizedException(HttpStatus.BAD_REQUEST, "400", ex.getMessage()));
        }
    }
}
