package com.sales_server.model.channel.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sales_server.model.channel.entity.dto.ChannelSearchDto;
import com.sales_server.model.channel.entity.form.ChannelSearchPageForm;
import com.sales_server.model.channel.entity.form.SaveChannelForm;
import com.sales_server.model.channel.entity.vo.ChannelPageVo;
import com.sales_server.model.channel.entity.vo.ChannelSearchVo;
import com.sales_server.model.channel.entity.vo.NicheChannelDetailVo;
import com.sales_server.model.channel.repository.ChannelInfoRepository;
import com.sales_server.model.channel.service.IChannelInfoService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 渠道信息service层
 * <AUTHOR>
 * @Date 2025/4/30 15:26
 **/
@Service
public class ChannelInfoServiceImpl implements IChannelInfoService {

    @Resource
    private ChannelInfoRepository channelInfoRepository;

    @Override
    public void saveSaleChannel(SaveChannelForm form) {
        checkSaleChannelNameExisted(form.getChannelName());
        channelInfoRepository.saveSaleChannel(form.getChannelName());
    }

    @Override
    public void saveNicheChannel(SaveChannelForm form) {
        if (StrUtil.isBlank(form.getParentChannelId())) {
            throw new IllegalArgumentException("销售渠道不能为空");
        }
        checkNicheChannelNameExisted(form);
        checkNicheChannelSortExisted(form);
        checkSaleChannelIdExisted(form.getParentChannelId());
        channelInfoRepository.saveNicheChannel(form);
    }

    @Override
    public void checkSaleChannelNameExisted(String saleChannel) {
        if (StrUtil.isBlank(saleChannel)) {
            throw new IllegalArgumentException("销售渠道不能为空");
        }

        if (saleChannel.length() > 10) {
            throw new IllegalArgumentException("销售渠道最多只允许填10个字符");
        }

        if (channelInfoRepository.checkSaleChannelNameExisted(saleChannel)) {
            throw new IllegalArgumentException("销售渠道重复！");
        }
    }

    @Override
    public void checkNicheChannelNameExisted(SaveChannelForm form) {
        if (StrUtil.isBlank(form.getChannelName())) {
            throw new IllegalArgumentException("细分渠道不能为空");
        }

        if (form.getChannelName().length() > 10) {
            throw new IllegalArgumentException("销售渠道最多只允许填10个字符");
        }

        if (channelInfoRepository.checkNicheChannelNameExisted(form)) {
            throw new IllegalArgumentException("细分渠道重复！");
        }
    }

    @Override
    public void checkNicheChannelSortExisted(SaveChannelForm form) {
        if (form.getSort() == null) {
            throw new IllegalArgumentException("排序值不能为空");
        }
        if (channelInfoRepository.checkNicheChannelSortExisted(form)) {
            throw new IllegalArgumentException("排序值重复！");
        }
    }

    @Override
    public void checkSaleChannelIdExisted(String parentId) {
        boolean b = channelInfoRepository.checkSaleChannelIdExisted(parentId);
        if (!b) {
            throw new IllegalArgumentException("销售渠道不存在");
        }
    }

    @Override
    public ChannelSearchVo getChannelList(int isLeaf) {
        return ChannelSearchVo.builder().channelSearchDtoList(channelInfoRepository.getAllChannelByLeaf(isLeaf)).build();
    }

    @Override
    public IPage<ChannelPageVo> getChannelPage(ChannelSearchPageForm form) {
        return channelInfoRepository.getChannelPage(form);
    }

    @Override
    public NicheChannelDetailVo getNicheChannelDetail(String nicheChannelId) {
        return channelInfoRepository.getNicheChannelDetail(nicheChannelId);
    }

    @Override
    public Map<String, String> getChannelIdNameMap(int isLeaf) {
        ChannelSearchVo channelSearchVo = getChannelList(isLeaf);
        List<ChannelSearchDto> channelSearchList = channelSearchVo.getChannelSearchDtoList();
        if (CollectionUtil.isEmpty(channelSearchList)) {
            return new HashMap<>();
        }
        return channelSearchList.stream().collect(Collectors.toMap(ChannelSearchDto::getChannelId, ChannelSearchDto::getChannelName));
    }

    public Map<String, String> getChildParentNameMap() {
        ChannelSearchPageForm form = new ChannelSearchPageForm();
        List<ChannelPageVo> allNicheChannel = channelInfoRepository.getChannelList(form);
        if (CollectionUtil.isEmpty(allNicheChannel)) {
            return new HashMap<>();
        }

        return allNicheChannel.stream().collect(Collectors.toMap(c -> c.getNicheChannelName().toUpperCase(Locale.ROOT), ChannelPageVo::getSaleChannelName));
    }
}
