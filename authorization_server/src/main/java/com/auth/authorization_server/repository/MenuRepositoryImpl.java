package com.auth.authorization_server.repository;

import cn.hutool.core.util.StrUtil;
import com.auth.authorization_server.enums.MenuStatusEnum;
import com.auth.authorization_server.enums.MenuTypeEnum;
import com.auth.authorization_server.enums.RoleStatusEnum;
import com.auth.authorization_server.mapper.MenuMapper;
import com.auth.authorization_server.model.dataObject.*;
import com.auth.authorization_server.req.MenuParams;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MenuRepositoryImpl extends ServiceImpl<MenuMapper, MenuDO> {

    public List<MenuDO> selectMenuList(MenuParams dto) {
        return baseMapper.selectList(Wrappers.<MenuDO>lambdaQuery()
                .like(StrUtil.isNotBlank(dto.getMenuName()), MenuDO::getMenuName, dto.getMenuName())
                .eq(StrUtil.isNotBlank(dto.getMenuStatus()), MenuDO::getMenuStatus, dto.getMenuStatus())
                .orderByAsc(MenuDO::getParentId)
                .orderByDesc(MenuDO::getOrderNum)
                .orderByAsc(MenuDO::getMenuName)
        );
    }

    public List<MenuDO> selectMenuListByUserId(MenuParams dto, String userId) {
        return baseMapper.selectJoinList(MenuDO.class, new MPJLambdaWrapper<MenuDO>()
                .distinct()
                .selectAll(MenuDO.class)
                .leftJoin(RoleMenuDO.class, RoleMenuDO::getMenuId, MenuDO::getId)
                .leftJoin(UserRoleDO.class, UserRoleDO::getRoleId, RoleMenuDO::getRoleId)
                .leftJoin(RoleDO.class, RoleDO::getId, UserRoleDO::getRoleId)
                .eq(UserRoleDO::getUserId, userId)
                .like(StrUtil.isNotBlank(dto.getMenuName()), MenuDO::getMenuName, dto.getMenuName())
                .eq(StrUtil.isNotBlank(dto.getMenuStatus()), MenuDO::getMenuStatus, dto.getMenuStatus())
                .orderByAsc(MenuDO::getParentId)
                .orderByDesc(MenuDO::getOrderNum)
                .orderByAsc(MenuDO::getMenuName)

        );
    }

    public List<MenuDO> selectMenuTreeAll() {
        return baseMapper.selectList(Wrappers.<MenuDO>lambdaQuery()
                .in(MenuDO::getMenuType, MenuTypeEnum.TYPE_DIR.getCode(), MenuTypeEnum.TYPE_MENU.getCode())
                .eq(MenuDO::getMenuStatus, MenuStatusEnum.YES_STATUS.getCode())
                .orderByAsc(MenuDO::getParentId)
                .orderByDesc(MenuDO::getOrderNum)
                .orderByAsc(MenuDO::getMenuName)
        );
    }

    public List<MenuDO> selectMenuTreeByUserId(String userId) {
        return baseMapper.selectJoinList(MenuDO.class, new MPJLambdaWrapper<MenuDO>()
                .distinct()
                .selectAll(MenuDO.class)
                .leftJoin(RoleMenuDO.class, RoleMenuDO::getMenuId, MenuDO::getId)
                .leftJoin(UserRoleDO.class, UserRoleDO::getRoleId, RoleMenuDO::getRoleId)
                .leftJoin(RoleDO.class, RoleDO::getId, UserRoleDO::getRoleId)
                .leftJoin(UserDO.class, UserDO::getId, UserRoleDO::getUserId)
                .eq(UserDO::getId, userId)
                .in(MenuDO::getMenuType, MenuTypeEnum.TYPE_DIR.getCode(), MenuTypeEnum.TYPE_MENU.getCode())
                .eq(MenuDO::getMenuStatus, MenuStatusEnum.YES_STATUS.getCode())
                .eq(RoleDO::getRoleStatus, RoleStatusEnum.YES_STATUS.getCode())
                .orderByAsc(MenuDO::getParentId)
                .orderByDesc(MenuDO::getOrderNum)
                .orderByAsc(MenuDO::getMenuName)
        );

    }

    public List<String> selectMenuListByRoleId(String roleId) {
        return baseMapper.selectJoinList(String.class, new MPJLambdaWrapper<MenuDO>()
                .select(MenuDO::getId)
                .leftJoin(RoleMenuDO.class, RoleMenuDO::getMenuId, MenuDO::getId)
                .eq(RoleMenuDO::getRoleId, roleId)
                .orderByAsc(MenuDO::getParentId)
                .orderByDesc(MenuDO::getOrderNum)
                .orderByAsc(MenuDO::getMenuName)
        );
    }

    public MenuDO checkMenuNameUnique(String menuName, String parentId) {
        return baseMapper.selectOne(Wrappers.<MenuDO>lambdaQuery()
                .eq(MenuDO::getMenuName, menuName)
                .eq(MenuDO::getParentId, parentId)
        );
    }

    public Long hasChildByMenuId(String menuId) {
        return baseMapper.selectCount(Wrappers.<MenuDO>lambdaQuery()
                .eq(MenuDO::getParentId, menuId));
    }

    public List<MenuDO> selectChildByMenuId(List<String> menuIds) {
        return baseMapper.selectList(Wrappers.<MenuDO>lambdaQuery()
                .in(MenuDO::getParentId, menuIds));
    }

    /**
     * 根据角色ID查询权限
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    public List<String> selectMenuPermsByRoleId(String roleId) {
        return baseMapper.selectJoinList(String.class, new MPJLambdaWrapper<MenuDO>()
                .distinct()
                .select(MenuDO::getPerms)
                .leftJoin(RoleMenuDO.class, RoleMenuDO::getMenuId, MenuDO::getId)
                .eq(MenuDO::getMenuStatus, MenuStatusEnum.YES_STATUS.getCode())
                .eq(RoleMenuDO::getRoleId, roleId));
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    public List<String> selectMenuPermsByUserId(String userId) {
        return baseMapper.selectJoinList(String.class, new MPJLambdaWrapper<MenuDO>()
                .distinct()
                .select(MenuDO::getPerms)
                .leftJoin(RoleMenuDO.class, RoleMenuDO::getMenuId, MenuDO::getId)
                .leftJoin(UserRoleDO.class, UserRoleDO::getRoleId, RoleMenuDO::getRoleId)
                .leftJoin(RoleDO.class, RoleDO::getId, UserRoleDO::getRoleId)
                .eq(MenuDO::getMenuStatus, MenuStatusEnum.YES_STATUS.getCode())
                .eq(RoleDO::getRoleStatus, RoleStatusEnum.YES_STATUS.getCode())
                .eq(UserRoleDO::getUserId, userId));
    }

    public List<MenuDO> selectMenuTreeListByUrlAndPermission(String url, String permission) {
        return baseMapper.selectMenuTreeListByUrlAndPermission(url, permission);
    }
}