package com.purchase.purchase_server.entity.vo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 发货详情页列表接口返回数据
 * <AUTHOR>
 * @Date 2025/1/8 14:39
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryPageListVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private IPage<DeliveryPurchaseListVo> deliveryPurchaseListVo;

    private DeliveryPurchaseCountVo deliveryPurchaseCount;
}
