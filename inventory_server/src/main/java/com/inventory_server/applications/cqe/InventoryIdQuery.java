package com.inventory_server.applications.cqe;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/10
 **/
@Data
public class InventoryIdQuery implements Serializable {
    @Serial
    private static final long serialVersionUID = -2304525724073512789L;
    @NotBlank(message = "库存信息id不能为空")
    private String inventoryInfoId;

    private String type;
}
