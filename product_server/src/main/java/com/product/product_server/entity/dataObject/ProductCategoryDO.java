package com.product.product_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.product.product_server.entity.BaseIdAutoEntity;
import com.product.product_server.enums.IsLeafEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * 产品分类表
 * @TableName cm_product_category
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_product_category")
public class ProductCategoryDO extends BaseIdAutoEntity {

    @Serial
    private static final long serialVersionUID = 8635674508851656910L;
    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * 父级id
     */
    private Integer parentId;

    /**
     * 祖籍列表
     */
    private String ancestors;

    /**
     * '是否为叶子节点：0-否，1-是'
     * @see IsLeafEnum
     */
    private String isLeaf;

    /**
     * 排序
     */
    private Integer categorySort;

}