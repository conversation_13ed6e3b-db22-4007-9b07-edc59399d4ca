package com.purchase.purchase_server.entity.dto.Lcl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 发货数量变更表
 * @TableName cm_lcl_shipping_num_history
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclShippingNumHistoryDTO{
    /**
     * 整理后到仓数据id
     */
    private String lclConsolidationTsiId;

    /**
     * 发货装柜时间
     */
    private String shippingStartDate;

    /**
     * 变更的值
     */
    private Integer changeValue;

    /**
     * 变更类型
     * @see com.purchase.purchase_server.enums.lcl.ChangeTypeEnum
     */
    private String changeType;
}