package com.product.product_server.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.product.product_server.mapper.CurrencyExchangeRateMapper;
import com.product.product_server.model.currency.dos.CurrencyExchangeRateDO;
import org.springframework.stereotype.Service;

/**
 * @Description 汇率持久层接口
 * <AUTHOR>
 * @Date 2025/4/10 17:01
 **/
@Service
public class CurrencyExchangeRateRepository extends ServiceImpl<CurrencyExchangeRateMapper, CurrencyExchangeRateDO> {

    public void saveCurrencyExchangeRate(CurrencyExchangeRateDO exchangeRate) {
        this.saveOrUpdate(exchangeRate, Wrappers.<CurrencyExchangeRateDO>lambdaUpdate()
                .eq(CurrencyExchangeRateDO::getFromCurrency, exchangeRate.getFromCurrency())
                .eq(CurrencyExchangeRateDO::getToCurrency, exchangeRate.getToCurrency())
                .eq(CurrencyExchangeRateDO::getExchangeUpdateDate, exchangeRate.getExchangeUpdateDate())
        );
    }

    public CurrencyExchangeRateDO getNewestCurrencyExchangeRate(String fromCurrency, String toCurrency) {
        return this.getOne(Wrappers.<CurrencyExchangeRateDO>lambdaQuery()
                .eq(CurrencyExchangeRateDO::getFromCurrency, fromCurrency)
                .eq(CurrencyExchangeRateDO::getToCurrency, toCurrency)
                .orderByDesc(CurrencyExchangeRateDO::getExchangeUpdateDate)
                .last("limit 1")
        );
    }
}
