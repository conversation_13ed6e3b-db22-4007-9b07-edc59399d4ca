package com.inventory_server.model.warning.urgentPurchase.entity.form;

import com.inventory_server.model.redundancy.entity.dos.InventorySaleRulesDO;
import com.inventory_server.model.redundancy.entity.dto.DeliveryCalResultDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

/**
 * @Description 加急补货mq消息表单
 * <AUTHOR>
 * @Date 2025/4/21 13:33
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UrgentPurchaseMQForm implements Serializable {

    private String inventoryInfoId;

    private String snapShotId;

    private DeliveryCalResultDto normalDeliveryResultDto;

    private InventorySaleRulesDO salesRules;

    private LocalDate calFinishedDate;

    private Map<String, BigDecimal> targetSalesMap;
}
