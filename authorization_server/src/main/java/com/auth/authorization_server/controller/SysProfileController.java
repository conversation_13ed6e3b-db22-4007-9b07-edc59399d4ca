package com.auth.authorization_server.controller;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.auth.authorization_server.aop.PreventReSubmit;
import com.auth.authorization_server.assembler.UserAssembler;
import com.auth.authorization_server.exception.BaseException;
import com.auth.authorization_server.req.UserParams;
import com.auth.authorization_server.service.ISysUserService;
import com.crafts_mirror.common.security.auth.AuthUtil;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.common.entity.LoginVo;
import com.auth.authorization_server.domain.dto.UserDTO;
import com.crafts_mirror.utils.constant.RedisKeyConstant;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.PatternUtils;
import com.crafts_mirror.utils.utils.RsaUtils;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

/**
 * 个人信息 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/userProfile")
@Slf4j
public class SysProfileController {
    @Resource
    private ISysUserService userService;

    @Resource
    private RedisTemplate<String, LoginVo> redisTemplate;

    @Resource
    private UserAssembler userAssembler;

    /**
     * 个人信息
     */
    @GetMapping("/detail")
    public ResultDTO<UserDTO> profile() {
        String username = SecurityUtils.getUsername();
        UserDTO user = userService.selectUserByUserName(username);
        //UserDTO userDTO = userAssembler.SysUserToDto(user);
        return ResultDTO.success(user);
    }

    /**
     * 修改用户
     */
    @PutMapping("/update")
    @PreventReSubmit
    public ResultDTO updateProfile(@RequestBody UserDTO user) {
        LoginVo loginUser = SecurityUtils.getLoginUser();
        loginUser.setNickName(user.getNickName());
        UserDTO currentUser = userService.selectUserById(loginUser.getUserId());
        currentUser.setNickName(user.getNickName());
        currentUser.setPhoneNumber(user.getPhoneNumber());
        if (StrUtil.isNotBlank(user.getPhoneNumber()) && !userService.checkPhoneUnique(currentUser)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "修改用户'" + loginUser.getUserName() + "'失败，手机号码已存在");
        }
        if (userService.updateUserProfile(currentUser)) {
            // 更新缓存用户信息
            redisTemplate.opsForValue().set(RedisKeyConstant.USER_TOKEN_KEY + loginUser.getUserId() + ":" + SecurityUtils.getToken(), loginUser, 6, TimeUnit.HOURS);
            return ResultDTO.success();
        }
        throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @PutMapping("/update/updatePwd")
    @PreventReSubmit
    public ResultDTO updatePwd(@RequestBody UserParams userParams) throws Exception {
        String username = SecurityUtils.getUsername();
        UserDTO user = userService.selectUserByUserName(username);
        String password = user.getPassWord();

        byte[] decodeOld = Base64.decode(userParams.getOldPassword());
        String decodeOldPassword = RsaUtils.getPassword(decodeOld);

        byte[] decodeNew = Base64.decode(userParams.getNewPassword());
        String decodeNewPassword = RsaUtils.getPassword(decodeNew);

        BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();

        if (!bCryptPasswordEncoder.matches(decodeOldPassword + user.getSalt(), password)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "修改密码失败，旧密码错误");
        }
        if (PatternUtils.PASSWORD_PATTERN.matcher(decodeNewPassword).matches()) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "请输入6-20位字母、数字或以上组合");
        }
        if (decodeOldPassword.equals(decodeNewPassword)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "新密码不能与旧密码相同");
        }

        String passwordEncrypt = new BCryptPasswordEncoder().encode(decodeNewPassword + user.getSalt());
        if (userService.resetUserPwd(username, passwordEncrypt) > 0) {
            AuthUtil.delLoginUser(user.getId());
            return ResultDTO.success();
        }
        throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "修改密码异常，请联系管理员");
    }
}
