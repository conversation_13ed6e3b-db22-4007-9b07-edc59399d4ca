package com.purchase.purchase_server.entity.dataObject;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * @Description 供应商-装柜信息表
 * <AUTHOR>
 * @Date 2024/6/5 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_factory_container")
public class FactoryContainerDO extends BaseEntity {
    /**
     * 省份
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String province;
    /**
     * 城市
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String city;
    /**
     * 详情地址
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String detailedAddress;
    /**
     * 地址代号
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String addressCode;
    /**
     * 默认标识
     */
    private String defaultIdentifier;

    /**
     * 供应商id
     */
    private String factoryInfoId;
}
