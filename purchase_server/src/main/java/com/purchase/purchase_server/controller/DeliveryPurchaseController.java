package com.purchase.purchase_server.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.purchase.purchase_server.aop.PreventReSubmit;
import com.purchase.purchase_server.entity.dto.delivery.DeliverySaleDestinationDto;
import com.purchase.purchase_server.entity.form.DeliveryCalMockInventoryForm;
import com.purchase.purchase_server.entity.form.DeliveryPurchaseForm;
import com.purchase.purchase_server.entity.form.DeliveryRecordPurchaseForm;
import com.purchase.purchase_server.entity.form.DeliveryWatchBoardForm;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.*;
import com.purchase.purchase_server.service.shipping.IDeliveryPurchaseService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.crafts_mirror.utils.constant.RedisKeyConstant.EXPORT_DELIVERY_LOCK;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.IMPORT_DELIVERY_LOCK;

/**
 * @Description 发货列表
 * <AUTHOR>
 * @Date 2023/12/6 10:48
 **/
@RestController
@RequestMapping(value = "deliveryPurchase")
@Slf4j
public class DeliveryPurchaseController {
    @Resource
    private IDeliveryPurchaseService deliveryPurchaseService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @RequiresPermissions("purchase:deliveryPurchase:calculation")
    @PostMapping("/delivery/import")
    public ResultDTO<String> importDeliveryInfo(@RequestParam("file") MultipartFile file,
                                                @RequestParam("shipmentStartDate") String shipmentStartDate,
                                                @RequestParam("shipmentEndDate") String shipmentEndDate) {
        try {
            if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(IMPORT_DELIVERY_LOCK, "1", 7, TimeUnit.HOURS))) {
                log.error("导入发货计划失败，当前有正在计算的发货计划");
                return ResultDTO.error("上一条导入还在分析中，请稍后再试");
            }
            log.warn("导入发货计划-------------准备开始导入日志");
            deliveryPurchaseService.importExcelToDeliveryPurchase(file.getInputStream(), file.getOriginalFilename(), file.getBytes(),
                    shipmentStartDate, shipmentEndDate);
        } catch (IOException e) {
            log.error("获取输入流异常：", e);
            throw new RuntimeException(e);
        }
        return ResultDTO.success("正在导入");
    }

    //发货计划导入模板
    @RequiresPermissions("purchase:deliveryPurchase:calculation")
    @PostMapping("/delivery/template/export")
    public void exportTemplate(HttpServletResponse response) {
        deliveryPurchaseService.exportTemplate(response);
    }

    @RequiresPermissions("purchase:deliveryPurchase:list")
    @PostMapping("/recordPageList")
    @ResponseBody
    public ResultDTO<IPage<DeliveryRecordPurchaseListVo>> deliveryRecordListData(@RequestBody DeliveryRecordPurchaseForm form) {
        IPage<DeliveryRecordPurchaseListVo> deliveryRecordPurchaseListVo = deliveryPurchaseService.recordPageList(form);
        return ResultDTO.success(deliveryRecordPurchaseListVo);
    }

    @RequiresPermissions("purchase:deliveryPurchase:list")
    @PostMapping("/pageList")
    @ResponseBody
    public ResultDTO<DeliveryPageListVo> deliveryListData(@RequestBody DeliveryPurchaseForm form) {

        return ResultDTO.success(DeliveryPageListVo.builder()
                // 列表页数据
                .deliveryPurchaseListVo(deliveryPurchaseService.pageList(form))
                // 计数结果
                .deliveryPurchaseCount(deliveryPurchaseService.shippingCount(form))
                .build());
    }

    /**
     * 发货数量统计
     *
     * @param form 发货搜索统计
     * @return 发货数量统计
     */
    @PostMapping("/delivery/shippingCount")
    @RequiresPermissions("purchase:deliveryPurchase:list")
    public ResultDTO<DeliveryPurchaseCountVo> shippingCount(@RequestBody DeliveryPurchaseForm form) {
        DeliveryPurchaseCountVo deliveryPurchaseCount = deliveryPurchaseService.shippingCount(form);
        return ResultDTO.success(deliveryPurchaseCount);
    }

    @RequiresPermissions("purchase:deliveryPurchase:sourceFile")
    @GetMapping("/fileInfo")
    @ResponseBody
    public ResultDTO<MissionCenterVo> selectFileInfo(@RequestParam("recordId") String recordId) {
        return ResultDTO.success(deliveryPurchaseService.selectFileInfo(recordId));
    }

    /**
     * 发货计划导出
     * @param form
     * @return
     */
    @RequiresPermissions("purchase:deliveryPurchase:downloadResult")
    @PostMapping("/delivery/export")
    public ResultDTO<?> exportDeliveryInfo(@RequestBody DeliveryPurchaseForm form) {
        if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(EXPORT_DELIVERY_LOCK + form.getRecordId(), "1", 7, TimeUnit.HOURS))) {
            return ResultDTO.error(ResponseCodeEnum.BAD_REQUEST, "当前该计划文件生成中，请稍后重试");
        }
        try {
            deliveryPurchaseService.exportDeliveryInfo(form);
        }catch (Exception e) {
            log.error("导出发货计划失败：{}", e.getMessage());
        }

        return ResultDTO.success();
    }

    @RequiresPermissions("purchase:deliveryPurchase:list")
    @PostMapping("/detail/importInfo")
    @ResponseBody
    public ResultDTO<TrialShippingImportInfoDetailVo> getTrialImportInfoDetail(String shippingProjectId) {
        TrialShippingImportInfoDetailVo baseInfo = deliveryPurchaseService.getTrialImportInfoDetail(shippingProjectId);
        return ResultDTO.success(baseInfo);
    }

    @RequiresPermissions("purchase:deliveryPurchase:list")
    @PostMapping("/detail/factoryPlainInfo")
    @ResponseBody
    public ResultDTO<ShippingFactoryPlainInfoVo> getFactoryPlainInfoDetail(String shippingProjectId) {
        ShippingFactoryPlainInfoVo baseInfo = deliveryPurchaseService.getShippingFactoryPlainInfoVo(shippingProjectId);
        return ResultDTO.success(baseInfo);
    }

    @RequiresPermissions("purchase:deliveryPurchase:list")
    @PostMapping("/detail/trialWatchBoard")
    @ResponseBody
    public ResultDTO<TrialWatchBoardVo> getTrialWatchBoard(@RequestBody DeliveryWatchBoardForm form) {
        TrialWatchBoardVo triaWatchBoard = deliveryPurchaseService.getTriaWatchBoard(form);
        return ResultDTO.success(triaWatchBoard);
    }

    @RequiresPermissions("purchase:deliveryPurchase:list")
    @PostMapping("/cal/mockInventory")
    @ResponseBody
    public ResultDTO<MockInventoryVo> calMockInventory(@RequestBody DeliveryCalMockInventoryForm form) {
        MockInventoryVo triaWatchBoard = deliveryPurchaseService.calTrialMockInventory(form);
        return ResultDTO.success(triaWatchBoard);
    }

    @RequiresPermissions("purchase:deliveryPurchase:edit")
    @PostMapping("/delivery/saveAndVoid")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO<Boolean> saveAndVoid(@RequestParam("recordId") String recordId,
                                          @RequestParam("trialStatus") String trialStatus) {
        try {
            return ResultDTO.success(deliveryPurchaseService.updateTrialStatus(recordId, trialStatus));
        } catch (Exception e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR, e.getMessage());
        }
    }

    @GetMapping("/user")
    @ResponseBody
    public ResultDTO<List<UserInteriorVO>> getBuyerSet() {
        return ResultDTO.success(deliveryPurchaseService.getUserList());
    }

    @GetMapping("/list/operator")
    @ResponseBody
    public ResultDTO<List<OperatorSearchVo>> getOperator() {
        return ResultDTO.success(deliveryPurchaseService.getOperator());
    }

    //删除试算失败或作废的数据
    @RequiresPermissions("purchase:deliveryPurchase:delete")
    @DeleteMapping("/delete/record/{recordId}")
    @PreventReSubmit
    public ResultDTO<Boolean> deleteRecord(@PathVariable String recordId) {
        return ResultDTO.success(deliveryPurchaseService.deleteRecord(recordId));
    }

    @RequiresPermissions("purchase:deliveryPurchase:downloadResult")
    @PostMapping("/download/export")
    @PreventReSubmit
    public ResultDTO<DeliveryRecordPurchaseListVo> downloadExport(@RequestBody DeliveryPurchaseForm form) {
        return ResultDTO.success(deliveryPurchaseService.downloadExport(form));
    }

    @RequiresPermissions("purchase:deliveryPurchase:list")
    @PostMapping("/info/detail/saleDestination")
    public ResultDTO<List<DeliverySaleDestinationDto>> getSaleDestination(@RequestBody DeliveryPurchaseForm form) {
        return ResultDTO.success(deliveryPurchaseService.getSaleDestination(form));
    }
}
