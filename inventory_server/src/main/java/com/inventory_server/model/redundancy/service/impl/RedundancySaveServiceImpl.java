package com.inventory_server.model.redundancy.service.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.base.Stopwatch;
import com.inventory_server.applications.dto.FactoryRemainInventoryDto;
import com.inventory_server.model.product.entity.dos.SelfProductDO;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.redundancy.entity.dos.InventorySaleRulesDO;
import com.inventory_server.model.redundancy.entity.dos.RedundantInventoryDO;
import com.inventory_server.model.redundancy.entity.dp.InventoryCalDp;
import com.inventory_server.model.redundancy.entity.dp.InventoryRulesDp;
import com.inventory_server.model.redundancy.entity.dp.RedundancyDp;
import com.inventory_server.model.redundancy.entity.dto.*;
import com.inventory_server.model.redundancy.repository.dataRepository.RedundantInventoryRepository;
import com.inventory_server.model.redundancy.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.util.concurrent.TimeUnit.MILLISECONDS;

/**
 * <AUTHOR>
 * @Date 2024/11/4 16:23
 **/
@Service
@Slf4j
public class RedundancySaveServiceImpl implements IRedundancySaveService {

    @Resource
    private RedundantInventoryRepository redundantInventoryRepository;

    @Resource
    private IInventoryFactoryPlanInfoService inventoryFactoryPlanInfoService;

    @Resource
    private IInventoryForeignStoreService inventoryForeignStoreService;

    @Resource
    private IInventorySaleRulesService inventorySaleRulesService;

    @Resource
    private IInventoryWatchBoardService inventoryWatchBoardService;

    @Resource
    private ILocalWatchBoardService localWatchBoardService;

    @Resource
    private ISoldOutDaysService soldOutDaysService;

    @Resource(name = "normalRedundancyEndCalServiceImpl")
    private IRedundancyEndCalService redundancyEndCalService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public NormalDeliveryInventorySaveDto saveSingleVirtualSkuRedundancyInfo(
            Map.Entry<String, InventoryCalDp> importEntry, DeliveryCalResultDto deliveryCalResultDto, DeliveryCalResultDto normalDeliveryResultDto,
            RedundancyDp foreignRedundancyDp, RedundancyDp onShippingRedundancyDp, RedundancyDp factoryRedundancyDp,
            LocalDate soldOutDate, LocalDate fullLinkSoldOutDate, int redundantDate, List<SoldOutDateBeforeTheoryDto> soldOutDateDtoList,
            Map<String, Integer> headShippingDateMap, String importFileId, List<SoldOutDateBeforeTheoryDto> fullLinkSoldOutDateDtoList,
            SalableDateDto salableDate, SalableDateDto fullLinkSalableDate, AchievementRateDto achievementRateDto
    ) {
        String virtualSku = importEntry.getKey();
        InventoryCalDp inventoryCalDp = importEntry.getValue();
        VirtualProductDO virtualProduct = inventoryCalDp.virtualProduct();
        SelfProductDO selfProductDO = inventoryCalDp.selfProduct();
        int foreignRedundancy = foreignRedundancyDp.getFirstRedundantInventoryInfo().getRedundantNum();
        int onShippingRedundancy = onShippingRedundancyDp.getRedundancyList().stream()
                .mapToInt(m -> Optional.ofNullable(m.getRedundantNum()).orElse(0))
                .sum();
        int factoryRedundancy = factoryRedundancyDp.getFirstRedundantInventoryInfo().getRedundantNum();

        // 保存冗余库存信息
        int soldOutDays = soldOutDateDtoList.stream().mapToInt(SoldOutDateBeforeTheoryDto::getSoldOutDays).sum();
        int fullLinkSoldOutDays = fullLinkSoldOutDateDtoList.stream().mapToInt(SoldOutDateBeforeTheoryDto::getSoldOutDays).sum();

        String endDateDesc = redundancyEndCalService.getEndDateDesc(inventoryCalDp, LocalDate.now(), headShippingDateMap, redundantDate);
        String inventoryInfoId = saveRedundantInventoryInfo(foreignRedundancy, onShippingRedundancy, factoryRedundancy,
                virtualProduct, importFileId, soldOutDays, soldOutDate, endDateDesc, fullLinkSoldOutDate, fullLinkSoldOutDays,
                deliveryCalResultDto.getSnapShotId(), salableDate, fullLinkSalableDate);

        Stopwatch stopwatch = Stopwatch.createStarted();
        // 保存售罄时间
        soldOutDaysService.insertSoldOutDays(soldOutDateDtoList, fullLinkSoldOutDateDtoList, inventoryInfoId);

        // 保存工厂交货信息
        List<DeliveryCalResultFactoryFinishedDto> factoryFinishedResultList = deliveryCalResultDto.getFactoryFinishedResultList();
        var factoryFinishedInventoryList = inventoryFactoryPlanInfoService.saveFactoryPlanInfo(factoryFinishedResultList,
                inventoryInfoId, selfProductDO.getProductName());

        // 保存海外仓在途、库存信息
        List<FactoryRemainInventoryDto> stockList = inventoryCalDp.stockQuantity().list();
        var inventoryForeignStoreList = inventoryForeignStoreService.saveForeignStoreInfo(inventoryInfoId, stockList);

        InventoryRulesDp inventoryRulesDp = inventoryCalDp.inventoryRules();
        Map<String, Double> shippingRatioMap = inventoryRulesDp.shippingRulesDp().shippingRatioMap();
        var intervention = inventorySaleRulesService.calInterventionalTime(inventoryInfoId, shippingRatioMap, virtualProduct.getId());

        // 保存试算规则
        InventorySaleRulesDO rulesDO = inventorySaleRulesService.saveInventorySaleRules(inventoryInfoId, inventoryCalDp, JSON.toJSONString(headShippingDateMap),
                redundantDate, achievementRateDto, intervention);

        // 保存试算看板表格
        // 由于cm_inventory_info的主键不会变更，因此需要删除旧的watchBoard数据
        inventoryWatchBoardService.deleteByInventoryId(inventoryInfoId);
        // 海外仓冗余试算看板
        inventoryWatchBoardService.saveForeignAndShippingWatchBoardInfo(inventoryInfoId, foreignRedundancyDp);
        // 在途冗余试算看板
        inventoryWatchBoardService.saveForeignAndShippingWatchBoardInfo(inventoryInfoId, onShippingRedundancyDp);

        // 工厂冗余试算看板
        localWatchBoardService.saveFactoryWatchBoard(inventoryInfoId, factoryRedundancyDp);
        log.warn("虚拟sku：{}保存耗时1：{}", virtualSku, stopwatch.elapsed(MILLISECONDS));

        return NormalDeliveryInventorySaveDto.builder()
                .inventoryId(inventoryInfoId)
                .factoryFinishedInventoryList(factoryFinishedInventoryList)
                .shippingInventoryList(normalDeliveryResultDto.getShippingInventoryList())
                .headShippingDays(headShippingDateMap)
                .virtualSku(virtualSku)
                .inventoryForeignStoreList(inventoryForeignStoreList)
                .rulesDO(rulesDO)
                .priorDeliveryList(normalDeliveryResultDto.getPriorDeliveryList())
                .build();
    }

    private String saveRedundantInventoryInfo(int foreignRedundancy, int onShippingRedundancy, int factoryRedundancy,
                                              VirtualProductDO virtualProduct, String importFileId, int soldOutDays,
                                              LocalDate soldOutDate, String endDateDesc, LocalDate fullLinkSoldOutDate,
                                              int fullLinkSoldOutDays, String snapshotId, SalableDateDto salableDate,
                                              SalableDateDto fullLinkSalableDate) {
        RedundantInventoryDO redundantInventoryDO = RedundantInventoryDO.builder()
                .virtualSkuId(virtualProduct.getId())
                .snapshotId(snapshotId)
                .foreignRedundantInventory((double) foreignRedundancy)
                .onShippingRedundantInventory(onShippingRedundancy)
                .factoryRedundantInventory((double) factoryRedundancy)
                .importFileId(importFileId)
                .calFinishedDate(LocalDate.now())
                .foreignTheoreticalSoldOutDate(soldOutDate)
                .daysBeforeSoldOut(soldOutDays)
                .fullLinkTheoreticalSoldOutDate(fullLinkSoldOutDate)
                .fullLinkDaysBeforeSoldOut(fullLinkSoldOutDays)
                .fullLinkSalableDays(fullLinkSalableDate.getSalableDays())
                .salableDays(salableDate.getSalableDays())
                .endDateDesc(endDateDesc)
                .isArtificialImport(1)
                .build();
        return redundantInventoryRepository.saveRedundantInventoryInfo(redundantInventoryDO);
    }
}
