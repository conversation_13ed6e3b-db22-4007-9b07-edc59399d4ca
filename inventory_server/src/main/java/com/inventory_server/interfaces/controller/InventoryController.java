package com.inventory_server.interfaces.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.inventory_server.applications.service.IInventoryAppService;
import com.inventory_server.applications.vo.response.ResultDTO;
import com.inventory_server.model.inventory.entity.form.AsyncRealTimeInventoryForm;
import com.inventory_server.model.inventory.entity.form.RealTimeInventoryPageForm;
import com.inventory_server.model.inventory.entity.vo.NonFBARealTimeInventoryVo;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description 实时库存
 * <AUTHOR>
 * @Date 2025/2/18 14:21
 **/
@RestController
@RequestMapping(value = "/realTime")
public class InventoryController {

    @Resource
    private IInventoryAppService inventoryAppService;

    @RequiresPermissions("inventory:realTime:page")
    @PostMapping("/page/nonFBA")
    public ResultDTO<IPage<NonFBARealTimeInventoryVo>> nonFBARealTimeInventoryPage(@RequestBody RealTimeInventoryPageForm form) {
        return ResultDTO.success(inventoryAppService.nonFBARealTimeInventoryPage(form));
    }

    @RequiresPermissions("inventory:realTime:page")
    @PostMapping("/async/nonFBA")
    public ResultDTO<Void> asyncNonFBARealTimeInventory(@RequestBody AsyncRealTimeInventoryForm form) {
        inventoryAppService.asyncNonFBARealTimeInventory(form);
        return ResultDTO.success();
    }
}
