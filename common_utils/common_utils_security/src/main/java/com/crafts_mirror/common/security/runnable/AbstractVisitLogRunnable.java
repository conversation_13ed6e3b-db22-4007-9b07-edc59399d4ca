package com.crafts_mirror.common.security.runnable;

import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.dp.MenuTitleForm;
import com.crafts_mirror.utils.dp.VisitLogForm;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.method.HandlerMethod;

import java.lang.reflect.Method;

import static com.crafts_mirror.utils.constant.SystemConstant.SYS_MENU_TITLE_LIST_URL;
import static com.crafts_mirror.utils.constant.SystemConstant.VISIT_LOGS_SYSTEM_SAVE_LOG_URL;

/**
 * <AUTHOR>
 * @Date 2024/8/19 11:35
 **/
public abstract class AbstractVisitLogRunnable extends refreshSecurity implements Runnable {

    private final Object handler;

    protected final RestTemplate restTemplate = ApplicationContextProvider.getBean(RestTemplate.class);

    public AbstractVisitLogRunnable(Object handler) {
        this.handler = handler;
    }

    public String getVisitLogTitle(String requestUrl) {
        String permission = "";
        if (this.handler instanceof HandlerMethod hm) {
            Method method = hm.getMethod();
            RequiresPermissions authorize = method.getAnnotation(RequiresPermissions.class);
            if (authorize != null) {
                String[] value = authorize.value();
                permission = String.join(",", value);
            }
        }

        // 拿请求url与权限字符去菜单管理中获取菜单名称
        String token = SecurityUtils.getLoginUser().getToken();
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, token);
        ResultDTO<String> postResult = restTemplateUtils.post(MenuTitleForm.builder().url(requestUrl).permission(permission).build(),
                ResultDTO.class, SYS_MENU_TITLE_LIST_URL);
        String menuTitle = postResult.getData();

        return StrUtil.isBlank(menuTitle) ? "未知日志标题" : menuTitle;
    }

    protected void sendVisitLogRequest(VisitLogForm visitLogForm) {
        String token = SecurityUtils.getLoginUser().getToken();
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, token);
        restTemplateUtils.post(visitLogForm, ResultDTO.class, VISIT_LOGS_SYSTEM_SAVE_LOG_URL);
    }
}
