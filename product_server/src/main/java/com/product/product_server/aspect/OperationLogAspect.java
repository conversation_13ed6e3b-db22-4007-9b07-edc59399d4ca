package com.product.product_server.aspect;

import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.dp.LogTrackNumDto;
import com.crafts_mirror.utils.dp.OperationLogForm;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.SystemConstant.OPERATION_LOGS_SYSTEM_SAVE_LOG_URL;

/**
 * @Description 业务日志Aspect层
 * <AUTHOR>
 * @Date 2023/12/13 13:52
 **/
@Component
@Slf4j
@Aspect
public class OperationLogAspect {
    @Resource
    private RestTemplate restTemplate;

    @Pointcut("@annotation(com.crafts_mirror.utils.aop.OperationLog)")
    public void operationLogPointCut() {
    }

    @AfterReturning(value = "operationLogPointCut() && @annotation(operationLog)", returning = "obj")
    public void saveOperationLog(JoinPoint joinPoint, OperationLog operationLog, Object obj) {
        // 获取操作人
        String username = StrUtil.isNotBlank(SecurityUtils.getUsername()) ? SecurityUtils.getUsername() : "系统";
        String operator = StrUtil.isNotBlank(username) ? username : operationLog.operator();

        // 获取追踪号
        Object[] args = joinPoint.getArgs();
        String trackNum = Arrays.stream(args)
                .filter(arg -> arg instanceof LogTrackNumDto)
                .map(arg -> ((LogTrackNumDto) arg).trackNum())
                .findFirst().orElse("");

        // 保存操作日志
        OperationLogForm form = new OperationLogForm(operator, operationLog.content(), operationLog.operationType(),
                trackNum, operator, operator);

        String authorization = Optional.ofNullable(SecurityUtils.getToken()).orElse("系统");
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, authorization);
        restTemplateUtils.post(form, ResultDTO.class, OPERATION_LOGS_SYSTEM_SAVE_LOG_URL);
    }
}
