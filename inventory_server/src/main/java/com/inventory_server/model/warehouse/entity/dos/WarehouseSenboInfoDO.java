package com.inventory_server.model.warehouse.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.BaseIdAutoEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * @Description 森帛仓库信息
 * <AUTHOR>
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_warehouse_senbo_info")
public class WarehouseSenboInfoDO extends BaseIdAutoEntity {

    private String warehouse;

    private Integer headShippingDate;

    private Integer urgentHeadShipDate;

    private Integer sort;

    private String remarks;

}
