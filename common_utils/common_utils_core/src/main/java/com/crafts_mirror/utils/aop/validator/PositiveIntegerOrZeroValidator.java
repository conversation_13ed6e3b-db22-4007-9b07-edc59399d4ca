package com.crafts_mirror.utils.aop.validator;

import cn.hutool.core.util.ObjectUtil;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @date 2024/6/7
 **/
public class PositiveIntegerOrZeroValidator implements ConstraintValidator<PositiveIntegerOrZero, Object> {
    private boolean isRequired;

    @Override
    public void initialize(PositiveIntegerOrZero constraintAnnotation) {
        isRequired = constraintAnnotation.isRequired();
        // 初始化方法，可以留空
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (isRequired && (value == null || ObjectUtil.isEmpty(value))) {
            return false;
        } else if (!isRequired && (value == null || ObjectUtil.isEmpty(value))) {
            return true;
        }
        try {
            String s = String.valueOf(value);
            int number = Integer.parseInt(s);
            return number >= 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}