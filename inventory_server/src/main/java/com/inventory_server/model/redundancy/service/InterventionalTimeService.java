package com.inventory_server.model.redundancy.service;

import com.inventory_server.model.redundancy.entity.dos.InventorySaleRulesDO;
import com.inventory_server.model.redundancy.entity.dto.InterventionalTimeDto;

import java.time.LocalDate;

public interface InterventionalTimeService {

    InterventionalTimeDto calculateInterventionalTime(LocalDate calFinishedDate, InventorySaleRulesDO salesRules);
}
