package com.product.product_server.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.product.product_server.entity.dataObject.FactoryContainerDO;
import com.product.product_server.entity.dataObject.FactoryInfoDO;
import com.product.product_server.entity.dto.FactoryInfoDTO;
import com.product.product_server.enums.FactoryContainerDefaultIdentifierEnum;
import com.product.product_server.mapper.FactoryInfoMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FactoryInfoRepositoryImpl extends ServiceImpl<FactoryInfoMapper, FactoryInfoDO> {

    public List<FactoryInfoDTO> selectFactoryInfoList(List<String> factoryIdList) {
        return baseMapper.selectJoinList(FactoryInfoDTO.class, new MPJLambdaWrapper<FactoryInfoDO>()
                .selectAll(FactoryInfoDO.class)
                .select(FactoryContainerDO::getAddressCode)
                .select(FactoryContainerDO::getProvince)
                .select(FactoryContainerDO::getCity)
                .leftJoin(FactoryContainerDO.class, on -> on
                        .eq(FactoryContainerDO::getFactoryInfoId, FactoryInfoDO::getId)
                        .eq(FactoryContainerDO::getDefaultIdentifier, FactoryContainerDefaultIdentifierEnum.DEFAULT_ADDRESS.getCode()))
                .in(FactoryInfoDO::getId, factoryIdList)
                .orderByAsc(FactoryInfoDO::getFactoryCode));
    }

}
