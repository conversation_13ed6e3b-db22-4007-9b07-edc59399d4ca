package com.inventory_server.model.redundancy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum StockQuantityImportSheetNameEnum {
    STOCKING_RULES("备货规则", "stockingRules"),
    TARGET_SALES("目标日销","targetSales"),
    AM_STOCK_QUANTITY("家具库存概况","amStockQuantity"),
    LAMP_STOCK_QUANTITY("灯具库存概况","lampStockQuantity"),
    STOCK_QUANTITY("在途","stockQuantity"),
    DELIVERY("计划","delivery");



    private final String code;
    private final String desc;
    public static StockQuantityImportSheetNameEnum ofCode(String code) {
        return Arrays.stream(StockQuantityImportSheetNameEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
    public static List<String> getCodeList() {
        return Arrays.stream(StockQuantityImportSheetNameEnum.values())
                .map(StockQuantityImportSheetNameEnum::getCode).collect(Collectors.toList());
    }
}
