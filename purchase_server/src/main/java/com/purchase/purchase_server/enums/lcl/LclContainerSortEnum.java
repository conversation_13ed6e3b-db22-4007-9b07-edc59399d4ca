package com.purchase.purchase_server.enums.lcl;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/27 9:11
 **/
@Getter
@AllArgsConstructor
public enum LclContainerSortEnum {
    SHIPPING_QUANTITY("shipping_quantity", "发货数量"),
    SHIPPING_VOLUME("shipping_volume", "发货体积"),
    CONTAINER_LOADING_RATE("container_loading_rate", "容积装载率"),
    SURPLUS_VOLUME("surplus_volume", "盈余体积"),
    SHIPPING_WEIGHT("shipping_weight", "发货重量"),
    INSPECTION_VOLUME_RATIO("inspection_volume_ratio", "商检体积占比"),

    ASC("asc","升序"),
    DESC("desc","降序");

    private final String code;
    private final String desc;
    public static LclContainerSortEnum ofCode(String code) {
        return Arrays.stream(LclContainerSortEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
    public static List<String> getCodeList() {
        return Arrays.stream(LclContainerSortEnum.values())
                .map(LclContainerSortEnum::getCode).collect(Collectors.toList());
    }
}
