package com.inventory_server.model.warning.unsalable.service;

import com.inventory_server.model.warning.unsalable.entity.dos.WarningFactoryUnsalableInventoryDO;
import com.inventory_server.model.warning.unsalable.entity.dos.WarningForeignUnsalableInventoryDO;

import java.util.List;
import java.util.Map;

public interface IUnsalableInventorySaveService {

    void saveUnsalableInventory(String inventoryId, int totalInventory, Map<String, WarningFactoryUnsalableInventoryDO> factoryUnsalableMap,
                                List<WarningForeignUnsalableInventoryDO> unsalableInventoryList);

    void deleteUnsalableInventoryByRedundancyInventoryId(String inventoryInfoId);

}
