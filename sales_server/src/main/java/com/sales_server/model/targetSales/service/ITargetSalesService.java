package com.sales_server.model.targetSales.service;

import com.crafts_mirror.utils.dp.CalculationForm;
import com.sales_server.infrastructures.entity.LogTrackNumDto;
import com.sales_server.model.targetSales.entity.dto.TargetDaySalesDto;
import com.sales_server.model.targetSales.entity.form.DaySalesEditForm;
import com.sales_server.model.targetSales.entity.form.TargetDaySalesDetailForm;
import com.sales_server.model.targetSales.entity.form.TargetMonthSalesPageForm;
import com.sales_server.model.targetSales.entity.vo.TargetMonthSalesVo;
import jakarta.servlet.http.HttpServletResponse;

import java.io.InputStream;
import java.util.List;
import java.util.Set;

public interface ITargetSalesService {

    Set<String> importTargetSalesInfo(InputStream file, String fileId);

    boolean editTargetSalesInfo(DaySalesEditForm form);

    List<TargetDaySalesDto> getTargetDaySalesInfo(TargetDaySalesDetailForm form);

    void exportTargetMonthSales(List<TargetMonthSalesVo> targetMonthSalesList, HttpServletResponse response, TargetMonthSalesPageForm form);

    void deleteTargetDaySales(String virtualSkuId, LogTrackNumDto logTrackNumDto);

    void createPartitionByYear();

    void prepareCalculationTargetSales(CalculationForm form);
}
