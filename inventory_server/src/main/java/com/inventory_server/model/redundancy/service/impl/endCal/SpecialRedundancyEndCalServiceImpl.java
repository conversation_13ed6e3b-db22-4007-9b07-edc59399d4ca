package com.inventory_server.model.redundancy.service.impl.endCal;

import com.inventory_server.model.redundancy.entity.dp.InventoryCalDp;
import com.inventory_server.model.redundancy.service.IRedundancyEndCalService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Map;

/**
 * @Description 特殊的冗余库存计算截止时间
 * <AUTHOR>
 * @Date 2025/2/5 16:20
 **/
@Service(value = "specialRedundancyEndCalServiceImpl")
public class SpecialRedundancyEndCalServiceImpl implements IRedundancyEndCalService {

    /**
     * 时间写死，可发版更改
     */
    @Override
    public LocalDate calRedundancyEndDate(InventoryCalDp inventoryCalDp, LocalDate startDate, Map<String, Integer> headShippingDateMap, int redundantDateNum) {
        return LocalDate.of(2025, 8, 31);
    }

    @Override
    public String getEndDateDesc(InventoryCalDp inventoryCalDp, LocalDate startDate, Map<String, Integer> headShippingDateMap, int redundantDateNum) {
        return "";
    }
}
