package com.purchase.purchase_server.entity.dataObject;


import com.baomidou.mybatisplus.annotation.*;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

/**
 * @TableName cm_replenishment_virtual_sku_purchase
 */
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName(value = "cm_replenishment_virtual_sku_purchase")
public class ReplenishmentVirtualSkuPurchaseDO extends BaseEntity {

    /**
     * 虚拟sku
     */
    private String virtualSkuId;

    /**
     * 海外仓数据情况原始数据
     */
    private String overseasOriginalData;

    /**
     * 海外仓库存
     */
    private String overseasInventory;

    /**
     * 海外仓在途
     */
    private String overseasShipping;

    /**
     * 目标日销
     */
    private String saleDestination;

    /**
     * 折后目标日销
     */
    private String discountSaleDestination;

    /**
     * 运营确认日销
     */
    private String operatorSaleDestination;

    /**
     * 实际日销
     */
    private Double actualDailySalesNum;
    /**
     * 试算当月目标日销
     */
    private Double targetSalesNum;
    /**
     * 子体达成率
     */
    private Double subEntityRate;
    /**
     * 父体达成率
     */
    private Double parentEntityRate;
    /**
     * 本地库存
     */
    private String localInventory;
    /**
     * 预计售罄时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private LocalDate expectedSoldOutDate;

    /**
     * 断货日
     */
    private String soldOutDate;

    /**
     * 补货状态（0：正常；1：需补货）
     * @see com.purchase.purchase_server.enums.ViReplenishmentStatusEnum
     */
    private String replenishmentStatus;

    /**
     * 补货状态原因
     * @see com.purchase.purchase_server.enums.ReplenishmentStatusReasonEnum
     */
    private String reason;
    /**
     * 运营确认量原因
     */
    @TableField(fill = FieldFill.UPDATE)
    private String confirmedReason;

    /**
     * 运营补充说明
     */
    @TableField(fill = FieldFill.UPDATE)
    private String operatorRemark;

    /**
     * 采购计划id
     */
    private String replenishmentProjectId;
    /**
     * 补货规则id
     */
    private String rulesId;
    /**
     * 快照记录id
     */
    private String productSnapshotId;
}