package com.product.product_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelProperty;
import com.product.product_server.convert.CommodityInspectionConverter;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 是否商检excel
 * <AUTHOR>
 * @Date 2025/4/29 13:31
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommodityInspectionExcel extends BaseExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = -5438509348233432L;

    @ExcelProperty("*SKU")
    private String selfSku;

    @ExcelProperty(value = "*是否商检", converter = CommodityInspectionConverter.class)
    private Integer commodityInspection;
}
