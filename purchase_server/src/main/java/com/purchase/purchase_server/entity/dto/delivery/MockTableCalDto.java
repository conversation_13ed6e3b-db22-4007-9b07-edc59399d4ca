package com.purchase.purchase_server.entity.dto.delivery;

import cn.hutool.core.date.DateTime;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.service.impl.CommonConsumptionServiceImpl;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * {@link CommonConsumptionServiceImpl#calDeliveryResults}
 * <AUTHOR>
 * @Date 2025/5/23 10:52
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MockTableCalDto {
    private List<FactoryRemainInventoryDto> inventoryDtos;
    private int calCircle;
    private List<FactoryRemainInventoryDto> factoryRemainInventoryList;
    private Map<String, Integer> headShippingDateMap;
    private DateTime projectCreateDate;
    private List<FactoryRemainInventoryDto> remainInventoryListForCal;
    private List<FactoryRemainInventoryDto> importFactoryRemainInventoryList;
    private List<FactoryRemainInventoryDto> priorDeliveryList;
    private int minShippingDate;
    private Map<String, Double> targetSalesMap;
    private Map<String, Double> inventorySaleDefRatio;
    private List<String> sortedWarehouseList;
}
