package com.crafts_mirror.utils.constant;

/**
 * @Description 时间格式常量类
 * <AUTHOR>
 * @Date 2024/3/8 13:14
 **/
public class DateFormatConstant {
    public static final String YYYY_MM_DD_DATE_FORMAT_SLASH = "yyyy/MM/dd";

    public static final String YYYY_M_D_DATE_FORMAT_SLASH = "yyyy/M/d";

    public static final String YYYY_MM_DD_DATE_FORMAT_HYPHEN = "yyyy-MM-dd";

    public static final String YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN = "yyyy-MM-dd HH:mm:ss";

    public static final String YYYY_MM_DD_H_M_DATE_FORMAT_HYPHEN = "yyyy-MM-dd H:m";

    public static final String YYYY_M_D_HH_MM_SS_DATE_FORMAT_SLASH = "yyyy/M/d HH:mm:ss";

    public static final String YYYY_MM_DD_DATE_FORMAT_CHINESE = "yyyy年MM月dd日";

    public static final String YYYY_M_DATE_FORMAT_CHINESE = "yyyy年M月";

    public static final String YYYY_MM_DATE_FORMAT_SLASH = "yyyy/MM";


    public static final String YY_MM_DD_DATE_FORMAT_CHINESE = "yyMMdd";
    public static final String YY_MM_DD_DATE_FORMAT_DOT = "yy.MM.dd";

}
