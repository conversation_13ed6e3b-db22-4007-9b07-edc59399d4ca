package com.auth.authorization_server.model.dataObject;

import com.auth.authorization_server.enums.DataPermissionTypeEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户和数据权限关联表 cm_sys_role_data_permission
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cm_sys_role_data_permission")
public class RoleDataPermissionDO implements Serializable {
    /**
     * 角色id
     */
    private String roleId;

    /**
     * user_id,dept_id,module_id
     */
    private String dataPermissionId;

    /**
     * 1-用户，2-部门，3-模块功能
     * @see DataPermissionTypeEnum
     */
    private String type;

}
