package com.purchase.purchase_server.entity.vo.Lcl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/25 13:56
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LclContainerInfoPage<T> extends Page<T> {

    @JsonFormat(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN)
    private String shippingStartDate;

    @JsonFormat(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN)
    private String shippingEndDate;

    /**
     * 是否整理过
     */
    private String isLclConsolidation;
    /**
     * 判断处理中
     */
    private Boolean isDataClean;

    /**
     * 状态
     */
    private String lclStatus;
}
