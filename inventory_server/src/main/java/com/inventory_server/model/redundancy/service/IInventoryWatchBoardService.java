package com.inventory_server.model.redundancy.service;

import com.inventory_server.model.redundancy.entity.bo.InventoryOnShippingRedundantBO;
import com.inventory_server.model.redundancy.entity.dos.InventoryForeignShipWatchBoardDO;
import com.inventory_server.model.redundancy.entity.dos.InventoryLocalWatchBoardDO;
import com.inventory_server.model.redundancy.entity.dp.RedundancyDp;

import java.util.List;

public interface IInventoryWatchBoardService {

    List<InventoryForeignShipWatchBoardDO> getForeignAndShipInventoryWatchBoardByInfoId(String infoId);

    List<InventoryForeignShipWatchBoardDO> getForeignAndShipInventoryWatchBoardByInfoIds(List<String> infoIds);

    List<InventoryLocalWatchBoardDO> getFactoryInventoryWatchBoardByInfoId(String infoId);

    boolean saveForeignAndShippingWatchBoardInfo(String inventoryId, RedundancyDp foreignRedundancy);

    void deleteByInventoryId(String inventoryId);

    List<InventoryOnShippingRedundantBO> getOnShippingRedundantInfoList(List<String> infoIdList);

}
