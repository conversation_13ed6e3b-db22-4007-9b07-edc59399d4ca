package com.inventory_server.applications.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/11
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DetailInventoryWatchBoardDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 6213688051856322060L;

    private String id;
    private String inventoryInfoId;
    private LocalDate arrivingDate;
    private Integer arrivingNum;
    private LocalDate judgeDate;
    private Double initialInventory;
    private Double safeInventory;
    private Integer initialTotalRedundancy;
    private Integer redundantNum;
    private Integer redundantType;
    private List<String> shipmentCodeList;
}
