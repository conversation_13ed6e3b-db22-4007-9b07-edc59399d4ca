package com.purchase.purchase_server.entity.dataObject;


import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * @Description 模拟数据表
 * <AUTHOR>
 * @Date 2024/1/6 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_mock_inventory_table")
public class MockInventoryDO extends BaseEntity {
    /**
     * 模拟在途库存
     */
    private String onshippingInventory;
    /**
     * 模拟剩余库存
     */
    private String remainInventory;
    /**
     * 模拟日销
     */
    private String everydaySale;
    /**
     * 试算id
     */
    private String shippingProjectId;
    /**
     * 规则Id
     */
    private String rulesId;
}
