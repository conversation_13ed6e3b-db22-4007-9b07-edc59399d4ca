package com.product.product_server.controller.interiorController;

import com.product.product_server.entity.response.ResultDTO;
import com.product.product_server.entity.vo.SenboWarehouseVo;
import com.product.product_server.service.IVirtualProductService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/9/14
 **/
@RestController
@RequestMapping(value = "/interior/shippingRatio")
@Slf4j
public class ShippingRatioInteriorController {

    @Resource
    private IVirtualProductService virtualProductService;

    @GetMapping("/{virtualId}")
    public ResultDTO<SenboWarehouseVo> getVirtualShippingRatio(@PathVariable String virtualId) {
        return ResultDTO.success(virtualProductService.getVirtualShippingRatio(virtualId));
    }
}
