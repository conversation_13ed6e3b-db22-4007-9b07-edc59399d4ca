package com.inventory_server.model.redundancy.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import com.inventory_server.model.redundancy.entity.dos.InventorySaleRulesDO;
import com.inventory_server.model.redundancy.mapper.InventorySaleRulesMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 海外仓库存持久层repository类
 * <AUTHOR>
 * @Date 2024/5/10 17:12
 **/
@Service
public class InventorySaleRulesRepository extends ServiceImpl<InventorySaleRulesMapper, InventorySaleRulesDO> {

    public List<InventorySaleRulesDO> getInventorySaleByInfoIds(List<String> infoIds) {
        return baseMapper.selectList(Wrappers.<InventorySaleRulesDO>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(infoIds), InventorySaleRulesDO::getInventoryInfoId, infoIds)
        );
    }

    public InventorySaleRulesDO getInventorySaleByInfoId(String infoId) {
        return baseMapper.selectOne(Wrappers.<InventorySaleRulesDO>lambdaQuery()
                .eq(InventorySaleRulesDO::getInventoryInfoId, infoId)
        );
    }

    public void deleteInventorySaleByInfoId(String inventoryInfoId) {
        List<InventorySaleRulesDO> list = baseMapper.selectList(Wrappers.<InventorySaleRulesDO>lambdaQuery()
                .eq(InventorySaleRulesDO::getInventoryInfoId, inventoryInfoId));
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<String> idList = list.stream().map(PhysicalBaseEntity::getId).toList();
        baseMapper.deleteByIds(idList);
    }

    public List<InventorySaleRulesDO> getAllInventorySaleRulesList() {
        return baseMapper.selectList(Wrappers.lambdaQuery());
    }

    public List<InventorySaleRulesDO> getAllRatioBiggerThanZeroWarehouseSaleRules(String warehouse) {
        return baseMapper.selectList(Wrappers.<InventorySaleRulesDO>lambdaQuery()
                .apply(String.format("sale_ratio->> '$.\"%s\"' > 0", warehouse)));
    }
}
