/*
 Navicat Premium Data Transfer

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 80035
 Source Host           : localhost:3306
 Source Schema         : crafts_mirror

 Target Server Type    : MySQL
 Target Server Version : 80035
 File Encoding         : 65001

 Date: 21/02/2024 13:58:09
*/

ALTER TABLE cm_self_product CHANGE COLUMN factory_code  factory_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工厂代号';

UPDATE cm_self_product
    INNER JOIN cm_factory_info
    ON cm_self_product.factory_id = cm_factory_info.factory_code AND cm_factory_info.status = '0'
SET cm_self_product.factory_id = cm_factory_info.id;


ALTER TABLE cm_virtual_product CHANGE COLUMN self_product_sku self_product_sku_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '自定义产品的skuId';
ALTER TABLE cm_virtual_product CHANGE COLUMN spu spu_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL;

UPDATE cm_virtual_product
    INNER JOIN cm_self_product
    ON cm_virtual_product.self_product_sku_id = cm_self_product.sku AND cm_self_product.status = '0'
SET cm_virtual_product.self_product_sku_id = cm_self_product.id;

UPDATE cm_virtual_product
    INNER JOIN cm_spu_product
    ON cm_virtual_product.spu_id = cm_spu_product.spu AND cm_spu_product.status = '0'
SET cm_virtual_product.spu_id = cm_spu_product.id;

ALTER TABLE cm_shipping_project CHANGE COLUMN destination_sku  virtual_sku_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '虚拟skuId';

UPDATE cm_shipping_project
    INNER JOIN cm_virtual_product
    ON cm_shipping_project.virtual_sku_id = cm_virtual_product.virtual_sku AND cm_virtual_product.status = '0'
SET cm_shipping_project.virtual_sku_id = cm_virtual_product.id;

ALTER TABLE cm_factory_finished_inventory CHANGE COLUMN destination_sku  virtual_sku_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '虚拟skuId';

ALTER TABLE cm_factory_finished_inventory ADD COLUMN is_old_status char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'sku类型(0-虚拟sku 1-老sku)';

UPDATE cm_factory_finished_inventory
    INNER JOIN cm_virtual_product
    ON cm_factory_finished_inventory.virtual_sku_id = cm_virtual_product.virtual_sku and cm_virtual_product.status = '0'
SET cm_factory_finished_inventory.virtual_sku_id = cm_virtual_product.id,
    cm_factory_finished_inventory.is_old_status = '0';

UPDATE cm_factory_finished_inventory
    INNER JOIN cm_virtual_product
    ON cm_factory_finished_inventory.virtual_sku_id = cm_virtual_product.old_sku and cm_virtual_product.status = '0'
SET cm_factory_finished_inventory.virtual_sku_id = cm_virtual_product.id,
    cm_factory_finished_inventory.is_old_status = '1';

ALTER TABLE cm_prepare_products_rules CHANGE COLUMN virtual_sku  virtual_sku_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '虚拟skuId';

UPDATE cm_prepare_products_rules
    INNER JOIN cm_virtual_product
    ON cm_prepare_products_rules.virtual_sku_id = cm_virtual_product.virtual_sku AND cm_virtual_product.status = '0'
SET cm_prepare_products_rules.virtual_sku_id = cm_virtual_product.id;

ALTER TABLE cm_replenishment_project CHANGE COLUMN self_sku self_sku_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '自定义产品的skuId';

UPDATE cm_replenishment_project
    INNER JOIN cm_self_product
    ON cm_replenishment_project.self_sku_id = cm_self_product.sku AND cm_self_product.status = '0'
SET cm_replenishment_project.self_sku_id = cm_self_product.id;

ALTER TABLE cm_replenishment_rules CHANGE COLUMN virtual_sku  virtual_sku_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '虚拟skuId';

UPDATE cm_replenishment_rules
    INNER JOIN cm_virtual_product
    ON cm_replenishment_rules.virtual_sku_id = cm_virtual_product.virtual_sku AND cm_virtual_product.status = '0'
SET cm_replenishment_rules.virtual_sku_id = cm_virtual_product.id;

ALTER TABLE cm_replenishment_virtual_sku_purchase CHANGE COLUMN destination_sku  virtual_sku_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '虚拟skuId';

UPDATE cm_replenishment_virtual_sku_purchase
    INNER JOIN cm_virtual_product
    ON cm_replenishment_virtual_sku_purchase.virtual_sku_id = cm_virtual_product.virtual_sku AND cm_virtual_product.status = '0'
SET cm_replenishment_virtual_sku_purchase.virtual_sku_id = cm_virtual_product.id;