package com.purchase.purchase_server.service.replenishment;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentRecordDO;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentSoldOutDaysDO;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentVirtualSkuPurchaseDO;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.AchievementRateDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectBaseParamDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentProjectSaveDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentRecordDto;
import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentStatusReasonDto;
import com.purchase.purchase_server.entity.form.ReplenishmentDetailForm;
import com.purchase.purchase_server.entity.form.ReplenishmentProjectForm;
import com.purchase.purchase_server.entity.vo.MissionCenterVo;
import com.purchase.purchase_server.entity.vo.ReplenishmentRecordPurchaseListVo;
import com.purchase.purchase_server.entity.vo.ReplenishmentRecordStatusVo;
import com.purchase.purchase_server.model.purchase.ReplenishDeliveryRangeAndNumDp;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【cm_replenishment_project】的数据库操作Service
 * @createDate 2024-01-17 10:45:51
 */
public interface IReplenishmentProjectService {
    void importExcelToDeliveryPurchase(String advicePurchaseStartDate, String advicePurchaseEndDate, InputStream inputStream, String fileName, byte[] byteArrayResource) throws IOException;

    void exportTemplate(HttpServletResponse response);

    ReplenishmentRecordDO getRecordCalculating();

    IPage<ReplenishmentRecordPurchaseListVo> recordPageList(ReplenishmentProjectForm form);

    ReplenishmentRecordStatusVo pageList(ReplenishmentProjectForm form);

    MissionCenterVo selectFileInfo(String recordId);

    /**
     * 保存所有补货计划数据
     *
     * @param projectSaveDto     补货计划信息
     * @param virtualProductDO   虚拟sku产品
     * @param achievementRateDto 子父体达成率
     */
    void saveReplenishmentProjectInfo(ReplenishmentProjectSaveDto projectSaveDto,
                                      VirtualProductDO virtualProductDO, AchievementRateDto achievementRateDto);

    ReplenishmentStatusReasonDto getReplenishmentStatusReason(AchievementRateDto achievementRateDto, VirtualProductDO virtualSkuDO,
                                                              Map<String, Map<String, ReplenishDeliveryRangeAndNumDp>> everydayReplenishmentMap,
                                                              ShippingProjectBaseParamDto shippingRules, boolean isTest);
    void updateReCalReplenishmentProjectInfo(ReplenishmentProjectSaveDto projectSaveDto, VirtualProductDO virtualProductDO,
                                             AchievementRateDto achievementRateDto, ReplenishmentVirtualSkuPurchaseDO virRepDO);

    /**
     * 某件商品的生产周期
     *
     * @param produceDayList 生产周期
     * @param nowDate        确定生产周期的月份
     * @return 生产周期
     */
    default int getTargetDateFactoryProduceDays(List<String> produceDayList, DateTime nowDate, int purchaseProjectDays) {
        if (produceDayList.size() == 1) {
            return Integer.parseInt(produceDayList.getFirst());
        }
        int month = DateUtil.monthEnum(nowDate.offsetNew(DateField.DAY_OF_YEAR, purchaseProjectDays)).getValue();
        return Integer.parseInt(produceDayList.get(month));
    }

    Boolean deleteRecord(String recordId);

    Boolean updateAdvicePurchaseDate(ReplenishmentRecordDto val);

    Boolean updateAdvicePurchaseDateAll();

    List<ReplenishmentSoldOutDaysDO> getBeforeSoldOutList(ReplenishmentProjectForm form);

    void reCalReplenishment(ReplenishmentDetailForm form);

    void refreshSaleDestination();
}
