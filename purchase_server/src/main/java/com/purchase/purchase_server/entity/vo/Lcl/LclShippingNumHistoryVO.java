package com.purchase.purchase_server.entity.vo.Lcl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

/**
 * 发货数量变更
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclShippingNumHistoryVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 7251812744371012361L;
    /**
     * 整理后到仓数据id
     */
    private String lclConsolidationTsiId;

    /**
     * 发货装柜时间
     */
    private String shippingStartDate;

    /**
     * 变更的值
     */
    private String changeValue;

    /**
     * 变更类型
     * @see com.purchase.purchase_server.enums.lcl.ChangeTypeEnum
     */
    private String changeType;
}