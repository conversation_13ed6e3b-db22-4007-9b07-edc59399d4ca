package com.inventory_server.model.redundancy.service;

import com.inventory_server.applications.dto.InventoryFactoryRedundantInfoDto;
import com.inventory_server.model.redundancy.entity.dos.InventoryFactoryRedundantInfoDO;

import java.util.List;

public interface IInventoryFactoryRedundantInfoService {

    List<InventoryFactoryRedundantInfoDO> getFactoryRedundantList(String infoId);

    List<InventoryFactoryRedundantInfoDto> getFactoryRedundantInfoList(List<String> infoIdList);

    void batchSaveFactoryRedundantInfo(List<InventoryFactoryRedundantInfoDO> list);

    void deleteByWatchBoardIds(List<String> watchBoardIds);
}
