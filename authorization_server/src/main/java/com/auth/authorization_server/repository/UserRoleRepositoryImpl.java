package com.auth.authorization_server.repository;

import com.auth.authorization_server.mapper.UserRoleMapper;
import com.auth.authorization_server.model.dataObject.UserRoleDO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class UserRoleRepositoryImpl extends ServiceImpl<UserRoleMapper, UserRoleDO> {

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    public long countUserRoleByRoleId(String roleId) {
        return baseMapper.selectCount(Wrappers.<UserRoleDO>lambdaQuery()
                .eq(UserRoleDO::getRoleId, roleId));
    }

    /**
     * 批量取消授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要删除的用户数据ID
     */
    public void deleteUserRoleInfos(String roleId, List<String> userIds) {
        baseMapper.delete(Wrappers.<UserRoleDO>lambdaQuery()
                .eq(UserRoleDO::getRoleId, roleId)
                .in(UserRoleDO::getUserId, userIds));
    }

    /**
     * 通过用户ID删除用户和角色关联
     *
     * @param userId 用户ID
     */
    public void deleteUserRoleByUserId(String userId) {
        baseMapper.delete(Wrappers.<UserRoleDO>lambdaQuery()
                .eq(UserRoleDO::getUserId, userId));
    }

}
