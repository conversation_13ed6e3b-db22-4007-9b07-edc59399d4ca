package com.inventory_server.interfaces.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.utils.common.entity.MissionCenterVo;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.inventory_server.model.file.entity.form.FileMissionPageForm;
import com.inventory_server.model.file.service.IFileCenterService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * @Description 文档中心controller层
 * <AUTHOR>
 * @Date 2025/2/18 14:48
 **/
@RestController
@RequestMapping(value = "/file")
public class FileCenterExteriorController {

    @Resource
    private IFileCenterService fileCenterService;

    @PostMapping("pageList/inventory/realTime")
    @ResponseBody
    @RequiresPermissions("inventory:realTime:page")
    public ResultDTO<IPage<MissionCenterVo>> getMissionPage(@RequestBody FileMissionPageForm form) {
        return ResultDTO.success(fileCenterService.getMissionPage(form));
    }
}
