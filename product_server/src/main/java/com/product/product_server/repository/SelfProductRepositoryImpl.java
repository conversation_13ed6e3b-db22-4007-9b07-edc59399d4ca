package com.product.product_server.repository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crafts_mirror.common.security.dataPermission.DataPermission;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.dp.LogTrackNumDto;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.product.product_server.entity.dataObject.*;
import com.product.product_server.entity.dto.SelfProductDto;
import com.product.product_server.entity.excelObject.SelfProductInfoExportExcel;
import com.product.product_server.entity.form.SelfProductForm;
import com.product.product_server.enums.PCSTypeEnum;
import com.product.product_server.mapper.SelfProductMapper;
import com.product.product_server.model.products.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.SecurityConstants.ROLE_DATA_PER_SELF;

/**
 * <AUTHOR>
 */
@Service
public class SelfProductRepositoryImpl extends ServiceImpl<SelfProductMapper, SelfProductDO> {

    public IPage<SelfProductDto> pageList(SelfProductForm form) {
        IPage<SelfProductDto> page = new Page<>(form.getCurrent(), form.getSize());
        try {
            String productName = form.getProductName();
            String buyer = form.getBuyer();
            String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_SELF, "t");
            return this.baseMapper.selectJoinPage(page, SelfProductDto.class,
                    new MPJLambdaWrapper<SelfProductDO>()
                            .selectAll(SelfProductDO.class)
                            .select(FactoryInfoDO::getFactoryCode)
                            .leftJoin(FactoryInfoDO.class, FactoryInfoDO::getId, SelfProductDO::getFactoryId)
                            .leftJoin(ProductCategoryDO.class, ProductCategoryDO::getId, SelfProductDO::getCategoryId)
                            .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), SelfProductDO::getSku, form.getSelfSkuList())
                            .like(StrUtil.isNotBlank(productName), SelfProductDO::getProductName, StrUtil.isBlank(productName) ? productName : productName.strip())
                            .eq(StrUtil.isNotBlank(buyer), SelfProductDO::getBuyer, StrUtil.isBlank(buyer) ? buyer : buyer.strip())
                            .eq(StrUtil.isNotBlank(form.getFactoryCode()), FactoryInfoDO::getFactoryCode, Optional.ofNullable(form.getFactoryCode()).map(String::strip).orElse(null))
                            // 品类暂时只查父级
                            .eq(StrUtil.isNotBlank(form.getCategoryId()), ProductCategoryDO::getParentId, form.getCategoryId())
                            .eq(form.getCommodityInspection() != null, SelfProductDO::getCommodityInspection, form.getCommodityInspection())
                            .eq(form.getPcsType() != null, SelfProductDO::getPcsType, form.getPcsType())
                            .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
                            .orderByDesc(SelfProductDO::getCreateDate));
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public SelfProductDO getOneBySelfSku(String selfSku) {
        return this.baseMapper.selectOne(Wrappers.<SelfProductDO>lambdaQuery().eq(SelfProductDO::getSku, selfSku));
    }

    public SelfProductDO productDetail(String productId) {
        return this.baseMapper.selectById(productId);
    }

    public SelfProductDO productDetailBySelfSku(String selfSkuId) {
        return this.baseMapper.selectOne(Wrappers.<SelfProductDO>lambdaQuery().eq(SelfProductDO::getId, selfSkuId));
    }



    public String saveSelfProduct(ImportSkuInfoDp importSkuInfo, FactoryInfoDO factoryInfoDO, SpecificationDp specification,
                                  PurchaseInfoDp purchaseInfo, CaseWeightInfoDp caseWeightInfo, String productName,
                                  String selfRemarks, String image, String category, Integer commodityInspection,
                                  PCSTypeEnum pcsTypeEnum) {
        String sku = importSkuInfo.sku();
        SelfProductDO selfProduct = convertSelfProduct(importSkuInfo, factoryInfoDO, specification, purchaseInfo, caseWeightInfo,
                productName, selfRemarks, image, category, commodityInspection, pcsTypeEnum);
        SelfProductDO existedDo = baseMapper.selectOne(Wrappers.<SelfProductDO>lambdaQuery().eq(SelfProductDO::getSku, sku));
        if (existedDo != null) {
            updateSelfProductByProductId(selfProduct, existedDo.getId());
        } else {
            insertSelfProduct(selfProduct);
        }
        return selfProduct.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public int saveRemarks(RemarksSaveDp saveDp) {
        return this.baseMapper.update(
                Wrappers.<SelfProductDO>lambdaUpdate()
                        .set(SelfProductDO::getRemarks, saveDp.remarks())
                        .eq(SelfProductDO::getId, saveDp.id())
        );
    }

    public SelfProductDO getSelfProductBySkuId(String skuId) {
        Objects.requireNonNull(skuId, "自定义sku不能为空");
        return this.baseMapper.selectOne(Wrappers.<SelfProductDO>lambdaQuery().eq(SelfProductDO::getId, skuId));
    }

    public int deleteSelfProduct(String selfProductId) {
        return this.baseMapper.deleteById(selfProductId);
    }

    private void updateSelfProductByProductId(SelfProductDO selfProduct, String productId) {
        selfProduct.setId(productId);
        this.baseMapper.updateById(selfProduct);
    }

    private void insertSelfProduct(SelfProductDO selfProduct) {
        this.baseMapper.insert(selfProduct);
    }

    private SelfProductDO convertSelfProduct(ImportSkuInfoDp importSkuInfo, FactoryInfoDO factoryInfoDO, SpecificationDp specification,
                                             PurchaseInfoDp purchaseInfo, CaseWeightInfoDp caseWeightInfo, String productName,
                                             String remarks, String image, String category, Integer commodityInspection,
                                             PCSTypeEnum pcsTypeEnum) {
        BigDecimal bigDecimal = purchaseInfo.priceDp().priceWithTax();
        Double taxPrice = null;
        if (bigDecimal != null) {
            taxPrice = bigDecimal.doubleValue();
        }

        return SelfProductDO.builder()
                .sku(importSkuInfo.sku())
                .image(image)
                .productName(productName)
                .categoryId(Integer.parseInt(category))
                .buyer(purchaseInfo.buyer())
                .factoryId(factoryInfoDO.getId())
                .caseLength(specification.singleLength())
                .caseHeight(specification.singleHeight())
                .caseWidth(specification.singleWidth())
                .singleCaseGrossWeight(caseWeightInfo.caseGrossWeight())
                .weightUnit(caseWeightInfo.weightUnit())
                .containerLoad(caseWeightInfo.containerLoad())
                .currency(purchaseInfo.currency())
                .purchaseDate(purchaseInfo.purchaseDate().purchaseDate())
                .taxes(purchaseInfo.priceDp().isTaxes())
                .price(purchaseInfo.priceDp().price().doubleValue())
                .priceWithTaxes(taxPrice)
                .remarks(remarks)
                .commodityInspection(commodityInspection)
                .pcsType(pcsTypeEnum.getCode())
                .build();
    }

    public List<SelfProductInfoExportExcel> getExportInfo(SelfProductForm form) {
        String productName = form.getProductName();
        String buyer = form.getBuyer();
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_SELF, "t");
        return this.baseMapper.selectJoinList(SelfProductInfoExportExcel.class, new MPJLambdaWrapper<SelfProductDO>()
                .select(SelfProductDO::getSku)
                .select(SelfProductDO::getImage)
                .select(SelfProductDO::getProductName)
                .select(SelfProductDO::getBuyer)
                .select(FactoryInfoDO::getFactoryCode)
                .select(VirtualProductDO::getChannel)
                .select(SpuProductDO::getSpu)
                .select(SpuProductDO::getSpuProductName)
                .select(VirtualProductDO::getOldSku)
                .select(VirtualProductDO::getVirtualSku)
                .select(VirtualProductDO::getOperator)
                .select(VirtualProductDO::getProductType)
                .select(VirtualProductDO::getProductStatus)
                .select(VirtualProductDO::getSubType)
                .select(VirtualProductDO::getBorrowingStrategy)
                .select(SelfProductDO::getCaseLength)
                .select(SelfProductDO::getCaseWidth)
                .select(SelfProductDO::getCaseHeight)
                .select(SelfProductDO::getWeightUnit)
                .select(SelfProductDO::getSingleCaseGrossWeight)
                .select(SelfProductDO::getContainerLoad)
                .select(SelfProductDO::getCurrency)
                .select(SelfProductDO::getPurchaseDate)
                .select(SelfProductDO::getTaxes)
                .select(SelfProductDO::getPrice)
                .select(SelfProductDO::getPriceWithTaxes)
                .select(SelfProductDO::getCommodityInspection)
                .select(SelfProductDO::getCategoryId)
                .selectAs(SelfProductDO::getRemarks, "selfRemarks")
                .selectAs(VirtualProductDO::getRemarks, "virtualRemarks")
                .leftJoin(VirtualProductDO.class, VirtualProductDO::getSelfProductSkuId, SelfProductDO::getId)
                .leftJoin(SpuProductDO.class, SpuProductDO::getId, VirtualProductDO::getSpuId)
                .leftJoin(FactoryInfoDO.class, FactoryInfoDO::getId, SelfProductDO::getFactoryId)
                .leftJoin(ProductCategoryDO.class, ProductCategoryDO::getId, SelfProductDO::getCategoryId)
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), SelfProductDO::getSku, form.getSelfSkuList())
                .like(StrUtil.isNotBlank(productName), SelfProductDO::getProductName, StrUtil.isBlank(productName) ? productName : productName.strip())
                .eq(StrUtil.isNotBlank(buyer), SelfProductDO::getBuyer, StrUtil.isBlank(buyer) ? buyer : buyer.strip())
                .eq(form.getPcsType() != null, SelfProductDO::getPcsType, form.getPcsType())
                .eq(form.getCommodityInspection() != null, SelfProductDO::getCommodityInspection, form.getCommodityInspection())
                .eq(StrUtil.isNotBlank(form.getFactoryCode()), FactoryInfoDO::getFactoryCode, Optional.ofNullable(form.getFactoryCode()).map(String::strip).orElse(null))
                // 品类暂时只查父级
                .eq(StrUtil.isNotBlank(form.getCategoryId()), ProductCategoryDO::getParentId, form.getCategoryId())
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
                .orderByDesc(SelfProductDO::getCreateDate));
    }

    @OperationLog(content = "更新商检信息", operationType = "自定义产品编辑")
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(SelfProductDO entity, LogTrackNumDto trackNumDto) {
        super.saveOrUpdate(entity);
    }
}
