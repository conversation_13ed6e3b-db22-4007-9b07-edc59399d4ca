package com.purchase.purchase_server.entity.dto.factory;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/15
 **/
@Data

public class FactoryDataCommand implements Serializable {


    @Serial
    private static final long serialVersionUID = 5985832100993089014L;

    @NotNull(message = "供应商信息不能为空")
    @Valid
    private FactoryInfoDto factoryInfoDto;
    @Valid
    private List<FactoryContainerDto> factoryContainerDtoList;
    @Valid
    @NotNull(message = "财务信息不能为空")
    private FactoryFinancialDto factoryFinancialDto;
}
