ALTER TABLE `crafts_mirror`.`cm_replenishment_virtual_sku_purchase`
    DROP COLUMN `confirmed_reason`;

ALTER TABLE `crafts_mirror`.`cm_replenishment_virtual_sku_purchase`
    ADD COLUMN `confirmed_reason` varchar(20) NULL COMMENT '运营确认原因' AFTER `reason`;

ALTER TABLE `crafts_mirror`.`cm_replenishment_project`
    ADD INDEX `replenishment_record_id`(`replenishment_record_id`) USING BTREE;

ALTER TABLE `crafts_mirror`.`cm_replenishment_virtual_sku_purchase`
    ADD INDEX `replenishment_project_id`(`replenishment_project_id`) USING BTREE;

ALTER TABLE `crafts_mirror`.`cm_factory_finished_inventory`
    ADD INDEX `shipping_project_id`(`shipping_project_id`) USING BTREE;