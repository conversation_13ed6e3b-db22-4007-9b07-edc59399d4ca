package com.inventory_server.model.redundancy.enums;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Map;
import java.util.function.BiFunction;

import static java.math.RoundingMode.CEILING;
import static java.math.RoundingMode.HALF_UP;

/**
 * 根据需求计算头程天数
 */
public enum ShippingDateEnum {
    MIN_HEAD_SHIPPING_DATE("-1", (headShippingDateMap, saleRationMap) ->
            headShippingDateMap.entrySet().stream()
                    .filter(f -> saleRationMap.containsKey(f.getKey()) && saleRationMap.get(f.getKey()) > 0)
                    .min(Map.Entry.comparingByValue())
                    .orElseThrow(() -> new RuntimeException("缺少发货比例或头程时间为空"))
                    .getValue()),
    MAX_HEAD_SHIPPING_DATE("1", (headShippingDateMap, saleRationMap) ->
            headShippingDateMap.entrySet().stream()
                    .filter(f -> saleRationMap.containsKey(f.getKey()) && saleRationMap.get(f.getKey()) > 0)
                    .max(Map.Entry.comparingByValue())
                    .orElseThrow(() -> new RuntimeException("缺少发货比例或头程时间为空"))
                    .getValue()),
    AVG_HEAD_SHIPPING_DATE("0", (headShippingDateMap, saleRationMap) -> {
        double sum = headShippingDateMap.entrySet().stream()
                .filter(f -> saleRationMap.containsKey(f.getKey()) && saleRationMap.get(f.getKey()) > 0)
                .mapToDouble(m -> {
                    String warehouse = m.getKey();
                    Double ratio = saleRationMap.get(warehouse);
                    return BigDecimal.valueOf(m.getValue() * ratio).setScale(3, HALF_UP).doubleValue();
                })
                .sum();
        return BigDecimal.valueOf(sum).setScale(0, CEILING).intValue();
    }
    );
    private final String code;
    private final BiFunction<Map<String, Integer>, Map<String, Double>, Integer> biFunction;

    ShippingDateEnum(String code, BiFunction<Map<String, Integer>, Map<String, Double>, Integer> biFunction) {
        this.code = code;
        this.biFunction = biFunction;
    }

    public static Integer getHeadShippingDateByCode(String code, Map<String, Integer> headShippingDateMap, Map<String, Double> saleRationMap) {
        ShippingDateEnum shippingDateEnum = Arrays.stream(values())
                .filter(f -> f.code.equals(code))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("没有该头程时间获取方式"));
        return shippingDateEnum.biFunction.apply(headShippingDateMap, saleRationMap);
    }
}
