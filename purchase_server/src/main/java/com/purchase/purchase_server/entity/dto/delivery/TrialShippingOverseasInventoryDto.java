package com.purchase.purchase_server.entity.dto.delivery;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Getter;

import java.io.Serializable;
import java.util.Map;

/**
 * @Description 实时库存
 * <AUTHOR>
 * @Date 2024/1/9 19:47
 **/
@Getter
public class TrialShippingOverseasInventoryDto implements Serializable {

    private Integer totalInventory;

    private Map<String, Double> inventoryMap;

    public TrialShippingOverseasInventoryDto() {
    }

    public TrialShippingOverseasInventoryDto(Map<String, Double> inventoryMap) {
        this.inventoryMap = inventoryMap;
        if (CollectionUtil.isEmpty(inventoryMap)) {
            totalInventory = 0;
        } else {
            this.totalInventory = inventoryMap.values().stream().mapToInt(Double::intValue).sum();
        }
    }
}
