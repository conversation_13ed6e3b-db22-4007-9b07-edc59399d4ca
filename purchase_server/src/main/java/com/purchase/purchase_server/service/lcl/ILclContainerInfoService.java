package com.purchase.purchase_server.service.lcl;

import com.purchase.purchase_server.entity.dto.Lcl.LclContainerInfoDto;
import com.purchase.purchase_server.entity.form.LclContainerDetailDeleteForm;
import com.purchase.purchase_server.entity.form.LclContainerMoveForm;
import com.purchase.purchase_server.entity.form.LclSearchPageForm;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cm_lcl_consolidation_record(拼柜装柜记录表)】的数据库操作Service
* @createDate 2025-05-27 16:47:06
*/
public interface ILclContainerInfoService {

    /**
     * 导出拼柜后数据
     */
    void exportLclContainerLoadingInfo(LclSearchPageForm form, HttpServletResponse response);

    /**
     * 移动或拆分柜子数据
     * @param form 移动/拆分表单
     * @return 操作结果
     */
    boolean moveOrSplitContainerData(LclContainerMoveForm form);

    String checkMoveOrSplitContainerData(LclContainerMoveForm form);

    String getShipmentCode(LclContainerMoveForm form);

    List<LclContainerInfoDto> shipmentCodeList(LclContainerMoveForm form);

    Boolean deleteLclContainerDetail(LclContainerDetailDeleteForm form);
}
