package com.purchase.purchase_server.repository.dataRepository.Lcl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.bo.Lcl.LclFinishedInventoryBO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclFinishedInventoryDO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclTrialShippingInventoryDO;
import com.purchase.purchase_server.entity.dataObject.ProductSnapshotDO;
import com.purchase.purchase_server.entity.dto.Lcl.LclConsolidationNonFinishedInventoryDTO;
import com.purchase.purchase_server.entity.form.InteriorInfoQuery;
import com.purchase.purchase_server.entity.form.LclConsolidationForm;
import com.purchase.purchase_server.entity.form.LclConsolidationRecordForm;
import com.purchase.purchase_server.entity.vo.Lcl.LclConsolidationFinishedInventoryVO;
import com.purchase.purchase_server.mapper.Lcl.LclFinishedInventoryMapper;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class LclFinishedInventoryRepositoryImpl extends ServiceImpl<LclFinishedInventoryMapper, LclFinishedInventoryDO> {

    public List<LclFinishedInventoryBO> listWithTrial(LclConsolidationForm form) {

        return baseMapper.listWithTrial(form.getLclRecordId(), form.getShippingStartDate());
    }

    public List<LclConsolidationFinishedInventoryVO> pageList(LclConsolidationRecordForm form) {
        return this.baseMapper.selectJoinList(LclConsolidationFinishedInventoryVO.class, new MPJLambdaWrapper<LclFinishedInventoryDO>()
                        .select(LclFinishedInventoryDO::getId)
                        .selectAs(LclTrialShippingInventoryDO::getId, "lclConsolidationTrialId")
                        .select("t2.factory_data->>'$.factoryCode' as factory_code")
                        .select("t2.factory_data->>'$.addressCode' as address_code")

                        .select(LclFinishedInventoryDO::getContractCode)
                        .select(LclFinishedInventoryDO::getShipmentCode)
                        .select(LclFinishedInventoryDO::getDeliveryType)
                        .select("t2.self_data->>'$.image' as image")
                        .select("t2.self_data->>'$.productName' as product_name")
                        .select("t2.self_data->>'$.sku' as self_sku")
                        .select("t2.self_data->>'$.commodityInspection' as commodity_inspection")
                        .select("t2.self_data->>'$.pcsType' as pcs_type")
                        .select("t2.self_data->>'$.categoryId' as category")

                        .select(ProductSnapshotDO::getSelfSkuId)
                        .select("t2.self_data->>'$.caseLength' as case_length")
                        .select("t2.self_data->>'$.caseWidth' as case_width")
                        .select("t2.self_data->>'$.caseHeight' as case_height")
                        .select("t2.self_data->>'$.singleCaseGrossWeight' as single_case_gross_weight")
                        .select("t2.virtual_data->>'$.productStatus' as product_status")

                        .select("CASE t.is_old_status WHEN '1' THEN t2.virtual_data->>'$.oldSku' WHEN '0' THEN t2.virtual_sku END as destination_sku")
                        .select(LclTrialShippingInventoryDO::getShippingStartDate)
                        .select(LclTrialShippingInventoryDO::getIsPackageFull)
                        .select(LclFinishedInventoryDO::getRemarks)

                        .select(LclFinishedInventoryDO::getFactoryFinishedDate)
                        .select(LclFinishedInventoryDO::getFactoryShippingPackageNum)
                        .select(LclFinishedInventoryDO::getFactoryRemainNum)
                        .select("t2.self_data->>'$.containerLoad' as container_load")
                        .select(LclTrialShippingInventoryDO::getDestinationWarehouse)
                        .select(LclTrialShippingInventoryDO::getShippingNum)
                        .select(LclFinishedInventoryDO::getShippingRatio)
                        .select(LclFinishedInventoryDO::getIsForeignFlag)

                        .leftJoin(LclTrialShippingInventoryDO.class, LclTrialShippingInventoryDO::getLclFactoryFinishedId, LclFinishedInventoryDO::getId)
                        .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, LclFinishedInventoryDO::getProductSnapshotId)
                        .eq(LclFinishedInventoryDO::getLclRecordId, form.getLclRecordId())
                        .like(StrUtil.isNotBlank(form.getContractCode()), LclFinishedInventoryDO::getContractCode, form.getContractCode())
                        .like(StrUtil.isNotBlank(form.getShipmentCode()), LclFinishedInventoryDO::getShipmentCode, form.getShipmentCode())
                        .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), ProductSnapshotDO::getSelfSku, form.getSelfSkuList())
                        .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), ProductSnapshotDO::getVirtualSku, form.getVirtualSkuList())
                        .between(StrUtil.isNotBlank(form.getShippingStartDate()) && StrUtil.isNotBlank(form.getShippingEndDate()),
                                LclTrialShippingInventoryDO::getShippingStartDate, form.getShippingStartDate(), form.getShippingEndDate())
                        .between(StrUtil.isNotBlank(form.getFactoryFinishedStartDate()) && StrUtil.isNotBlank(form.getFactoryFinishedEndDate()),
                                LclFinishedInventoryDO::getFactoryFinishedDate, form.getFactoryFinishedStartDate(), form.getFactoryFinishedEndDate())
                        .apply(CollectionUtil.isNotEmpty(form.getOldSkuList()), "t2.virtual_data->>'$.oldSku' in (" + String.join(",",
                                form.getOldSkuList().stream()
                                        .map(s -> "'" + s + "'")
                                        .toList()) + ")")
                        .apply(CollectionUtil.isNotEmpty(form.getProductStatusList()), "t2.virtual_data->>'$.productStatus' in (" + String.join(",",
                                form.getProductStatusList().stream()
                                        .map(s -> "'" + s + "'")
                                        .toList()) + ")")
                        .apply(StrUtil.isNotBlank(form.getProductName()), "t2.self_data->>'$.productName' like {0}", StrUtil.isBlank(form.getProductName()) ? form.getProductName() : "%" + form.getProductName().strip() + "%")
                        .apply(StrUtil.isNotBlank(form.getFactoryCode()), "t2.factory_data->>'$.factoryCode' like {0}", StrUtil.isBlank(form.getFactoryCode()) ? form.getFactoryCode() : "%" + form.getFactoryCode().strip() + "%")
                //.inSql(StrUtil.isNotBlank(form.getIsPackageFull()), LclFinishedInventoryDO::getId,
                //        "SELECT lcl_factory_finished_id" +
                //                "    FROM cm_lcl_trial_shipping_inventory" +
                //                "    GROUP BY lcl_factory_finished_id" +
                //                "    HAVING MIN(is_package_full) = '" + form.getIsPackageFull() + "' AND MAX(is_package_full) = '" + form.getIsPackageFull() + "'")

        );
    }

    public LclConsolidationFinishedInventoryVO selectContainerLoad(String finishedId) {

        return baseMapper.selectJoinOne(LclConsolidationFinishedInventoryVO.class, new MPJLambdaWrapper<LclFinishedInventoryDO>()
                .selectAs(LclFinishedInventoryDO::getId, "id")
                .select("t1.self_data->>'$.containerLoad' as container_load")
                .select(LclFinishedInventoryDO::getFactoryRemainNum)
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, LclFinishedInventoryDO::getProductSnapshotId)
                .eq(LclFinishedInventoryDO::getId, finishedId)
        );
    }

    public int dropById(String factoryFinishedId) {
        if (StrUtil.isBlank(factoryFinishedId)) {
            return 0;
        }
        return baseMapper.dropById(factoryFinishedId);
    }

    public int deleteByLclRecordId(String lclRecordId) {
        return baseMapper.deleteByLclRecordId(lclRecordId);
    }

    public Set<String> getSnapIds(InteriorInfoQuery infoQuery) {
        List<LclFinishedInventoryDO> list = baseMapper.selectList(Wrappers.<LclFinishedInventoryDO>lambdaQuery()
                .select(LclFinishedInventoryDO::getProductSnapshotId)
                .between(StrUtil.isNotBlank(infoQuery.getCreateStartDate()) && StrUtil.isNotBlank(infoQuery.getCreateEndDate()),
                        LclFinishedInventoryDO::getCreateDate, infoQuery.getCreateStartDate(), infoQuery.getCreateEndDate()));
        return CollectionUtil.isEmpty(list) ? Collections.emptySet() :
                list.stream()
                        .map(LclFinishedInventoryDO::getProductSnapshotId)
                        .collect(Collectors.toSet());
    }

    public List<LclFinishedInventoryBO> selectListById(LclConsolidationForm form) {
        return this.baseMapper.selectJoinList(LclFinishedInventoryBO.class, new MPJLambdaWrapper<LclFinishedInventoryDO>()
                .selectAll(LclFinishedInventoryDO.class)
                .selectCollection(LclTrialShippingInventoryDO.class, LclFinishedInventoryBO::getLclTrialDTOList)
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, LclFinishedInventoryDO::getProductSnapshotId)
                .leftJoin(LclTrialShippingInventoryDO.class, LclTrialShippingInventoryDO::getLclFactoryFinishedId, LclFinishedInventoryDO::getId)
                .eq(LclFinishedInventoryDO::getId, form.getId())
        );
    }

    public List<LclConsolidationNonFinishedInventoryDTO> listByLclRecordId(String lclRecordId) {
        return baseMapper.listByLclRecordId(lclRecordId);
    }
}
