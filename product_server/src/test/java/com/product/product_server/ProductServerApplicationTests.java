package com.product.product_server;

import cn.hutool.core.util.IdUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Random;

@SpringBootTest
class ProductServerApplicationTests {

    @Test
    void contextLoads() {
        Long threadId = Thread.currentThread().threadId();
        System.out.println("当前线程的ID为：" + threadId);
    }

}
