package com.sales_server.model.channel.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 细分渠道详情
 * <AUTHOR>
 * @Date 2025/5/8 16:16
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NicheChannelDetailVo implements Serializable {

    private String saleChannelId;

    private String saleChannelName;

    private String nicheChannelId;

    private String nicheChannelName;

    private Integer sort;

    private String remarks;
}
