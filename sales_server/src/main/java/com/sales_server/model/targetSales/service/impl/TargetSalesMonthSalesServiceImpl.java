package com.sales_server.model.targetSales.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.sales_server.infrastructures.entity.EditInfo;
import com.sales_server.model.channel.service.IChannelInfoService;
import com.sales_server.model.product.entity.dp.VirtualProductInfoDp;
import com.sales_server.model.targetSales.entity.TargetSalesPage;
import com.sales_server.model.targetSales.entity.dos.SalesTargetSalesMonthInfoDO;
import com.sales_server.model.targetSales.entity.dto.TargetSalesAggDto;
import com.sales_server.model.targetSales.entity.form.TargetMonthSalesPageForm;
import com.sales_server.model.targetSales.entity.vo.SpuTargetMonthSalesVo;
import com.sales_server.model.targetSales.entity.vo.TargetMonthSalesVo;
import com.sales_server.model.targetSales.repository.dataRepository.TargetSalesMonthRepository;
import com.sales_server.model.targetSales.repository.dataRepository.TargetSalesRepository;
import com.sales_server.model.targetSales.service.ITargetSalesMonthSalesService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.utils.DateUtils.YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN_FORMAT;
import static java.math.RoundingMode.HALF_UP;

/**
 * @Description 目标日销聚合层service
 * <AUTHOR>
 * @Date 2024/11/12 14:24
 **/
@Service
@Slf4j
public class TargetSalesMonthSalesServiceImpl implements ITargetSalesMonthSalesService {

    @Resource
    private TargetSalesMonthRepository targetSalesMonthRepository;

    @Resource
    private TargetSalesRepository targetSalesRepository;

    @Resource
    private IChannelInfoService channelInfoService;

    /**
     * 按月份聚合目标日销，并保存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTargetSalesMonthAggData(Set<String> successfulVirtualIdSet) {
        List<String> totalList = new ArrayList<>(successfulVirtualIdSet);
        List<List<String>> partitionList = ListUtil.partition(totalList, 500);

        for (List<String> partition : partitionList) {
            List<TargetSalesAggDto> targetSalesAggList = targetSalesRepository.aggTargetSales(partition);

            List<SalesTargetSalesMonthInfoDO> list = new ArrayList<>(targetSalesAggList.size());
            for (var targetAgg : targetSalesAggList) {
                String virtualSkuId = targetAgg.getVirtualSkuId();
                LocalDate targetDate = targetAgg.getTargetDate();

                // 若已经存在的话，则获取id并存入实体类中，方便后续更新
                list.add(SalesTargetSalesMonthInfoDO.builder()
                        .targetDate(targetDate)
                        .targetSales(targetAgg.getTargetSales())
                        .virtualSkuId(virtualSkuId)
                        .build());
            }
            targetSalesMonthRepository.deleteAndSaveMonthSalesByVirtualId(partition, list);
        }
    }

    @Override
    public List<TargetMonthSalesVo> getMonthTargetSalesList(TargetMonthSalesPageForm form, List<String> virtualSkuIdList,
                                                            Map<String, String> userMap) {
        List<SalesTargetSalesMonthInfoDO> list = targetSalesMonthRepository.targetSalesAggDtoList(form, virtualSkuIdList);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        // 遍历选择的日期，若map为空，则该月份目标销售补0
        return list.stream().collect(Collectors.groupingBy(SalesTargetSalesMonthInfoDO::getVirtualSkuId))
                .entrySet().stream()
                .map(entry -> {
                    // 获取最后编辑用户以及编辑时间
                    EditInfo editInfo = entry.getValue().stream()
                            .max(Comparator.comparing(SalesTargetSalesMonthInfoDO::getUpdateDate))
                            .map(m -> new EditInfo(userMap.getOrDefault(m.getUpdateBy(), m.getUpdateBy()), DatePattern.NORM_DATETIME_FORMAT.format(m.getUpdateDate())))
                            .orElseThrow(() -> new RuntimeException("缺少最后编辑者信息"));

                    Map<String, Double> collect = entry.getValue().stream()
                            .collect(Collectors.toMap(m ->
                                            String.format("%s年%s月", m.getTargetDate().getYear(), m.getTargetDate().getMonth().getValue()),
                                    SalesTargetSalesMonthInfoDO::getTargetSales, Double::sum, HashMap::new));
                    double sum = collect.values().stream().mapToDouble(m -> m).sum();
                    sum = BigDecimal.valueOf(sum).setScale(3, HALF_UP).doubleValue();

                    // 遍历选择的日期，若map为空，则该月份目标销售补0
                    LocalDate startDate = LocalDateTimeUtil.ofDate(form.getStartDate());
                    Map<String, Double> targetMonthSaleMap = new LinkedHashMap<>(16);
                    while (!startDate.isAfter(form.getEndDate())) {
                        String format = String.format("%s年%s月", startDate.getYear(), startDate.getMonth().getValue());
                        targetMonthSaleMap.putIfAbsent(format, collect.getOrDefault(format, 0D));
                        startDate = startDate.plusMonths(1);
                    }
                    return TargetMonthSalesVo.builder().virtualSkuId(entry.getKey()).monthTargetSaleMap(targetMonthSaleMap).editInfo(editInfo).totalSalesNum(sum).build();
                })
                .toList();
    }

    @Override
    public TargetSalesPage combineTargetSalesPageBySpu(TargetMonthSalesPageForm form, List<TargetMonthSalesVo> targetMonthSaleList) {
        var monthSet = targetMonthSaleList.getFirst().getMonthTargetSaleMap().keySet();
        Map<String, String> childParentNameMap = channelInfoService.getChildParentNameMap();
        Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap(1);
        Map<String, String> saleChannelIdNameMap = channelInfoService.getChannelIdNameMap(0);

        Comparator<TargetMonthSalesVo> skuComparator;
        Comparator<SpuTargetMonthSalesVo> spuComparator;
        if (form.getSort() != null) {
            spuComparator = Comparator.comparing((SpuTargetMonthSalesVo c) -> c.getMonthTargetSaleMap().getOrDefault(form.getSort(), c.getTotalSalesNum()));
            skuComparator = Comparator.comparing((TargetMonthSalesVo c) -> c.getMonthTargetSaleMap().getOrDefault(form.getSort(), c.getTotalSalesNum()));
            if (StrUtil.isNotBlank(form.getDirection()) && "desc".equals(form.getDirection())) {
                spuComparator = spuComparator.reversed();
                skuComparator = skuComparator.reversed();
            }
        } else {
            spuComparator = Comparator.comparing(SpuTargetMonthSalesVo::getSpuId).thenComparing(SpuTargetMonthSalesVo::getSaleChannel);
            skuComparator = Comparator.comparing((TargetMonthSalesVo c) -> LocalDateTime.parse(c.getEditInfo().editDate(), YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN_FORMAT)).reversed();
        }

        Map<String, List<TargetMonthSalesVo>> groupedTargetSalesMap = targetMonthSaleList.stream()
                .peek(p -> {
                    String channelId = p.getVirtualProductInfoDp().getChannel();
                    String channelName = channelIdNameMap.getOrDefault(channelId, channelId);
                    p.getVirtualProductInfoDp().setChannel(channelName);
                    p.setSaleChannel(childParentNameMap.getOrDefault(channelName.toUpperCase(Locale.ROOT), channelName));
                })
                .sorted(skuComparator)
                .collect(Collectors.groupingBy(c -> c.getVirtualProductInfoDp().getSpu() + c.getSaleChannel()));

        List<SpuTargetMonthSalesVo> list = groupedTargetSalesMap.values().stream()
                .filter(targetMonthSalesVoList -> {
                    String saleChannel = form.getSaleChannel();
                    if (StrUtil.isBlank(saleChannel)) return true;
                    String saleChannelName = saleChannelIdNameMap.get(saleChannel);
                    if (StrUtil.isBlank(saleChannelName)) return false;
                    return saleChannelName.equals(targetMonthSalesVoList.getFirst().getSaleChannel());
                })
                .map(targetMonthSalesVoList -> {
                    VirtualProductInfoDp virtualProduct = targetMonthSalesVoList.getFirst().getVirtualProductInfoDp();

                    Map<String, Double> monthTargetSaleMap = addMonthTargetSaleMapBySpu(targetMonthSalesVoList);
                    double sum = monthTargetSaleMap.values().stream().mapToDouble(c -> c).sum();
                    return SpuTargetMonthSalesVo.builder()
                            .spuId(virtualProduct.getSpu())
                            .spuName(virtualProduct.getSpuName())
                            .saleChannel(targetMonthSalesVoList.getFirst().getSaleChannel())
                            .monthTargetSaleMap(monthTargetSaleMap)
                            .targetMonthSalesList(targetMonthSalesVoList)
                            .totalSalesNum(BigDecimal.valueOf(sum).setScale(3, HALF_UP).doubleValue())
                            .build();
                })
                .sorted(spuComparator)
                .toList();

        Integer size = form.getSize();
        Integer current = form.getCurrent();
        // 计算分页起始和结束索引
        int fromIndex = (current - 1) * size;
        int toIndex = Math.min(fromIndex + size, list.size());
        if (fromIndex > list.size()) {
            return new TargetSalesPage();
        }
        List<SpuTargetMonthSalesVo> subList = list.subList(fromIndex, toIndex);
        TargetSalesPage targetSalesPage = new TargetSalesPage(form.getCurrent(), form.getSize(), monthSet);
        targetSalesPage.setRecords(subList);
        targetSalesPage.setTotal(list.size());

        return targetSalesPage;
    }

    private Map<String, Double> addMonthTargetSaleMapBySpu(List<TargetMonthSalesVo> value) {
        Map<String, Double> monthTargetSaleMap = new LinkedHashMap<>(16);
        for (var vo : value) {
            for (var tempEntry : vo.getMonthTargetSaleMap().entrySet()) {
                monthTargetSaleMap.compute(tempEntry.getKey(), (k, v) -> v == null
                        ? tempEntry.getValue() :
                        BigDecimal.valueOf(v + tempEntry.getValue()).setScale(3, HALF_UP).doubleValue());
            }
        }
        return monthTargetSaleMap;
    }

    @Override
    public void combineProductInfoAndTargetSalesInfo(List<VirtualProductInfoDp> virtualProductInfoList,
                                                     List<TargetMonthSalesVo> targetMonthSalesList,
                                                     Map<String, String> userMap) {
        Map<String, VirtualProductInfoDp> collectMap = virtualProductInfoList.stream().collect(Collectors.toMap(VirtualProductInfoDp::getVirtualSkuId, m -> m));

        for (var salesInfo : targetMonthSalesList) {
            String virtualSkuId = salesInfo.getVirtualSkuId();
            VirtualProductInfoDp productInfoDp = collectMap.get(virtualSkuId);
            if (productInfoDp == null) {
                continue;
            }
            productInfoDp.setOperator(userMap.get(productInfoDp.getOperator()));
            salesInfo.setVirtualProductInfoDp(productInfoDp);
        }
    }

    @Override
    public void deleteMonthSalesByVirtualId(String virtualId) {
        targetSalesMonthRepository.deleteMonthSalesByVirtualId(virtualId);
    }
}
