package com.inventory_server.applications.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryWatchBoardDto {
    private String watchBoardId;
    private LocalDate judgeDate;
    private Integer shippingNum;
    private Integer unShippingNum;
    private Double initialInventory;
    private Double safeInventory;
    private Integer totalRedundancy;
    private Integer localRedundancy;
}
