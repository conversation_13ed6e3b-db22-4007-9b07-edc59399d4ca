package com.auth.authorization_server.domain.vo;

import com.auth.authorization_server.domain.dto.DeptDTO;
import com.auth.authorization_server.domain.dto.RoleDTO;
import com.auth.authorization_server.domain.dto.UserDTO;
import com.auth.authorization_server.model.vo.TreeSelect;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoVO implements Serializable {


    @Serial
    private static final long serialVersionUID = -807958477023636981L;

    private List<RoleDTO> roles;

    private List<TreeSelect> depts;

    private UserDTO sysUser;

    private List<String> roleIds;

    private List<TreeSelect> deptIds;
}
