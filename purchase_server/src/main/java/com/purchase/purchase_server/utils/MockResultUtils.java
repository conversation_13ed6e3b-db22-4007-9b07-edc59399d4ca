package com.purchase.purchase_server.utils;

import cn.hutool.core.collection.CollectionUtil;

import java.util.*;

/**
 * @Description 模拟表格结果处理工具类
 * <AUTHOR>
 * @Date 2024/7/12 10:50
 **/
public class MockResultUtils {
    public static <T extends Number> void checkEmptyWarehouse(Set<String> zeroRatioSet, Map<String, TreeMap<String, T>> map) {
        for (Map.Entry<String, TreeMap<String, T>> entry : map.entrySet()) {
            Map<String, T> warehouseShippingMap = entry.getValue();

            warehouseShippingMap.entrySet().stream()
                    .filter(e -> zeroRatioSet.contains(e.getKey()) && e.getValue().doubleValue() > 0)
                    .forEach(warehouseEntry -> zeroRatioSet.remove(warehouseEntry.getKey()));
        }
    }

    public static void removeUselessMockResult(Map<String, TreeMap<String, Double>> remainInventoryMap,
                                               Map<String, TreeMap<String, Integer>> onShippingInventory,
                                               Map<String, TreeMap<String, Double>> everydaySaleMap, Set<String> zeroRatioSet) {
        if (CollectionUtil.isEmpty(zeroRatioSet)) {
            return;
        }
        for (TreeMap<String, Double> dayMap: remainInventoryMap.values()) {
            zeroRatioSet.forEach(dayMap::remove);
        }
        for (TreeMap<String, Integer> dayMap: onShippingInventory.values()) {
            zeroRatioSet.forEach(dayMap::remove);
        }
        for (TreeMap<String, Double> dayMap: everydaySaleMap.values()) {
            zeroRatioSet.forEach(dayMap::remove);
        }
    }
}
