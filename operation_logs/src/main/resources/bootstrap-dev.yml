spring:
  cloud:
    nacos:
      config:
        server-addr: http://************:8848
        file-extension: yml
        group: DEFAULT_GROUP
        namespace: cce9aae0-9b59-46bb-bbb3-d98b321b1e15
        username: nacos
        password: nacos
        prefix: ${spring.application.name}
        refresh-enabled: true
        shared-configs:
          - data-id: shared-redis.yml
            group: DEFAULT_GROUP
            refresh: true #动态刷新配置开启
          - data-id: shared-datasource.yml
            group: DEFAULT_GROUP
            refresh: true #动态刷新配置开启
      discovery:
#        ip: ***********
        server-addr: http://************:8848
        group: DEFAULT_GROUP
        namespace: cce9aae0-9b59-46bb-bbb3-d98b321b1e15
        username: nacos
        password: nacos