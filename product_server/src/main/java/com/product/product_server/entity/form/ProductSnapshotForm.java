package com.product.product_server.entity.form;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/29 13:25
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSnapshotForm implements Serializable {
    private List<String> snapshotList;

    private List<String> virtualSkuId;

    private List<String> selfSkuIds;

    private List<String> factoryInfoIds;

}
