package com.crafts_mirror.utils.utils;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMessage;
import org.springframework.http.server.ServerHttpRequest;

import java.net.InetSocketAddress;
import java.util.Optional;

/**
 * @Description ip地址工具类
 * <AUTHOR>
 * @Date 2024/2/1 13:32
 **/
public class IPUtils {
    public static <T extends HttpMessage> String getIpAddress(T request) {
        HttpHeaders headers = request.getHeaders();
        String ip = headers.getFirst("x-forwarded-for");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            // 多次反向代理后会有多个ip值，第一个ip才是真实ip
            if (ip.contains(",")) {
                ip = ip.split(",")[0];
            }
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("X-Real-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            if(request instanceof ServerHttpRequest) {
                ip = ((ServerHttpRequest)request).getRemoteAddress().getAddress().getHostAddress();
            } else if(request instanceof org.springframework.http.server.reactive.ServerHttpRequest serverHttpRequest) {
                ip = Optional.ofNullable(serverHttpRequest.getRemoteAddress()).orElse(new InetSocketAddress(8080)).getAddress().getHostAddress();
            }
        }
        return ip;
    }
}
