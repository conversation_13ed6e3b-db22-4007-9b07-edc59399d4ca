package com.inventory_server.infrastructures.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/21 19:15
 **/
@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "usa.warehouse") // 配置文件的前缀
public class WarehouseConstants {

    private List<String> name;

}