package com.purchase.purchase_server.entity.form;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/10
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class InventoryInfoQuery extends BasePageForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 5246045518164475315L;
    /**
     * 虚拟skuList
     */
    private List<String> selfSkuIdList;
}
