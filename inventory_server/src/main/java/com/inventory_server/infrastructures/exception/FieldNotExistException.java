package com.inventory_server.infrastructures.exception;

import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description 某字段未配置异常处理
 * <AUTHOR>
 * @Date 2023/12/22 11:20
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class FieldNotExistException extends RuntimeException {
    private ResponseCodeEnum responseCode;

    public FieldNotExistException(String message) {
        super(message);
        this.responseCode = ResponseCodeEnum.ERROR;
    }

    public FieldNotExistException(ResponseCodeEnum responseCode, String message) {
        super(message);

        setResponseCode(responseCode);
    }
}
