package com.auth.authorization_server.model.dataObject;

import com.auth.authorization_server.model.PhysicalBaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;

/**
 * 部门表 cm_sys_dept
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cm_sys_dept")
public class DeptDO extends PhysicalBaseEntity {

    @Serial
    private static final long serialVersionUID = 3738279501202518695L;
    /**
     * 部门ID
     */
    private String id;

    /**
     * 父部门ID
     */
    private String parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 显示顺序
     */
    private Integer deptSort;
}
