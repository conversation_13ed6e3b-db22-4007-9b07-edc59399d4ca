package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 易仓采购单跟单状态表
 * @TableName cm_yicang_purchase_order_track
 */
@TableName(value ="cm_yicang_purchase_order_track")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YicangPurchaseOrderTrackDO implements Serializable {

    @Serial
    private static final long serialVersionUID = -6572652537130995345L;

    private String id;
    /**
     * 跟单状态中文名称
     */
    private String name;

    /**
     * 跟单状态英文名称
     */
    private String nameEn;

    /**
     * 采购单表id
     */
    private String sbPoId;
}