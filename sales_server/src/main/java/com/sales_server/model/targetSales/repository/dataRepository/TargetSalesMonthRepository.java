package com.sales_server.model.targetSales.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crafts_mirror.common.security.dataPermission.DataPermission;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.sales_server.model.product.entity.dos.VirtualProductDO;
import com.sales_server.model.targetSales.entity.dos.SalesTargetSalesMonthInfoDO;
import com.sales_server.model.targetSales.entity.form.TargetMonthSalesPageForm;
import com.sales_server.model.targetSales.mapper.TargetSalesMonthMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

import static com.crafts_mirror.utils.constant.SecurityConstants.ROLE_DATA_PER_TARGET_SALES;

/**
 * <AUTHOR>
 * @Date 2024/11/12 13:36
 **/
@Service
public class TargetSalesMonthRepository extends ServiceImpl<TargetSalesMonthMapper, SalesTargetSalesMonthInfoDO> {

    public SalesTargetSalesMonthInfoDO getOneByVirtualSkuAndDate(String virtualId, LocalDate targetDate) {
        return baseMapper.selectOne(Wrappers.<SalesTargetSalesMonthInfoDO>lambdaQuery()
                .eq(SalesTargetSalesMonthInfoDO::getTargetDate, targetDate)
                .eq(SalesTargetSalesMonthInfoDO::getVirtualSkuId, virtualId));
    }

    public List<SalesTargetSalesMonthInfoDO> targetSalesAggDtoList(TargetMonthSalesPageForm form, List<String> virtualSkuIdList) {
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_TARGET_SALES, "t1");
        return baseMapper.selectJoinList(SalesTargetSalesMonthInfoDO.class, new MPJLambdaWrapper<SalesTargetSalesMonthInfoDO>()
                .leftJoin(VirtualProductDO.class, VirtualProductDO::getId, SalesTargetSalesMonthInfoDO::getVirtualSkuId)
                .between(SalesTargetSalesMonthInfoDO::getTargetDate, form.getStartDate(), form.getEndDate())
                .in(CollectionUtil.isNotEmpty(virtualSkuIdList), SalesTargetSalesMonthInfoDO::getVirtualSkuId, virtualSkuIdList)
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
        );
    }

    public void deleteMonthSalesByVirtualId(String virtualId) {
        baseMapper.delete(Wrappers.<SalesTargetSalesMonthInfoDO>lambdaQuery()
                .eq(SalesTargetSalesMonthInfoDO::getVirtualSkuId, virtualId));
    }

    public void deleteAllMonthSales(Collection<String> collection) {
        if (CollectionUtil.isEmpty(collection)) {
            return;
        }
        baseMapper.delete(Wrappers.<SalesTargetSalesMonthInfoDO>lambdaQuery().in(SalesTargetSalesMonthInfoDO::getVirtualSkuId, collection));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteAndSaveMonthSalesByVirtualId(List<String> partition, List<SalesTargetSalesMonthInfoDO> list) {
        deleteAllMonthSales(partition);
        saveOrUpdateBatch(list);
    }
}
