package com.auth.authorization_server.service.impl;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.auth.authorization_server.domain.vo.PostVO;
import com.auth.authorization_server.service.ISysPostService;
import com.crafts_mirror.utils.constant.RedisKeyConstant;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 菜单 业务层处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SysPostServiceImpl implements ISysPostService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public List<PostVO> selectPostList() {
        Map<String, String> postMap = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKeyConstant.POST), new TypeReference<>() {});
        List<PostVO> postList = new ArrayList<>();
        for (Map.Entry<String, String> entry : postMap.entrySet()) {
            postList.add(PostVO.builder().id(entry.getKey()).postName(entry.getValue()).build());
        }
        return postList;
    }
}
