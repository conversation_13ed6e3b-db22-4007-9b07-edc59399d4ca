package com.auth.authorization_server.assembler;

import com.auth.authorization_server.domain.dto.DeptDTO;
import com.auth.authorization_server.model.dataObject.DeptDO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * 数据转换工具类
 *
 * <AUTHOR>
 * @date 2024/5/11
 */

@Mapper(componentModel = "spring", uses = DeptConvert.class)
public interface DeptAssembler {
    DeptDTO deptDoToDto(DeptDO val);

    List<DeptDTO> deptDoListToDto(List<DeptDO> val);

    DeptDO deptDTOToDo(DeptDTO val);
}














