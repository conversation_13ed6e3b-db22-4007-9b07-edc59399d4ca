<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.purchase.purchase_server.mapper.Lcl.LclTrialShippingInventoryMapper">

    <!-- 删除 -->
    <delete id="delete">
        DELETE
        FROM cm_lcl_trial_shipping_inventory
        WHERE lcl_factory_finished_id = #{factoryFinishedId}
          AND shipping_start_date = #{shippingStartDate}
    </delete>

    <delete id="deleteById">
        DELETE
        FROM cm_lcl_trial_shipping_inventory
        WHERE id = #{id}
    </delete>

    <delete id="deleteByFinId">
        DELETE
        FROM cm_lcl_trial_shipping_inventory
        WHERE lcl_factory_finished_id = #{finId}
    </delete>

    <delete id="deleteByLclRecordId">
        DELETE
        FROM cm_lcl_trial_shipping_inventory t
        WHERE
            t.lcl_factory_finished_id IN (SELECT id FROM cm_lcl_finished_inventory WHERE lcl_record_id = #{lclRecordId})
    </delete>
</mapper>
