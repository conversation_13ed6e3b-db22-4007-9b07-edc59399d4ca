package com.product.product_server.utils.easyExcelUtil.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.constant.SystemConstant;
import com.crafts_mirror.utils.enums.UserPostEnum;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.product.product_server.entity.dataObject.ProductCategoryDO;
import com.product.product_server.entity.dataObject.VirtualProductDO;
import com.product.product_server.entity.form.FileMissionForm;
import com.product.product_server.entity.form.UserParams;
import com.product.product_server.entity.response.ResultDTO;
import com.product.product_server.entity.vo.BuyerSearchVo;
import com.product.product_server.entity.vo.UserInteriorVO;
import com.product.product_server.enums.UserStatusEnum;
import com.product.product_server.exception.FieldNotExistException;
import com.product.product_server.repository.ProductCategoryRepositoryImpl;
import com.product.product_server.repository.VirtualProductRepositoryImpl;
import com.product.product_server.service.IChannelService;
import com.product.product_server.service.IProductImportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.client.RestTemplate;

import java.util.*;

import static com.crafts_mirror.utils.constant.SystemConstant.SYS_USER_INFO;
import static com.crafts_mirror.utils.enums.ImportExcelStatusEnum.FINISHED;

/**
 * @Description 导入产品抽象类
 * <AUTHOR>
 * @Date 2023/12/21 11:35
 **/
@Slf4j
public abstract class AbstractProductImportListener<T> implements ReadListener<T> {

    protected int successfulTimes = 0;
    protected int failedTimes = 0;

    protected final RestTemplate restTemplate = ApplicationContextProvider.getBean(RestTemplate.class);

    protected final IProductImportService productImportService = ApplicationContextProvider.getBean(IProductImportService.class);

    private final StringRedisTemplate stringRedisTemplate = ApplicationContextProvider.getBean(StringRedisTemplate.class);

    protected final VirtualProductRepositoryImpl virtualProductRepository = ApplicationContextProvider.getBean(VirtualProductRepositoryImpl.class);

    protected final ProductCategoryRepositoryImpl productCategoryRepositoryImpl = ApplicationContextProvider.getBean(ProductCategoryRepositoryImpl.class);

    protected final IChannelService channelService = ApplicationContextProvider.getBean(IChannelService.class);

    public AbstractProductImportListener(String fileName, String importExcelId) {
        this.authorization = SecurityUtils.getToken();
        this.fileName = fileName;
        this.importExcelId = importExcelId;
    }

    protected final List<String> errorList = new ArrayList<>(32);


    protected final String authorization;

    protected final String fileName;

    protected String importExcelId;


    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.warn("导入商品-------------所有excel解析完成，准备发送日志消息");
        // 保存产品导入进度
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, authorization);
        restTemplateUtil.post(FileMissionForm.builder()
                        .missionId(importExcelId)
                        .fileName(fileName)
                        .finishDate(new Date())
                        .importStatus(FINISHED.getMessage())
                        .importResult(String.format("成功：%d；失败：%d", successfulTimes, failedTimes))
                        .failedResultList(errorList)
                        .build(),
                ResultDTO.class, SystemConstant.FILE_SYSTEM_MISSION_CENTER_URL
        );
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        log.warn("导入商品-------------excel导入遇到问题，正在记录错误信息");
        failedTimes++;
        switch (exception) {
            case ExcelDataConvertException excelException -> {
                String error;
                if (excelException.getCause() == null) {
                    error = String.format("第 %d 行，第 %d 列数据异常，请核对数据格式是否正确，该单元格是否多了前后空格",
                            excelException.getRowIndex() + 1, excelException.getColumnIndex() + 1);
                } else {
                    error = String.format("第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getRowIndex() + 1, excelException.getCause().getMessage());
                }
                    errorList.add(error);
            }
            case IllegalArgumentException illegalArgumentException ->
                    errorList.add(String.format("第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getRowIndex() + 1, illegalArgumentException.getMessage()));
            case DataIntegrityViolationException dataIntegrityViolationException ->
                    errorList.add(String.format("第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getRowIndex() + 1, dataIntegrityViolationException.getMessage()));
            case NullPointerException nullPointerException ->
                    errorList.add(String.format("第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getRowIndex() + 1, nullPointerException.getMessage()));
            case FieldNotExistException fieldNotExistException ->
                    errorList.add(String.format("第 %d 行数据异常，异常原因 %s，请联系开发配置",
                            context.readSheetHolder().getRowIndex() + 1, fieldNotExistException.getMessage()));
            case TooManyResultsException ignored ->
                    errorList.add(String.format("第 %d 行数据异常，异常原因：系统中存在了重复的商品，请联系开发人员定位重复商品数据",
                            context.readSheetHolder().getRowIndex() + 1));
            case RuntimeException runtimeException -> {
                errorList.add(String.format("第 %d 行数据异常，异常原因 %s，该异常未被正常捕获，造成后续数据都无法继续导入，请联系开发人员，修复异常数据后再次导入",
                        context.readSheetHolder().getRowIndex() + 1, exception.getMessage()));
                doAfterAllAnalysed(context);
                throw new RuntimeException(runtimeException.getMessage());
            }
            default ->
                    errorList.add(String.format("第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getRowIndex() + 1, exception.getMessage()));
        }
        log.warn("excel转实体类异常，第 {} 行数据异常，异常原因：{}",
                context.readSheetHolder().getRowIndex() + 1, exception.getMessage());
    }

    protected void validChannelExisted(String channel, Set<String> channelNameSet) {
        if (StrUtil.isBlank(channel)) {
            return;
        }
        if (!channelNameSet.contains(channel.toUpperCase(Locale.ROOT))) {
            throw new FieldNotExistException("渠道不存在");
        }
    }

    protected List<BuyerSearchVo> validNickNameExisted(String nickName, UserPostEnum userPostEnum) {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, authorization);
        UserParams params = new UserParams();
        params.setNickName(nickName);
        params.setUserStatus(UserStatusEnum.YES_STATUS.getCode());
        //params.setPostId(userPostEnum.getCode());
        ResultDTO resultDTO = restTemplateUtil.post(params, ResultDTO.class, SYS_USER_INFO);
        List userList = JSON.to(List.class, resultDTO.getData());

        if (CollectionUtil.isEmpty(userList)) {
            throw new FieldNotExistException(userPostEnum.getMessage() + nickName + "不存在");
        }
        List<BuyerSearchVo> searchVoList = new ArrayList<>();
        userList.forEach(i -> {
            UserInteriorVO userInteriorVO = JSON.to(UserInteriorVO.class, i);
            if (userPostEnum.getCode().equals(userInteriorVO.getPostId())) {
                searchVoList.add(BuyerSearchVo.builder().userName(userInteriorVO.getUserName()).nickName(userInteriorVO.getNickName()).build());
            }
        });
        if (CollectionUtil.isEmpty(searchVoList)) {
            throw new FieldNotExistException(nickName + "的职位不是" + userPostEnum.getMessage());
        }
        return searchVoList;
    }

    protected void checkVirtualSkuAndOldSkuConsistency(String virtualSku, String oldSku) throws TooManyResultsException {
        // 校验老sku和虚拟sku的对应关系是否是一一对应
        VirtualProductDO virtualProductDO = virtualProductRepository.getVirtualProductByVirtualSku(virtualSku);
        if (virtualProductDO != null && !StrUtil.equals(virtualProductDO.getOldSku(), oldSku)) {
            throw new IllegalArgumentException(String.format("导入数据的虚拟sku：%s、老sku：%s与" +
                            "数据库中存在的虚拟sku：%s、老sku：%s 不为一一对应关系", virtualSku, oldSku,
                    virtualProductDO.getVirtualSku(), virtualProductDO.getOldSku()));
        }
        virtualProductDO = virtualProductRepository.getVirtualProductByOldSku(virtualSku);
        if (virtualProductDO != null) {
            throw new IllegalArgumentException(String.format("导入数据的虚拟sku：%s，与数据库中虚拟sku为：%s商品的老sku一致，请修改导入数据或者现有的商品数据",
                    virtualSku, virtualProductDO.getVirtualSku()));
        }
        if (StrUtil.isNotBlank(oldSku)) {
            virtualProductDO = virtualProductRepository.getVirtualProductByOldSku(oldSku);
            if (virtualProductDO != null && !StrUtil.equals(virtualProductDO.getVirtualSku(), virtualSku)) {
                throw new IllegalArgumentException(String.format("导入数据的虚拟sku：%s、老sku：%s与" +
                                "数据库中存在的虚拟sku：%s、老sku：%s 不为一一对应关系", virtualSku, oldSku,
                        virtualProductDO.getVirtualSku(), virtualProductDO.getOldSku()));
            }

            virtualProductDO = virtualProductRepository.getVirtualProductByVirtualSku(oldSku);
            if (virtualProductDO != null) {
                throw new IllegalArgumentException(String.format("导入数据的老sku：%s，与数据库中虚拟sku为：%s的商品一致，请修改导入数据或者现有的商品数据",
                        oldSku, virtualProductDO.getVirtualSku()));
            }
        }
    }
    protected String validCategoryExisted(String category) {
        if (StrUtil.isBlank(category)) {
            throw new FieldNotExistException("请填写大类");
        }
        var productCategoryDOS = productCategoryRepositoryImpl.selectCategoryList();
        var list = productCategoryDOS.stream().filter(productCategoryDO -> productCategoryDO.getCategoryName().equals(category)).toList();
        if (CollectionUtil.isEmpty(list)) {
            throw new FieldNotExistException("大类不存在");
        }
        ProductCategoryDO first = list.getFirst();
        var categoryDO = productCategoryDOS.stream().filter(productCategoryDO -> productCategoryDO.getParentId().toString().equals(first.getId())).findFirst().orElse(null);
        if (categoryDO == null){
            throw new FieldNotExistException("大类不存在");
        }
        return categoryDO.getId();
    }
}
