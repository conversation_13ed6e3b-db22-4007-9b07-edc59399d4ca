package com.auth.authorization_server.service;

import com.auth.authorization_server.domain.vo.UserVO;
import com.auth.authorization_server.req.UserParams;
import com.crafts_mirror.common.security.dataPermission.vo.RoleDataPerVO;

import java.util.List;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface ISysUserInteriorService {


    /**
     * 根据用户列表
     *
     * @return 用户信息集合信息
     */
    List<UserVO> selectUserList();

    /**
     * 根据条件查询已用户信息
     *
     * @param userName 用户名称
     * @return 用户信息
     */
    List<UserVO> selectUserInfo(UserParams userName);

    /**
     * 获取数据权限用户
     *
     * @return 用户信息
     */
    RoleDataPerVO getDataPermissionUser(String moduleId);


}
