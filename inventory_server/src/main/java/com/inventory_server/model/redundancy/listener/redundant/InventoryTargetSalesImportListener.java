package com.inventory_server.model.redundancy.listener.redundant;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.product.repository.databaseRepository.VirtualProductRepositoryImpl;
import com.inventory_server.model.redundancy.entity.dto.ImportActualDestinationSalesDto;
import com.inventory_server.model.redundancy.entity.dto.ImportTargetSalesDto;
import com.inventory_server.model.redundancy.entity.excelObj.RedundantDestinationSalesExcel;
import com.inventory_server.model.redundancy.entity.vo.UpgradeInfoInteriorVo;
import com.inventory_server.model.redundancy.listener.template.ExcelConstants;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

import static java.math.RoundingMode.HALF_UP;

/**
 * @Description 导入补货计划-目标日销
 * <AUTHOR>
 * @Date 2024/1/16 15:49
 **/
@Slf4j
public class InventoryTargetSalesImportListener extends AbstractInventoryImportListener<RedundantDestinationSalesExcel> {

    public InventoryTargetSalesImportListener(String fileName, List<String> errorList, ImportTargetSalesDto importTargetSalesDto) {
        super(fileName, errorList);
        this.importTargetSalesDto = importTargetSalesDto;
    }

    ImportTargetSalesDto importTargetSalesDto;

    private final Set<String> existedSku = new HashSet<>();

    private final VirtualProductRepositoryImpl virtualProductRepositoryImpl = ApplicationContextProvider.getBean(VirtualProductRepositoryImpl.class);

    @Override
    public void invoke(RedundantDestinationSalesExcel data, AnalysisContext analysisContext) {
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }
        String virtualSku = data.getVirtualSku();

        VirtualProductDO virtualProductDO = virtualProductRepositoryImpl.getOneByVirtualSkuOrOldSku(virtualSku);

        if (virtualProductDO == null) {
            throw new IllegalArgumentException(String.format("虚拟sku %s，不存在", virtualSku));
        }
        if (virtualSku.equals(virtualProductDO.getOldSku())) {
            throw new IllegalArgumentException("不可以填写老sku");
        }

        boolean returnFlag = false;
        if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())) {
            UpgradeInfoInteriorVo upgradeInfoInteriorVo = selectUpgradeInfo(virtualProductDO.getUpgradeId());
            if (ObjectUtil.isNotEmpty(upgradeInfoInteriorVo)) {
                if (upgradeInfoInteriorVo.getOriginalSkuId().equals(virtualProductDO.getId())) {
                    importTargetSalesDto.getTargetSalesUpgradeMap().put(upgradeInfoInteriorVo.getOriginalSku(), upgradeInfoInteriorVo.getUpgradeSku());
                    returnFlag = true;
                }
            }
        }

        if (!existedSku.add(virtualSku)) {
            throw new IllegalArgumentException(String.format("虚拟sku %s 重复", virtualSku));
        }
        if (returnFlag) {
            return;
        }

        ImportActualDestinationSalesDto destinationSalesDto = ImportActualDestinationSalesDto.builder()
                .virtualSku(virtualSku)
                .sevenDaySales(convertToNum(data.getSevenDaySales()).toString())
                .fourteenDaySales(convertToNum(data.getFourteenDaySales()).toString())
                .thirtyDaySales(convertToNum(data.getThirtyDaySales()).toString())
                .actualDailySales(convertToNum(data.getActualDailySales()).toString())
                .build();

        importTargetSalesDto.getDestinationSalesDtoMap().put(virtualSku, destinationSalesDto);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {

        List<String> fixedColumns = ExcelConstants.Sheet2.FIXED_COLUMNS;
        //删除headMap中的空值
        headMap.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty());
        List<String> headMapList = new ArrayList<>(headMap.values());
        //比较两个list集合值是否相等
        if (!fixedColumns.equals(headMapList)) {
            throw new RuntimeException("实际日销表头错误，请检查表头是否正确");
        }
    }

    private BigDecimal convertToNum(String value) {
        if (!isValidNumber(value)) {
            throw new IllegalArgumentException("请正确填写数字");
        }
        return new BigDecimal(value).setScale(3, HALF_UP);
    }

    // 检查值是否为数字且大于0的辅助方法
    private static boolean isValidNumber(String value) {
        try {
            if (StrUtil.isNotBlank(value)) {
                return Double.parseDouble(value) >= 0;
            } else {
                // 数量不可以为空
                return false;
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("请正确填写数字");
        }
    }
}
