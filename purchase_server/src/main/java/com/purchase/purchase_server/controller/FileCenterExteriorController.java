package com.purchase.purchase_server.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.purchase.purchase_server.entity.form.FileMissionPageForm;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.MissionCenterVo;
import com.purchase.purchase_server.service.FileCenterService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import static com.purchase.purchase_server.entity.consts.FilePathConstant.FILE_PATH_PURCHASE_ORDER;
import static com.purchase.purchase_server.entity.consts.FilePathConstant.FILE_PATH_PURCHASE_SHIPMENT;

/**
 * @Description 文档中心外部调用接口
 * <AUTHOR>
 * @Date 2023/12/12 14:04
 **/
@RestController
@RequestMapping("/center/exterior")
public class FileCenterExteriorController {

    @Resource
    private FileCenterService fileMissionCenterService;

    /**
     * 文件记录-采购单
     */
    @PostMapping("pageList")
    @ResponseBody
    @RequiresPermissions("purchase:purchaseOrder:pageList")
    public ResultDTO<IPage<MissionCenterVo>> getMissionPage(@RequestBody FileMissionPageForm form) {
        return ResultDTO.success(fileMissionCenterService.getMissionPage(form));
    }

    /**
     * 文件记录-货件
     */
    @PostMapping("pageList/shipment")
    @ResponseBody
    @RequiresPermissions("purchase:shipment:pageList")
    public ResultDTO<IPage<MissionCenterVo>> getMissionPageShipment(@RequestBody FileMissionPageForm form) {
        return ResultDTO.success(fileMissionCenterService.getMissionPage(form));
    }

    // 下载源文件-采购单
    @RequiresPermissions("purchase:purchaseOrder:pageList")
    @GetMapping("/download/sourceFile")
    @ResponseBody
    public ResultDTO<String> sourceFile(@RequestParam("missionId") String missionId) {
        return ResultDTO.success(fileMissionCenterService.selectFileInfo(missionId, FILE_PATH_PURCHASE_ORDER));
    }

    /**
     * 下载源文件-货件
     *
     * @param missionId 文件中心id
     * @return 下载链接
     */
    @RequiresPermissions("purchase:shipment:pageList")
    @GetMapping("/download/sourceFile/shipment")
    @ResponseBody
    public ResultDTO<String> sourceFileShipment(@RequestParam("missionId") String missionId) {
        return ResultDTO.success(fileMissionCenterService.selectFileInfo(missionId, FILE_PATH_PURCHASE_SHIPMENT));
    }
}
