package com.purchase.purchase_server.entity.vo;

import com.purchase.purchase_server.entity.dto.ForeignInventoryDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingDetailPlainDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectBaseParamDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;
import java.util.TreeMap;

/**
 * @Description 发货列表详情页导入数据信息
 * <AUTHOR>
 * @Date 2024/1/9 11:55
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TrialShippingImportInfoDetailVo implements Serializable {

    /**
     * 虚拟sku
     */
    private String virtualSku;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 基本参数
     */
    private ShippingProjectBaseParamDto baseParamDto;

    /**
     * 目标日销
     */
    private TreeMap<String, Double> destinationEverydaySale;

    /**
     * 海外仓库存
     */
    private List<ForeignInventoryDto> enableUseInventoryList;

    /**
     * 海外仓在途
     */
    private List<ForeignInventoryDto> onShippingInventoryList;

    /**
     * 计划
     */
    private List<ShippingDetailPlainDto> shippingDetailPlainDtoList;

    /**
     * 发货计划创建时间
     */
    private String projectCreateDate;

    /**
     * 是否可编辑
     */
    private String shippingStatus;

    /**
     * 仓库顺序数组
     */
    private List<String> warehouseSortList;

}
