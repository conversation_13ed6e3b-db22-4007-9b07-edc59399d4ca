package com.purchase.purchase_server.repository.dataRepository.Lcl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclConsolidationRecordDO;
import com.purchase.purchase_server.entity.dto.Lcl.LclConsolidationRecordDTO;
import com.purchase.purchase_server.entity.form.LclConsolidationRecordForm;
import com.purchase.purchase_server.mapper.Lcl.LclConsolidationRecordMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class LclConsolidationRecordRepositoryImpl extends ServiceImpl<LclConsolidationRecordMapper, LclConsolidationRecordDO> {


    public List<LclConsolidationRecordDO> list(LclConsolidationRecordDTO dto) {
        return baseMapper.selectList(Wrappers.<LclConsolidationRecordDO>lambdaQuery()
                .eq(StrUtil.isNotBlank(dto.getId()), LclConsolidationRecordDO::getId, dto.getId())
                .eq(StrUtil.isNotBlank(dto.getShippingRecordId()), LclConsolidationRecordDO::getShippingRecordId, dto.getShippingRecordId()));
    }

    public IPage<LclConsolidationRecordDO> pageList(LclConsolidationRecordForm form) {
        IPage<LclConsolidationRecordDO> page = new Page<>(form.getCurrent(), form.getSize());
        return baseMapper.selectPage(page, Wrappers.<LclConsolidationRecordDO>lambdaQuery()
                .eq(StrUtil.isNotBlank(form.getLclStatus()), LclConsolidationRecordDO::getLclStatus, form.getLclStatus())
                .eq(StrUtil.isNotBlank(form.getCreateBy()), LclConsolidationRecordDO::getCreateBy, form.getCreateBy())
                .between(StrUtil.isNotBlank(form.getCreateStartDate()) && StrUtil.isNotBlank(form.getCreateEndDate()),
                        LclConsolidationRecordDO::getCreateDate, form.getCreateStartDate(), form.getCreateEndDate())
                .orderByDesc(LclConsolidationRecordDO::getCreateDate)
        );
    }

    public int delete(String id) {
        return baseMapper.delete(id);
    }
}
