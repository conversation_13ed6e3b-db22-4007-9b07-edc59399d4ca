package com.purchase.purchase_server.service.factory;


import com.purchase.purchase_server.entity.dataObject.FactoryContainerDO;
import com.purchase.purchase_server.entity.dto.factory.FactoryContainerDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/6
 **/
public interface IFactoryContainerService {

    boolean insertOrUpdateContainerInfo(List<FactoryContainerDto> factoryContainerDOList);

    boolean insertOrUpdateContainerInfoByImport(List<FactoryContainerDto> factoryContainerDOList);

    List<FactoryContainerDO> listByFactoryInfoIds(List<String> ids);

    boolean deleteByFactoryInfoId(FactoryContainerDto val);
}
