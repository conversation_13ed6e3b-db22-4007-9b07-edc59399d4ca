package com.purchase.purchase_server.enums.purchaseOrders;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum PayStatusEnum {
    NOT_APPLIED("0", "未申请付款"),
    PAID("1", "已付款"),
    PARTIALLY_PAID("2", "未付清"),
    APPLIED_UNPAID("3", "已申请未付款");

    private final String code;
    private final String desc;

    public static PayStatusEnum ofCode(String code) {
        return Arrays.stream(PayStatusEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
