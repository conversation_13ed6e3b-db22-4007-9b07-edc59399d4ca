package com.auth.authorization_server.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.auth.authorization_server.aop.PreventReSubmit;
import com.auth.authorization_server.exception.BaseException;
import com.auth.authorization_server.model.dataObject.RoleDO;
import com.auth.authorization_server.model.dataObject.UserDO;
import com.auth.authorization_server.model.vo.TreeSelect;
import com.auth.authorization_server.req.UserLoginDTO;
import com.auth.authorization_server.req.UserParams;
import com.auth.authorization_server.domain.dto.MenuDTO;
import com.auth.authorization_server.domain.dto.RoleDTO;
import com.auth.authorization_server.domain.vo.UserVO;
import com.auth.authorization_server.domain.vo.UserInfoVO;
import com.auth.authorization_server.service.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.common.entity.LoginVo;
import com.auth.authorization_server.domain.dto.UserDTO;
import com.crafts_mirror.utils.constant.SecurityConstants;
import com.crafts_mirror.utils.context.SecurityContextHolder;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.JwtUtil;
import com.crafts_mirror.utils.utils.PatternUtils;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.Constants.AUTHORIZATION;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(path = "/user", produces = "application/json;charset=utf-8")
@Slf4j
public class SysUserController {

    @Resource
    private ILoginService loginService;

    @Resource
    private ISysUserService userService;

    @Resource
    private ISysRoleService roleService;

    @Resource
    private ISysMenuService menuService;

    @Resource
    private ISysDeptService deptService;

    @Resource
    private ISysPermissionService permissionService;

    @PostMapping("/login")
    public ResultDTO login(@RequestBody @Validated UserLoginDTO user, HttpServletRequest request, HttpServletResponse response) throws Exception {
        String authorization = request.getHeader(AUTHORIZATION);
        LoginVo loginVo = loginService.login(user, authorization);
        response.setHeader(JwtUtil.HEADER, loginVo.getToken());
        response.setHeader("Access-control-Expost-Headers", JwtUtil.HEADER);

        if (ObjectUtil.isNotEmpty(loginVo)) {
            SecurityContextHolder.set(SecurityConstants.DETAILS_USER_ID, loginVo.getUserId());
            SecurityContextHolder.set(SecurityConstants.DETAILS_USERNAME, loginVo.getUserName());
            SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginVo);
        }
        return ResultDTO.success(loginVo);
    }

    @GetMapping("/logout")
    public ResultDTO logout(HttpServletRequest request, HttpServletResponse response) {
        loginService.logout(request, response);
        return ResultDTO.success("退出成功");
    }

    @PostMapping("/register")
    public ResultDTO register(@RequestBody UserDO userDO) {
        loginService.register(userDO);
        return ResultDTO.success();
    }

    @GetMapping("/menuList")
    public ResultDTO getMenuList() {
        return ResultDTO.success(loginService.getMenuList());
    }

    /**
     * 获取用户列表
     */
    @RequiresPermissions("system:user:list")
    @PostMapping("/list")
    public ResultDTO<IPage<UserVO>> list(@RequestBody UserParams user) {
        return ResultDTO.success(userService.selectUserList(user));
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/info/getInfo")
    public ResultDTO<UserVO> getInfo() {
        UserDTO user = userService.selectUserById(SecurityUtils.getUserId());
        // 角色集合
        //Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);

        List<MenuDTO> menus = menuService.selectMenuTreeByUserId("1");
        List<MenuDTO> filterMenus = menuService.selectMenuTreeByUserId(SecurityUtils.getUserId());
        UserVO userVO = new UserVO();
        userVO.setSysUser(user);
        //userDTO.setRoles(roles);
        userVO.setPermissions(permissions);
        userVO.setRouterVo(menuService.buildMenus(menus));
        userVO.setFilterRouterVo(menuService.buildMenus(filterMenus));
        return ResultDTO.success(userVO);
    }

    /**
     * 根据用户编号获取详细信息
     */
    //@RequiresPermissions("system:user:query")
    @RequiresPermissions("system:user:list")
    @GetMapping(value = { "/detail/", "/detail/{userId}" })
    public ResultDTO<UserInfoVO> getInfo(@PathVariable(value = "userId", required = false) String userId) {
        userService.checkUserDataScope(userId);
        UserInfoVO userInfoVO = new UserInfoVO();
        List<RoleDTO> roles = roleService.selectRoleAll();
        userInfoVO.setRoles(UserDO.isAdmin(userId) ? roles : roles.stream().filter(r -> !RoleDO.isAdmin(r.getId())).collect(Collectors.toList()));
        List<TreeSelect> deptDTOS = deptService.selectDeptTreeList();
        userInfoVO.setDepts(deptDTOS);
        if (StrUtil.isNotBlank(userId)) {
            UserDTO userDTO = userService.selectUserById(userId);
            userInfoVO.setSysUser(userDTO);
            userInfoVO.setRoleIds(userDTO.getRoles().stream().map(RoleDTO::getId).collect(Collectors.toList()));
            List<TreeSelect> deptResult = userDTO.getDepts().stream()
                    .map(dept -> new TreeSelect(dept.getId(), dept.getDeptName(), new ArrayList<>()))
                    .collect(Collectors.toList());
            userInfoVO.setDeptIds(deptResult);
        }
        return ResultDTO.success(userInfoVO);
    }

    /**
     * 新增用户
     */
    //@RequiresPermissions("system:user:add")
    @RequiresPermissions("system:user:list")
    @PostMapping("/insert")
    @PreventReSubmit
    public ResultDTO<Boolean> add(@Valid @RequestBody UserDTO user) throws Exception {
        if (!PatternUtils.NUMBER_OR_WORD.matcher(user.getUserName()).matches()){
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "用户名只允许纯数字、纯字母和两者的搭配");
        }
        roleService.checkRoleDataScope(user.getRoleIds());
        if (!userService.checkUserNameUnique(user)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StrUtil.isNotBlank(user.getPhoneNumber()) && !userService.checkPhoneUnique(user)) {
            throw new BaseException(ResponseCodeEnum.ERROR, "新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        }

        return ResultDTO.success(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    //@RequiresPermissions("system:user:edit")
    @RequiresPermissions("system:user:list")
    @PutMapping("/update")
    @PreventReSubmit
    public ResultDTO<Boolean> edit(@Valid @RequestBody UserDTO user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getId());
        roleService.checkRoleDataScope(user.getRoleIds());
        if (!userService.checkUserNameUnique(user)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StrUtil.isNotBlank(user.getPhoneNumber()) && !userService.checkPhoneUnique(user)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        return ResultDTO.success(userService.updateUser(user));
    }

    /**
     * 重置密码
     */
    //@RequiresPermissions("system:user:edit")
    @RequiresPermissions("system:user:list")
    @PutMapping("/update/resetPwd")
    @PreventReSubmit
    public ResultDTO<Boolean> resetPwd(@RequestBody UserDTO user) throws Exception {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getId());
        return ResultDTO.success(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    //@RequiresPermissions("system:user:edit")
    @RequiresPermissions("system:user:list")
    @PutMapping("/update/changeStatus")
    @PreventReSubmit
    public ResultDTO<Boolean> changeStatus(@RequestBody UserDTO user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getId());
        return ResultDTO.success(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    //@RequiresPermissions("system:user:query")
    @RequiresPermissions("system:user:list")
    @GetMapping("/info/authRole/{userId}")
    public ResultDTO<UserInfoVO> authRole(@PathVariable("userId") String userId) {
        UserDTO user = userService.selectUserById(userId);
        List<RoleDTO> roles = roleService.selectRolesByUserId(userId);
        UserInfoVO userInfoVO = new UserInfoVO();
        userInfoVO.setSysUser(user);
        userInfoVO.setRoles(UserDO.isAdmin(userId) ? roles : roles.stream().filter(r -> !RoleDO.isAdmin(r.getId())).collect(Collectors.toList()));
        return ResultDTO.success(userInfoVO);
    }

    /**
     * 用户授权角色
     */
    //@RequiresPermissions("system:user:edit")
    @RequiresPermissions("system:user:list")
    @PutMapping("/update/authRole")
    public ResultDTO insertAuthRole(String userId, String[] roleIds) {
        userService.checkUserDataScope(userId);
        roleService.checkRoleDataScope(roleIds);
        userService.insertUserAuth(userId, roleIds);
        return ResultDTO.success();
    }
}