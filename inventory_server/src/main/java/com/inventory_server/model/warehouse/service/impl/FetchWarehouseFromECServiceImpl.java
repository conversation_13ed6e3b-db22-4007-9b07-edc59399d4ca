package com.inventory_server.model.warehouse.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.crafts_mirror.utils.common.entity.HttpRequestDetail;
import com.crafts_mirror.utils.constant.YiCangConstant;
import com.crafts_mirror.utils.listener.HttpResponseListener;
import com.crafts_mirror.utils.utils.YCRequestBody;
import com.inventory_server.infrastructures.entity.LogTrackNumDto;
import com.inventory_server.model.warehouse.entity.dos.YCResponseEntity;
import com.inventory_server.model.warehouse.entity.dto.WarehouseDetailBizContent;
import com.inventory_server.model.warehouse.entity.dto.WarehouseDetailDto;
import com.inventory_server.model.warehouse.entity.dto.WarehouseSummaryDto;
import com.inventory_server.model.warehouse.entity.form.WarehouseDetailForm;
import com.inventory_server.model.warehouse.entity.form.WarehouseSummaryForm;
import com.inventory_server.model.warehouse.repository.httpRepository.FetchWarehouseHttpRepository;
import com.inventory_server.model.warehouse.service.IFetchWarehouseFromECService;
import com.inventory_server.model.warehouse.service.IWarehouseSaveService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.http.HttpResponse;
import java.util.*;

/**
 * @Description 从易仓处获取仓库信息的service层
 * <AUTHOR>
 * @Date 2024/6/28 13:49
 **/
@Service
@Slf4j
public class FetchWarehouseFromECServiceImpl implements IFetchWarehouseFromECService {

    @Resource
    private IWarehouseSaveService warehouseSaveService;

    @Resource
    private FetchWarehouseHttpRepository fetchWarehouseHttpRepository;

    @Override
    public List<WarehouseSummaryDto> fetchWarehouseSummaryList() {
        // 实际请求参数
        WarehouseSummaryForm form = new WarehouseSummaryForm(0, 500);
        YCRequestBody<WarehouseSummaryForm> ycRequestBody = new YCRequestBody<>("V1.0.0", "getWarehouseList", form)
                .createECRequestBody();
        String body = JSON.toJSONString(ycRequestBody);
        // 请求路径
        URI uri = URI.create(YiCangConstant.GET_WAREHOUSE_SUMMARY_URL);
        HttpRequestDetail<YCResponseEntity> detail = new HttpRequestDetail<>(HttpRequestDetail.HttpMethod.POST, uri, body, YCResponseEntity.class);
        // 获取仓库概述并返回
        return fetchWarehouseHttpRepository.fetchAllWarehouseSummaryList(detail);
    }

    @Override
    public void asyncFetchAndSaveWarehouseDetailList(List<String> yiCangWarehouseCodeList) {
        // 为了防止一次性请求数据过大，所以分页获取
        List<List<String>> splitCodeList = CollectionUtil.split(yiCangWarehouseCodeList, 50);

        URI uri = URI.create(YiCangConstant.GET_WAREHOUSE_SUMMARY_URL);
        List<HttpRequestDetail<YCResponseEntity>> detailList = new ArrayList<>(8);
        for (List<String> yiCangCodeList : splitCodeList) {
            WarehouseDetailForm form = new WarehouseDetailForm(yiCangCodeList);
            YCRequestBody<WarehouseDetailForm> ycRequestBody = new YCRequestBody<>("V1.0.0", "getWarehouse", form)
                    .createECRequestBody();
            String body = JSON.toJSONString(ycRequestBody);
            HttpRequestDetail<YCResponseEntity> detail = new HttpRequestDetail<>(HttpRequestDetail.HttpMethod.POST, uri, body, YCResponseEntity.class);
            detailList.add(detail);
        }
        String token = SecurityUtils.getToken();
        String userName = SecurityUtils.getUsername();
        log.warn("从易仓处获取仓库信息：请求参数：{}", detailList.stream().map(HttpRequestDetail::body).toList());
        HttpResponseListener<YCResponseEntity> httpResponseListener = new HttpResponseListener<>() {
            @Override
            public void onResponse(HttpResponse<YCResponseEntity> response) {
                int statusCode = response.statusCode();
                if (statusCode != 200) {
                    log.error("从易仓处获取仓库详情异常");
                    throw new IllegalArgumentException("从易仓处获取仓库详情异常");
                }

                YCResponseEntity body = response.body();
                if (!Objects.equals(body.getCode(), "200")) {
                    JSONArray jsonArray = JSON.parseArray(response.body().getBizContent());
                    JSONObject jsonObject = jsonArray.getJSONObject(0);
                    log.error("从易仓处获取仓库详情异常：{}", jsonObject.get("errorMsg"));
                    throw new IllegalArgumentException("从易仓处获取仓库详情异常" + body.getMessage());
                }

                String bizContent = body.getBizContent();
                WarehouseDetailBizContent warehouseSummaryBizContent = JSON.parseObject(bizContent, WarehouseDetailBizContent.class);
                List<WarehouseDetailDto> warehouseDetailList = warehouseSummaryBizContent.getData();
                log.warn("从易仓处获取仓库信息：拉取到的易仓仓库id：{}", warehouseDetailList.stream().map(WarehouseDetailDto::getYcWarehouseId).toList());

                Map<String, String> map = new HashMap<>();

                warehouseSaveService.saveWarehouseInfo(warehouseDetailList, new LogTrackNumDto(map, token, userName));
            }

            @Override
            public void onError(Throwable throwable, HttpRequestDetail<YCResponseEntity> requestDetail) {
                log.error("从易仓处获取仓库列表异常", throwable);
                throw new RuntimeException("从易仓处获取仓库列表异常", throwable);
            }

            @Override
            public void afterAll() {

            }
        };

        fetchWarehouseHttpRepository.fetchWarehouseDetailList(detailList, httpResponseListener);
    }
}
