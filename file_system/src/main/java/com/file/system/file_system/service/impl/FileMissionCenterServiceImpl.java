package com.file.system.file_system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.file.system.file_system.entity.dataObject.FileMissionCenterDO;
import com.file.system.file_system.entity.form.FileMissionForm;
import com.file.system.file_system.entity.form.FileMissionPageForm;
import com.file.system.file_system.entity.vo.MissionCenterVo;
import com.file.system.file_system.entity.vo.UserInteriorVO;
import com.file.system.file_system.repository.FileMissionRepositoryImpl;
import com.file.system.file_system.service.IFileMissionCenterService;
import com.file.system.file_system.service.ISysUserIneriorService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 文档中心业务编排层
 * <AUTHOR>
 * @Date 2023/12/11 17:59
 **/
@Service
public class FileMissionCenterServiceImpl implements IFileMissionCenterService {

    @Resource
    private FileMissionRepositoryImpl fileMissionRepository;

    @Resource
    private ISysUserIneriorService sysUserIneriorService;

    @Override
    public String saveFileMission(FileMissionForm form) {
        return fileMissionRepository.saveFileMission(form);
    }

    @Override
    public IPage<MissionCenterVo> getMissionPage(FileMissionPageForm form) {
        IPage<FileMissionCenterDO> page = fileMissionRepository.getFileMissionPage(form);
        if(page == null || page.getTotal() == 0) {
            return new Page<>(form.getCurrent(), form.getSize(), 0);
        }
        IPage<MissionCenterVo> missionCenterVoPage = new Page<>(form.getCurrent(), form.getSize(), page.getTotal());
        List<UserInteriorVO> userList = sysUserIneriorService.getUserList();
        Map<String, String> userMap = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));
        List<MissionCenterVo> missionCenterVo = page.getRecords().stream().map(this::convertMissionCenterVo).collect(Collectors.toList());


        missionCenterVo.forEach(i -> {
            i.setCreateBy(userMap.getOrDefault(i.getCreateBy(), i.getCreateBy()));
        });
        missionCenterVoPage.setRecords(missionCenterVo);
        return missionCenterVoPage;
    }

    @Override
    public void deleteMission(FileMissionForm form) {
        fileMissionRepository.removeById(form.getMissionId());
    }

    private MissionCenterVo convertMissionCenterVo(FileMissionCenterDO mission) {
        List<String> failResultList = null;
        if(StrUtil.isNotBlank(mission.getFailedResult())) {
            failResultList = Arrays.asList(mission.getFailedResult().split(";"));
        }
        return MissionCenterVo.builder()
                .missionId(mission.getId())
                .fileName(mission.getFileName())
                .filePath(mission.getFilePath())
                .createDate(mission.getCreateDate())
                .finishDate(mission.getFinishDate())
                .missionType(mission.getType())
                .missionStatus(mission.getImportStatus())
                .missionResult(mission.getImportResult())
                .failedResultList(failResultList)
                .createBy(mission.getCreateBy())
                .build();
    }
}
