package com.inventory_server.model.redundancy.service.impl.endCal;

import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.utils.dp.ProduceDaysDp;
import com.inventory_server.model.product.enums.VirtualProductTypeEnum;
import com.inventory_server.model.redundancy.entity.dp.InventoryCalDp;
import com.inventory_server.model.redundancy.enums.ShippingDateEnum;
import com.inventory_server.model.redundancy.repository.redisRepository.IRedundancyCalEndDateRepository;
import com.inventory_server.model.redundancy.service.IRedundancyEndCalService;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.REDUNDANCY_SHIPPING_DATE_FLAG;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.SPECIAL_REDUNDANCY_END_DATE;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.*;
import static com.crafts_mirror.utils.utils.DateUtils.YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT;
import static com.inventory_server.model.product.enums.VirtualProductTypeEnum.CHILDREN;
import static com.inventory_server.model.product.enums.VirtualProductTypeEnum.SEASONAL;

/**
 * @Description 正常冗余库存截止时间
 * <AUTHOR>
 * @Date 2025/2/5 16:25
 **/
@Service(value = "normalRedundancyEndCalServiceImpl")
public class NormalRedundancyEndCalServiceImpl implements IRedundancyEndCalService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IRedundancyCalEndDateRepository redundancyCalEndDateRepository;

    private final DateTimeFormatter hyphenFormatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_HYPHEN);
    private final DateTimeFormatter slashFormatter = YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT;

    private static final Set<String> specialEndCalDateCategorySet = new HashSet<>();
    private static final Set<VirtualProductTypeEnum> specialEndCalDateProductTypeSet = new HashSet<>();

    static {
        specialEndCalDateCategorySet.add("2");
        specialEndCalDateCategorySet.add("4");
        specialEndCalDateProductTypeSet.add(CHILDREN);
        specialEndCalDateProductTypeSet.add(SEASONAL);
    }

    /**
     * 正常截止日常计算公式：截止时间=库存导入日today+today对应生产交期+国内中转天数+加权头程时间+安全天数+7+30
     * 特殊截止日期：业务自行决定，在redis中更改，默认为25年8月31号
     *
     * @param inventoryCalDp 冗余库存计算参数
     * @param startDate 开始日期
     * @return 截止日期
     */
    @Override
    public LocalDate calRedundancyEndDate(InventoryCalDp inventoryCalDp, LocalDate startDate, Map<String, Integer> headShippingDateMap,
                                          int redundantDateNum) {
        String categoryId = inventoryCalDp.selfProduct().getCategoryId();
        Integer productType = inventoryCalDp.virtualProduct().getProductType();
        VirtualProductTypeEnum typeEnum = VirtualProductTypeEnum.ofCode(productType);

        // 产品分类为灯具或产品类型为季节性产品、儿童产品需要走特殊截止日期
        String needSpecial = stringRedisTemplate.opsForValue().get(SPECIAL_REDUNDANCY_NEEDED);
        if ("1".equals(needSpecial) && (specialEndCalDateCategorySet.contains(categoryId) || specialEndCalDateProductTypeSet.contains(typeEnum))) {
            String endDate = stringRedisTemplate.opsForValue().get(SPECIAL_REDUNDANCY_END_DATE);
            if (StrUtil.isNotBlank(endDate)) {
                try {
                    return LocalDate.parse(endDate);
                } catch (Exception e) {
                    throw new RuntimeException(String.format("缓存中的日期为：%s，不符合%s格式，请修改", endDate, YYYY_MM_DD_DATE_FORMAT_HYPHEN));
                }
            } else {
                LocalDate localDate = LocalDate.of(2025, 8, 31);
                String formatEndDate = hyphenFormatter.format(localDate);
                stringRedisTemplate.opsForValue().set(SPECIAL_REDUNDANCY_END_DATE, formatEndDate);
                return localDate;
            }
        } else {
            // 产品分类为家具，且产品类型为非季节性、儿童产品的需要通过公式计算截止日期
            String shippingDateFlag = stringRedisTemplate.opsForValue().get(REDUNDANCY_SHIPPING_DATE_FLAG);
            if (StrUtil.isBlank(shippingDateFlag)) {
                shippingDateFlag = "0";
                stringRedisTemplate.opsForValue().set(REDUNDANCY_SHIPPING_DATE_FLAG, "0");
            }
            Map<String, Double> shippingRatioMap = inventoryCalDp.inventoryRules().shippingRulesDp().shippingRatioMap();
            // 根据需求获取头程时间
            int headShippingDate = ShippingDateEnum.getHeadShippingDateByCode(shippingDateFlag, headShippingDateMap, shippingRatioMap);

            // 获取下单的生产交期
            String purchaseDate = inventoryCalDp.selfProduct().getPurchaseDate();
            ProduceDaysDp produceDaysDp = ProduceDaysDp.initProduceDaysDp(purchaseDate);

            // 采购下单天数
            int purchaseDays = inventoryCalDp.inventoryRules().daysRulesDp().purchaseDays();
            LocalDate produceEndDate = produceDaysDp.getProduceEndDate(startDate.plusDays(purchaseDays));

            // 中转天数
            int transitDays = inventoryCalDp.inventoryRules().daysRulesDp().transitDays();

            // 获取安全天数业务规定的固定安全天数
            int safeDays = inventoryCalDp.inventoryRules().daysRulesDp().safeDays();

            // 业务规定的固定冗余天数
            int extraRedundancyDate = redundancyCalEndDateRepository.getExtraRedundancyDate();

            // 冗余库存计算截止时间
            return produceEndDate.plusDays(transitDays + headShippingDate + safeDays + redundantDateNum + extraRedundancyDate);
        }
    }

    @Override
    public String getEndDateDesc(InventoryCalDp inventoryCalDp, LocalDate startDate, Map<String, Integer> headShippingDateMap,
                                 int redundantDateNum) {
        String categoryId = inventoryCalDp.selfProduct().getCategoryId();
        Integer productType = inventoryCalDp.virtualProduct().getProductType();
        VirtualProductTypeEnum typeEnum = VirtualProductTypeEnum.ofCode(productType);
        LocalDate localDate = calRedundancyEndDate(inventoryCalDp, startDate, headShippingDateMap, redundantDateNum);

        String needSpecial = stringRedisTemplate.opsForValue().get(SPECIAL_REDUNDANCY_NEEDED);
        if ("1".equals(needSpecial) && (specialEndCalDateCategorySet.contains(categoryId) || specialEndCalDateProductTypeSet.contains(typeEnum))) {
            return String.format("截止时间：%s，最久备货时间", slashFormatter.format(localDate));
        }

        Map<String, Double> shippingRatioMap = inventoryCalDp.inventoryRules().shippingRulesDp().shippingRatioMap();

        // 获取下单的生产交期
        String purchaseDate = inventoryCalDp.selfProduct().getPurchaseDate();
        ProduceDaysDp produceDaysDp = ProduceDaysDp.initProduceDaysDp(purchaseDate);
        int purchaseDays = inventoryCalDp.inventoryRules().daysRulesDp().purchaseDays();
        LocalDate factoryStartDate = startDate.plusDays(purchaseDays);
        int produceDay = produceDaysDp.getProduceDay(factoryStartDate);

        // 中转天数
        Integer transitDays = inventoryCalDp.inventoryRules().daysRulesDp().transitDays();

        // 根据需求获取头程时间
        String shippingDateFlag = stringRedisTemplate.opsForValue().get(REDUNDANCY_SHIPPING_DATE_FLAG);
        int headShippingDate = ShippingDateEnum.getHeadShippingDateByCode(shippingDateFlag, headShippingDateMap, shippingRatioMap);

        // 获取安全天数业务规定的固定安全天数
        Integer safeDays = inventoryCalDp.inventoryRules().daysRulesDp().safeDays();

        // 业务规定的固定冗余天数
        int extraRedundancyDate = redundancyCalEndDateRepository.getExtraRedundancyDate();
        return String.format("截止时间：%s=%s+%s+%s+%s+%s+%s+%s+%s", slashFormatter.format(localDate), slashFormatter.format(startDate),
                purchaseDays, produceDay, transitDays, headShippingDate, safeDays, redundantDateNum, extraRedundancyDate);
    }
}
