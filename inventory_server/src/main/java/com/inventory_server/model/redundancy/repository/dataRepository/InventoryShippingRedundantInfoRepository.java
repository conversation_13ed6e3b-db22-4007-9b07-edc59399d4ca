package com.inventory_server.model.redundancy.repository.dataRepository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.inventory_server.applications.dto.InventoryShipRedundantInfoDto;
import com.inventory_server.model.redundancy.entity.dos.InventoryShippingRedundantInfoDO;
import com.inventory_server.model.redundancy.mapper.InventoryShippingRedundantInfoMapper;
import com.inventory_server.model.warehouse.entity.dos.WarehouseSenboInfoDO;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @Description 在途冗余数据库交互层
 * <AUTHOR>
 * @Date 2024/12/14 11:26
 **/
@Service
public class InventoryShippingRedundantInfoRepository extends ServiceImpl<InventoryShippingRedundantInfoMapper, InventoryShippingRedundantInfoDO> {

    public List<InventoryShipRedundantInfoDto> getShipRedundantList(List<String> watchBoardIdList) {
        return baseMapper.selectJoinList(InventoryShipRedundantInfoDto.class, new MPJLambdaWrapper<InventoryShippingRedundantInfoDO>()
                .select(WarehouseSenboInfoDO::getWarehouse)
                .select(InventoryShippingRedundantInfoDO::getShipmentCode)
                .select(InventoryShippingRedundantInfoDO::getEnableUsingDate)
                .select(InventoryShippingRedundantInfoDO::getRedundantNum)
                .select(InventoryShippingRedundantInfoDO::getOnShippingWatchBoardId)
                .select(InventoryShippingRedundantInfoDO::getStartShippingDate)
                .leftJoin(WarehouseSenboInfoDO.class, WarehouseSenboInfoDO::getId, InventoryShippingRedundantInfoDO::getWarehouse)
                .in(InventoryShippingRedundantInfoDO::getOnShippingWatchBoardId, watchBoardIdList)
                .orderByAsc(InventoryShippingRedundantInfoDO::getEnableUsingDate)
                .orderByAsc(InventoryShippingRedundantInfoDO::getWarehouse));
    }

    public List<InventoryShipRedundantInfoDto> getShipRedundantListByWatchBoardId(String watchBoardId) {
        return baseMapper.selectJoinList(InventoryShipRedundantInfoDto.class, new MPJLambdaWrapper<InventoryShippingRedundantInfoDO>()
                .select(WarehouseSenboInfoDO::getWarehouse)
                .select(InventoryShippingRedundantInfoDO::getShipmentCode)
                .select(InventoryShippingRedundantInfoDO::getEnableUsingDate)
                .select(InventoryShippingRedundantInfoDO::getRedundantNum)
                .leftJoin(WarehouseSenboInfoDO.class, WarehouseSenboInfoDO::getId, InventoryShippingRedundantInfoDO::getWarehouse)
                .eq(InventoryShippingRedundantInfoDO::getOnShippingWatchBoardId, watchBoardId)
                .orderByAsc(InventoryShippingRedundantInfoDO::getWarehouse));
    }

}
