package com.purchase.purchase_server.enums.lcl;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ShipmentCodeCreationEnum {

    FURNITURE("家具", "JX"),
    LAMP("灯具", "LP"),
    MIX("家具+灯具", "KM"),
    ;

    private final String type;

    private final String prefix;

    /**
     * 根据年限获取前缀的后半部分
     * 目前定死25
     * @return
     */
    public static String getYearSuffix() {
        return "25-";
    }
}
