package com.inventory_server.interfaces.interior;

import com.inventory_server.applications.service.IWarehouseAppService;
import com.inventory_server.applications.vo.response.ResultDTO;
import com.inventory_server.model.warehouse.entity.dos.WarehouseSenboInfoDO;
import com.inventory_server.model.warehouse.entity.dos.WarehouseSenboInfoSnapshotDO;
import com.inventory_server.model.warehouse.entity.vo.SenboWarehouseVo;
import com.inventory_server.model.warehouse.repository.databaseRepository.WarehouseSenboSnapshotRepository;
import com.inventory_server.model.warehouse.service.IWarehouseService;
import com.inventory_server.model.warehouse.service.IWarehouseSenboSnapshotService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Description 内部服务调用
 * <AUTHOR>
 * @Date 2024/7/16 16:50
 **/
@Validated
@RestController
@RequestMapping(value = "interior/warehouse")
@Slf4j
public class WarehouseInfoController {

    @Resource
    private IWarehouseAppService warehouseAppService;

    @Resource
    private IWarehouseService warehouseService;

    @Resource
    private WarehouseSenboSnapshotRepository warehouseSenboSnapshotRepository;

    @GetMapping("/senbo/warehouse")
    @ResponseBody
    public ResultDTO<SenboWarehouseVo> getSenboWarehouse() {
        return ResultDTO.success(warehouseAppService.getSenboWarehouseVo());
    }

    @GetMapping("/senbo/warehouseWithoutMiddleTransit")
    @ResponseBody
    public ResultDTO<SenboWarehouseVo> getSenboWarehouseWithoutMiddleTransit() {
        return ResultDTO.success(SenboWarehouseVo.builder().senboWarehouseList(warehouseService.getSenboWarehouseListWithoutMiddleTransit()).build());
    }

    @PostMapping("/get/senboWarehouse/snapshot")
    @ResponseBody
    public ResultDTO<String> getLatestWarehouseSnapshot() {
        WarehouseSenboInfoSnapshotDO senboInfoDO = warehouseSenboSnapshotRepository.getNewestWarehouseSenboInfo();
        return ResultDTO.success(senboInfoDO.getWarehouseSenboInfo());
    }
}
