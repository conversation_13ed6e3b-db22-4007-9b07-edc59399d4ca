package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 * 
 * @TableName cm_replenishment_record
 */

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value ="cm_replenishment_record")
public class ReplenishmentRecordDO extends BaseEntity {

    /**
     * 试算状态（-2：作废；-1：试算失败；0：试算中；1：待保存；2：已保存）
     * @see com.purchase.purchase_server.enums.ReplenishmentStatusEnum
     */
    private String replenishmentStatus;

    /**
     * 数据来源类型（0：导入；）
     */
    private String dataSourceType;

    /**
     * 数据来源ID
     */
    private String dataSourceId;

    /**
     * 理论补货日（开始）
     */
    private Date advicePurchaseStartDate;

    /**
     * 理论补货日（结束）
     */
    private Date advicePurchaseEndDate;

    /**
     * 试算失败原因
     */
    private String failedResult;

    /**
     * 任务结果
     */
    private String taskResult;
}