<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.purchase.purchase_server.mapper.Lcl.LclFinishedInventoryMapper">

    <resultMap id="LclFinishedInventoryBOMap" type="com.purchase.purchase_server.entity.bo.Lcl.LclFinishedInventoryBO">
        <id property="id" column="id"/>
        <result property="shipmentCode" column="shipment_code"/>
        <result property="deliveryType" column="delivery_type"/>
        <result property="contractCode" column="contract_code"/>
        <result property="factoryFinishedDate" column="factory_finished_date"/>
        <result property="factoryRemainNum" column="factory_remain_num"/>
        <result property="factoryShippingPackageNum" column="factory_shipping_package_num"/>
        <result property="lclRecordId" column="lcl_record_id"/>
        <result property="virtualSkuId" column="virtual_sku_id"/>
        <result property="isOldStatus" column="is_old_status"/>
        <result property="productSnapshotId" column="product_snapshot_id"/>
        <result property="shippingRatio" column="shipping_ratio"/>
        <result property="isForeignFlag" column="is_foreign_flag"/>
        <result property="remarks" column="remarks"/>
        <result property="categoryId" column="category_Id"/>

        <!-- 基本属性映射 -->
        <result property="selfSkuId" column="self_sku_id"/>
        <result property="caseLength" column="case_length"/>
        <result property="caseWidth" column="case_width"/>
        <result property="caseHeight" column="case_height"/>
        <result property="containerLoad" column="container_load"/>
        <result property="destinationSku" column="destination_sku"/>
        <!-- 其他基本属性映射... -->
        <result property="headShippingDays" column="head_shipping_days"/>

        <!-- 一对多关系映射 -->
        <collection property="lclTrialDTOList" column="lcl_factory_finished_id" ofType="com.purchase.purchase_server.entity.dto.Lcl.LclTrialShippingInventoryDTO">
            <!-- 试单发货清单映射 -->
            <id property="id" column="trial_id"/>
            <result property="shippingStartDate" column="shipping_start_date"/>
            <result property="packageNum" column="package_num"/>
            <result property="destinationWarehouse" column="destination_warehouse"/>
            <result property="lclFactoryFinishedId" column="lcl_factory_finished_id"/>
            <result property="shippingNum" column="shipping_num"/>
            <result property="isPackageFull" column="is_package_full"/>
            <result property="remarks" column="trial_remarks"/>
        </collection>
    </resultMap>

    <select id="listWithTrial" resultMap="LclFinishedInventoryBOMap">
        SELECT
            t1.id,t1.delivery_type,t1.remarks,t1.is_foreign_flag,t1.shipment_code,t1.contract_code,t1.factory_finished_date,t1.factory_shipping_package_num,t1.factory_remain_num,t1.factory_shipping_package_num,
            t1.lcl_record_id,t1.virtual_sku_id,t1.is_old_status,t1.product_snapshot_id,t1.shipping_ratio,
            t3.self_sku_id,
            t3.self_data->>'$.caseLength' as case_length,
            t3.self_data->>'$.caseWidth' as case_width,
            t3.self_data->>'$.caseHeight' as case_height,
            t3.self_data->>'$.containerLoad' as container_load,
            t3.self_data->>'$.categoryId' as category_Id,


            CASE t1.is_old_status
                WHEN '0' THEN t3.virtual_data->>'$.virtualSku'
                WHEN '1' THEN t3.virtual_data->>'$.oldSku'
                END as destination_sku,
            t4.id as trial_id,
            t4.shipping_start_date,t4.package_num,t4.destination_warehouse,
            t4.lcl_factory_finished_id,t4.shipping_num,t4.is_package_full,t4.remarks as trial_remarks,
            t2.head_shipping_days
        FROM cm_lcl_finished_inventory t1
                 LEFT JOIN cm_lcl_consolidation_record t2 ON t2.id = t1.lcl_record_id AND t2.STATUS = '0'
                 LEFT JOIN cm_product_snapshot t3 ON t3.id = t1.product_snapshot_id AND t3.STATUS = '0'
                 LEFT JOIN cm_lcl_trial_shipping_inventory t4 ON t4.lcl_factory_finished_id = t1.id AND t4.STATUS = '0'
        WHERE t1.STATUS = '0' and t2.id = #{lclRecordId}
        and t4.shipping_start_date >= #{shippingStartDate}
        ORDER BY t1.factory_finished_date ASC
    </select>

    <!-- 删除 -->
    <delete id="delete">
        DELETE
        FROM cm_lcl_finished_inventory
        WHERE id = #{factoryFinishedId}
    </delete>


    <update id="dropById">
        UPDATE cm_lcl_finished_inventory
        SET status = '1'
        WHERE id = #{factoryFinishedId}
    </update>

    <delete id="deleteByLclRecordId">
        DELETE
        FROM cm_lcl_finished_inventory
        WHERE lcl_record_id = #{lclRecordId}
    </delete>

    <select id="listByLclRecordId" resultType="com.purchase.purchase_server.entity.dto.Lcl.LclConsolidationNonFinishedInventoryDTO">
        SELECT
            t.id,
            t.shipment_code,
            t.contract_code,
            t.factory_finished_date,
            t.factory_shipping_package_num,
            t.factory_remain_num,
            t.lcl_record_id,
            t.virtual_sku_id,
            t.is_old_status,
            t.delivery_type,
            t.remarks,
            t.product_snapshot_id,
            t.is_foreign_flag,
            t.lcl_foreign_id,
            t1.self_data->>'$.productName' as product_name,
            t1.self_data->>'$.sku' as self_sku,
            t1.self_data->>'$.containerLoad' as container_load,
            t1.self_data->>'$.caseLength' as case_length,
            t1.self_data->>'$.caseWidth' as case_width,
            t1.self_data->>'$.caseHeight' as case_height,
            t1.self_data->>'$.singleCaseGrossWeight' as single_case_gross_weight,
            t1.self_data->>'$.commodityInspection' as commodity_inspection,
            t1.self_data->>'$.buyer' as buyer,
            CASE t1.self_data->>'$.taxes' WHEN '1' THEN t1.self_data->>'$.priceWithTaxes' WHEN '0' THEN t1.self_data->>'$.price' END as unit_or_contract_price,
            t1.self_data->>'$.pcsType' as pcs_type,
            t1.virtual_data->>'$.channel' as channel,
            t1.virtual_data->>'$.operator' as operator,
            CASE t.is_old_status WHEN '1' THEN t1.virtual_data->>'$.oldSku' WHEN '0' THEN t1.virtual_sku END as destination_sku,
            t1.factory_data->>'$.addressCode' as address_code,
            t1.factory_data->>'$.purchaser' as purchaser,
            t1.factory_data->>'$.orderTracker' as order_tracker,
            t1.factory_data->>'$.currency' as currency
        FROM
            cm_lcl_finished_inventory t
        LEFT JOIN
            product_snapshot t1 ON t1.id = t.product_snapshot_id
        WHERE
            t.lcl_record_id = #{lclRecordId}
    </select>
</mapper>
