package com.purchase.purchase_server.assembler;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Date;
import java.util.List;

/**
 * 转换工具类
 *
 * <AUTHOR>
 * @date 2021/8/26
 */

public class YiCangPurchaseOrdersConvert {
    public static String listToString (List<String> value){
        if (CollectionUtil.isEmpty(value)) {
            return null;
        }
        return String.join(",", value);
    }

    public static Date stringToDate (String value){
        if (StrUtil.isBlank(value) || "0000-00-00 00:00:00".equals(value)) {
            return null;
        }
        return DateUtil.parse(value);
    }
}
