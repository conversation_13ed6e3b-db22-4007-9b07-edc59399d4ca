package com.product.product_server.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.product.product_server.entity.dataObject.VirtualUpgradeRelationDO;
import com.product.product_server.mapper.VirtualUpgradeMapper;
import org.springframework.stereotype.Service;

/**
 * @Description 产品升级款关系相关repository类
 * <AUTHOR>
 * @Date 2023/12/6 14:55
 **/
@Service
public class VirtualUpgradeRelationRepositoryImpl extends ServiceImpl<VirtualUpgradeMapper, VirtualUpgradeRelationDO> {

    public void deleteUpgradeRelation(String id){
        baseMapper.deleteUpgradeRelation(id);
    }
}
