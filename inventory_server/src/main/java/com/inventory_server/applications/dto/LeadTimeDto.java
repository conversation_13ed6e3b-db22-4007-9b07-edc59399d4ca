package com.inventory_server.applications.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/15
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LeadTimeDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 3753397410239012444L;

    private String saleRuleId;
    /**
     * 采购周期
     */
    private Integer purchaseDays;
    /**
     * 中转天数
     */
    private Integer transitDays;
    /**
     * 正常备货生产周期
     */
    private Integer normalPurchaseCircle;
    /**
     * 紧急备货生产周期
     */
    private Integer urgentPurchaseCircle;
    /**
     * 头程时间
     */
    private Integer headShippingDate;

    /**
     * 加急头程天数
     */
    private Integer urgentHeadShippingDate;


    /**
     * 加急补货仓库
     */
    private String urgentPurchaseWarehouse;
}
