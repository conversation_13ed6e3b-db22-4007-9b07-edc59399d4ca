package com.product.product_server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum IsLeafEnum {
    YES("1", "是"),
    NO("0", "否")
    ;

    private final String code;
    private final String desc;

    public static IsLeafEnum ofCode(String code) {
        if (code == null){
            return null;
        }
        return Arrays.stream(IsLeafEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
