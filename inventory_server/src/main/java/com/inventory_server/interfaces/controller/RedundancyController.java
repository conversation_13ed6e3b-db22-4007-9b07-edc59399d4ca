package com.inventory_server.interfaces.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.utils.enums.YesOrNoEnum;
import com.inventory_server.applications.cqe.InventoryIdQuery;
import com.inventory_server.applications.cqe.InventoryInfoQuery;
import com.inventory_server.applications.cqe.LeadTimeCommand;
import com.inventory_server.applications.dto.*;
import com.inventory_server.applications.service.IRedundantInventoryAppService;
import com.inventory_server.applications.vo.response.ResultDTO;
import com.inventory_server.infrastructures.aop.PreventReSubmit;
import com.inventory_server.infrastructures.entity.LogTrackNumDto;
import com.inventory_server.infrastructures.exception.RequestTooFrequentlyException;
import com.inventory_server.model.product.entity.vo.OperatorSearchVo;
import com.inventory_server.model.redundancy.entity.dos.InventorySoldOutDaysDO;
import com.inventory_server.model.redundancy.entity.dto.ProductCategoryDTO;
import com.inventory_server.model.redundancy.entity.dto.RedundancySaleDestinationDto;
import com.inventory_server.model.redundancy.entity.form.UrgentHeadShipDateForm;
import com.inventory_server.model.redundancy.entity.vo.NormalDeliveryWatchBoardVo;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.crafts_mirror.utils.constant.RedisKeyConstant.REDUNDANCY_IN_CAL;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.WAREHOUSE_UPDATE_LOCK;

/**
 * @Description 冗余库存Controller层
 * <AUTHOR>
 * @Date 2024/5/7 13:50
 **/
@Validated
@RestController
@RequestMapping(value = "/redundant")
@Slf4j
public class RedundancyController {

    @Resource
    private IRedundantInventoryAppService redundantInventoryAppService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @RequiresPermissions("inventory:redundant:import")
    @PostMapping("/import")
    @PreventReSubmit
    public ResultDTO<String> importDeliveryInfo(@RequestParam("file") MultipartFile file) {
        log.warn("导入冗余库存-------------准备开始导入日志");
        stringRedisTemplate.opsForValue().set(REDUNDANCY_IN_CAL, "1", 2, TimeUnit.HOURS);
        try {
            redundantInventoryAppService.calRedundantInventoryApp(file.getInputStream(), file.getBytes(), file.getOriginalFilename());
        } catch (IOException e) {
            log.error("导入冗余库存excel失败，异常信息：", e);
        } finally {
            stringRedisTemplate.delete(REDUNDANCY_IN_CAL);
        }
        return ResultDTO.success("正在导入");
    }

    /**
     * 库存导入模板
      */
    @PostMapping("/template/export")
    public void exportTemplate(HttpServletResponse response) {
        redundantInventoryAppService.exportTemplate(response);
    }

    @RequiresPermissions("inventory:redundant:list")
    @PostMapping("/pageList")
    public ResultDTO<IPage<InventoryInfoDto>> getPageList(@RequestBody InventoryInfoQuery form) {
        return ResultDTO.success(redundantInventoryAppService.pageList(form));
    }

    @RequiresPermissions("inventory:redundant:export")
    @PostMapping("/pageList/export")
    public void export(@RequestBody InventoryInfoQuery form, HttpServletResponse response) {
        redundantInventoryAppService.export(form, response);
    }

    /**
     * 详情页基本参数、目标日销
     * @param inventoryInfoId 库存信息id
     * @return 返回基本参数和目标日销，其他参数是{@link RedundancyController#getShippingCalculation(String)}
     * 以及{@link RedundancyController#getShippingCalculationWithRequirement(String)}中返回
     */
    @RequiresPermissions("inventory:redundant:list")
    @GetMapping("/detail/inventoryRules")
    public ResultDTO<DetailInventoryRulesDto> getDetailRule(@RequestParam @NotBlank(message = "库存信息id不能为空") String inventoryInfoId) {
        return ResultDTO.success(redundantInventoryAppService.getDetailInventoryRules(inventoryInfoId));
    }

    /**
     * 详情页 未来发货和冗余试算看板 watchBoard表单以及模拟试算表格信息
     * @param inventoryInfoId 库存信息id
     * @return 返回试算数据
     */
    @RequiresPermissions("inventory:redundant:list")
    @GetMapping("/detail/shippingCalculation")
    public ResultDTO<DetailInventoryRulesDto> getShippingCalculation(@RequestParam @NotBlank(message = "库存信息id不能为空") String inventoryInfoId) {
        return ResultDTO.success(redundantInventoryAppService.getShippingCalculation(inventoryInfoId));
    }

    @RequiresPermissions("inventory:redundant:list")
    @GetMapping("/list/delivery/watchBoard")
    public ResultDTO<NormalDeliveryWatchBoardVo> getNormalDeliveryWatchBoard(@RequestParam @NotBlank(message = "库存信息id不能为空") String inventoryInfoId) {
        return ResultDTO.success(redundantInventoryAppService.getNormalDeliveryWatchBoard(inventoryInfoId));
    }

    /**
     * 详情页 模拟发货试算看板 watchBoard表单以及模拟试算表格信息
     * @param inventoryInfoId 库存信息id
     * @return 返回试算数据
     */
    @RequiresPermissions("inventory:redundant:list")
    @GetMapping("/detail/normal/shippingCalculation")
    public ResultDTO<NormalDeliveryWatchBoardVo> getNormalShippingCalculation(@RequestParam @NotBlank(message = "库存信息id不能为空") String inventoryInfoId) {
        return ResultDTO.success(redundantInventoryAppService.getNormalShippingCalculation(inventoryInfoId));
    }

    /**
     * 详情页 库存和在途试算看板 watchBoard表单以及模拟试算表格信息
     * @param inventoryInfoId 库存信息id
     * @return 返回试算数据
     */
    @RequiresPermissions("inventory:redundant:list")
    @GetMapping("/detail/shippingCalculationWithRequirement")
    public ResultDTO<DetailInventoryRulesDto> getShippingCalculationWithRequirement(@RequestParam @NotBlank(message = "库存信息id不能为空") String inventoryInfoId) {
        return ResultDTO.success(redundantInventoryAppService.getShippingCalculationWithRequirement(inventoryInfoId));
    }

    /**
     * 售罄前断货天数
     * @param infoId 库存信息
     * @return 断货天数
     */
    @RequiresPermissions("inventory:redundant:list")
    @PostMapping("/beforeSoldOutList")
    public ResultDTO<List<InventorySoldOutDaysDO>> getBeforeSoldOutList(@RequestBody @Valid InventoryIdQuery infoId) {
        return ResultDTO.success(redundantInventoryAppService.getBeforeSoldOutList(infoId));
    }

    /**
     * 海外仓冗余列表
     * @param infoId 库存信息
     * @return 海外仓冗余列表
     */
    @PostMapping("/foreignRedundantList")
    public ResultDTO<List<InventoryForeignRedundantInfoDto>> getForeignRedundantList(@RequestBody @Valid InventoryIdQuery infoId) {
        return ResultDTO.success(redundantInventoryAppService.getForeignRedundantInfo(infoId));
    }

    /**
     * 海外仓冗余列表
     * @return 海外仓冗余列表
     */
    @GetMapping("/list/watchBoard/foreignRedundancyInfo")
    public ResultDTO<List<InventoryForeignRedundantInfoDto>> getForeignRedundantList(@RequestParam String watchBoardId) {
        return ResultDTO.success(redundantInventoryAppService.getForeignRedundantInfoByWatchBoardId(watchBoardId));
    }

    @PostMapping("/onShippingRedundantList")
    public ResultDTO<List<InventoryShipRedundantInfoDto>> getOnShippingRedundantList(@RequestBody @Valid InventoryIdQuery infoId) {
        return ResultDTO.success(redundantInventoryAppService.getOnShippingRedundantInfo(infoId));
    }

    /**
     * 在途冗余列表
     * @return 在途冗余列表
     */
    @GetMapping("/list/watchBoard/onShippingRedundancyInfo")
    public ResultDTO<List<InventoryShipRedundantInfoDto>> getOnShippingRedundantList(@RequestParam String watchBoardId) {
        return ResultDTO.success(redundantInventoryAppService.getOnShippingRedundantInfoByWatchBoardId(watchBoardId));
    }

    /**
     * 工厂冗余列表
     * @param infoId 库存信息
     * @return 工厂冗余列表
     */
    @PostMapping("/factoryRedundantList")
    public ResultDTO<List<InventoryFactoryRedundantInfoDto>> getFactoryRedundantList(@RequestBody @Valid InventoryIdQuery infoId) {
        return ResultDTO.success(redundantInventoryAppService.getFactoryRedundantInfo(infoId));
    }

    @PostMapping("/leadTime")
    public ResultDTO<LeadTimeDto> getLeadTime(@RequestBody @Valid InventoryIdQuery infoId) {
        return ResultDTO.success(redundantInventoryAppService.getLeadTime(infoId));
    }

    @PostMapping("/leadTime/updateUrgentPurchase")
    public ResultDTO<Boolean> updateUrgentProduceDays(@RequestBody @Valid LeadTimeCommand leadTimeCommand) {
        if (YesOrNoEnum.YES.getCodeStr().equals(stringRedisTemplate.opsForValue().get(REDUNDANCY_IN_CAL))) {
            throw new RequestTooFrequentlyException("加急数据计算中，请稍后重试");
        }
        if (YesOrNoEnum.YES.getCodeStr().equals(stringRedisTemplate.opsForValue().get(WAREHOUSE_UPDATE_LOCK))) {
            throw new RequestTooFrequentlyException("加急数据计算中，请稍后重试");
        }
        stringRedisTemplate.opsForValue().set(WAREHOUSE_UPDATE_LOCK, "1", 10, TimeUnit.MINUTES);
        try {
            return ResultDTO.success(redundantInventoryAppService.updateUrgentProduceDays(leadTimeCommand));
        } finally {
            stringRedisTemplate.delete(WAREHOUSE_UPDATE_LOCK);
        }
    }

    @PostMapping("/leadTime/updateUrgentHeadShipDate")
    public ResultDTO<Boolean> updateUrgentHeadShipDate(@RequestBody @Valid UrgentHeadShipDateForm urgentForm) {
        if (YesOrNoEnum.YES.getCodeStr().equals(stringRedisTemplate.opsForValue().get(REDUNDANCY_IN_CAL))) {
            throw new RequestTooFrequentlyException("加急数据计算中，请稍后重试");
        }
        if (YesOrNoEnum.YES.getCodeStr().equals(stringRedisTemplate.opsForValue().get(WAREHOUSE_UPDATE_LOCK))) {
            throw new RequestTooFrequentlyException("加急数据计算中，请稍后重试");
        }
        stringRedisTemplate.opsForValue().set(WAREHOUSE_UPDATE_LOCK, "1", 10, TimeUnit.MINUTES);
        try {
            return ResultDTO.success(redundantInventoryAppService.updateUrgentHeadShipDate(urgentForm));

        } finally {
            stringRedisTemplate.delete(WAREHOUSE_UPDATE_LOCK);
        }
    }

    @RequiresPermissions("inventory:redundant:export")
    @GetMapping("/fileInfo")
    public ResultDTO<String> selectFileInfo(@RequestParam("filePath") String filePath) {
        return ResultDTO.success(redundantInventoryAppService.selectFileInfo(filePath));
    }

    @PostMapping("/foreignAndFactoryList")
    public ResultDTO<InventoryFactoryAndForeignInfoDto> getForeignAndFactoryList(@RequestBody InventoryInfoQuery form) {
        return ResultDTO.success(redundantInventoryAppService.getFactoryRedundantInfo(form));
    }

    /**
     * 删除库存
     */
    @RequiresPermissions("inventory:redundant:delete")
    @PostMapping("/delete")
    @PreventReSubmit
    public ResultDTO<Boolean> remove(@RequestBody InventoryInfoQuery form) {
        return ResultDTO.success(redundantInventoryAppService.deleteFactoryByInfoIds(form.getIds()));
    }

    @GetMapping ("/list/operator")
    @ResponseBody
    public ResultDTO<List<OperatorSearchVo>> getOperator() {
        return ResultDTO.success(redundantInventoryAppService.getOperator());
    }

    /**
     * 获取产品品类列表
     */
    @GetMapping("/category")
    public ResultDTO<List<ProductCategoryDTO>> categoryList() {
        return ResultDTO.success(redundantInventoryAppService.category());
    }

    @PostMapping("/info/detail/saleDestination")
    public ResultDTO<List<RedundancySaleDestinationDto>> getSaleDestination(@RequestBody InventoryInfoQuery form) {
        return ResultDTO.success(redundantInventoryAppService.getSaleDestination(form));
    }

    @RequiresPermissions("sales:targetDaySales:edit")
    @PostMapping("/update/detail/saleDestination")
    @PreventReSubmit
    public ResultDTO<Boolean> updateSaleDestination(@RequestBody InventoryInfoQuery form) {
        redundantInventoryAppService.updateSaleDestination(form, new LogTrackNumDto());
        return ResultDTO.success();
    }
}
