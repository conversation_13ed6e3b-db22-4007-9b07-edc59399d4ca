package com.auth.authorization_server.model.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户和角色关联表 cm_sys_role_menu
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_sys_role_menu")
public class RoleMenuDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 2573732010204699277L;
    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 用户ID
     */
    private String menuId;

}
