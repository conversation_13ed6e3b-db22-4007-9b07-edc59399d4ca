package com.inventory_server.model.redundancy.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_inventory_watch_board_copy1")
public class InventoryWatchBoardCopyDO extends PhysicalBaseEntity {

  private String inventoryInfoId;
  private String warehouse;
  private LocalDate expectedShippingDate;
  private LocalDate expectedArrivingDate;
  private Integer arrivedNum;
  private Double foreignInventoryNum;
  private Double localInventoryNum;
  private Integer type;
}
