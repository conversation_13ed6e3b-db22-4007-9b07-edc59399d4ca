package com.sales_server.model.targetSales.entity.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.sales_server.infrastructures.entity.BaseExcel;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 导入目标日销excel类
 * <AUTHOR>
 * @Date 2024/11/8 16:52
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TargetDaySalesExcelEntity extends BaseExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = -14394958405203434L;

    @NotNull(message = "虚拟SKU不能为空")
    @ExcelProperty("*虚拟SKU")
    private String virtualSku;

    @NotNull(message = "日期不能为空")
//    @Pattern(regexp = "(19|20|21)\\d{2}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])", message = "日期格式不对")
    @ExcelProperty("*日期")
    private String localDate;

    @NotNull(message = "目标日销不能为空")
    @ExcelProperty("*目标日销")
    private String targetSales;

}
