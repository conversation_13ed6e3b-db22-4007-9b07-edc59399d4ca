package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 * @Description 发货计划表
 * <AUTHOR>
 * @Date 2024/1/5 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_trial_shipping_inventory")
public class TrialShippingInventoryDO extends BaseEntity {
    /**
     * 实际发货时间
     */
    private Date realShippingStartDate;
    /**
     * 预计到货时间
     */
    private Date expectedArrivingDate;
    /**
     * 箱数
     */
    private Integer packageNum;
    /**
     * 发货量统计区间
     */
    private String deliveryDateRange;
    /**
     * 目标仓
     */
    private String destinationWarehouse;
    /**
     * 备注
     */
    private String remarks;

    private String factoryFinishedId;
    /**
     * 发货数量
     */
    private Integer shippingNum;
    /**
     * 箱子是否装满
     */
    private String isPackageFull;

    @TableField(exist = false)
    private Integer deliveryType;
}
