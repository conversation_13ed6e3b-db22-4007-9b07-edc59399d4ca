package com.product.product_server.model.currency.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.product.product_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_currency_exchange_rate")
public class CurrencyExchangeRateDO extends BaseEntity {
  private String fromCurrency;
  private String toCurrency;
  private Double exchange;
  private LocalDateTime exchangeUpdateDate;
  private Integer isMiddleExchange;
}
