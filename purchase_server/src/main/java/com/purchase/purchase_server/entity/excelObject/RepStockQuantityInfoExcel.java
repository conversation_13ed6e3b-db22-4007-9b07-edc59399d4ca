package com.purchase.purchase_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description excel导入补货计划-库存转换成的实体列
 * <AUTHOR>
 * @Date 2024/1/16 17:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepStockQuantityInfoExcel extends BaseExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = -1439495840203434L;

    @ExcelProperty("*货件号")
    private String shipmentCode;

    @ExcelProperty("*虚拟SKU")
    private String virtualSku;

    @ExcelProperty("*库存数")
    private String stockQuantity;

    @ExcelProperty("*库存可用时间")
    private String inventoryTime;

    @ExcelProperty("*仓库")
    private String storeHouse;

    @ExcelProperty("*出货时间")
    private String startShippingTime;

    @ExcelProperty("备注")
    private String remarks;
}
