package com.inventory_server.model.inventory.entity.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;

/**
 * @Description 同步实时库存表单
 * <AUTHOR>
 * @Date 2025/2/18 17:59
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AsyncRealTimeInventoryForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1243243265654L;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN)
    private String startDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN)
    private String endDate;
}
