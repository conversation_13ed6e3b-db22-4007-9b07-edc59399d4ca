package com.sales_server.model.targetSales.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.sales_server.model.targetSales.entity.dos.TargetSalesInfoDO;
import com.sales_server.model.targetSales.entity.dto.TargetSalesAggDto;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

public interface TargetSalesMapper extends MPJBaseMapper<TargetSalesInfoDO> {

    List<TargetSalesAggDto> aggregateAllDayTargetSales(@Param("set") Collection<String> set);

    void createPartitionByYear(@Param("name") String name, @Param("localDate") LocalDate localDate);
}
