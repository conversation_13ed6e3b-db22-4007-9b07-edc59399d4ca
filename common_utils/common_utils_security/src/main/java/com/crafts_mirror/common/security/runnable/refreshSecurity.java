package com.crafts_mirror.common.security.runnable;

import com.crafts_mirror.utils.common.entity.LoginVo;
import com.crafts_mirror.utils.constant.SecurityConstants;
import com.crafts_mirror.utils.context.SecurityContextHolder;

/**
 * <AUTHOR>
 * @date 2024/8/26
 **/
public abstract class refreshSecurity {
    public void refreshSecurityContextHolder(LoginVo loginVo){
        SecurityContextHolder.setUserId(loginVo.getUserId());
        SecurityContextHolder.setUserName(loginVo.getUserName());
        SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginVo);
    }
}
