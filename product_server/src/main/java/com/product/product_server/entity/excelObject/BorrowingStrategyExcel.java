package com.product.product_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/4/15 11:17
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BorrowingStrategyExcel extends BaseExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = 8051021370283708634L;
    @ExcelProperty("*虚拟SKU")
    private String virtualSku;

    @ExcelProperty("*借货策略")
    private String BorrowingStrategy;
}
