package com.sales_server.model.targetSales.repository.redisRepository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.sales_server.model.targetSales.entity.dto.TargetDaySalesDto;
import com.sales_server.model.targetSales.repository.redisRepository.ITargetSalesRedisRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.TARGET_SALES_VIRTUAL_SKU_PREFIX;
import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * <AUTHOR>
 * @Date 2025/3/18 13:37
 **/
@Service
@Slf4j
public class TargetSalesRedisRepository implements ITargetSalesRedisRepository {

    @Resource
    private RedisTemplate<String, TargetDaySalesDto> redisTemplate;

    private static final long EXPIRE_TIME = 60 * 60;

    @Override
    public boolean saveTargetSalesToRedis(String virtualId, List<TargetDaySalesDto> targetDaySalesInfoList) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);
        String key = TARGET_SALES_VIRTUAL_SKU_PREFIX + virtualId;
        byte[] keyBytes = key.getBytes(UTF_8);
        Map<byte[], byte[]> map = targetDaySalesInfoList.stream()
                .collect(Collectors.toMap(info ->
                                        info.getTargetDate().format(formatter).getBytes(UTF_8),
                                info -> info.getTargetSales().toString().getBytes(UTF_8))
                );

        try {
            if (CollectionUtil.isEmpty(map)) {
                return true;
            }
            redisTemplate.executePipelined((RedisCallback<?>) connection -> {
                connection.hashCommands().hMSet(keyBytes, map);
                connection.commands().expire(keyBytes, EXPIRE_TIME);
                return null;
            });
        } catch (Exception e) {
            log.error("缓存虚拟商品目标日销失败，异常原因：{}", e.getMessage());
            throw new IllegalArgumentException("缓存虚拟商品目标日销失败，请联系研发人员");
        }
        return true;
    }

    @Override
    public boolean deleteCachedTargetSales(String virtualId) {
        return redisTemplate.delete(TARGET_SALES_VIRTUAL_SKU_PREFIX + virtualId);
    }
}
