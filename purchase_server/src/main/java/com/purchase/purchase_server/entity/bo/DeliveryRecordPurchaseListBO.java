package com.purchase.purchase_server.entity.bo;


import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * @Description 发货计划记录列表页展示
 * <AUTHOR>
 * @Date 2024/4/7 14:21
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryRecordPurchaseListBO extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -2658228707480977407L;
    /**
     * 发货记录Id
     */
    private String recordId;

    /**
     * 状态
     */
    private String trialStatus;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 发货专柜时间
     */
    private LocalDateTime shippingStartDate;
    private LocalDateTime shippingEndDate;

    /**
     * 装柜id
     */
    private String lclRecordId;

    /**
     * 发货导出文件
     */
    private String exportFilePath;
}
