package com.sales_server.model.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.common.entity.OperatorSearchDto;
import com.crafts_mirror.utils.common.entity.OperatorSearchVo;
import com.crafts_mirror.utils.common.entity.UserParams;
import com.crafts_mirror.utils.enums.UserPostEnum;
import com.crafts_mirror.utils.enums.UserStatusEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.sales_server.model.system.service.ISysUserInteriorService;
import com.sales_server.model.user.entity.vo.UserInteriorVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

import static com.crafts_mirror.utils.constant.SystemConstant.SYS_USER_INFO;
import static com.crafts_mirror.utils.constant.SystemConstant.SYS_USER_LIST_URL;

/**
 * <AUTHOR>
 * @description 针对表【cm_replenishment_virtual_sku_purchase】的数据库操作Service实现
 * @createDate 2024-01-17 10:45:51
 */
@Service
@Slf4j
public class SysUserInteriorServiceImpl implements ISysUserInteriorService {
    @Resource
    protected RestTemplate restTemplate;

    @Override
    public List<UserInteriorVO> getUserList() {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO resultDTO = restTemplateUtil.get(SYS_USER_LIST_URL, ResultDTO.class);
        List data = JSON.to(List.class, resultDTO.getData());
        List<UserInteriorVO> map = new ArrayList<>();
        data.forEach(i -> {
            UserInteriorVO userInteriorVO = JSON.to(UserInteriorVO.class, i);
            map.add(userInteriorVO);
        });
        return map;
    }

    @Override
    public OperatorSearchVo getOperator() {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        UserParams params = new UserParams();
        params.setPostId(UserPostEnum.OPERATIONS_MANAGER.getCode());
        params.setUserStatus(UserStatusEnum.YES_STATUS.getCode());
        ResultDTO resultDTO = restTemplateUtil.post(params, ResultDTO.class, SYS_USER_INFO);
        List userList = JSON.to(List.class, resultDTO.getData());
        List list = userList.stream().map(i -> {
                    UserInteriorVO userInteriorVO = JSON.to(UserInteriorVO.class, i);
                    return OperatorSearchDto.builder().userName(userInteriorVO.getUserName()).nickName(userInteriorVO.getNickName()).build();
                })
                .toList();
        return new OperatorSearchVo(list);
    }
}




