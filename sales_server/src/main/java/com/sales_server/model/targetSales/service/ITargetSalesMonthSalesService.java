package com.sales_server.model.targetSales.service;

import com.sales_server.model.product.entity.dp.VirtualProductInfoDp;
import com.sales_server.model.targetSales.entity.TargetSalesPage;
import com.sales_server.model.targetSales.entity.form.TargetMonthSalesPageForm;
import com.sales_server.model.targetSales.entity.vo.TargetMonthSalesVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ITargetSalesMonthSalesService {

    /**
     * 保存目标月销数据
     */
    void saveTargetSalesMonthAggData(Set<String> successfulVirtualIdSet);

    /**
     * 根据搜索条件和虚拟skuId获取目标月销
     *
     * @param form 搜索条件
     * @param virtualSkuIdList 虚拟skuId
     * @return 目标月销
     */
    List<TargetMonthSalesVo> getMonthTargetSalesList(TargetMonthSalesPageForm form, List<String> virtualSkuIdList, Map<String, String> userMap);

    TargetSalesPage combineTargetSalesPageBySpu(TargetMonthSalesPageForm form, List<TargetMonthSalesVo> targetMonthSaleList);

    void combineProductInfoAndTargetSalesInfo(List<VirtualProductInfoDp> virtualProductInfoList,
                                              List<TargetMonthSalesVo> targetMonthSalesList,
                                              Map<String, String> userMap);

    void deleteMonthSalesByVirtualId(String virtualId);
}
