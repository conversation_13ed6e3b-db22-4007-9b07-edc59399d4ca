package com.crafts_mirror.utils.enums.product;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum VirtualProductStatusEnum {
    NORMAL(0, "正常"),
    NEW_ARRIVAL_TEST_SAMPLE(1, "新品测款"),
    HALT_DISTRIBUTION(2, "停发"),
    SOLD_OUT_STOP_SELLING(3, "售完停售"),
    STOP_SELLING(4, "淘汰"),
    NEW_ARRIVAL_NORMAL_SAMPLE(5, "新品不断货");

    private final Integer code;
    private final String desc;

    public static VirtualProductStatusEnum ofDesc(String value) {
        return Arrays.stream(VirtualProductStatusEnum.values())
                .filter(it -> it.getDesc().equals(value))
                .findFirst()
                .orElse(null);
    }

    public static VirtualProductStatusEnum ofCode(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(VirtualProductStatusEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
