package com.purchase.purchase_server.utils.easyExcelUtil.listener.Replenishment;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentRecordDO;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.form.UpgradeProductInteriorForm;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.UpgradeInfoInteriorVo;
import com.purchase.purchase_server.repository.dataRepository.ReplenishmentRecordRepositoryImpl;
import com.purchase.purchase_server.utils.easyExcelUtil.listener.AbstractImportListener;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.SystemConstant.PRODUCTS_VIRTUAL_UPGRADE_INFO_URL;

/**
 * @Description 导入补货计划抽象类
 * <AUTHOR>
 * @Date 2024/1/15 11:35
 **/
@Slf4j
public abstract class AbstractRepImportListener<T> extends AbstractImportListener<T> {
    protected final ReplenishmentRecordRepositoryImpl replenishmentRecordRepositoryImpl = ApplicationContextProvider.getBean(ReplenishmentRecordRepositoryImpl.class);

    public AbstractRepImportListener(List<SenboWarehouseDto> senboWarehouseList) {
        super(senboWarehouseList);
    }

    protected final RestTemplate restTemplate = ApplicationContextProvider.getBean(RestTemplate.class);

    public UpgradeInfoInteriorVo selectUpgradeInfo(String upgradeId){
        var restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        var virtualProductListVo = restTemplateUtil.post(UpgradeProductInteriorForm.builder().id(upgradeId).build(), ResultDTO.class, PRODUCTS_VIRTUAL_UPGRADE_INFO_URL);
        return JSON.to(UpgradeInfoInteriorVo.class, virtualProductListVo.getData());
    }

    @Override
    protected String getCalculationType() {
        return "补货计划导入";
    }

    @Override
    protected AbstractManager getManager() {
        return ReplenishmentManager.getInstance();
    }

    @Override
    protected AbstractMapManager getMapManager() {
        return ReplenishmentMapManager.getInstance();
    }

    @Override
    protected void validRulesSkuMatchRequired() {
        AbstractMapManager instance = getMapManager();
        var rulesSet = instance.getStockingRulesMap().keySet();
        var targetSalesSet = instance.getTargetSalesMap().keySet();

        StringBuilder error = new StringBuilder();
        HashMap<String, String> stockingRulesUpgradeMap = instance.getStockingRulesUpgradeMap();
        for (var upgrade: stockingRulesUpgradeMap.entrySet()){
            if (!rulesSet.contains(upgrade.getValue())){
                error.append(String.format("备货规则中未找到被升级款：%s 对应的升级款：%s；\n", upgrade.getKey(), upgrade.getValue()));
            }
        }

        HashMap<String, String> targetSalesUpgradeMap = instance.getTargetSalesUpgradeMap();
        for (var upgrade: targetSalesUpgradeMap.entrySet()){
            if (!targetSalesSet.contains(upgrade.getValue())){
                error.append(String.format("目标日销中未找到被升级款：%s 对应的升级款：%s；\n", upgrade.getKey(), upgrade.getValue()));
            }
        }

        if (StrUtil.isNotBlank(error.toString())) {
            throw new IllegalArgumentException(error.toString());
        }

        if (!targetSalesSet.containsAll(rulesSet)) {
            rulesSet.removeAll(targetSalesSet);
            throw new IllegalArgumentException(String.format("备货规则里下列商品没有目标日销：%s \n", rulesSet));
        }

    }
    @Override
    protected void updateRecordResult() {
        AbstractManager manager = getManager();

        replenishmentRecordRepositoryImpl.updateById(ReplenishmentRecordDO.builder()
                .id(manager.getRecordId())
                .taskResult(String.format("成功：%d；失败：%d", manager.getSuccessfulTimes(), manager.getFailedTimes()))
                .failedResult(String.join(";", Optional.ofNullable(manager.getErrorList()).orElse(new ArrayList<>()))).build());

    }
}
