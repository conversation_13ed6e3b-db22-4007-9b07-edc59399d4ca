package com.inventory_server.model.warning.urgentPurchase.entity.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum;
import com.inventory_server.infrastructures.entity.BaseExcel;
import com.inventory_server.model.product.enums.VirtualProductTypeEnum;
import com.inventory_server.model.product.enums.VirtualSubTypeEnum;
import com.inventory_server.model.warning.urgentPurchase.entity.vo.UrgentPurchasePageVo;
import lombok.*;

import java.time.LocalDate;

/**
 * @Description 加急补货导出
 * <AUTHOR>
 * @Date 2025/4/22 20:18
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ContentStyle(wrapped = BooleanEnum.TRUE,
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER)
public class UrgentPurchaseExcel extends BaseExcel {
    @ExcelProperty(value = "虚拟SKU", order = 1)
    private String virtualSku;

    @ExcelProperty(value = "自定义SKU", order = 2)
    private String selfSku;

    @ExcelProperty(value = "产品名称", order = 3)
    private String productName;

    @ExcelProperty(value = "大类", order = 4)
    private String category;

    @ExcelProperty(value = "渠道", order = 5)
    private String channel;

    @ExcelProperty(value = "运营", order = 6)
    private String operator;

    @ExcelProperty(value = "产品状态", order = 7)
    private String productStatus;

    @ExcelProperty(value = "子体类型", order = 8)
    private String subType;

    @ExcelProperty(value = "产品类型", order = 9)
    private String productType;

    @ExcelProperty(value = "加急补货仓", order = 10)
    private String urgentWarehouse;

    /**
     * 可干预时间
     */
    @ExcelProperty(value = "可干预时间", order = 11)
    private String interventionalTimeDto;

    /**
     * 加急补货日
     */
    @ExcelProperty(value = "加急补货日", order = 12)
    private LocalDate urgentPurchaseDate;

//    /**
//     * 加急补货前断货天数
//     */
//    @ExcelProperty(value = "加急补货前断货天数", order = 13)
//    private Integer soldOutDaysBeforeUrgentPurchase;

    /**
     * 加急补货交期
     */
    @ExcelProperty(value = "加急补货交期", order = 14)
    private String urgentFactoryFinishedDate;

    /**
     * 加急补货量
     */
    @ExcelProperty(value = "加急补货数量", order = 15)
    private Integer urgentPurchaseNum;

    /**
     * 加急补货统计区间
     */
    @ExcelProperty(value = "加急补货统计区间", order = 16)
    private String urgentPurchaseRange;

    /**
     * 海外仓理论售罄时间
     */
    @ExcelProperty(value = "海外仓理论售罄时间", order = 17)
    private String foreignTheoreticalSoldOutDate;

    /**
     * 海外仓售罄前断货天数
     */
    @ExcelProperty(value = "海外仓售罄前断货天数", order = 18)
    private Integer daysBeforeSoldOut;

    /**
     * 全链路理论售罄时间
     */
    @ExcelProperty(value = "全链路理论售罄时间", order = 19)
    private String fullLinkTheoreticalSoldOutDate;

    /**
     * 售罄前断货天数
     */
    @ExcelProperty(value = "全链路售罄前断货天数", order = 20)
    private Integer fullLinkDaysBeforeSoldOut;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间", order = 21)
    private String updateDate;

    public UrgentPurchaseExcel(UrgentPurchasePageVo vo) {
        this.virtualSku = vo.getVirtualSku();
        this.selfSku = vo.getSelfSku();
        this.productName = vo.getProductName();
        this.category = vo.getCategory();
        this.channel = vo.getChannel();
        this.productStatus = vo.getProductStatus() == null ? null : VirtualProductStatusEnum.ofCode(vo.getProductStatus()).getDesc();
        this.subType = vo.getSubType() == null ? null : VirtualSubTypeEnum.ofCode(vo.getSubType()).getDesc();
        this.productType = vo.getProductType() == null ? null : VirtualProductTypeEnum.ofCode(vo.getProductType()).getDesc();
        this.operator = vo.getOperator();
        this.urgentWarehouse = vo.getUrgentWarehouse();

        this.interventionalTimeDto = vo.getInterventionalTimeDto().purchaseDateToString();
        this.urgentPurchaseDate = vo.getUrgentPurchaseDate();
        this.urgentFactoryFinishedDate = vo.getUrgentFactoryFinishedDate();
        this.urgentPurchaseNum = vo.getUrgentPurchaseNum();
        this.urgentPurchaseRange = vo.getUrgentPurchaseRange();
        this.foreignTheoreticalSoldOutDate = vo.getForeignTheoreticalSoldOutDate();

        this.daysBeforeSoldOut = vo.getDaysBeforeSoldOut();
        this.fullLinkTheoreticalSoldOutDate = vo.getFullLinkTheoreticalSoldOutDate();
        this.fullLinkDaysBeforeSoldOut = vo.getFullLinkDaysBeforeSoldOut();
        this.updateDate = vo.getUpdateDate();
    }
}
