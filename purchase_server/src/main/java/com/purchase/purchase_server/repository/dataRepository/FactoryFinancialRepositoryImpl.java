package com.purchase.purchase_server.repository.dataRepository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.purchase.purchase_server.entity.dataObject.FactoryFinancialDO;
import com.purchase.purchase_server.mapper.FactoryFinancialMapper;
import org.springframework.stereotype.Service;


/**
 * @Description 供应商-财务相关repository类
 * <AUTHOR>
 * @Date 2024/6/6 14:55
 **/
@Service
public class FactoryFinancialRepositoryImpl extends ServiceImpl<FactoryFinancialMapper, FactoryFinancialDO> {
    public FactoryFinancialDO listByfactoryInfoId(String factoryInfoid){
        return baseMapper.selectOne(Wrappers.<FactoryFinancialDO>lambdaQuery()
                .eq(FactoryFinancialDO::getFactoryInfoId, factoryInfoid)
        );
    }

    public void deleteByFactoryInfoId(String val){
        baseMapper.delete(Wrappers.<FactoryFinancialDO>lambdaQuery().eq(FactoryFinancialDO::getFactoryInfoId, val));
    }
}
