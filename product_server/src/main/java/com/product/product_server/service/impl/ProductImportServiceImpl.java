package com.product.product_server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.utils.SnowflakeIdWorker;
import com.product.product_server.entity.dataObject.FactoryInfoDO;
import com.product.product_server.entity.dataObject.VirtualProductDO;
import com.product.product_server.entity.dto.SenboWarehouseDto;
import com.product.product_server.entity.excelObject.*;
import com.product.product_server.entity.form.FileMissionForm;
import com.product.product_server.entity.form.SpuInfoSaveForm;
import com.product.product_server.entity.response.ResultDTO;
import com.product.product_server.enums.PCSTypeEnum;
import com.product.product_server.exception.BusinessException;
import com.product.product_server.model.products.*;
import com.product.product_server.model.virtualProduct.VirtualProductSkuDp;
import com.product.product_server.model.virtualProduct.VirtualProductSkuStatusDp;
import com.product.product_server.repository.SelfProductRepositoryImpl;
import com.product.product_server.repository.SpuProductRepositoryImpl;
import com.product.product_server.repository.VirtualProductRepositoryImpl;
import com.product.product_server.repository.interiorRepository.WarehouseRepository;
import com.product.product_server.service.IChannelService;
import com.product.product_server.service.IFileCenterService;
import com.product.product_server.service.IProductImportService;
import com.product.product_server.service.IProductSnapshotService;
import com.product.product_server.utils.easyExcelUtil.EasyExcelImportUtils;
import com.product.product_server.utils.easyExcelUtil.listener.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

import static com.crafts_mirror.utils.constant.SystemConstant.FILE_SYSTEM_MISSION_CENTER_URL;
import static com.crafts_mirror.utils.constant.SystemConstant.FILE_SYSTEM_PUT_OBJECT_BY_FILE;
import static com.product.product_server.consts.FilePathConstant.FILE_PATH_PRODUCT_VIRTUAL;

/**
 * @Description 导入产品业务层
 * <AUTHOR>
 * @Date 2023/12/8 17:19
 **/
@Service
@Slf4j
public class ProductImportServiceImpl implements IProductImportService {

    @Resource
    private SelfProductRepositoryImpl selfProductRepository;

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepository;

    @Resource
    private SpuProductRepositoryImpl spuProductRepository;

    @Resource
    private IProductSnapshotService productSnapshotService;

    @Resource
    private WarehouseRepository warehouseRepository;
    @Resource
    private RestTemplate restTemplate;

    @Resource
    private SnowflakeIdWorker snowflakeIdWorker;

    @Resource
    private IFileCenterService fileCenterService;

    @Resource
    private IChannelService channelService;

    @Override
    public void importExcelToSelfProduct(InputStream file, byte[] byteArrayResource, String fileName) {
        String importExcelId = addImportFileMissionInfo(byteArrayResource, fileName, "自定义sku商品信息导入", FILE_PATH_PRODUCT_VIRTUAL);
        Map<String, String> channelNameIdMap = channelService.getChannelNameIdMap();
        EasyExcelImportUtils.read(file, SelfProductInfoExcel.class, new ProductImportListener(fileName, importExcelId, channelNameIdMap), 1);
    }

    public void updateSelfProductCommodityInspection(InputStream file, byte[] byteArrayResource, String fileName) {
        String importExcelId = addImportFileMissionInfo(byteArrayResource, fileName, "更新商检信息", FILE_PATH_PRODUCT_VIRTUAL);
        EasyExcelImportUtils.read(file, CommodityInspectionExcel.class, new CommodityInspectionListener(fileName, importExcelId), 1);
    }

    @Override
    public void importExcelToVirtualProduct(InputStream file, byte[] byteArrayResource, String fileName) {
        String importExcelId = addImportFileMissionInfo(byteArrayResource, fileName, "虚拟sku商品信息导入", FILE_PATH_PRODUCT_VIRTUAL);
        Map<String, String> channelNameIdMap = channelService.getChannelNameIdMap();
        EasyExcelImportUtils.read(file, VirtualProductInfoExcel.class, new VirtualProductImportListener(fileName, importExcelId, channelNameIdMap), 1);
    }

    @Override
    public void updateVirtualProductUpgrade(InputStream file, byte[] byteArrayResource, String fileName) {
        String importExcelId = addImportFileMissionInfo(byteArrayResource, fileName, "升级产品关联", FILE_PATH_PRODUCT_VIRTUAL);
        EasyExcelImportUtils.read(file, UpgradeExcel.class, new UpgradeUpdateListener(fileName, importExcelId), 1);
    }

    @Override
    public void updateVirtualBorrowingStrategy(InputStream file, byte[] byteArrayResource, String fileName) {
        String importExcelId = addImportFileMissionInfo(byteArrayResource, fileName, "借货策略导入", FILE_PATH_PRODUCT_VIRTUAL);
        EasyExcelImportUtils.read(file, BorrowingStrategyExcel.class, new BorrowingStrategyUpdateListener(fileName, importExcelId), 1);
    }

    @Override
    public <T extends BaseExcel> void updateVirtualProductInfoPartly(InputStream file, byte[] byteArrayResource, String fileName, AbstractUpdateVirtualProductInfoListener.UpdateType updateType)
            throws NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException {
        String importExcelId = addImportFileMissionInfo(byteArrayResource, fileName, String.format("虚拟sku %s 字段更新", updateType.getDesc()), FILE_PATH_PRODUCT_VIRTUAL);

        // 获取监听器
        var listenerClass = updateType.getListener();
        var constructor = listenerClass.getConstructor(String.class, String.class);
        ReadListener<T> listener = (ReadListener<T>) constructor.newInstance(fileName, importExcelId);

        // 获取待转化的实体类
        Class<T> excelEntityClass = (Class<T>) updateType.getExcelEntity();

        // 解析excel以及更新字段
        EasyExcelImportUtils.read(file, excelEntityClass, listener, 1);
    }

    /**
     * 别问为什么不批量插入数据，问就是赶进度，没时间
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSelfProductInfo(ImportSkuInfoDp importSkuInfo, FactoryInfoDO factoryInfoDO, ImportVirtualSkuInfoDp virtualSkuInfo,
                                    SpecificationDp specification, PurchaseInfoDp purchaseInfo, CaseWeightInfoDp caseWeightInfo,
                                    SelfProductInfoExcel product, SpuInfoSaveForm spuInfoSaveForm, String imageUrl, PCSTypeEnum pcsTypeEnum) {
        // 保存自定义产品
        String savedSelfProductId = selfProductRepository.saveSelfProduct(importSkuInfo, factoryInfoDO, specification,
                purchaseInfo, caseWeightInfo, product.getProductName(), product.getSelfRemarks(), imageUrl,product.getCategory(),
                product.getCommodityInspection(), pcsTypeEnum);
        if (StrUtil.isBlank(savedSelfProductId)) {
            throw new RuntimeException("保存自定义产品至数据库失败，原因未知，请联系开发人员：");
        }
        // 保存spu相关信息
        if (spuInfoSaveForm != null) {
            String spuId = spuProductRepository.saveSpuProduct(spuInfoSaveForm);
            // 保存虚拟产品
            virtualProductRepository.saveVirtualProduct(importSkuInfo, virtualSkuInfo, product.getVirtualRemarks(), savedSelfProductId, spuId);
        }
        List<VirtualProductDO> virtualDO = virtualProductRepository.list(Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getSelfProductSkuId, savedSelfProductId));
        if (CollectionUtil.isNotEmpty(virtualDO)) {
            productSnapshotService.saveProductSnapshotList(virtualDO.stream().map(VirtualProductDO::getId).toList());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveVirtualProductInfo(VirtualProductInfoExcel excel, VirtualProductSkuDp virtualProduct, String selfProductId) {
        String spuId = spuProductRepository.saveSpuProduct(
                new SpuInfoSaveForm(null, virtualProduct.spu(), virtualProduct.spuProductName(), null, new ArrayList<>())
        );
        virtualProductRepository.saveVirtualProduct(new ImportSkuInfoDp(excel.getSku(), excel.getOldSku()),
                new ImportVirtualSkuInfoDp(virtualProduct.channel(), virtualProduct.spu(), virtualProduct.virtualSku(), virtualProduct.operator(),
                        new VirtualProductSkuStatusDp(false, excel.getSubType(), excel.getProductStatus(),
                                excel.getProductType())), excel.getRemarks(), selfProductId, spuId);
        VirtualProductDO virtualDO = virtualProductRepository.getOne(Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getVirtualSku, virtualProduct.virtualSku()));
        if (virtualDO != null) {
            productSnapshotService.saveProductSnapshot(virtualDO.getId());
        }
    }

    /**
     * 发货仓库比例导入
     *
     * @param file     数据
     * @param fileName 文件名称
     */
    @Override
    public void importProductShippingRatio(InputStream file, byte[] byteArrayResource, String fileName) {
        try {
            String importExcelId = addImportFileMissionInfo(byteArrayResource, fileName, "发货仓库比例导入", FILE_PATH_PRODUCT_VIRTUAL);
            // 从数据库中获取现有的仓库列表
            List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseWithoutMTList();
            EasyExcelImportUtils.read(file, new VirtualShippingRatioImportListener(fileName, importExcelId, senboWarehouseList), 2);
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "导入异常，请到导入记录查看");
        }
    }

    private String addImportFileMissionInfo(byte[] byteArrayResource, String fileName, String type, String filePath) {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        //上传oss
        String snowId = String.valueOf(snowflakeIdWorker.nextId());
        String key = snowId + "." + Objects.requireNonNull(fileName).substring(fileName.lastIndexOf(".") + 1);
        MultiValueMap<String, Object> httpEntity = fileCenterService.putFile(byteArrayResource, fileName, filePath + key, DateUtil.offsetDay(new Date(), 730).toString());
        ResultDTO<String> resultDTO = restTemplateUtil.post(httpEntity, ResultDTO.class, FILE_SYSTEM_PUT_OBJECT_BY_FILE);
        if (!Objects.equals(resultDTO.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("导入发货计划时上传oss失败，异常原因：{}", resultDTO.getMessage());
            throw new RuntimeException("导入发货计划时上传oss失败，异常原因：" + resultDTO.getMessage());
        }


        ResultDTO<String> restResult = restTemplateUtil.post(
                FileMissionForm.builder().fileName(fileName)
                        .importStatus("进行中").type(type)
                        .filePath(key)
                        .build(),
                ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL
        );
        if (!Objects.equals(restResult.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("导入自定义产品时插入文件中心失败，异常原因：{}", restResult.getMessage());
            throw new RuntimeException("导入自定义产品时插入文件中心失败，异常原因：" + restResult.getMessage());
        }
        return restResult.getData();
    }
}
