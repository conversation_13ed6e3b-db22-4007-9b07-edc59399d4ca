package com.inventory_server.model.redundancy.entity.aggregate;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;

/**
 * <AUTHOR>
 * @date 2024/5/10
 **/
@Data
public class InventoryFactoryPlanInfoA {

    private String id;

    private String inventoryInfoId;

    private String contractCode;

    @JsonFormat(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, timezone = "GMT+8")
    private Date factoryFinishedDate;

    private Integer storeNum;

    private Integer shippingNum;

    private Integer remainNum;

    private String destinationSku;

    private String remarks;

}
