package com.purchase.purchase_server.service.shipping.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum;
import com.crafts_mirror.utils.utils.DateUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.utils.SnowflakeIdWorker;
import com.purchase.purchase_server.entity.BaseEntity;
import com.purchase.purchase_server.entity.bo.ArrivalDetailsBO;
import com.purchase.purchase_server.entity.bo.DeliveryRecordPurchaseListBO;
import com.purchase.purchase_server.entity.bo.TrialShippingInventoryBO;
import com.purchase.purchase_server.entity.bo.WatchBoardBO;
import com.purchase.purchase_server.entity.dataObject.*;
import com.purchase.purchase_server.entity.dto.FactoryFinishedInventoryDto;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.entity.dto.ForeignInventoryDto;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.dto.delivery.*;
import com.purchase.purchase_server.entity.excelObject.DeliveryPurchaseExportNotYetExcel;
import com.purchase.purchase_server.entity.excelObject.DeliveryPurchaseInfoExcel;
import com.purchase.purchase_server.entity.excelObject.DeliveryRemainExportInfoExcel;
import com.purchase.purchase_server.entity.excelObject.StockQuantityInfoExcel;
import com.purchase.purchase_server.entity.form.*;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.*;
import com.purchase.purchase_server.enums.*;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.repository.dataRepository.*;
import com.purchase.purchase_server.repository.interiorRepository.WarehouseRepository;
import com.purchase.purchase_server.repository.redis.IChannelRedisRepository;
import com.purchase.purchase_server.service.*;
import com.purchase.purchase_server.service.channel.IChannelInfoService;
import com.purchase.purchase_server.service.replenishment.ISysUserInteriorService;
import com.purchase.purchase_server.service.shipping.IDeliveryPurchaseService;
import com.purchase.purchase_server.service.shipping.IShippingCalculationService;
import com.purchase.purchase_server.service.shipping.IShippingSaveCalResultService;
import com.purchase.purchase_server.service.targetSales.ITargetSalesService;
import com.purchase.purchase_server.utils.MockResultUtils;
import com.purchase.purchase_server.utils.commonUtils.WarehouseSortUtil;
import com.purchase.purchase_server.utils.easyExcelUtil.ExcelMergeUtil;
import com.purchase.purchase_server.utils.easyExcelUtil.listener.Deliver.AMStockQuantityImportListener;
import com.purchase.purchase_server.utils.easyExcelUtil.listener.Deliver.DeliveryImportListener;
import com.purchase.purchase_server.utils.easyExcelUtil.listener.Deliver.StockQuantityImportListener;
import com.purchase.purchase_server.utils.easyExcelUtil.listener.Deliver.StockingRulesImportListener;
import com.purchase.purchase_server.utils.easyExcelUtil.listener.Replenishment.ExcelConstants;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.DeliveryManager;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.DeliveryMapManager;
import com.purchase.purchase_server.utils.easyExcelUtil.template.Deilvery.ExcelTemplateGenerator;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateField.DAY_OF_YEAR;
import static com.crafts_mirror.utils.constant.Constants.CHANNEL_MAPPING;
import static com.crafts_mirror.utils.constant.DateFormatConstant.*;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.EXPORT_DELIVERY_LOCK;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.IMPORT_DELIVERY_LOCK;
import static com.crafts_mirror.utils.constant.SystemConstant.*;
import static com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum.*;
import static com.purchase.purchase_server.consts.SpecialDeliveryCollection.NOT_DELIVERY_SET;
import static com.purchase.purchase_server.consts.SpecialDeliveryCollection.TEST_SAMPLE_DELIVERY_SET;
import static com.purchase.purchase_server.entity.consts.FilePathConstant.FILE_PATH_PURCHASE_DELIVERY;
import static com.purchase.purchase_server.entity.consts.FilePathConstant.FILE_PATH_PURCHASE_DELIVERY_EXPORT;
import static com.purchase.purchase_server.enums.DeliveryImportSheetNameEnum.*;
import static com.purchase.purchase_server.enums.DeliveryTypeEnum.URGENT;
import static com.purchase.purchase_server.enums.StockingRulesImportHeadEnum.*;
import static com.purchase.purchase_server.enums.TrialStatusAdditionalTypeEnum.NEED_TO_SHIP;
import static com.purchase.purchase_server.enums.TrialStatusAdditionalTypeEnum.NOT_YET_SHIPPING_PERIOD;
import static java.math.RoundingMode.HALF_UP;

@Service
@Slf4j
public class DeliveryPurchaseServiceImpl implements IDeliveryPurchaseService {
    @Resource
    protected RestTemplate restTemplate;

    @Resource
    private DeliveryPurchaseRepositoryImpl deliveryPurchaseRepositoryImpl;

    @Resource
    private PrepareProductsRulesRepositoryImpl prepareProductsRulesRepository;

    @Resource
    private TrialInventorySaleDestinationRepositoryImpl inventorySaleDestinationRepository;

    @Resource
    private FactoryFinishedInventoryRepositoryImpl factoryFinishedInventoryRepository;

    @Resource
    private TrialShippingInventoryRepositoryImpl trialShippingInventoryRepository;

    @Resource
    private ShippingProjectRepositoryImpl shippingProjectRepository;

    @Resource
    private ProductSnapRepositoryImpl productSnapRepository;

    @Resource
    private MockInventoryRepositoryImpl mockInventoryRepository;

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepository;

    @Resource(name = "noRepeatableCalculationServiceImpl")
    private IShippingCalculationService shippingCalculationService;

    @Resource
    private SelfProductRepositoryImpl selfProductRepository;

    @Resource
    private IProductSnapshotService productSnapshotService;

    @Resource
    private IFactoryFinishedInventoryService factoryFinishedInventoryService;

    @Resource
    private ShippingRecordRepositoryImpl shippingRecordRepository;

    @Resource
    private IShippingSaveCalResultService shippingSaveCalResultService;

    @Resource
    private DeliveryForeignInventoryRepositoryImpl deliveryForeignInventoryRepository;

    @Resource(name = "calRemainInventoryService")
    private ICalRemainInventoryService calRemainInventoryService;

    @Resource
    private FileCenterService fileCenterService;

    @Resource
    private SnowflakeIdWorker snowflakeIdWorker;

    @Resource
    private ISysUserInteriorService sysUserInteriorService;

    @Resource
    private WarehouseRepository warehouseRepository;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private ICommonConsumptionService commonConsumptionService;

    @Resource
    private IChannelRedisRepository channelRedisRepository;

    @Resource
    private ITargetSalesService targetSalesService;

    @Resource
    private IChannelInfoService channelInfoService;

    @Override
    @Async
    public void importExcelToDeliveryPurchase(InputStream file, String fileName, byte[] byteArrayResource,
                                              String shipmentStartDate, String shipmentEndDate) {
        DeliveryManager deliveryManager = DeliveryManager.getInstance();
        DeliveryMapManager deliveryMapManager = DeliveryMapManager.getInstance();
        if (DateUtil.parse(shipmentStartDate).compareTo(DateUtil.parse(DateUtil.today())) < 0) {
            throw new IllegalArgumentException("请选择正确的时间");
        }
        if (DateUtil.parse(shipmentStartDate).compareTo(DateUtil.parse(shipmentEndDate)) > 0) {
            throw new IllegalArgumentException("开始时间大于结束时间，请选择正确的时间");
        }
        deliveryMapManager.setDefaultStartDate(DateUtil.parse(shipmentStartDate));
        deliveryMapManager.setDefaultEndDate(DateUtil.parse(shipmentEndDate));

        // 查询所有的虚拟商品
        List<VirtualProductDO> allVirtualProductList = virtualProductRepository.getAllVirtualProductList();
        Map<String, VirtualProductDO> skuVirtualProductMap = allVirtualProductList.stream().collect(Collectors.toMap(VirtualProductDO::getVirtualSku, m -> m));
        deliveryMapManager.setSkuVirtualProductMap(skuVirtualProductMap);
        Map<String, VirtualProductDO> oldSkuVirtualProductMap = allVirtualProductList.stream()
                .filter(f -> StrUtil.isNotBlank(f.getOldSku()))
                .collect(Collectors.toMap(VirtualProductDO::getOldSku, m -> m));
        deliveryMapManager.setOldSkuVirtualProductMap(oldSkuVirtualProductMap);
        // 保存产品导入进度
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ShippingRecordDO shippingRecord = ShippingRecordDO.builder()
                .shippingStartDate(deliveryMapManager.getDefaultStartDate())
                .shippingEndDate(deliveryMapManager.getDefaultEndDate())
                .trialStatus(ShippingTrialStatusEnum.CALCULATING.getCode())
                .dataSourceType(DataSourceTypeEnum.IMPORT.getCode())
                .build();
        ExcelReader excelReader = null;
        boolean isSaved = false;
        try {
            // 只有在导入第一行时插入正在导入的上传状态数据
            if (!StrUtil.isNotBlank(deliveryManager.getRecordId())) {
                //上传oss
                String snowId = String.valueOf(snowflakeIdWorker.nextId());
                String key = snowId + "." + Objects.requireNonNull(fileName).substring(fileName.lastIndexOf(".") + 1);
                MultiValueMap<String, Object> httpEntity = fileCenterService.putFile(byteArrayResource, fileName, FILE_PATH_PURCHASE_DELIVERY + key, DateUtil.offsetDay(new Date(), 730).toString());
                ResultDTO<String> resultDTO = restTemplateUtil.post(httpEntity, ResultDTO.class, FILE_SYSTEM_PUT_OBJECT_BY_FILE);
                if (!Objects.equals(resultDTO.getStatus(), ResponseCodeEnum.OK.getCode())) {
                    log.error("导入发货计划时上传oss失败，异常原因：{}", resultDTO.getMessage());
                    throw new RuntimeException("导入发货计划时上传oss失败，异常原因：" + resultDTO.getMessage());
                }
                //插入文件记录
                ResultDTO<String> restResult = restTemplateUtil.post(
                        FileMissionForm.builder().fileName(fileName)
                                .importStatus("导入成功").type("发货计划导入")
                                .filePath(key)
                                .build(),
                        ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL
                );
                if (!Objects.equals(restResult.getStatus(), ResponseCodeEnum.OK.getCode())) {
                    log.error("导入发货计划时插入文件中心失败，异常原因：{}", restResult.getMessage());
                    throw new RuntimeException("导入发货计划时插入文件中心失败，异常原因：" + restResult.getMessage());
                } else {
                    shippingRecord.setDataSourceId(restResult.getData());
                    shippingRecordRepository.save(shippingRecord);
                    deliveryManager.setRecordId(shippingRecord.getId());
                }
            }
            // 从数据库中获取现有的仓库列表
            List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
            Map<String, String> channelMapping = channelRedisRepository.getChannelMapping();
            Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();
            if (CollectionUtil.isEmpty(channelMapping)) {
                channelMapping = CHANNEL_MAPPING;
            }
            ReadSheet readSheet1 = EasyExcel.readSheet(0).headRowNumber(1)
                    .registerReadListener(new StockingRulesImportListener(senboWarehouseList)).autoTrim(true).build();
            ReadSheet readSheet2 = EasyExcel.readSheet(1).headRowNumber(1)
                    .registerReadListener(new AMStockQuantityImportListener(senboWarehouseList, channelMapping, channelIdNameMap)).autoTrim(true).build();
            ReadSheet readSheet3 = EasyExcel.readSheet(2).headRowNumber(1)
                    .registerReadListener(new AMStockQuantityImportListener(senboWarehouseList, channelMapping, channelIdNameMap)).autoTrim(true).build();
            ReadSheet readSheet4 = EasyExcel.readSheet(3).head(StockQuantityInfoExcel.class)
                    .registerReadListener(new StockQuantityImportListener(senboWarehouseList)).autoTrim(true).build();
            ReadSheet readSheet5 = EasyExcel.readSheet(4).head(DeliveryPurchaseInfoExcel.class)
                    .registerReadListener(new DeliveryImportListener(senboWarehouseList)).autoTrim(true).build();
            ZipSecureFile.setMinInflateRatio(0);
            excelReader = EasyExcel.read(file).build();
            List<ReadSheet> readSheets = excelReader.excelExecutor().sheetList();

            if (excelReader.excelExecutor().sheetList().size() != 5) {
                throw new RuntimeException("导入发货计划失败，excel中sheet页数量不正确");
            } else if (!STOCKING_RULES.getCode().equals(readSheets.get(0).getSheetName()) ||
                    !AM_STOCK_QUANTITY.getCode().equals(readSheets.get(1).getSheetName()) ||
                    !LAMP_STOCK_QUANTITY.getCode().equals(readSheets.get(2).getSheetName()) ||
                    !STOCK_QUANTITY.getCode().equals(readSheets.get(3).getSheetName()) ||
                    !DELIVERY.getCode().equals(readSheets.get(4).getSheetName())) {
                throw new RuntimeException("请检查sheet是否正确");
            }
            excelReader.read(readSheet1, readSheet2, readSheet3, readSheet4, readSheet5);

            // 开始试算
            isSaved = trialShipping(senboWarehouseList);
            if (isSaved) {
                shippingRecord.setTrialStatus(ShippingTrialStatusEnum.UNSAVED.getCode());
                shippingRecordRepository.updateById(shippingRecord);
            } else {
                log.error("导入发货计划时试算发货计划失败，异常原因未知");
                throw new RuntimeException("导入发货计划时试算发货计划失败，异常原因未知");
            }
        } catch (Exception e) {
            log.error("导入发货计划异常", e);
            shippingRecord.setTrialStatus(ShippingTrialStatusEnum.CALCULATION_FAILED.getCode());
            shippingRecord.setFailedResult(String.join(";", deliveryManager.getErrorList().isEmpty() ? Collections.singletonList(e.getMessage()) : deliveryManager.getErrorList()));
            shippingRecord.setTaskResult("导入失败");
            shippingRecordRepository.updateById(shippingRecord);
        } finally {
            deliveryManager.resetVariables();
            deliveryMapManager.resetVariables();
            if (excelReader != null) {
                log.warn("导入发货计划关闭");
                excelReader.finish();
            }
            stringRedisTemplate.delete(IMPORT_DELIVERY_LOCK);
        }

        if (isSaved) {
            if (Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(EXPORT_DELIVERY_LOCK + deliveryManager.getRecordId(), "1", 7, TimeUnit.HOURS))) {
                CompletableFuture.runAsync(() -> exportDeliveryInfo(DeliveryPurchaseForm.builder()
                        .recordId(deliveryManager.getRecordId())
                        .shippingStartDate(DateUtil.format(deliveryMapManager.getDefaultStartDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN))
                        .shippingEndDate(DateUtil.format(deliveryMapManager.getDefaultEndDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN)).build()));
            }
        }
    }

    @Override
    public void exportTemplate(HttpServletResponse response) {
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("发货计划模板", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();

            List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
            ExcelTemplateGenerator excelTemplateGenerator = new ExcelTemplateGenerator();
            excelTemplateGenerator.generateTemplate(excelWriter, senboWarehouseList);
        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    @Override
    public IPage<DeliveryRecordPurchaseListVo> recordPageList(DeliveryRecordPurchaseForm form) {
        List<String> recordIds;
        if (StrUtil.isNotBlank(form.getProductName()) || CollectionUtil.isNotEmpty(form.getSelfSkuList()) ||
                CollectionUtil.isNotEmpty(form.getVirtualSkuList()) || CollectionUtil.isNotEmpty(form.getOldSkuList())) {
            List<String> snapIds = productSnapRepository.selectSnapIds(new QuerySnapDp(form.getSelfSkuList(), form.getVirtualSkuList(),
                    form.getProductName(), form.getOldSkuList(), new ArrayList<>(), new ArrayList<>(), null, null));
            if (CollectionUtil.isEmpty(snapIds)) {
                return new Page<>(form.getCurrent(), form.getSize(), 0);
            }
            recordIds = shippingProjectRepository.selectRecordIdBySnap(snapIds);
            if (CollectionUtil.isEmpty(recordIds)) {
                return new Page<>(form.getCurrent(), form.getSize(), 0);
            }
            form.setRecordIds(recordIds);
        }

        IPage<DeliveryRecordPurchaseListBO> page = shippingRecordRepository.getRecordPageList(form);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return new Page<>(form.getCurrent(), form.getSize(), page.getTotal());
        }
        List<DeliveryRecordPurchaseListVo> deliveryRecordPurchaseListVos = DeliveryRecordPurchaseListVo.coverRecordPurchaseListVo(page.getRecords());
        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));
        for (DeliveryRecordPurchaseListVo s : deliveryRecordPurchaseListVos) {
            String createBy = collect.get(s.getCreateBy());
            if (StrUtil.isBlank(createBy)) {
                createBy = s.getCreateBy();
            }
            s.setCreateBy(createBy);
        }
        IPage<DeliveryRecordPurchaseListVo> returnPage = new Page<>(form.getCurrent(), form.getSize());
        returnPage.setTotal(page.getTotal());
        returnPage.setRecords(deliveryRecordPurchaseListVos);
        return returnPage;
    }

    @Override
    public IPage<DeliveryPurchaseListVo> pageList(DeliveryPurchaseForm form) {
        DeliveryRecordPurchaseListBO record = shippingRecordRepository.getRecordByRecordId(form.getRecordId());
        if (record == null) {
            throw new RuntimeException("没有该发货记录");
        }
        if (StrUtil.isBlank(form.getShippingStartDate()) || StrUtil.isBlank(form.getShippingEndDate())) {
            DateTimeFormatter sdf = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_HYPHEN);
            form.setShippingStartDate(record.getShippingStartDate().format(sdf));
            form.setShippingEndDate(record.getShippingEndDate().format(sdf));
        }
        List<String> neededProjectIdList = shippingRecordRepository.getProjectIdList(form);
        neededProjectIdList = neededProjectIdList.stream().distinct().collect(Collectors.toList());
        TrialStatusAdditionalTypeEnum type = TrialStatusAdditionalTypeEnum.getByCode(form.getTrialStatus());
        IPage<TrialShippingInventoryBO> iPage = switch (type) {
            case NEED_TO_SHIP -> needToShip(neededProjectIdList, form);
            case NO_NEED_TO_SHIP -> noNeedToShip(neededProjectIdList, form);
            case NOT_YET_SHIPPING_PERIOD -> notYetShipping(neededProjectIdList, form);
        };

        if (iPage == null || iPage.getSize() == 0) {
            return new Page<>(form.getCurrent(), form.getSize());
        }
        List<DeliveryPurchaseListVo> deliveryPurchaseList;

        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();

        if (NOT_YET_SHIPPING_PERIOD.getCode().equals(form.getTrialStatus())) {
            deliveryPurchaseList = DeliveryPurchaseListVo.convertDeliveryPurchaseList(iPage.getRecords(), collect, channelIdNameMap);
        } else {
            deliveryPurchaseList = DeliveryPurchaseListVo.convertDeliveryPurchaseMediumList(iPage.getRecords(), collect, channelIdNameMap);
            for (DeliveryPurchaseListVo deliveryPurchaseListVo : deliveryPurchaseList) {
                double sum = deliveryPurchaseListVo.getFactoryShippingNum().stream()
                        .mapToDouble(Double::parseDouble)
                        .sum();
                List<String> totalList = new ArrayList<>();
                // 使用BigDecimal处理结果,去掉不必要的小数零
                String formattedTotal = BigDecimal.valueOf(sum)
                        .stripTrailingZeros()
                        .toPlainString();
                totalList.add(formattedTotal);
                deliveryPurchaseListVo.setFactoryShippingNum(totalList);
            }
            if (NEED_TO_SHIP.getCode().equals(form.getTrialStatus())) {
                for (DeliveryPurchaseListVo delVo : deliveryPurchaseList) {
                    List<TrialShippingInventoryDO> trialShippingInventoryList = deliveryPurchaseRepositoryImpl.selectIsPackageFullList(delVo.getFactoryFinishedId());
                    if (trialShippingInventoryList.stream().anyMatch(t -> "0".equals(t.getIsPackageFull()))) {
                        delVo.setIsPackageFull("0");
                    }
                }
            }
        }
        IPage<DeliveryPurchaseListVo> returnPage = new Page<>(form.getCurrent(), form.getSize());
        returnPage.setTotal(iPage.getTotal());
        returnPage.setRecords(deliveryPurchaseList);
        return returnPage;
    }

    private IPage<TrialShippingInventoryBO> needToShip(List<String> neededProjectIdList, DeliveryPurchaseForm form) {
        List<DeliveryNumDto> deliveryNumInfoList = factoryFinishedInventoryService.getDeliveryNumInfo(neededProjectIdList, form);

        return convertNeedAndNoNeedDeliveryInfo(deliveryNumInfoList, form);
    }

    private IPage<TrialShippingInventoryBO> noNeedToShip(List<String> neededProjectIdList, DeliveryPurchaseForm form) {
        // 获取当前日期范围内，没有发货以及没有发完的商品id
        List<DeliveryNumDto> deliveryInfoList = factoryFinishedInventoryService.getNoNeedDeliveryInfo(neededProjectIdList, form);
        deliveryInfoList = deliveryInfoList.stream().filter(f -> f.getNumber() > 0).toList();

        return convertNeedAndNoNeedDeliveryInfo(deliveryInfoList, form);
    }

    private IPage<TrialShippingInventoryBO> convertNeedAndNoNeedDeliveryInfo(List<DeliveryNumDto> deliveryInfoList, DeliveryPurchaseForm form) {
        if (CollectionUtil.isEmpty(deliveryInfoList)) {
            return new Page<>(form.getCurrent(), form.getSize());
        }
        Set<String> idSet = deliveryInfoList.stream().map(DeliveryNumDto::getShippingProjectId).collect(Collectors.toSet());
        Map<String, Integer> collect = deliveryInfoList.stream().collect(Collectors.toMap(DeliveryNumDto::getFactoryFinishedId, DeliveryNumDto::getNumber));

        IPage<TrialShippingInventoryBO> iPage = shippingRecordRepository.needShipList(form, idSet, collect.keySet());

        for (var bo : iPage.getRecords()) {
            String factoryFinishedId = bo.getFactoryFinishedId();
            if (collect.containsKey(factoryFinishedId)) {
                bo.setFactoryShippingNum(collect.get(factoryFinishedId));
            }
        }
        return iPage;
    }

    private IPage<TrialShippingInventoryBO> notYetShipping(List<String> neededProjectIdList, DeliveryPurchaseForm form) {
        return shippingRecordRepository.notYetShippingList(form, neededProjectIdList);
    }

    @Override
    public MissionCenterVo selectFileInfo(String recordId) {
        FileMissionCenterDO fileMissionCenterDO = shippingRecordRepository.selectFailResultList(recordId);
        List<String> failResultList = null;
        if (StrUtil.isNotBlank(fileMissionCenterDO.getFailedResult())) {
            failResultList = Arrays.asList(fileMissionCenterDO.getFailedResult().split(";"));
        }
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        String format = String.format(FILE_SYSTEM_MISSION_CENTER_MISSION_GET_URL + "?url=%s", FILE_PATH_PURCHASE_DELIVERY + fileMissionCenterDO.getFilePath());
        ResultDTO<String> resultDTO = restTemplateUtils.get(format, ResultDTO.class);
        return MissionCenterVo.builder()
                .missionId(fileMissionCenterDO.getId())
                .fileName(fileMissionCenterDO.getFileName())
                .filePath(Optional.ofNullable(resultDTO).orElse(ResultDTO.success("")).getData())
                .createDate(fileMissionCenterDO.getCreateDate())
                .finishDate(fileMissionCenterDO.getFinishDate())
                .missionType(fileMissionCenterDO.getType())
                .missionStatus(fileMissionCenterDO.getImportStatus())
                .missionResult(fileMissionCenterDO.getImportResult())
                .failedResultList(failResultList)
                .build();
    }

    @Override
    @Async
    public void exportDeliveryInfo(DeliveryPurchaseForm form) {
        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

        ShippingRecordDO byId = shippingRecordRepository.getById(form.getRecordId());
        if (ObjectUtil.isEmpty(byId)) {
            stringRedisTemplate.delete(EXPORT_DELIVERY_LOCK + form.getRecordId());
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "发货计划不存在！");
        }

        List<String> neededProjectIdList = shippingRecordRepository.getProjectIdList(form);
        List<DeliveryNumDto> deliveryInfoList = factoryFinishedInventoryService.getDeliveryNumInfo(neededProjectIdList, form);
        Map<String, Integer> collect = deliveryInfoList.stream().collect(Collectors.toMap(DeliveryNumDto::getFactoryFinishedId, DeliveryNumDto::getNumber));

        //数据拼接
        List<ArrivalDetailsBO> arrivalDetailsList = shippingRecordRepository.selectShipDetailsList(form);

        // 获取模拟销售情况表格
        Set<String> deliveryIdSet = arrivalDetailsList.stream().map(ArrivalDetailsBO::getDeliveryProjectId).collect(Collectors.toSet());
        LocalDate startDate = LocalDate.from(LocalDateTimeUtil.of(DateUtil.parse(form.getShippingStartDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN)));
        LocalDate endDate = LocalDate.from(LocalDateTimeUtil.of(DateUtil.parse(form.getShippingEndDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN)));

        Map<String, MockInventoryVo> mockInventoryVoMap = getMockInventoryVoMap(deliveryIdSet, startDate, endDate);

        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> userMap = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();

        List<TrialShippingInventoryBO> trialShippingList = shippingRecordRepository.selectExportList(form);
        for (var shippingInventory : trialShippingList) {
            int productStatus = Integer.parseInt(shippingInventory.getReason());
            if (NORMAL.getCode() != productStatus && SOLD_OUT_STOP_SELLING.getCode() != productStatus) {
                String statusDesc = VirtualProductStatusEnum.ofCode(productStatus).getDesc();
                if (TEST_SAMPLE_DELIVERY_SET.contains(productStatus)) {
                    if (shippingInventory.getIsTestUnsalableInventory()) {
                        shippingInventory.setReason(String.format("%s产品，有无计划库存", statusDesc));
                    } else {
                        shippingInventory.setReason(statusDesc);
                    }
                } else {
                    shippingInventory.setReason(statusDesc);
                }
            } else {
                shippingInventory.setReason(null);
            }

            // 工厂交货数量
            int factoryShippingNum = shippingInventory.getFactoryShippingNum();
            shippingInventory.setFactoryShippingNum(collect.getOrDefault(shippingInventory.getFactoryFinishedId(), 0));
            shippingInventory.setRemainNum(factoryShippingNum - collect.getOrDefault(shippingInventory.getFactoryFinishedId(), 0));
        }
        StopWatch sw = new StopWatch();
        sw.start("发货导出生成");
        Map<String, Integer> headShipping = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getHeadShippingDate));
        List<List<Object>> dataList = new ArrayList<>();
        for (ArrivalDetailsBO arrivalDetailsBO : arrivalDetailsList) {
            String realShippingStartDate = arrivalDetailsBO.getRealShippingStartDate();
            String destinationWarehouse = arrivalDetailsBO.getDestinationWarehouse();
            List<Object> data = new ArrayList<>();
            data.add(arrivalDetailsBO.getContractCode());
            data.add(arrivalDetailsBO.getDestinationSku());
            data.add(arrivalDetailsBO.getProductName());
            data.add(channelIdNameMap.getOrDefault(arrivalDetailsBO.getChannel(), arrivalDetailsBO.getChannel()));
            data.add(arrivalDetailsBO.getSelfProductSku());
            data.add(arrivalDetailsBO.getContainerLoad());
            data.add(arrivalDetailsBO.getCaseLength());
            data.add(arrivalDetailsBO.getCaseWidth());
            data.add(arrivalDetailsBO.getCaseHeight());
            data.add(null);
            data.add(arrivalDetailsBO.getCaseVolume());
            data.add(arrivalDetailsBO.getSingleCaseGrossWeight());
            String buyer = userMap.get(arrivalDetailsBO.getBuyer());
            if (StrUtil.isBlank(buyer)) {
                buyer = arrivalDetailsBO.getBuyer();
            }
            data.add(buyer);
            data.add(realShippingStartDate);
            data.add(arrivalDetailsBO.getFactoryFinishedDate());
            data.add(arrivalDetailsBO.getRemarks());
            data.add(arrivalDetailsBO.getFactoryGrossWeight());

            // 获取模拟发货表格
            String deliveryProjectId = arrivalDetailsBO.getDeliveryProjectId();

            var trialInventorySaleDestination = inventorySaleDestinationRepository.getTrialInventorySaleDestination(deliveryProjectId);
            String destinationEverydaySale = trialInventorySaleDestination.getDestinationEverydaySale();
            TreeMap<String, Double> destinationSale = JSON.parseObject(destinationEverydaySale, new TypeReference<>() {
            });

            var shippingProjectBaseParam = prepareProductsRulesRepository.getShippingProjectBaseParam(arrivalDetailsBO.getDeliveryProjectId());


            TreeMap<String, Double> shippingRatioMap = JSON.parseObject(shippingProjectBaseParam.getShippingRatio(), new TypeReference<>() {
            });
            // 转换里层key
            TreeMap<String, TreeMap<String, Double>> convertedDestinationSale = new TreeMap<>();

            for (var entry : destinationSale.entrySet()) {
                TreeMap<String, Double> map = new TreeMap<>();
                for (var ratioEntry : shippingRatioMap.entrySet()) {
                    map.put(idNameMap.get(ratioEntry.getKey()), BigDecimal.valueOf(ratioEntry.getValue() * entry.getValue()).setScale(3, HALF_UP).doubleValue());
                }
                convertedDestinationSale.put(entry.getKey(), map);
            }
            int headShippingDate = headShipping.get(destinationWarehouse);
            DateTime expectedArrivingDate = DateUtil.parse(realShippingStartDate, YYYY_MM_DD_DATE_FORMAT_SLASH).offsetNew(DAY_OF_YEAR, headShippingDate);
            String firstSoldOut;
            try {
                MockInventoryVo mockInventory = mockInventoryVoMap.getOrDefault(deliveryProjectId, new MockInventoryVo());
                firstSoldOut = calRemainInventoryService.getFirstSoldOut(mockInventory.getMockRemainInventoryMap(),
                        idNameMap.get(destinationWarehouse), expectedArrivingDate, convertedDestinationSale);
            } catch (Exception e) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, form.getRecordId() + e.getMessage());
            }

            data.add(firstSoldOut);
            for (SenboWarehouseDto senboWarehouse : senboWarehouseList) {
                if (destinationWarehouse.equals(senboWarehouse.getSenboWarehouseId())) {
                    data.add(arrivalDetailsBO.getShippingNum());
                } else {
                    data.add(null);
                }
            }
            for (SenboWarehouseDto senboWarehouse : senboWarehouseList) {
                if (destinationWarehouse.equals(senboWarehouse.getSenboWarehouseId())) {
                    data.add(arrivalDetailsBO.getFactoryShippingVolume());
                } else {
                    data.add(null);
                }
            }
            dataList.add(data);
        }
        sw.stop();
        log.info("发货导出生成耗时：{}", sw.getTotalTimeMillis() / (1000.0 * 60));
        // 未到发货期
        List<TrialShippingInventoryBO> factoryNotYetList = shippingRecordRepository.selectFactoryNotYetList(form);

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ExcelWriter excelWriter = null;
        try {
            excelWriter = EasyExcel.write(out).build();

            WriteSheet writeSheet1 = EasyExcel.writerSheet(1, "分析结果").head(DeliveryRemainExportInfoExcel.class).registerWriteHandler(new ExcelMergeUtil(trialShippingList.size(), 0, 2)).build();
            WriteSheet writeSheet2 = EasyExcel.writerSheet(2, "到仓明细").head(exportHead(senboWarehouseList)).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            WriteSheet writeSheet3 = EasyExcel.writerSheet(3, "未到发货期").head(DeliveryPurchaseExportNotYetExcel.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();

            excelWriter.write(trialShippingList, writeSheet1);
            excelWriter.write(dataList, writeSheet2);
            excelWriter.write(factoryNotYetList, writeSheet3);

        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        try {
            RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
            //上传oss
            String snowId = String.valueOf(snowflakeIdWorker.nextId());
            String key = snowId + ".xlsx";
            String shippingStartDate = DateUtil.format(DateUtil.parse(form.getShippingStartDate()), YYYY_MM_DD_DATE_FORMAT_HYPHEN);
            String shippingEndDate = DateUtil.format(DateUtil.parse(form.getShippingEndDate()), YYYY_MM_DD_DATE_FORMAT_HYPHEN);

            MultiValueMap<String, Object> httpEntity = fileCenterService.putFile(out.toByteArray(), "发货导出" + shippingStartDate + "~" + shippingEndDate + ".xlsx", FILE_PATH_PURCHASE_DELIVERY_EXPORT + key, DateUtil.offsetDay(new Date(), 7).toString());
            ResultDTO<String> resultDTO = restTemplateUtil.post(httpEntity, ResultDTO.class, FILE_SYSTEM_PUT_OBJECT_BY_FILE);
            if (!Objects.equals(resultDTO.getStatus(), ResponseCodeEnum.OK.getCode())) {
                log.error("导入发货计划时上传oss失败，异常原因：{}", resultDTO.getMessage());
                throw new RuntimeException("导入发货计划时上传oss失败，异常原因：" + resultDTO.getMessage());
            }
            shippingRecordRepository.updateById(ShippingRecordDO.builder()
                    .id(form.getRecordId())
                    .exportFilePath(resultDTO.getMessage()).build());
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, e.getMessage());
        } finally {
            stringRedisTemplate.delete(EXPORT_DELIVERY_LOCK + form.getRecordId());
        }
    }

    private Map<String, MockInventoryVo> getMockInventoryVoMap(Set<String> deliveryProjectIdSet, LocalDate startDate, LocalDate endDate) {
        if (CollectionUtil.isEmpty(deliveryProjectIdSet)) {
            return new HashMap<>();
        }

        Map<String, MockInventoryVo> map = new HashMap<>(deliveryProjectIdSet.size());
        for (String deliveryProjectId : deliveryProjectIdSet) {
            DeliveryWatchBoardForm watchBoardForm = new DeliveryWatchBoardForm(deliveryProjectId, startDate, endDate);
            TrialWatchBoardVo triaWatchBoard = getTriaWatchBoard(watchBoardForm);

            List<FactoryRemainInventoryDto> remainInventoryList = new ArrayList<>();
            for (var dto : triaWatchBoard.getWatchBoardWarehouseList()) {
                remainInventoryList.add(FactoryRemainInventoryDto.builder()
                        .warehouse(dto.getWarehouse())
                        .enableUsingDate(DateUtils.convertToDate(dto.getArrivalDate(), YYYY_MM_DD_DATE_FORMAT_CHINESE))
                        .storeNum(Double.valueOf(dto.getShippingAmount()))
                        .build());
            }

            DeliveryCalMockInventoryForm calMockForm = new DeliveryCalMockInventoryForm();
            calMockForm.setShippingProjectId(deliveryProjectId);
            calMockForm.setRemainInventoryList(remainInventoryList);

            MockInventoryVo mockInventory = calTrialMockInventory(calMockForm);
            map.put(deliveryProjectId, mockInventory);
        }
        return map;
    }

    private List<List<String>> exportHead(List<SenboWarehouseDto> senboWarehouseList) {
        List<List<String>> headList = new ArrayList<>();
        for (String fixedColumn : ExcelConstants.Sheet1.FIXED_COLUMNS) {
            List<String> head = new ArrayList<>();
            head.add(fixedColumn);
            headList.add(head);
        }
        for (SenboWarehouseDto senboWarehouse : senboWarehouseList) {
            List<String> head = new ArrayList<>();
            head.add(senboWarehouse.getSenboWarehouse());
            headList.add(head);
        }
        for (SenboWarehouseDto senboWarehouse : senboWarehouseList) {
            List<String> head = new ArrayList<>();
            head.add(senboWarehouse.getSenboWarehouse() + "体积");
            headList.add(head);
        }
        return headList;
    }

    private boolean trialShipping(List<SenboWarehouseDto> senboWarehouseList) {
        DeliveryMapManager deliveryMapManager = DeliveryMapManager.getInstance();
        DateTime defaultStartDate = DateUtil.beginOfDay(deliveryMapManager.getDefaultStartDate());

        // 库存
        var skuStockQuantityMap = deliveryMapManager.getStockQuantityMap();
        // 规则
        var skuStockingRulesMap = deliveryMapManager.getStockingRulesMap();
        // 计划
        var skuDeliveryMap = deliveryMapManager.getFactoryPlanMap();

        Map<String, String> virtualSkuAndOldSkuMap = shippingCalculationService.convertOldSkuInfoToVirtualSkuInfo(deliveryMapManager);
        List<VirtualProductDO> virtualSkuList = virtualProductRepository.convertDestinationSkuToVirtualSku(skuStockingRulesMap.keySet());

        List<String> selfIdList = virtualSkuList.stream().map(VirtualProductDO::getSelfProductSkuId).toList();
        List<SelfProductDO> selfProductList = selfProductRepository.getSelfProductInfoBySelfIdList(selfIdList);
        Map<String, Integer> skuIdContainLoadMap = selfProductList.stream()
                .collect(Collectors.toMap(SelfProductDO::getId, SelfProductDO::getContainerLoad));

        Map<String, String> skuIdNameMap = selfProductList.stream()
                .collect(Collectors.toMap(SelfProductDO::getId, SelfProductDO::getProductName));

        // 先发一道请求给销售模块，准备待试算商品的所有日销数据存入redis
        List<String> list = virtualSkuList.stream().map(BaseEntity::getId).toList();
        LocalDate now = LocalDate.now();
        LocalDate twoMonthBefore = now.minusMonths(2).withDayOfMonth(1);
        targetSalesService.prepareTargetSales(list, twoMonthBefore);

        Map<String, Integer> headShippingDateMap = senboWarehouseList.stream()
                .collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getHeadShippingDate));

        DateTime projectCreateDate = DateUtil.beginOfDay(new Date());

        int redundantDays = calRemainInventoryService.getRedundantDateNum();

        for (VirtualProductDO virtualProduct : virtualSkuList) {
            log.info("新建发货计划——————虚拟sku {} 开始计算", virtualProduct);
            String selfSkuId = virtualProduct.getSelfProductSkuId();
            String virtualProductId = virtualProduct.getId();

            String productName = skuIdNameMap.get(selfSkuId);

            String virtualSku = virtualProduct.getVirtualSku();
            ShippingProjectBaseParamDto shippingRules = getShippingRules(virtualSku, virtualProductId,
                    senboWarehouseList.stream().map(SenboWarehouseDto::getSenboWarehouseId).toList(), skuStockingRulesMap);
            Integer containerLoad = skuIdContainLoadMap.get(selfSkuId);
            shippingRules.setContainLoader(containerLoad);
            shippingRules.setHeadShippingDays(headShippingDateMap);

            List<FactoryRemainInventoryDto> factoryRemainInventoryList = skuStockQuantityMap.getOrDefault(virtualSku, new ArrayList<>());
            Map<String, Double> targetSalesMap = targetSalesService.getTargetSales(virtualProductId, now);
            List<FactoryFinishedInventoryDto> finishedInventoryList = skuDeliveryMap.getOrDefault(virtualSku, new ArrayList<>());

            // 获取最长的头程时间
            DateTime maxSaleDate = targetSalesMap.keySet().stream()
                    .map(DateUtil::parse)
                    .max(Comparator.comparing(k -> k))
                    .orElse(DateUtil.endOfMonth(defaultStartDate));
            int calCircle = (int) DateUtil.betweenDay(defaultStartDate, maxSaleDate, true);

            // 单个sku的试算
            TrialCalReplenishmentDto trialCalReplenishment;
            int productStatus = virtualProduct.getProductStatus();
            Map<String, Integer> sortedMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSort));
            List<String> sortedWarehouseList = WarehouseSortUtil.getSortedWarehouseList(shippingRules.getShippingRatio(), sortedMap);
            boolean isUnsalable = false;
            if (NOT_DELIVERY_SET.contains(productStatus)) {
                // 状态是停发、淘汰的产品不发货，只计算模拟库存衰减
                Map<String, Double> saleRatioMap = shippingRules.getShippingRatio();
                int minShippingDate = headShippingDateMap.entrySet().stream()
                        .filter(h -> saleRatioMap.containsKey(h.getKey()) && saleRatioMap.get(h.getKey()) > 0)
                        .min(Map.Entry.comparingByValue())
                        .orElseThrow(() -> new NullPointerException("头程时间为空"))
                        .getValue();

                MockTableCalDto mockTableCalDto = new MockTableCalDto(new ArrayList<>(), calCircle,
                        new ArrayList<>(), headShippingDateMap, projectCreateDate, new ArrayList<>(),
                        factoryRemainInventoryList, new ArrayList<>(), minShippingDate, targetSalesMap, saleRatioMap, sortedWarehouseList);
                trialCalReplenishment = commonConsumptionService.calDeliveryResults(mockTableCalDto);
            } else if (TEST_SAMPLE_DELIVERY_SET.contains(productStatus)) {
                trialCalReplenishment = shippingCalculationService.trialShippingCalculation(defaultStartDate, redundantDays,
                        new ArrayList<>(), factoryRemainInventoryList, virtualProduct, calCircle, targetSalesMap,
                        projectCreateDate, finishedInventoryList, shippingRules, sortedWarehouseList, productName,
                        senboWarehouseList
                );
                // 判断测款产品是否无计划
                isUnsalable = checkUnsalableInventory(trialCalReplenishment.getShippingInventoryList(), finishedInventoryList);
            } else {
                trialCalReplenishment = shippingCalculationService.trialShippingCalculation(defaultStartDate, redundantDays,
                        new ArrayList<>(), factoryRemainInventoryList, virtualProduct, calCircle, targetSalesMap,
                        projectCreateDate, finishedInventoryList, shippingRules, sortedWarehouseList, productName,
                        senboWarehouseList
                );
            }

            shippingSaveCalResultService.saveSingleSkuTrialCalculation(trialCalReplenishment, virtualProduct,
                    virtualSkuAndOldSkuMap, skuStockingRulesMap, shippingRules, containerLoad, finishedInventoryList,
                    factoryRemainInventoryList, headShippingDateMap, isUnsalable, redundantDays);
        }
        return true;
    }

    private boolean checkUnsalableInventory(List<FactoryRemainInventoryDto> shippingInventoryList,
                                            List<FactoryFinishedInventoryDto> factoryFinishedInventoryList) {
        var factoryRemainInventoryList = Optional.ofNullable(shippingInventoryList).orElse(new ArrayList<>());
        int totalDeliveryNum = factoryRemainInventoryList.stream().mapToInt(m -> m.getStoreNum().intValue()).sum();
        int totalFactoryNum = factoryFinishedInventoryList.stream().mapToInt(m -> m.getShippingNum().intValue()).sum();

        return totalFactoryNum > totalDeliveryNum;
    }

    /**
     * @param virtualSku          虚拟sku
     * @param skuStockingRulesMap sku发货规则
     * @return 发货规则
     */
    private ShippingProjectBaseParamDto getShippingRules(String virtualSku, String virtualSkuId, List<String> warehouseList,
                                                         Map<String, HashMap<String, String>> skuStockingRulesMap) {
        var stockRulesMap = skuStockingRulesMap.get(virtualSku);
        int safeDays;
        int shippingCircle;
        String produceDays = "";
        String headShippingDays = "{}";
        int purchaseProjectDays = 0;
        int transitDays;
        String ruleId = null;
        Map<String, Double> shippingRatioMap = new HashMap<>(8);
        if (stockRulesMap != null) {
            safeDays = Integer.parseInt(stockRulesMap.get(SAFE_DAYS.getCode()));
            shippingCircle = Integer.parseInt(stockRulesMap.get(SHIPPING_CIRCLE.getCode()));
            transitDays = Integer.parseInt(stockRulesMap.get(TRANSIT_DAYS.getCode()));

            // 获取发货比例
            for (String warehouse : warehouseList) {
                double warehouseRatio = Double.parseDouble(stockRulesMap.getOrDefault(warehouse, "0").replaceAll("%", ""));
                shippingRatioMap.put(warehouse, warehouseRatio / 100);
            }
        } else {
            // 从数据库拿虚拟sku规则数据，如果依然为空则抛异常
            PrepareProductsRulesDO productsRules = prepareProductsRulesRepository.getShippingProjectBaseParamByVirtualSku(virtualSkuId);
            Objects.requireNonNull(productsRules, "虚拟sku：" + virtualSku + "缺少备货规则，请重新导入");
            ruleId = productsRules.getId();

            safeDays = productsRules.getSafeDays();
            transitDays = productsRules.getTransitDays();
            shippingCircle = productsRules.getReplenishFrequency();
            headShippingDays = productsRules.getHeadShippingDays();
            shippingRatioMap = JSON.parseObject(productsRules.getShippingRatio(), new TypeReference<HashMap<String, Double>>() {
            });
        }

        return ShippingProjectBaseParamDto.builder()
                .ruleId(ruleId)
                .safeDays(safeDays)
                .transitDays(transitDays)
                .purchaseProjectDays(purchaseProjectDays)
                .produceDays(produceDays)
                .headShippingDays(JSON.parseObject(headShippingDays, new TypeReference<>() {
                }))
                .shippingFrequency(shippingCircle)
                .shippingRatio(shippingRatioMap)
                .build();
    }

    @Override
    public TrialShippingImportInfoDetailVo getTrialImportInfoDetail(String shippingProjectId) {
        ShippingProjectDO shippingProject = shippingProjectRepository.getById(shippingProjectId);

        ShippingRecordDO recordDO = shippingRecordRepository.getById(shippingProject.getShippingRecordId());
        // 获取发货计划详情基础数据
        PrepareProductsRulesDO productsRules = prepareProductsRulesRepository.getShippingProjectBaseParam(shippingProjectId);

        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));
        var headShippingDaysMap = JSON.parseObject(productsRules.getHeadShippingDays(), new TypeReference<TreeMap<String, Integer>>() {
        });
        var shippingRatioMap = JSON.parseObject(productsRules.getShippingRatio(), new TypeReference<TreeMap<String, Double>>() {
        });

        // 排序
        Map<String, Integer> sortedHeadShippingDays = new LinkedHashMap<>();
        Map<String, Double> sortedShippingRatioDays = new LinkedHashMap<>();
        // 创建一个映射，用于快速查找仓库在列表中的索引
        Map<String, Integer> warehouseIndexMap = new HashMap<>();
        for (int i = 0; i < senboWarehouseList.size(); i++) {
            warehouseIndexMap.put(String.valueOf(senboWarehouseList.get(i).getSenboWarehouseId()), i);
        }

        // 分组并排序
        Map<String, Double> sortedEntries = shippingRatioMap.entrySet().stream()
                .collect(Collectors.partitioningBy(e -> e.getValue() > 0))
                .entrySet().stream()
                .sorted((e1, e2) -> Boolean.compare(e2.getKey(), e1.getKey())) // true (> 0) 在前
                .flatMap(entry -> entry.getValue().stream()
                        .sorted((e1, e2) -> {
                            Integer index1 = warehouseIndexMap.get(e1.getKey());
                            Integer index2 = warehouseIndexMap.get(e2.getKey());
                            return Integer.compare(
                                    index1 != null ? index1 : Integer.MAX_VALUE,
                                    index2 != null ? index2 : Integer.MAX_VALUE
                            );
                        }))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));

        // 填充排序后的映射
        for (Map.Entry<String, Double> entry : sortedEntries.entrySet()) {
            String warehouseId = entry.getKey();
            String warehouseName = idNameMap.get(warehouseId);
            sortedShippingRatioDays.put(warehouseName, entry.getValue());

            if (headShippingDaysMap.containsKey(warehouseId)) {
                sortedHeadShippingDays.put(warehouseName, headShippingDaysMap.get(warehouseId));
            }
        }

        ShippingProjectBaseParamDto baseParamDto = ShippingProjectBaseParamDto.builder()
                .headShippingDays(sortedHeadShippingDays)
                .shippingRatio(sortedShippingRatioDays)
                .purchaseProjectDays(productsRules.getPurchaseProjectDays())
                .produceDays(productsRules.getProduceDays())
                .safeDays(productsRules.getSafeDays())
                .changeableSafeDays(productsRules.getChangeableSafeDays())
                .shippingFrequency(productsRules.getReplenishFrequency())
                .transitDays(productsRules.getTransitDays())
                .build();

        // 发货计划详情
        List<DeliveryForeignInventoryDO> deliveryForeignInventoryList = deliveryForeignInventoryRepository.getByShippingProjectId(shippingProjectId);
        List<ForeignInventoryDto> enableUsingList = new ArrayList<>();
        List<ForeignInventoryDto> onShippingList = new ArrayList<>();
        Set<String> containsInventorySet = new HashSet<>();
        for (var foreignInventory : deliveryForeignInventoryList) {
            DateTime enableUsingDate = DateTime.of(foreignInventory.getEnableUsingDate());
            String startShippingDate = "";
            if (foreignInventory.getStartShippingDate() != null) {
                startShippingDate = DateTime.of(foreignInventory.getStartShippingDate()).toString(YYYY_MM_DD_DATE_FORMAT_HYPHEN);
            }
            String warehouseId = foreignInventory.getWarehouseId();
            ForeignInventoryDto inventoryDto = ForeignInventoryDto.builder()
                    .warehouse(idNameMap.get(warehouseId))
                    .virtualSku(foreignInventory.getVirtualSku())
                    .shipmentCode(foreignInventory.getShipmentCode())
                    .storeNum(foreignInventory.getStoreNum().doubleValue())
                    .enableUsingDate(enableUsingDate)
                    .enableUsingDateString(enableUsingDate.toString(YYYY_MM_DD_DATE_FORMAT_HYPHEN))
                    .startShippingDate(startShippingDate)
                    .remarks(foreignInventory.getRemarks())
                    .build();

            // 根据到货时间去判断该库存是剩余库存还是在途库存
            if (enableUsingDate.compareTo(shippingProject.getCreateDate()) <= 0) {
                enableUsingList.add(inventoryDto);
            } else {
                onShippingList.add(inventoryDto);
            }
            containsInventorySet.add(warehouseId);
        }

        List<ProductSnapshotDO> productSnapshotList = factoryFinishedInventoryRepository.getSnapshotByShippingProjectId(shippingProjectId);
        ProductSnapshotDO upgradeProduct = productSnapshotList.stream()
                .filter(f -> f.getUpgradeType() == null || "1".equals(f.getUpgradeType()))
                .findFirst()
                .orElse(productSnapshotList.getFirst());
        SelfProductDO selfProductDO = JSONObject.parseObject(upgradeProduct.getSelfData(), SelfProductDO.class);
        // 获取发货计划目标日销数据
        TrialInventorySaleDestinationDO overseasInventory = inventorySaleDestinationRepository.getTrialInventorySaleDestination(shippingProjectId);
        if (overseasInventory == null) {
            return TrialShippingImportInfoDetailVo.builder()
                    .virtualSku(upgradeProduct.getVirtualSku())
                    .productName(selfProductDO.getProductName())
                    .onShippingInventoryList(onShippingList)
                    .enableUseInventoryList(enableUsingList)
                    .shippingStatus(recordDO.getTrialStatus())
                    .baseParamDto(baseParamDto)
                    .build();
        }

        // 获取目标日销
        LocalDate startDate = DateUtils.convertToLocalDate(shippingProject.getCreateDate());
        LocalDate localDate = startDate.minusMonths(2).withDayOfMonth(1);
        String targetSales = overseasInventory.getDestinationEverydaySale();
        var targetSalesMap = commonConsumptionService.calTargetSalesMap(targetSales, localDate);

        // 计划
        List<ShippingDetailPlainDto> shippingDetailPlainList = factoryFinishedInventoryService.getShippingDetailPlainDtoList(
                shippingProjectId, productSnapshotList, ShippingSourceTypeEnum.SHIPPING.getCode()
        );

        DeliveryWatchBoardForm form = new DeliveryWatchBoardForm();
        form.setShippingProjectId(shippingProjectId);
        List<WatchBoardBO> trialWatchBoardInfo = shippingProjectRepository.getTrialWatchBoardInfo(form);
        Set<String> deliveryWarehouseSet = trialWatchBoardInfo.stream().map(WatchBoardBO::getDestinationWarehouse).collect(Collectors.toSet());

        Set<String> keysToRemove = shippingRatioMap.entrySet().stream()
                .filter(entry -> entry.getValue() == 0)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
        keysToRemove.removeAll(deliveryWarehouseSet);
        keysToRemove.removeAll(containsInventorySet);
        List<String> sortedWarehouseList = sortedEntries.keySet().stream()
                .filter(aDouble -> !keysToRemove.contains(aDouble))
                .map(idNameMap::get)
                .toList();

        return TrialShippingImportInfoDetailVo.builder()
                .virtualSku(upgradeProduct.getVirtualSku())
                .productName(selfProductDO.getProductName())
                .destinationEverydaySale(targetSalesMap)
                .onShippingInventoryList(sortForeignInventoryDtoList(onShippingList))
                .enableUseInventoryList(sortForeignInventoryDtoList(enableUsingList))
                .shippingDetailPlainDtoList(shippingDetailPlainList)
                .projectCreateDate(DateUtil.format(overseasInventory.getCreateDate(), YYYY_MM_DD_DATE_FORMAT_SLASH))
                .shippingStatus(recordDO.getTrialStatus())
                .baseParamDto(baseParamDto)
                .warehouseSortList(sortedWarehouseList)
                .build();
    }

    @Override
    public ShippingFactoryPlainInfoVo getShippingFactoryPlainInfoVo(String shippingProjectId) {
        List<ProductSnapshotDO> productSnapshotList = factoryFinishedInventoryRepository.getSnapshotByShippingProjectId(shippingProjectId);

        // 工厂计划
        List<ShippingDetailPlainDto> shippingDetailPlainDtoList = factoryFinishedInventoryService.getShippingDetailPlainDtoList(
                shippingProjectId, productSnapshotList, ShippingSourceTypeEnum.SHIPPING.getCode());
        return ShippingFactoryPlainInfoVo.builder().shippingDetailPlainDtoList(shippingDetailPlainDtoList).build();
    }

    @Override
    public TrialWatchBoardVo getTriaWatchBoard(DeliveryWatchBoardForm form) {
        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

        // 获取默认发货展示时间
        ShippingRecordDO shippingRecord = shippingRecordRepository.getByProjectId(form.getShippingProjectId());
        if (shippingRecord == null) {
            throw new RuntimeException("没有该次发货导入记录");
        }
        form.setShippingStartDate(form.getShippingStartDate() == null ? DateUtils.convertToLocalDate(shippingRecord.getShippingStartDate()) : form.getShippingStartDate());
        form.setShippingEndDate(form.getShippingEndDate() == null ? DateUtils.convertToLocalDate(shippingRecord.getShippingEndDate()) : form.getShippingEndDate());

        List<WatchBoardBO> trialWatchBoardInfo = shippingProjectRepository.getTrialWatchBoardInfo(form);
        if (CollectionUtil.isEmpty(trialWatchBoardInfo)) {
            return TrialWatchBoardVo.builder()
                    .watchBoardWarehouseList(new ArrayList<>())
                    .deliveryStartDate(form.getShippingStartDate())
                    .deliveryDateSet(new HashSet<>())
                    .deliveryEndDate(form.getShippingEndDate())
                    .build();
        }

        Set<LocalDate> deliveryDateSet = trialWatchBoardInfo.stream()
                .map(m -> DateUtils.convertToLocalDate(m.getRealShippingStartDate()))
                .collect(Collectors.toSet());

        List<ShippingProjectWatchBoardWarehouseDto> warehouseDtoList = trialWatchBoardInfo.stream()
                .filter(f -> {
                    Date shippingDate = f.getRealShippingStartDate();
                    DateTime startDate = DateUtils.convertToDateTime(form.getShippingStartDate());
                    DateTime endDate = DateUtils.convertToDateTime(form.getShippingEndDate());
                    return startDate.isBeforeOrEquals(shippingDate) && endDate.isAfterOrEquals(shippingDate);
                })
                .map(m -> {
                    double volume = m.getCaseWidth() * m.getCaseHeight() * m.getCaseLength() * m.getPackageNum();
                    double volumeDouble = BigDecimal.valueOf(volume)
                            .divide(BigDecimal.valueOf(1000000), 2, HALF_UP).doubleValue();
                    return ShippingProjectWatchBoardWarehouseDto.builder()
                            .trialId(m.getShippingInventoryId())
                            .shippingAmount(m.getShippingNum())
                            .contractCode(m.getContractCode())
                            .deliveryDateRange(m.getDeliveryDateRange())
                            .warehouse(idNameMap.get(m.getDestinationWarehouse()))
                            .arrivalDate(DateUtil.format(m.getExpectedArrivingDate(), YYYY_MM_DD_DATE_FORMAT_CHINESE))
                            .shippingStartDate(DateUtil.format(m.getRealShippingStartDate(), YYYY_MM_DD_DATE_FORMAT_CHINESE))
                            .remarks(m.getRemarks())
                            .perCaseNum(m.getContainerLoad())
                            .caseAmount(m.getPackageNum())
                            .outCaseLength(m.getCaseLength())
                            .outCaseWidth(m.getCaseWidth())
                            .outCaseHeight(m.getCaseHeight())
                            .volume(volumeDouble)
                            .weight(BigDecimal.valueOf(m.getSingleCaseGrossWeight() * m.getPackageNum()).setScale(3, HALF_UP).doubleValue())
                            .factoryFinishedDate(DateUtil.format(m.getFactoryFinishedDate(), YYYY_MM_DD_DATE_FORMAT_SLASH))
                            .factoryFinishedId(m.getFactoryFinishedId())
                            .deliveryType(m.getDeliveryType())
                            .build();
                })
                .sorted(Comparator.comparing((ShippingProjectWatchBoardWarehouseDto c) -> c.getDeliveryType() != null && c.getDeliveryType().equals(URGENT.getCode())).reversed()
                        .thenComparing((ShippingProjectWatchBoardWarehouseDto c) -> DateUtil.parse(c.getFactoryFinishedDate()))
                        .thenComparing(c -> DateUtil.parse(c.getArrivalDate()))
                        .thenComparing(ShippingProjectWatchBoardWarehouseDto::getWarehouse))
                .toList();
        double sum = warehouseDtoList.stream().mapToInt(ShippingProjectWatchBoardWarehouseDto::getShippingAmount).sum();

        return TrialWatchBoardVo.builder()
                .watchBoardWarehouseList(warehouseDtoList)
                .deliveryStartDate(form.getShippingStartDate())
                .deliveryEndDate(form.getShippingEndDate())
                .deliveryDateSet(deliveryDateSet)
                .totalShippingNum(BigDecimal.valueOf(sum).setScale(3, HALF_UP).doubleValue())
                .build();
    }

    @Override
    public MockInventoryVo calTrialMockInventory(DeliveryCalMockInventoryForm form) {
        String shippingProjectId = form.getShippingProjectId();
        ShippingRecordDO recordDo = shippingRecordRepository.getByProjectId(shippingProjectId);
        List<DeliveryForeignInventoryDO> deliveryForeignInventoryList = deliveryForeignInventoryRepository.getByShippingProjectId(shippingProjectId);
        // 导入的剩余库存
        List<FactoryRemainInventoryDto> remainInventoryDtoList = new ArrayList<>();
        for (var foreignInventoryDo : deliveryForeignInventoryList) {
            String enableUsingDate = DateUtil.format(foreignInventoryDo.getEnableUsingDate(), YYYY_MM_DD_DATE_FORMAT_SLASH);
            String warehouse = foreignInventoryDo.getWarehouseId();
            double storeNum = foreignInventoryDo.getStoreNum().doubleValue();
            remainInventoryDtoList.add(FactoryRemainInventoryDto.builder()
                    .enableUsingDate(DateUtil.parse(enableUsingDate))
                    .warehouse(warehouse)
                    .shipmentCode(foreignInventoryDo.getShipmentCode())
                    .storeNum(storeNum)
                    .build());
        }

        TrialInventorySaleDestinationDO saleDestinationAndInventory = inventorySaleDestinationRepository.getTrialInventorySaleDestination(shippingProjectId);
        // 目标日销
        Map<String, Double> targetSalesMap = JSON.parseObject(saleDestinationAndInventory.getDestinationEverydaySale(), new TypeReference<>() {
        });

        PrepareProductsRulesDO baseParam = prepareProductsRulesRepository.getShippingProjectBaseParam(shippingProjectId);
        Map<String, Integer> headShippingDateMap = JSON.parseObject(baseParam.getHeadShippingDays(), new TypeReference<>() {
        });
        Map<String, Double> shippingRatio = JSON.parseObject(baseParam.getShippingRatio(), new TypeReference<>() {
        });

        int minShippingDate = headShippingDateMap.entrySet().stream()
                .filter(h -> shippingRatio.containsKey(h.getKey()) && shippingRatio.get(h.getKey()) > 0)
                .map(Map.Entry::getValue)
                .sorted().findFirst().orElseThrow(() -> new NullPointerException("头程时间为空"));

        DateTime projectCreateDate = DateTime.of(recordDo.getCreateDate());
        DateTime maxCalDate = targetSalesMap.keySet().stream().map(DateUtil::parse)
                .max(DateUtil::compare)
                .orElse(DateUtil.endOfMonth(projectCreateDate));
        int calCircle = (int) DateUtil.betweenDay(projectCreateDate, maxCalDate, true);

        List<FactoryRemainInventoryDto> remainInventoryListForCal = new ArrayList<>(remainInventoryDtoList);
        List<FactoryRemainInventoryDto> remainInventoryList = form.getRemainInventoryList();

        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, Integer> sortedMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSort));
        List<String> sortedWarehouseList = WarehouseSortUtil.getSortedWarehouseList(shippingRatio, sortedMap);
        Map<String, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));
        Map<String, String> nameIdMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));

        for (var dto : remainInventoryList) {
            dto.setWarehouse(nameIdMap.get(dto.getWarehouse()));
        }
        MockTableCalDto mockTableCalDto = new MockTableCalDto(remainInventoryList,
                calCircle, new ArrayList<>(), headShippingDateMap, projectCreateDate, remainInventoryListForCal,
                remainInventoryDtoList, new ArrayList<>(), minShippingDate, targetSalesMap, shippingRatio, sortedWarehouseList);
        TrialCalReplenishmentDto trialCalReplenishmentDto = commonConsumptionService.calDeliveryResults(mockTableCalDto);

        // 在应用启动时或者需要使用此功能的地方初始化
        WarehouseSortUtil.initializeOrderMap(senboWarehouseList);

        Map<String, Map<String, Double>> daySaleProductMap = trialCalReplenishmentDto.getEverydaySaleProductMap();
        Map<String, Map<String, Double>> dayRemainInventoryMap = trialCalReplenishmentDto.getEverydayRemainInventoryMap();
        Map<String, Map<String, Double>> dayOnShippingInventoryMap = trialCalReplenishmentDto.getEverydayOnShippingInventoryMap();

        Set<String> zeroRatioSet = shippingRatio.entrySet().stream().filter(f -> f.getValue() > 0).map(Map.Entry::getKey).collect(Collectors.toSet());

        // 使用工具类进行排序和键替换，同时将Double转为Integer
        Map<String, TreeMap<String, Integer>> onShippingInventoryMap = WarehouseSortUtil.sortAndReplaceWarehouseKeys(
                dayOnShippingInventoryMap, idNameMap, Double::intValue // Double to Integer
        );

        MockResultUtils.checkEmptyWarehouse(zeroRatioSet, onShippingInventoryMap);

        Map<String, TreeMap<String, Double>> remainInventoryMap = WarehouseSortUtil.sortAndReplaceWarehouseKeys(
                dayRemainInventoryMap, idNameMap, Function.identity() // 不进行类型转换
        );
        MockResultUtils.checkEmptyWarehouse(zeroRatioSet, remainInventoryMap);

        Map<String, TreeMap<String, Double>> daysSaleInventoryMap = WarehouseSortUtil.sortAndReplaceWarehouseKeys(
                daySaleProductMap, idNameMap, Function.identity() // 不进行类型转换
        );

        MockResultUtils.removeUselessMockResult(remainInventoryMap, onShippingInventoryMap, daysSaleInventoryMap, zeroRatioSet);

        return MockInventoryVo.builder()
                .mockDaysSaleInventoryMap(daysSaleInventoryMap)
                .mockRemainInventoryMap(remainInventoryMap)
                .mockShippingInventoryMap(onShippingInventoryMap)
                .build();
    }

    @Override
    public DeliveryPurchaseCountVo shippingCount(DeliveryPurchaseForm purchaseForm) {
        DeliveryPurchaseCountVo deliveryPurchaseCountVo = new DeliveryPurchaseCountVo();

        DeliveryRecordPurchaseListBO recordBo = shippingRecordRepository.getRecordByRecordId(purchaseForm.getRecordId());
        if (recordBo == null) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "发计划不存在");
        }

        List<String> neededProjectIdList = shippingRecordRepository.getProjectIdList(purchaseForm);
        neededProjectIdList = neededProjectIdList.stream().distinct().collect(Collectors.toList());
        // 发货计划数
        List<DeliveryNumDto> needDeliveryList = factoryFinishedInventoryService.getDeliveryNumInfo(neededProjectIdList, purchaseForm);
        int deliverySum = needDeliveryList.stream().mapToInt(DeliveryNumDto::getNumber).sum();
        deliveryPurchaseCountVo.setShippingNum(deliverySum);

        // 无需发货计划数
        List<DeliveryNumDto> noNeedDeliveryList = factoryFinishedInventoryService.getNoNeedDeliveryInfo(neededProjectIdList, purchaseForm);
        int noNeedDeliverySum = noNeedDeliveryList.stream().mapToInt(DeliveryNumDto::getNumber).sum();
        deliveryPurchaseCountVo.setRemainNum(noNeedDeliverySum);

        // 未到发货期数量
        purchaseForm.setCurrent(1);
        purchaseForm.setSize(-1);
        IPage<TrialShippingInventoryBO> iPage = shippingRecordRepository.notYetShippingList(purchaseForm, neededProjectIdList);
        if (iPage == null || CollectionUtil.isEmpty(iPage.getRecords())) {
            deliveryPurchaseCountVo.setNotYetShippingPeriodNum(0);
        } else {
            int notYetSum = iPage.getRecords().stream().mapToInt(TrialShippingInventoryBO::getFactoryShippingNum).sum();
            deliveryPurchaseCountVo.setNotYetShippingPeriodNum(notYetSum);
        }

        deliveryPurchaseCountVo.setTrialStatus(recordBo.getTrialStatus());
        deliveryPurchaseCountVo.setShippingStartDate(StrUtil.isNotBlank(purchaseForm.getShippingStartDate()) ? purchaseForm.getShippingStartDate() : DateUtil.format(recordBo.getShippingStartDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN));
        deliveryPurchaseCountVo.setShippingEndDate(StrUtil.isNotBlank(purchaseForm.getShippingEndDate()) ? purchaseForm.getShippingEndDate() : DateUtil.format(recordBo.getShippingEndDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN));
        deliveryPurchaseCountVo.setLclRecordId(recordBo.getLclRecordId());
        deliveryPurchaseCountVo.setExportFilePath(recordBo.getExportFilePath());
        return deliveryPurchaseCountVo;
    }

    private List<ForeignInventoryDto> sortForeignInventoryDtoList(List<ForeignInventoryDto> inventoryDtoList) {
        return inventoryDtoList.stream()
                .sorted(Comparator.comparing(ForeignInventoryDto::getEnableUsingDate)
                        .thenComparing(ForeignInventoryDto::getWarehouse))
                .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTrialStatus(String recordId, String status) {
        if (!ShippingTrialStatusEnum.contains(status)) {
            throw new RuntimeException("状态码错误");
        }
        if (ShippingTrialStatusEnum.SAVED.getCode().equals(status)) {
            ShippingRecordDO byId = shippingRecordRepository.getById(recordId);
            if (ShippingTrialStatusEnum.ABROGATE.getCode().equals(byId.getTrialStatus())) {
                throw new RuntimeException("保存失败，该计划已被作废");
            }
        }
        return shippingRecordRepository.updateById(ShippingRecordDO.builder()
                .id(recordId).trialStatus(status).build());
    }

    @Override
    public List<UserInteriorVO> getUserList() {
        return sysUserInteriorService.getUserList();
    }

    @Override
    public List<OperatorSearchVo> getOperator() {
        return sysUserService.getOperator();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteRecord(String recordId) {
        ShippingRecordDO recordDO = shippingRecordRepository.getById(recordId);
        if (ObjectUtil.isEmpty(recordDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "删除失败，该计划不存在");
        }
        if (!ShippingTrialStatusEnum.CALCULATION_FAILED.getCode().equals(recordDO.getTrialStatus()) &&
                !ShippingTrialStatusEnum.ABROGATE.getCode().equals(recordDO.getTrialStatus())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "删除失败，该计划不是试算失败或作废状态");
        }

        List<ShippingProjectDO> shippingProjectDOList = shippingProjectRepository.list(ShippingProjectDO.builder().shippingRecordId(recordDO.getId()).build());

        if (CollectionUtil.isNotEmpty(shippingProjectDOList)) {
            List<String> shippingProjectIds = new ArrayList<>();
            List<String> factoryFinishedIds = new ArrayList<>();
            shippingProjectDOList.forEach(shipProjectDO -> {
                shippingProjectIds.add(shipProjectDO.getId());
                List<FactoryFinishedInventoryDO> listByShippingProjectId = factoryFinishedInventoryRepository.getListByShippingProjectId(Collections.singletonList(shipProjectDO.getId()), ShippingSourceTypeEnum.SHIPPING.getCode());
                factoryFinishedIds.addAll(listByShippingProjectId.stream().map(FactoryFinishedInventoryDO::getId).toList());
            });

            mockInventoryRepository.deleteByShippingIds(shippingProjectIds);

            trialShippingInventoryRepository.deleteByFactoryFinishedIds(factoryFinishedIds);

            inventorySaleDestinationRepository.deleteByShippingIds(shippingProjectIds);

            factoryFinishedInventoryRepository.deleteByShippingIds(shippingProjectIds, ShippingSourceTypeEnum.SHIPPING.getCode());

            shippingProjectRepository.deleteByIds(shippingProjectIds);
        }
        String dataSourceId = recordDO.getDataSourceId();
        shippingRecordRepository.deleteById(recordDO.getId());


        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        restTemplateUtils.post(
                FileMissionForm.builder().missionId(dataSourceId)
                        .build(),
                ResultDTO.class, FILE_SYSTEM_DELETE_MISSION_CENTER_URL
        );

        return Boolean.TRUE;
    }

    @Override
    public DeliveryRecordPurchaseListVo downloadExport(DeliveryPurchaseForm form) {
        if (StrUtil.isNotBlank(stringRedisTemplate.opsForValue().get(EXPORT_DELIVERY_LOCK + form.getRecordId()))) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "当前该计划文件生成中，请稍后重试");
        }
        ShippingRecordDO recordDO = shippingRecordRepository.getById(form.getRecordId());

        if (ObjectUtil.isEmpty(recordDO) || StrUtil.isBlank(recordDO.getExportFilePath())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "文件不存在,请先生成导出文件");
        }
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO<Boolean> fileResult = restTemplateUtil.post(FileMissionForm.builder()
                .filePath(recordDO.getExportFilePath())
                .build(), ResultDTO.class, FILE_SYSTEM_EXIST_MISSION_CENTER_URL);

        if (!Objects.equals(fileResult.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("下载发货导出文件失败，异常原因：{}", fileResult.getMessage());
            throw new RuntimeException("下载发货导出文件失败，异常原因：" + fileResult.getMessage());
        }
        Boolean exist = JSON.to(Boolean.class, fileResult.getData());
        if (Boolean.FALSE.equals(exist)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "文件已过期,请先生成导出文件");
        }
        return DeliveryRecordPurchaseListVo.builder().exportFilePath(recordDO.getExportFilePath()).build();
    }

    @Override
    public List<DeliverySaleDestinationDto> getSaleDestination(DeliveryPurchaseForm form) {

        List<TrialInventorySaleDestinationDO> trialInventoryDOList = inventorySaleDestinationRepository.select(TrialInventorySaleDestinationDO.builder()
                .shippingProjectId(form.getShippingProjectId())
                .build());
        if (CollectionUtil.isEmpty(trialInventoryDOList)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "发货数据不存在");
        }
        List<DeliverySaleDestinationDto> result = new ArrayList<>();

        for (var trial:trialInventoryDOList){
            String destinationEverydaySale = trial.getDestinationEverydaySale();
            Map<String, BigDecimal> saleDestinationMap = JSONObject.parseObject(destinationEverydaySale, Map.class);
            String saleDestinationYm = form.getSaleDestinationYm();

            // 获取指定年月的数据
            Map<String, BigDecimal> filteredMap = saleDestinationMap.entrySet().stream()
                    .filter(entry -> entry.getKey().startsWith(saleDestinationYm))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey, // 将 2024/01/01 转换为 2024-01-01
                            Map.Entry::getValue));
            // 解析年月，计算该月的天数
            YearMonth ym = YearMonth.parse(saleDestinationYm, DateTimeFormatter.ofPattern(YYYY_MM_DATE_FORMAT_SLASH));
            int daysInMonth = ym.lengthOfMonth();

            // 为该月的每一天创建记录
            for (int day = 1; day <= daysInMonth; day++) {
                LocalDate date = ym.atDay(day);
                String dateStr = LocalDateTimeUtil.format(date, YYYY_MM_DD_DATE_FORMAT_SLASH);
                // 如果日期存在于原始数据中，使用原始值，否则设为0
                BigDecimal value = filteredMap.getOrDefault(dateStr, new BigDecimal(0));
                result.add(new DeliverySaleDestinationDto(dateStr, value.doubleValue()));
            }
        }
        // 按日期排序
        result.sort(Comparator.comparing(DeliverySaleDestinationDto::getSaleDestinationDate));
        return result;
    }
}
