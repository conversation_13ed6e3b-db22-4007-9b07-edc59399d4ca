package com.inventory_server.applications.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryForeignRedundantInfoDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -7170752445404830514L;
    private String inventoryInfoId;
    private String warehouse;
    private Double redundantNum;
    private String watchBoardId;
}
