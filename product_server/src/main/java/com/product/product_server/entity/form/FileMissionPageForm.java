package com.product.product_server.entity.form;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.crafts_mirror.utils.dp.BasePageForm;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 文档任务中心列表页
 * <AUTHOR>
 * @Date 2023/12/12 14:17
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class FileMissionPageForm extends BasePageForm implements Serializable {
    /**
     * id
     */
    private String missionId;

    /**
     * 类型
     */
    private List<String> type;
}
