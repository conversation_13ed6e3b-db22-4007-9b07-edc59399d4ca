package com.inventory_server.model.warning.unsalable.entity.form;

import com.crafts_mirror.utils.dp.BasePageForm;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/25 11:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UnsalablePageForm extends BasePageForm {
    private String productName;

    private List<String> selfSkuList;

    private List<String> virtualSkuList;

    private List<String> oldSkuList;

    private String categoryId;

    /**
     * 币种
     */
    private String currency;

    private String channel;

    private Integer productStatus;

    private List<Integer> productStatusList;

    private Integer productType;

    private Integer subType;

    private String operator;
}
