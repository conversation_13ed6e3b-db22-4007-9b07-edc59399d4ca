package com.purchase.purchase_server.model.purchase;

import com.purchase.purchase_server.entity.excelObject.DeliveryPurchaseInfoExcel;

/**
 * @Description 发货计划导入-计划数据校验
 * <AUTHOR>
 * @Date 2023/12/29 10:56
 **/
public record DeliveryInfoDp(DeliveryPurchaseInfoExcel deliveryPurchaseInfoExcel) implements CheckDeliveryInfo{
    public DeliveryInfoDp(DeliveryPurchaseInfoExcel deliveryPurchaseInfoExcel) {
        this.deliveryPurchaseInfoExcel = deliveryPurchaseInfoExcel;
        new ImportVirtualSkuInfoDp(deliveryPurchaseInfoExcel.getVirtualSku());
        getTime(deliveryPurchaseInfoExcel.getDeliveryDate());
        checkNum(deliveryPurchaseInfoExcel.getShipments());
        checkRemark(deliveryPurchaseInfoExcel.getRemark());
        checkContractCode(deliveryPurchaseInfoExcel.getContractCode());
    }
}
