package com.purchase.purchase_server.entity.dto.PurchaseOrder;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 采购单交期计划
 * <AUTHOR>
 * @Date 2024/11/11 14:20
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class YicangPurchaseOrderScheduleDto implements Serializable {


    @Serial
    private static final long serialVersionUID = 5334020132204841480L;

    private String id;

    /**
     * 工厂交期
     */
    private String expectedDeliveryDate;
    /**
     * 预期交货数量
     */
    private Integer expectedDeliveryQuantity;
    /**
     * 采购单虚拟表Id
     */
    private String sbPoVirtualId;
}
