CREATE TABLE `cm_inventory_inventory_info`
(
    `id`                          varchar(50)                                                  NOT NULL,
    `self_sku`                    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '自定义sku',
    `yc_warehouse_id`             int                                                          NOT NULL COMMENT '易仓仓库id',
    `sale_status`                 int                                                           DEFAULT NULL COMMENT '产品销售状态id',
    `pi_purchase_onway_qty`       int                                                           DEFAULT NULL COMMENT '采购在途数量',
    `pi_return_onway_qty`         int                                                           DEFAULT NULL COMMENT '退件在途数量',
    `pi_pending_qty`              int                                                           DEFAULT NULL COMMENT '待上架数量',
    `pi_in_used_qty`              int                                                           DEFAULT NULL COMMENT '可用数量',
    `pi_warning_qty`              int                                                           DEFAULT NULL COMMENT '预警数量',
    `pi_sellable_qty`             int                                                           DEFAULT NULL COMMENT '可销数量',
    `pi_shared_qty`               int                                                           DEFAULT NULL COMMENT '分销数量',
    `pi_can_sale_days` double (10,5) DEFAULT NULL COMMENT '可售天数',
    `pi_reserved_qty`             int                                                           DEFAULT NULL COMMENT '待出数量',
    `pi_no_stock_qty`             int                                                           DEFAULT NULL COMMENT '缺货数量',
    `pi_no_stock_days`            int                                                           DEFAULT NULL COMMENT '缺货天数',
    `pi_unsellable_qty`           int                                                           DEFAULT NULL COMMENT '不良品数量',
    `pi_outbound_qty`             int                                                           DEFAULT NULL COMMENT '待出不良品数量',
    `inventory_cost`              decimal(15, 6)                                                DEFAULT NULL COMMENT '库存成本',
    `currency_code`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '币种',
    `pi_update_time`              datetime                                                      DEFAULT NULL COMMENT '最后更新时间',
    `actual_usable_inventory_qty` int                                                           DEFAULT NULL COMMENT '实际可用库存',
    `pi_planned_qty`              int                                                           DEFAULT NULL COMMENT '计划库存数量',
    `purchase_quantity`           int                                                           DEFAULT NULL COMMENT '采购库存数量',
    `create_date`                 datetime                                                      DEFAULT NULL,
    `update_date`                 datetime                                                      DEFAULT NULL,
    `create_by`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL,
    `update_by`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL,
    `status`                      char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL,
    PRIMARY KEY (`id`),
    KEY                           `self_sku` (`self_sku`) USING BTREE,
    KEY                           `yc_warehouse_id` (`yc_warehouse_id`) USING BTREE,
    KEY                           `self_sku_warehouse_id` (`self_sku`,`yc_warehouse_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;