package com.inventory_server.model.warehouse.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Description 状态：-1已废弃,0不可用,1可用
 * <AUTHOR>
 * @Date 2024/7/3 10:50
 **/
@Getter
public enum WarehouseStatusEnum {
    DEPRECATED(-1, "已废弃"),
    UNAVAILABLE(0, "不可用"),
    AVAILABLE(1, "可用")
    ;
    private final Integer type;

    private final String desc;

    private static final Map<Integer, String> map = new HashMap<>(4);

    static {
        map.put(DEPRECATED.type, DEPRECATED.desc);
        map.put(UNAVAILABLE.type, UNAVAILABLE.desc);
        map.put(AVAILABLE.type, AVAILABLE.desc);
    }

    WarehouseStatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByType(int type) {
        return Optional.ofNullable(map.get(type)).orElseThrow(() -> new NullPointerException("没有对应的仓库状态"));
    }
}
