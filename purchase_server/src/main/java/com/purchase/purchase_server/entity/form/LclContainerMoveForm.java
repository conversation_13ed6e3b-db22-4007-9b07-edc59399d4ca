package com.purchase.purchase_server.entity.form;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 拼柜装柜数据移动/拆分表单
 * <AUTHOR>
 * @Date 2025/6/10
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LclContainerMoveForm implements Serializable {
    @Serial
    private static final long serialVersionUID = -1L;

    /**
     * 源计划ID
     */
    private List<String> sourceDetailIds;
    
    /**
     * 目标容器ID，为空表示创建新容器
     */
    private String targetContainerId;

    /**
     * 目标容器名称
     */
    private String targetContainerName;
    
    /**
     * 拆分的数量
     */
    private Integer lclShippingNum;
    
    /**
     * 操作类型：0-移动, 1-拆分
     * @see com.purchase.purchase_server.enums.lcl.MoveOrSplitTypeEnum
     */
    private Integer operationType;

    /**
     * 记录ID
     */
    private String recordId;
}