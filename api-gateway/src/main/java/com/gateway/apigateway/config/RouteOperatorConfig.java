package com.gateway.apigateway.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gateway.apigateway.operator.RouteOperator;
import org.springframework.cloud.gateway.route.RouteDefinitionWriter;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: 雪竹
 * @description: 动态路由配置
 * @dateTime: 2023/11/24 10:30
 **/
@Configuration
public class RouteOperatorConfig {
    @Bean
    public RouteOperator routeOperator(ObjectMapper objectMapper,
                                       RouteDefinitionWriter routeDefinitionWriter,
                                       ApplicationEventPublisher applicationEventPublisher) {
        return new RouteOperator(objectMapper,
                routeDefinitionWriter,
                applicationEventPublisher);
    }

}
