package com.inventory_server.model.product.entity.dos;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @Description 自定义产品
 * <AUTHOR>
 * @Date 2023/12/6 13:56
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_self_product")
public class SelfProductDO extends BaseEntity {
    private String sku;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String image;
    /**
     * 品名
     */
    private String productName;

    /**
     * 分类id
     */
    private String categoryId;

    /**
     * 采购人员
     */
    private String buyer;
    /**
     * 供应商代码
     */
    private String factoryId;
    /**
     * 单品规格长
     */
    private Double singleLength;
    /**
     * 单品规格宽
     */
    private Double singleWidth;
    /**
     * 单品规格高
     */
    private Double singleHeight;
    /**
     * 包装规格长
     */
    private Double packageLength;
    /**
     * 包装规格宽
     */
    private Double packageWidth;
    /**
     * 包装规格高
     */
    private Double packageHeight;
    /**
     * 外箱规格长
     */
    private Double caseLength;
    /**
     * 外箱规格宽
     */
    private Double caseWidth;
    /**
     * 外箱规格高
     */
    private Double caseHeight;
    /**
     * 单品净重
     */
    private Double singleProductNetWeight;
    /**
     * 单品毛重
     */
    private Double singleProductGrossWeight;
    /**
     * 重量单位
     */
    private String weightUnit;
    /**
     * 单箱毛重
     */
    private Double singleCaseGrossWeight;
    /**
     * 装箱量
     */
    private Integer containerLoad;
    /**
     * 币种，暂时只有人民币和美元，以后可能需要建字典维护
     */
    private String currency;
    /**
     * 生产交期
     */
    private String purchaseDate;
    /**
     * 是否含税
     */
    private Integer taxes;
    /**
     * 单价
     */
    private Double price;
    /**
     * 含税单价
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Double priceWithTaxes;
    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remarks;

}
