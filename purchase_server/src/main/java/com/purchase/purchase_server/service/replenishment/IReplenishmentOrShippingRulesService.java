package com.purchase.purchase_server.service.replenishment;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.ProductRulesDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectBaseParamDto;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @createDate 2024-01-17 10:19:20
 */
public interface IReplenishmentOrShippingRulesService {

    default ShippingProjectBaseParamDto getShippingRules(VirtualProductDO virtualProductDO, List<String> warehouseList,
                                                         Map<String, HashMap<String, String>> skuStockingRulesMap) {
        var stockRulesMap = skuStockingRulesMap.get(virtualProductDO.getVirtualSku());
        String ruleId = null;
        Map<String, Double> shippingRatioMap;
        ProductRulesDto productRules;
        Map<String, Integer> headShippingDateMap;
        if (stockRulesMap != null) {
            productRules = getProductRulesFromImportInfo(stockRulesMap, warehouseList);
            headShippingDateMap = getHeadShippingDaysMapFromRedis();
        } else {
            // 从数据库拿虚拟sku规则数据，如果依然为空则抛异常
            productRules = getProductRulesFromDatabase(virtualProductDO);
            ruleId = productRules.getRulesId();
            headShippingDateMap = JSON.parseObject(productRules.getHeadShippingDays(), new TypeReference<>() {
            });
        }

        shippingRatioMap = JSON.parseObject(productRules.getShippingRatio(), new TypeReference<HashMap<String, Double>>() {
        });
        return ShippingProjectBaseParamDto.builder()
                .ruleId(ruleId)
                .virtualSku(virtualProductDO.getVirtualSku())
                .safeDays(productRules.getSafeDays())
                .transitDays(productRules.getTransitDays())
                .purchaseProjectDays(productRules.getPurchaseProjectDays())
                .produceDays(productRules.getProduceDays())
                .headShippingDays(headShippingDateMap)
                .purchaseCircle(productRules.getPurchaseCircle())
                .shippingFrequency(productRules.getShippingFrequency())
                .shippingRatio(shippingRatioMap)
                .build();
    }

    ProductRulesDto getProductRulesFromDatabase(VirtualProductDO virtualSku);

    ProductRulesDto getProductRulesFromDatabase(String rulesId);

    ProductRulesDto getProductRulesFromImportInfo(Map<String, String> stockRulesMap, List<String> warehouseList);

    Map<String, Integer> getHeadShippingDaysMapFromRedis();

    ProductRulesDto getProductRulesById(String id);
}
