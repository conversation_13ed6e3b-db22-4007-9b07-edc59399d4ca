drop table cm_sys_menu;
drop table cm_sys_permission;
drop table cm_sys_role;
drop table cm_sys_role_menu;
drop table cm_sys_role_permission;
drop table cm_sys_user_role;
drop table cm_user;
drop table t_permission;
drop table t_role;
drop table t_role_permission;
drop table t_user_role;

alter table cm_sys_user
    modify id varchar(50) not null;

alter table cm_sys_user
    change username user_name varchar(255) default '' not null comment '用户账号';

alter table cm_sys_user
    change name nick_name varchar(255) not null comment '用户昵称' after user_name;

alter table cm_sys_user
    add phone_number varchar(11) null comment '手机号码' after nick_name;

alter table cm_sys_user
    change password pass_word varchar(255) default '' not null comment '密码';

alter table cm_sys_user
    modify salt varchar(255) not null comment '盐' after pass_word;

alter table cm_sys_user
    drop column enabled;

alter table cm_sys_user
    drop column last_login_time;

alter table cm_sys_user
    add user_status char default '0' null comment '帐号状态（0正常 1停用）' after salt;

alter table cm_sys_user
    add status char default '0' not null;

alter table cm_sys_user
    add create_by varchar(50) null comment '创建人';

alter table cm_sys_user
    add create_date datetime null comment '创建时间';

alter table cm_sys_user
    add update_by varchar(50) null comment '更新人';

alter table cm_sys_user
    add update_date datetime null comment '更新时间';

UPDATE cm_sys_user SET create_by = 'admin', create_date = NOW() WHERE status = '0';

/*
 Navicat Premium Data Transfer

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 80035
 Source Host           : localhost:3306
 Source Schema         : carfts_mirror_2

 Target Server Type    : MySQL
 Target Server Version : 80035
 File Encoding         : 65001

 Date: 15/08/2024 09:58:39
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `cm_sys_menu`;
CREATE TABLE `cm_sys_menu`
(
    `id`          varchar(50)  NOT NULL,
    `menu_name`   varchar(50)  NOT NULL COMMENT '菜单名称',
    `parent_id`   varchar(50)  NULL DEFAULT NULL COMMENT '父菜单ID',
    `order_num`   int          NULL DEFAULT 0 COMMENT '显示顺序',
    `path`        varchar(255) NULL DEFAULT NULL COMMENT '路由地址',
    `path_name`   varchar(255) NULL DEFAULT NULL COMMENT '路由名称',
    `component`   varchar(255) NULL DEFAULT NULL COMMENT '组件路径',
    `is_frame`    char(1)      NULL DEFAULT '1' COMMENT '是否为外链（0是 1否）',
    `menu_type`   char(1)      NULL DEFAULT NULL COMMENT '菜单类型（0目录 1菜单 2按钮）',
    `menu_status` char(1)      NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
    `perms`       varchar(100) NULL DEFAULT NULL COMMENT '权限标识',
    `icon`        varchar(255) NULL DEFAULT NULL COMMENT '菜单图标',
    `url`         varchar(255) NULL DEFAULT NULL COMMENT 'url',
    `status`      char(1)      NOT NULL DEFAULT '0',
    `create_by`   varchar(50)  NULL DEFAULT NULL COMMENT '创建者',
    `create_date` datetime     NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(50)  NULL DEFAULT NULL COMMENT '更新者',
    `update_date` datetime     NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统菜单表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cm_sys_role
-- ----------------------------
DROP TABLE IF EXISTS `cm_sys_role`;
CREATE TABLE `cm_sys_role`
(
    `id`          varchar(50)  NOT NULL,
    `role_name`   varchar(30)  NOT NULL COMMENT '角色名称',
    `role_sort`   int          NULL DEFAULT NULL COMMENT '显示顺序',
    `role_status` char(1)      NOT NULL COMMENT '角色状态（0正常 1停用）',
    `remarks`     varchar(255) NULL DEFAULT NULL,
    `status`      char(1)      NOT NULL DEFAULT '0',
    `create_by`   varchar(50)  NULL DEFAULT NULL,
    `create_date` datetime     NULL DEFAULT NULL,
    `update_by`   varchar(50)  NULL DEFAULT NULL,
    `update_date` datetime     NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统角色表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cm_sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `cm_sys_role_menu`;
CREATE TABLE `cm_sys_role_menu`
(
    `role_id` varchar(50) NOT NULL COMMENT '角色ID',
    `menu_id` varchar(50) NOT NULL COMMENT '用户ID',
    PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户和角色关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cm_sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `cm_sys_user_role`;
CREATE TABLE `cm_sys_user_role`
(
    `user_id` varchar(50) NOT NULL COMMENT '用户ID',
    `role_id` varchar(50) NOT NULL COMMENT '角色ID',
    PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户和角色关联表'
  ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

INSERT INTO `crafts_mirror`.`cm_sys_role` (`id`, `role_name`, `role_sort`, `role_status`, `remarks`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1', '超级管理员', 999, '0', NULL, '0', 'admin', '2024-07-31 13:09:51', NULL, NULL);
INSERT INTO `crafts_mirror`.`cm_sys_user_role` (`user_id`, `role_id`) VALUES ('1', '1');


INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1818191501742698497', '系统管理', '0', 700, '/system', 'System', '', '1', '0', '0', '', 'Tools', NULL, '0', 'admin', '2024-07-30 16:01:19', 'admin', '2024-08-16 17:14:09');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1818204003196997633', '菜单管理', '1818191501742698497', 910, '/menu/manage', 'MenuManage', 'system/MenuManage', '1', '1', '0', 'system:menu:list', '', NULL, '0', 'admin', '2024-07-30 16:36:30', 'admin', '2024-08-20 19:38:30');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823682061001662466', '采购', '0', 900, '/purchase', 'Purchase', NULL, '1', '0', '0', NULL, 'ShoppingCart', NULL, '0', 'admin', '2024-08-14 19:24:20', 'admin', '2024-08-16 17:13:50');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823682188558835713', '库存', '0', 800, '/inventory', 'Inventory', NULL, '1', '0', '0', NULL, 'HomeFilled', NULL, '0', 'admin', '2024-08-14 19:24:51', 'admin', '2024-08-19 21:27:14');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823682776231157761', '补货计划', '1823682061001662466', 999, '/replenishment/plan-list', 'ReplenishmentPlanList', 'replenishment/ReplenishmentPlanList', '1', '1', '0', 'purchase:replenishment:list', '', NULL, '0', 'admin', '2024-08-14 19:27:11', 'admin', '2024-08-20 19:39:41');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823682943642607618', '发货计划', '1823682061001662466', 800, '/shipping/plan-list', 'ShippingPlanList', 'shipping/ShippingPlanList', '1', '1', '0', 'purchase:deliveryPurchase:list', '', NULL, '0', 'admin', '2024-08-14 19:27:51', 'admin', '2024-08-20 19:39:46');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823683109858680834', '供应商管理', '1823682061001662466', 700, '/supplier/manage', 'SupplierManage', 'supplier/SupplierManage', '1', '1', '0', 'purchase:factory:list', '', NULL, '0', 'admin', '2024-08-14 19:28:30', 'admin', '2024-08-20 19:39:50');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823683466282246145', '库存管理', '1823682188558835713', 900, '/inventory/manage', 'InventoryManage', 'inventory/InventoryManage', '1', '1', '0', 'inventory:redundant:list', '', NULL, '0', 'admin', '2024-08-14 19:29:55', 'admin', '2024-08-20 19:39:55');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823683903278391297', '仓库管理', '1823682188558835713', 700, '/warehouse/manage', 'WarehouseManage', 'warehouse/WarehouseManage', '1', '1', '0', 'inventory:warehouse:list', '', NULL, '0', 'admin', '2024-08-14 19:31:40', 'admin', '2024-08-20 19:39:59');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823684557543677954', '用户管理', '1818191501742698497', 700, '/user/manage', 'UserManage', 'system/UserManage', '1', '1', '0', 'system:user:list', '', NULL, '0', 'admin', '2024-08-14 19:34:16', 'admin', '2024-08-20 19:32:02');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823684774204645378', '角色管理', '1818191501742698497', 800, '/role/manage', 'RoleManage', 'system/RoleManage', '1', '1', '0', 'system:role:list', '', NULL, '0', 'admin', '2024-08-14 19:35:07', 'admin', '2024-08-20 19:38:23');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823685022624882690', '产品', '0', 999, '/product', 'Product', NULL, '1', '0', '0', NULL, 'Handbag', NULL, '0', 'admin', '2024-08-14 19:36:06', 'admin', '2024-08-20 19:40:16');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823685276267028482', '产品管理', '1823685022624882690', 900, 'manage', 'ProductManage', 'product/ProductManage', '1', '1', '0', 'product:selfProduct:list', '', NULL, '0', 'admin', '2024-08-14 19:37:07', 'admin', '2024-08-20 19:39:27');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823685462569623553', '虚拟SKU管理', '1823685022624882690', 800, 'virtual-sku', 'ProductVirtualSKU', 'product/ProductVirtualSKU', '1', '1', '0', 'product:virtualProduct:list', '', NULL, '0', 'admin', '2024-08-14 19:37:51', 'admin', '2024-08-20 19:39:36');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823696948440334338', '导出补货建议', '1823682776231157761', 900, NULL, NULL, NULL, '1', '2', '0', 'purchase:replenishment:export', NULL, NULL, '0', 'admin', '2024-08-14 20:23:30', 'admin', '2024-08-20 15:20:39');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823697409943797761', '导出数据源', '1823682776231157761', 700, NULL, NULL, NULL, '1', '2', '0', 'purchase:replenishment:sourceFile', NULL, NULL, '0', 'admin', '2024-08-14 20:25:20', 'admin', '2024-08-20 15:20:40');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823899867219496962', '导入库存', '1823683466282246145', 900, NULL, NULL, NULL, '1', '2', '0', 'inventory:redundant:import', NULL, NULL, '0', 'admin', '2024-08-15 09:49:49', 'admin', '2024-08-19 21:27:14');
INSERT INTO `crafts_mirror`.`cm_sys_menu` (`id`, `menu_name`, `parent_id`, `order_num`, `path`, `path_name`, `component`, `is_frame`, `menu_type`, `menu_status`, `perms`, `icon`, `url`, `status`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ('1823899932977795073', '导出库存/源数据', '1823683466282246145', 800, NULL, NULL, NULL, '1', '2', '0', 'inventory:redundant:export', NULL, NULL, '0', 'admin', '2024-08-15 09:50:05', 'admin', '2024-08-19 21:27:14');



