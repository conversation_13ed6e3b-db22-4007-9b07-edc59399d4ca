package com.inventory_server.model.redundancy.entity.aggregate;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;

/**
 * @Description 数量为double类型的计划数据
 * <AUTHOR>
 * @Date 2025/4/16 17:25
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryFactoryPlanInfoDouble {
    private String id;

    private String inventoryInfoId;

    private String contractCode;

    @JsonFormat(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, timezone = "GMT+8")
    private Date factoryFinishedDate;

    private Integer storeNum;

    private Double shippingNum;

    private Double remainNum;

    private String destinationSku;

    private String remarks;
}
