package com.inventory_server.model.redundancy.entity.dos;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

import com.inventory_server.infrastructures.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_inventory_test_calculation_log")
public class InventoryTestCalculationLogDO extends BaseEntity {

    private String virtualSku;
    private Integer redundancyDays;
    private LocalDate arrivingDate;
    private Double sumTargetSales;
    private Double foreignRedundancy;
    private Double factoryRedundancy;
    private String warehouseForRedundancy;
    private String warehouseFacRedundancy;
    private String arrivingWarehouse;
    private String warehouseTotalRedundancy;

    public InventoryTestCalculationLogDO(String virtualSku, Integer redundancyDays, LocalDate arrivingDate, double sumTargetSales) {
        this.virtualSku = virtualSku;
        this.redundancyDays = redundancyDays;
        this.arrivingDate = arrivingDate;
        this.sumTargetSales = sumTargetSales;
    }

    public InventoryTestCalculationLogDO(String virtualSku, Integer redundancyDays, LocalDate arrivingDate, String warehouseTotalRedundancy) {
        this.virtualSku = virtualSku;
        this.redundancyDays = redundancyDays;
        this.arrivingDate = arrivingDate;
        this.warehouseTotalRedundancy = warehouseTotalRedundancy;
    }

    public InventoryTestCalculationLogDO clonePart() {
        return new InventoryTestCalculationLogDO(virtualSku, redundancyDays, arrivingDate, warehouseTotalRedundancy);
    }
}
