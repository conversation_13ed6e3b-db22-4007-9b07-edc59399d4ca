package com.product.product_server.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;

import java.math.BigDecimal;

/**
 * @Description 单箱数量不为整数时的校验
 * <AUTHOR>
 * @Date 2023/12/21 19:47
 **/
public class ContainerLoaderNumberConverter implements Converter<Integer> {

    @Override
    public Integer convertToJavaData(ReadConverterContext<?> context) {
        BigDecimal value = context.getReadCellData().getOriginalNumberValue();
        if(value == null) {
            throw new IllegalArgumentException("单箱数量不能为空");
        }
        double doubleValue = value.doubleValue();
        if(doubleValue <= 0 || doubleValue - (int)doubleValue != 0) {
            throw new IllegalArgumentException("单箱数量必须是正整数");
        }
        return (int)doubleValue;
    }
}
