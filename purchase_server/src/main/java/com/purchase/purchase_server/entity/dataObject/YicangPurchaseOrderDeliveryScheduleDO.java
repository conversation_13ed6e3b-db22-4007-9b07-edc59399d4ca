package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.util.Date;

/**
 * 易仓采购单交期计划表
 * @TableName cm_yicang_purchase_order_delivery_schedule
 */
@TableName(value ="cm_yicang_purchase_order_delivery_schedule")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class YicangPurchaseOrderDeliveryScheduleDO extends BaseEntity {
    @Serial
    private static final long serialVersionUID = -6563200283805366753L;
    /**
     * 预期交货时间
     */
    private Date expectedDeliveryDate;

    /**
     * 预期交货数量
     */
    private Integer expectedDeliveryQuantity;

    /**
     * 采购单虚拟表Id
     */
    private String sbPoVirtualId;
}