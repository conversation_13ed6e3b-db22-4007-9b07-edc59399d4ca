package com.purchase.purchase_server.entity.dto.PurchaseOrder;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 采购单概述
 * <AUTHOR>
 * @Date 2024/11/4 14:20
 **/
@Data
@Builder
public class OrdersSummaryDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 8125950317487447144L;
    /**
     * 采购单Id
     */
    @JSONField(name = "po_id")
    private Integer poId;

    /**
     * 采购单号
     */
    @JSONField(name = "po_code")
    private String poCode;

    /**
     * 仓库Id
     */
    @JSONField(name = "warehouse_id")
    private Integer warehouseId;

    /**
     * 头程运输方式
     */
    @JSONField(name = "shipping_method_id_head")
    private Integer shippingMethodIdHead;

    /**
     * 跟踪号
     */
    @JSONField(name = "tracking_no")
    private String trackingNo;

    /**
     * 参考号
     */
    @JSONField(name = "ref_no")
    private String refNo;

    /**
     * 供应商Id
     */
    @JSONField(name = "suppiler_id")
    private Integer suppilerId;

    /**
     * 总应付金额
     */
    @JSONField(name = "payable_amount")
    private Float payableAmount;

    /**
     * 总实付金额
     */
    @JSONField(name = "actually_amount")
    private Float actuallyAmount;

    /**
     * 币种
     */
    @JSONField(name = "currency_code")
    private String currencyCode;

    /**
     * 付款状态：
     * 0未申请付款，
     * 3已申请未付款，1已付款 ，
     * 2未付清
     */
    @JSONField(name = "pay_status")
    private Integer payStatus;

    /**
     * 采购单状态：
     * 10已确认(供应商反馈)，
     * 1待确认，
     * 2审核中，
     * 3已审批(入库在途)，
     * 5到货异常，
     * 8已完成，
     * 9撤销，
     * 11已取消(供应商反馈) ('10'、'11' 这2种状态未开通供应商系统忽略)
     */
    @JSONField(name = "po_staus")
    private Integer poStaus;

    /**
     * 创建时间
     */
    @JSONField(name = "date_create")
    private String dateCreate;

    /**
     * 预计到货时间
     */
    @JSONField(name = "date_eta")
    private String dateEta;

    /**
     * 审核时间
     */
    @JSONField(name = "date_release")
    private String dateRelease;

    /**
     * 采购单完成时间
     */
    @JSONField(name = "po_completion_time")
    private String poCompletionTime;

    /**
     * 更新时间
     * （以下业务场景下会更新：
     * 1，采购单，操作 ： 编辑、提交审核、撤销提交审核 、 采购单撤销、强制完成采购单、批量导入跟踪号信息
     * 2，收货、
     * 3，质检（以及操作未到货）
     * 4，取消上架
     * 5，出纳付款，操作“确认付款”
     * 6，采购变更，变更单， 操作：创建变更单、作废、驳回、审核通过
     * 7，收货异常处理（触发更新：payable_amount“应付金额”、receiving_exception_status “收货异常状态” 或者 po_staus “采购单状态”时）
     * 8，QC异常处理（触发更新：qc_exception_status “QC异常状态” 或者 po_staus“采购单状态”时）
     * 9，类型为“退货”、“良品换货”、“次品换良品”出库单，使用的采购单批次，确认出库）
     */
    @JSONField(name = "po_update_time")
    private String poUpdateTime;

    /**
     * 中转仓库Id
     */
    @JSONField(name = "to_warehouse_id")
    private Integer toWarehouseId;

    /**
     * 收货异常：
     * 0没有异常、
     * 1收货异常、
     * 2QC异常
     * 3收货&质检异常
     */
    @JSONField(name = "receiving_exception")
    private Integer receivingException;

    /**
     * 采购员（用户ID）
     */
    @JSONField(name = "operator_purchase")
    private Integer operatorPurchase;

    /**
     * 收货异常是否已经处理
     * 0无、
     * 1没有处理、
     * 2已经处理
     */
    @JSONField(name = "receiving_exception_handle")
    private Integer receivingExceptionHandle;

    /**
     * 采购单是否通过审批，未通过或通过
     */
    @JSONField(name = "return_verify")
    private String returnVerify;

    /**
     * 运输金额
     */
    @JSONField(name = "pay_ship_amount")
    private Float payShipAmount;

    /**
     * 建立方式，
     * 0系统生成，
     * 1人工建立
     */
    @JSONField(name = "create_type")
    private Integer createType;

    /**
     * 跟单状态序号
     */
    @JSONField(name = "pts_status_sort")
    private String ptsStatusSort;

    /**
     * 结算方式
     */
    @JSONField(name = "account_type")
    private Integer accountType;

    /**
     * 跟单员（用户ID）
     */
    @JSONField(name = "pts_oprater")
    private Integer ptsOprater;

    /**
     * 交易支付单号，如淘宝交易号
     */
    @JSONField(name = "transaction_no")
    private String transactionNo;

    /**
     * 采购单承运商ID
     */
    @JSONField(name = "ps_id")
    private Integer psId;

    /**
     * 备注
     */
    @JSONField(name = "po_remark")
    private String poRemark;

    /**
     * 收货异常状态
     */
    @JSONField(name = "receiving_exception_status")
    private Integer receivingExceptionStatus;

    /**
     * 质检异常状态
     */
    @JSONField(name = "qc_exception_status")
    private Integer qcExceptionStatus;

    /**
     * 供应商名称
     */
    @JSONField(name = "supplier_name")
    private String supplierName;

    /**
     * 供应商代码
     */
    @JSONField(name = "supplier_code")
    private String supplierCode;

    /**
     * 仓库代码
     */
    @JSONField(name = "warehouse_code")
    private String warehouseCode;

    /**
     * 仓库描述
     */
    @JSONField(name = "warehouse_desc")
    private String warehouseDesc;

    /**
     * 入库单号
     */
    @JSONField(name = "receiving_code")
    private String receivingCode;

    /**
     * 是否是审核未通过，回退的采购单
     * 0否
     * 1是
     */
    @JSONField(name = "verify")
    private Integer verify;

    /**
     * 到货（
     * 1今日到
     * 2晚到
     * ）
     */
    @JSONField(name = "mark_eta")
    private Integer markEta;

    /**
     * 跟单员
     */
    @JSONField(name = "pts_name")
    private String ptsName;

    /**
     * 承运商名称（ps_id为0时不返回）
     */
    @JSONField(name = "ps_name")
    private String psName;

    /**
     * 承运商网址（ps_id为0时不返回）
     */
    @JSONField(name = "ps_url")
    private String psUrl;

    /**
     * 最新的采购单跟踪备注
     */
    @JSONField(name = "pt_note")
    private String ptNote;

    /**
     * 最新的采购单跟踪备注时间
     */
    @JSONField(name = "pt_add_time")
    private String ptAddTime;

    /**
     * 预期总数
     */
    @JSONField(name = "qty_expected_all")
    private Integer qtyExpectedAll;

    /**
     * 实到总数
     */
    @JSONField(name = "qty_receving_all")
    private Integer qtyRecevingAll;

    /**
     * 采购总数
     */@JSONField(name = "qty_eta_all")
    private Integer qtyEtaAll;

    /**
     * 跟踪号数量
     */
    @JSONField(name = "trackings")
    private Integer trackings;

    /**
     * 是否网采：
     * 0否，
     * 1是
     */
    @JSONField(name = "po_is_net")
    private Integer poIsNet;

    /**
     * 支付方式，
     * 1现金、
     * 2在线，
     * 3银行账号
     */
    @JSONField(name = "pay_type")
    private Integer payType;

    /**
     * 银行名称，支付方式为银行账号时返回
     */
    @JSONField(name = "bank_name")
    private String bankName;

    /**
     * 支付账户
     */
    @JSONField(name = "pay_account")
    private String payAccount;

    /**
     * 预期总金额
     */
    @JSONField(name = "sum_amount")
    private Float sumAmount;

    /**
     * 预计出厂时间
     */
    @JSONField(name = "date_expected")
    private String dateExpected;

    /**
     * 补货方式：
     * 0缺货入库，
     * 1警报入库，
     * 2特采入库，
     * 3正常入库，
     * 4样品采购入库，
     * 5备货采购，
     * 6试销采购，
     * 7返修入库
     */
    @JSONField(name = "po_type")
    private Integer poType;

    /**
     * 网采单号（多个）格式：["1111111","222222"]
     */
    @JSONField(name = "single_net_number")
    private List<String> singleNetNumber;

    /**
     * 税费
     */
    @JSONField(name = "total_tax_fee")
    private Float totalTaxFee;

    /**
     * 采购审核备注
     */
    @JSONField(name = "payment_note")
    private String paymentNote;

    /**
     * 采购公司
     */
    @JSONField(name = "company")
    private String company;

    /**
     * 创建人
     */
    @JSONField(name = "operator_create")
    private String operatorCreate;

    /**
     * 跟踪号集合（所有跟踪号，格式：["123","234"]）beta版未上线
     */
    @JSONField(name = "tracking_no_set")
    private List<String> trackingNoSet;

    /**
     * 组织机构id
     */
    @JSONField(name = "user_organization_id")
    private Integer userOrganizationId;

    /**
     * 是否退税（0-否 1-是）
     */
    @JSONField(name = "is_rebate_tax")
    private Integer isRebateTax;

    /**
     * 组织机构名称
     */
    @JSONField(name = "user_organization_name")
    private String userOrganizationName;

    /**
     * 预付比例
     */
    @JSONField(name = "account_proportion")
    private Integer accountProportion;

    /**
     * 支付周期
     */
    @JSONField(name = "payment_cycle_type")
    private String paymentCycleType;

    /**
     * 是否需要复审 0 不需要 1 需要
     */
    @JSONField(name = "is_re_examine")
    private Integer isReExamine;

    /**
     * 最近收货时间
     */
    @JSONField(name = "latest_receiving_time")
    private Date latestReceivingTime;

    /**
     * 采购单跟单状态
     */
    @JSONField(name = "track")
    private Object track;

    /**
     * 系统跟单状态
     */
    @JSONField(name = "systemTrack")
    private Object systemTrack;


    @JSONField(name = "detail")
    private List<YicangPurchaseOrderDetailDTO> detail;

    private String factoryId;

}
