package com.sales_server.applications.service;

import com.crafts_mirror.utils.dp.CalculationForm;
import com.sales_server.model.targetSales.entity.TargetSalesPage;
import com.sales_server.model.targetSales.entity.dto.TargetDaySalesDto;
import com.sales_server.model.targetSales.entity.form.DaySalesEditForm;
import com.sales_server.model.targetSales.entity.form.TargetDaySalesDetailForm;
import com.sales_server.model.targetSales.entity.form.TargetMonthSalesPageForm;
import com.sales_server.model.targetSales.entity.vo.TargetMonthSalesVo;
import jakarta.servlet.http.HttpServletResponse;

import java.io.InputStream;
import java.util.List;

public interface ITargetSalesAppService {

    void importTargetSalesInfo(InputStream file, byte[] fileBytes, String fileName);

    List<TargetMonthSalesVo> getTargetMonthSaleList(TargetMonthSalesPageForm form);

    TargetSalesPage getTargetMonthSalePageBySpu(TargetMonthSalesPageForm form);

    boolean editTargetSalesInfo(DaySalesEditForm form);

    List<TargetDaySalesDto> getTargetDaySalesInfo(TargetDaySalesDetailForm form);

    void exportTargetMonthSales(TargetMonthSalesPageForm form, HttpServletResponse response);

    void deleteTargetSales(String virtualId);

    void prepareCalculationTargetSales(CalculationForm form);

}
