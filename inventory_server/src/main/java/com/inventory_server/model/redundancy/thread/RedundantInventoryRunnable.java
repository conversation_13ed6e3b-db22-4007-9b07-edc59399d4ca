package com.inventory_server.model.redundancy.thread;

import com.inventory_server.model.redundancy.entity.dp.InventoryCalDp;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 冗余库存计算线程
 * <AUTHOR>
 * @Date 2024/4/25 14:13
 **/
@Slf4j
public class RedundantInventoryRunnable implements Runnable {
    private final String username;

    private final InventoryCalDp inventoryCalDp;

    public RedundantInventoryRunnable(String username, InventoryCalDp inventoryCalDp) {
        this.username = username;
        this.inventoryCalDp = inventoryCalDp;
    }

    @Override
    public void run() {

    }
}
