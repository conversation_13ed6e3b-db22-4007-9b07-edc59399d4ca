package com.product.product_server.entity.vo;

import com.product.product_server.entity.dto.VirtualSkuProductUpdateDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 自定义商品更新
 * <AUTHOR>
 * @Date 2024/3/7 9:59
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SelfProductUpdateVo implements Serializable {


    @Serial
    private static final long serialVersionUID = -3376294878120193848L;

    private Boolean resultStatus;

    private String message;

    private List<VirtualSkuProductUpdateDto> virtualSkuList;

}
