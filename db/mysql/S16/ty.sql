ALTER TABLE `crafts_mirror`.`cm_trial_shipping_inventory`
    ADD COLUMN `delivery_date_range` text NULL COMMENT '发货量统计区间' AFTER `expected_arriving_date`;

ALTER TABLE `crafts_mirror`.`cm_shipping_record`
    MODIFY COLUMN `shipping_start_date` datetime NOT NULL COMMENT '默认最早发货装柜时间' AFTER `id`,
    MODIFY COLUMN `shipping_end_date` datetime NOT NULL COMMENT '默认最晚发货装柜时间' AFTER `shipping_start_date`;

RENAME TABLE `crafts_mirror`.`cm_prepare_products_rules` TO `crafts_mirror`.`cm_delivery_rules`;

ALTER TABLE `crafts_mirror`.`cm_delivery_rules`
    ADD COLUMN `delivery_project_id` varchar(50) NULL COMMENT '发货计划id' AFTER `virtual_sku_id`;

UPDATE `crafts_mirror`.`cm_delivery_rules` AS dr
    JOIN `crafts_mirror`.`cm_mock_inventory_table` AS mit
    ON dr.id = mit.rules_id
SET dr.delivery_project_id = mit.shipping_project_id;