package com.purchase.purchase_server.service.purchaseOrder;

import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrderSaveScheduleDto;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.PurchaseOrderImportDTO;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrdersScheduleVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cm_yicang_purchase_order_delivery_schedule(易仓采购单交期计划表)】的数据库操作Service
 * @createDate 2024-11-01 14:30:43
 */
public interface IYicangPurchaseOrderDeliveryScheduleService {

    /**
     * 采购单交期计划
     *
     * @param sbPoVirtualId 采购单虚拟表Id
     */
    void saveOrderVirtualByImport(PurchaseOrderImportDTO dto, String sbPoVirtualId);


    /**
     * 根据采购单虚拟表Id删除
     *
     * @param sbPoVirtualIds 采购单id
     */
    void removeBySbPoVirtualIds(List<String> sbPoVirtualIds);

    /**
     * @param sbPoVirtualId senbo采购单id
     * @return 交期计划
     */
    List<YicangPurchaseOrdersScheduleVO> getOrderSchedule(String sbPoVirtualId);


    /**
     * @param val 交期计划
     */
    boolean saveOrderSchedule(OrderSaveScheduleDto val);
}
