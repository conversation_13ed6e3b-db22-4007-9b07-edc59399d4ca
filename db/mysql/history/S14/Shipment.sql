SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_shipment_detail
-- ----------------------------
DROP TABLE IF EXISTS `cm_shipment_detail`;
CREATE TABLE `cm_shipment_detail`
(
    `id`                    varchar(50)  NOT NULL,
    `shipment_plan_id`      varchar(50)  NULL DEFAULT NULL COMMENT '货件计划表ID',
    `contract_no`           varchar(50)  NULL DEFAULT NULL COMMENT '合同号',
    `virtual_sku_id`        varchar(50)  NULL DEFAULT NULL COMMENT 'senbo虚拟skuId',
    `destination_sku`       varchar(255) NULL DEFAULT NULL COMMENT 'senbo虚拟sku',
    `is_old_status`         char(1)      NULL DEFAULT NULL COMMENT 'sku类型(0-虚拟sku 1-老sku)',
    `operator`              varchar(255) NULL DEFAULT NULL COMMENT '运营',
    `channel`               varchar(255) NULL DEFAULT NULL COMMENT '渠道',
    `self_product_id`       varchar(50)  NULL DEFAULT NULL COMMENT '自定义skuId',
    `self_product_name`     varchar(255) NULL DEFAULT NULL COMMENT '产品名称',
    `self_sku`              varchar(255) NULL DEFAULT NULL COMMENT '自定义sku',
    `image`                 varchar(255) NULL DEFAULT NULL COMMENT '图片链接',
    `factory_id`            varchar(50)  NULL DEFAULT NULL COMMENT '供应商id',
    `factory_code`          varchar(100) NULL DEFAULT NULL COMMENT '供应商代码',
    `factory_delivery_date` date         NULL DEFAULT NULL COMMENT '工厂交期',
    `shipping_quantity`     int          NULL DEFAULT NULL COMMENT '发货数量',
    `status`                char(1)      NOT NULL,
    `create_by`             varchar(50)  NULL DEFAULT NULL COMMENT '创建人',
    `update_by`             varchar(50)  NULL DEFAULT NULL COMMENT '更新人',
    `create_date`           datetime     NULL DEFAULT NULL COMMENT '创建时间',
    `update_date`           datetime     NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '货件明细发货数量表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cm_shipment_plan
-- ----------------------------
DROP TABLE IF EXISTS `cm_shipment_plan`;
CREATE TABLE `cm_shipment_plan`
(
    `id`                     varchar(50)  NOT NULL,
    `shipment_code`          varchar(50)  NULL DEFAULT NULL COMMENT '货件号',
    `warehouse_id`           varchar(50)  NULL DEFAULT NULL COMMENT '仓库id',
    `warehouse_name`         varchar(255) NULL DEFAULT NULL COMMENT '仓库名称',
    `actual_loading_time`    date         NULL DEFAULT NULL COMMENT '实际装柜时间',
    `shipping_date`          date         NULL DEFAULT NULL COMMENT '开船时间',
    `estimated_arrival_time` date         NULL DEFAULT NULL COMMENT '预计到港时间',
    `actual_arrival_time`    date         NULL DEFAULT NULL COMMENT '实际到港时间',
    `estimated_sign_time`    date         NULL DEFAULT NULL COMMENT '预计签收时间',
    `actual_sign_time`       date         NULL DEFAULT NULL COMMENT '实际签收时间',
    `warehouse_shelf_time`   date         NULL DEFAULT NULL COMMENT '海外仓上架时间',
    `actual_sign`            date         NULL DEFAULT NULL COMMENT '实际签收',
    `shipment_type`          varchar(50)  NULL DEFAULT NULL COMMENT '货件类型 0-灯具，1-家具，2-灯具+家具，3-家具+灯具',
    `status`                 char(1)      NOT NULL,
    `create_by`              varchar(50)  NULL DEFAULT NULL COMMENT '创建人',
    `update_by`              varchar(50)  NULL DEFAULT NULL COMMENT '更新人',
    `create_date`            datetime     NULL DEFAULT NULL COMMENT '创建时间',
    `update_date`            datetime     NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '货件计划表'
  ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
