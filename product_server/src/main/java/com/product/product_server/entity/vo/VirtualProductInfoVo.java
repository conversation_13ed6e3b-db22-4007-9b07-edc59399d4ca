package com.product.product_server.entity.vo;

import com.product.product_server.model.virtualProduct.VirtualProductInfoDp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/14 10:46
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VirtualProductInfoVo implements Serializable {

    private List<VirtualProductInfoDp> virtualProductInfoList;
}
