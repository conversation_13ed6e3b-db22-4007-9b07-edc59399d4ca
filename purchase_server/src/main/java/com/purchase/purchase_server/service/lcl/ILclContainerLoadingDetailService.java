package com.purchase.purchase_server.service.lcl;

import com.purchase.purchase_server.entity.bo.Lcl.LclContainerLoadingBO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclContainerDetailDO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclContainerInfoDO;
import com.purchase.purchase_server.entity.dto.ProductCategoryDTO;
import com.purchase.purchase_server.entity.form.LclSearchPageForm;
import com.purchase.purchase_server.entity.form.LclUpdateShippingNumForm;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @createDate 2025-5-22 16:47:07
*/
public interface ILclContainerLoadingDetailService {

    /**
     * 装柜信息整理
     */
    LclContainerInfoDO lclContainerInfoByDetail(List<LclContainerLoadingBO> loadedItems, Map<String, ProductCategoryDTO> categoryDTOMap);

    List<LclContainerDetailDO> getDetailProductSnapshotIdListByFactoryFinishedInfo(LclSearchPageForm form);

    List<String> getContainerIdList(List<String> snapshotIdList, String recordId);

    List<LclContainerDetailDO> getContainerDetailInfoList(List<String> snapshotIdList, String containerId);

    void updateShippingNum(LclUpdateShippingNumForm form);

    void updateContainerInfo(String containerInfoId);
}
