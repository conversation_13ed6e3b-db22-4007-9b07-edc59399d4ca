package com.product.product_server.service;


import com.product.product_server.entity.vo.ProductSnapshotVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IProductSnapshotService {

    void saveProductSnapshot(String virtualProductId);

    void saveProductSnapshotList(List<String> virtualProductIdList);

    void saveProductSnapshotListBySelfList(List<String> selfProductIdList);

    void saveProductSnapshotByFactory(List<String> factoryInfoIds);

    ProductSnapshotVo getProductSnapshotList(List<String> snapshotIdList);
}
