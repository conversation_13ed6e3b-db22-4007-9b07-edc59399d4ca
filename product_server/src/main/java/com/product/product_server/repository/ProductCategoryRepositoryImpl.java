package com.product.product_server.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.product.product_server.entity.dataObject.ProductCategoryDO;
import com.product.product_server.mapper.ProductCategoryMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ProductCategoryRepositoryImpl extends ServiceImpl<ProductCategoryMapper, ProductCategoryDO> {

    public List<ProductCategoryDO> selectCategoryList(){
        return this.baseMapper.selectList(Wrappers.<ProductCategoryDO>lambdaQuery()
                .orderByAsc(ProductCategoryDO::getParentId)
                .orderByAsc(ProductCategoryDO::getCategorySort)
                .orderByAsc(ProductCategoryDO::getCategoryName));
    }

}
