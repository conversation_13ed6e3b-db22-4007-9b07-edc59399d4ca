package com.auth.authorization_server.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/6/14
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class menuDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 2250218366872004882L;

    private Map<String, Set<String>> menuMap;
}
