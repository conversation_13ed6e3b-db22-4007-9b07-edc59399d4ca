package com.auth.authorization_server.model.dp;

import cn.hutool.core.util.StrUtil;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * @Description 密码验证的dp，此处使用record模式，全字段构造类隐式调用，如果需要在构造函数中添加逻辑的话，显示创建一个即可
 * <AUTHOR>
 * @Date 2023/12/2 13:41
 **/
public record PasswordDP(String password, String salt) {

    public boolean checkInputPassword(String inputPassword) {
        if (StrUtil.isBlank(inputPassword)) {
            return false;
        }

        String encryptPassword = new BCryptPasswordEncoder().encode(inputPassword + salt);
        return password.equals(encryptPassword);
    }
}
