package com.purchase.purchase_server.service.purchaseOrder.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.google.common.util.concurrent.RateLimiter;
import com.purchase.purchase_server.assembler.YiCangPurchaseOrdersAssembler;
import com.purchase.purchase_server.entity.dataObject.FactoryInfoDO;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrdersDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrdersSummaryDto;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.ProcessStats;
import com.purchase.purchase_server.entity.dto.factory.FactoryInfoDto;
import com.purchase.purchase_server.entity.form.FileMissionForm;
import com.purchase.purchase_server.entity.form.PurchaseOrdersSummaryForm;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.enums.purchaseOrders.IsImportedEnum;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.repository.dataRepository.FactoryInfoRepositoryImpl;
import com.purchase.purchase_server.repository.httpRepository.FetchPurchaseOrdersHttpRepository;
import com.purchase.purchase_server.service.purchaseOrder.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.SYNCHRONIZE_PURCHASE_ORDER_LOCK;
import static com.crafts_mirror.utils.constant.SystemConstant.FILE_SYSTEM_MISSION_CENTER_URL;

/**
 * @Description 从易仓处获取采购单信息的service层
 * <AUTHOR>
 * @Date 2024/11/4 13:49
 **/
@Service
@Slf4j
public class FetchOrdersFromECServiceImpl implements IFetchOrdersFromECService {

    @Resource
    private FetchPurchaseOrdersHttpRepository fetchPurchaseOrdersHttpRepository;

    @Resource
    private IPurchaseOrdersService purchaseOrdersService;
    @Resource
    private IYicangPurchaseOrderDetailService purchaseOrderDetailService;
    @Resource
    private IYicangPurchaseOrderTrackService purchaseOrderTrackService;
    @Resource
    private IYicangPurchaseOrderSystemTrackService purchaseOrderSystemTrackService;
    @Resource
    private YiCangPurchaseOrdersAssembler purchaseOrdersAssembler;

    @Resource
    protected RestTemplate restTemplate;

    @Resource
    private FactoryInfoRepositoryImpl factoryInfoRepository;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    // 创建RateLimiter实例，限制为每3秒一次调用（0.333次/秒）
    private final RateLimiter rateLimiter = RateLimiter.create(0.3);

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void fetchPurchaseOrdersSummaryList(String jobParam) {

        var restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        String username = StrUtil.isNotBlank(SecurityUtils.getUsername()) ? SecurityUtils.getUsername() : "系统";
        ResultDTO<String> restResult = restTemplateUtil.post(
                FileMissionForm.builder()
                        .importStatus("同步分析中").type("同步采购单")
                        .fileName("")
                        .createBy(username)
                        .build(),
                ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL
        );
        if (!Objects.equals(restResult.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("导入补货计划时插入文件中心失败，异常原因：{}", restResult.getMessage());
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "导入补货计划时插入文件中心失败，异常原因：" + restResult.getMessage());
        }

        if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(SYNCHRONIZE_PURCHASE_ORDER_LOCK, "1", 3, TimeUnit.HOURS))) {
            log.error("同步采购单失败，当前有正在同步采购单计划");
            restTemplateUtil.post(
                    FileMissionForm.builder()
                            .missionId(restResult.getData())
                            .importStatus("同步失败")
                            .type("同步采购单")
                            .importResult("同步任务进行中请稍后再试")
                            .updateBy(username)
                            .build(),
                    ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL
            );
            return;
        }

        // 构建查询表单
        var form = buildInitialForm(jobParam);
        ProcessStats stats = ProcessStats.builder()
                .successCount(0)
                .failCount(0)
                .failResult(new ArrayList<>())
                .build();
        try {
            // 获取总数并计算分页
            rateLimiter.acquire();
            var total = fetchPurchaseOrdersHttpRepository
                    .fetchAllPurchaseOrdersSummaryList(form)
                    .getTotal();

            if (total > 0) {
                // 分页获取订单列表
                var ordersList = fetchAllOrders(form, total);
                log.info("从易仓处获取采购单信息成功，获取到的采购单总数为：{}", total);

                // 处理订单列表
                ordersList.forEach(order -> processOrder(order, stats));
            }
            restTemplateUtil.post(
                    FileMissionForm.builder()
                            .missionId(restResult.getData())
                            .importStatus("同步成功")
                            .type("同步采购单")
                            .failedResultList(stats.getFailResult())
                            .importResult(String.format("成功：%d；失败：%d",
                                    stats.getSuccessCount(), stats.getFailCount()))
                            .finishDate(new Date())
                            .updateBy(username)
                            .build(),
                    ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL
            );
        } catch (Exception e) {
            restTemplateUtil.post(
                    FileMissionForm.builder()
                            .missionId(restResult.getData())
                            .importStatus("同步失败")
                            .type("同步采购单")
                            .failedResultList(Collections.singletonList(e.getMessage()))
                            .finishDate(new Date())
                            .updateBy(username)
                            .build(),
                    ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL
            );

        } finally {
            stringRedisTemplate.delete(SYNCHRONIZE_PURCHASE_ORDER_LOCK);
        }
    }


    private PurchaseOrdersSummaryForm buildInitialForm(String jobParam) {
        PurchaseOrdersSummaryForm form = new PurchaseOrdersSummaryForm();

        if (StrUtil.isBlank(jobParam)) {
            var now = LocalDateTime.now();
            var yesterday = now.minusDays(2);
            var startDate = LocalDateTimeUtil.format(LocalDateTimeUtil.beginOfDay(yesterday), YYYY_MM_DD_DATE_FORMAT_HYPHEN);
            var endDate = LocalDateTimeUtil.format(LocalDateTimeUtil.endOfDay(now.plusDays(1)), YYYY_MM_DD_DATE_FORMAT_HYPHEN);
            form.setSearchDateType("updateTime");
            form.setDateFor(startDate);
            form.setDateTo(endDate);
        } else if (!jobParam.equals("-1")) {
            form = JSON.parseObject(jobParam, PurchaseOrdersSummaryForm.class);
        }
        form.setPage(1);
        form.setPageSize(1);
        return form;
    }

    private List<OrdersSummaryDto> fetchAllOrders(PurchaseOrdersSummaryForm form, Integer total) {
        int pageSize = 20;
        int totalPage = (int) Math.ceil((double) total / pageSize);

        return IntStream.rangeClosed(1, totalPage)
                .mapToObj(i -> {
                    form.setPage(i);
                    form.setPageSize(pageSize);
                    rateLimiter.acquire();
                    System.out.println("第" + i + "页易仓");
                    return fetchPurchaseOrdersHttpRepository
                            .fetchAllPurchaseOrdersSummaryList(form)
                            .getData();
                })
                .flatMap(List::stream)
                .toList();
    }

    private void processOrder(OrdersSummaryDto order, ProcessStats stats) {
        List<String> failList = new ArrayList<>();
        // 校验供应商代码
        String supplierCode = order.getSupplierCode();
        if (StrUtil.isBlank(supplierCode)) {
            failList.add("采购单：" + order.getPoCode() + "供应商代码为空");
        } else {
            FactoryInfoDto info = new FactoryInfoDto();
            info.setFactoryCode(supplierCode);
            List<FactoryInfoDO> factoryInfoDOS = factoryInfoRepository.listByfactoryInfoQuery(info);
            if (CollectionUtil.isEmpty(factoryInfoDOS)) {
                failList.add("采购单：" + order.getPoCode() + ",供应商代码：" + supplierCode + "不存在");
            } else {
                order.setFactoryId(factoryInfoDOS.getFirst().getId());
            }
        }
        if (CollectionUtil.isEmpty(failList)) {
            // 检查是否已经导入
            var purchaseOrdersDOList = purchaseOrdersService.select(
                    OrdersSummaryDto.builder().poId(order.getPoId()).build()
            );
            if (CollectionUtil.isNotEmpty(purchaseOrdersDOList)) {
                log.warn(String.valueOf(order.getPoId()));
                processExistingOrder(order, purchaseOrdersDOList.getFirst(), failList);
            } else {
                processNewOrder(order, failList);
            }
        }
        if (CollectionUtil.isNotEmpty(failList)) {
            stats.getFailResult().addAll(failList);
            stats.setFailCount(stats.getFailCount() + 1);
        } else {
            stats.setSuccessCount(stats.getSuccessCount() + 1);
        }
    }

    private void processExistingOrder(OrdersSummaryDto order, YicangPurchaseOrdersDO existingOrder,
                                      List<String> failResult) {
        var sbPoId = existingOrder.getId();
        // 更新采购单基本信息
        updateOrderBasicInfo(order, sbPoId);
        // 更新采购单跟单状态
        updateTrackingInfo(order, sbPoId);
        if (IsImportedEnum.NOT_IMPORT.getCode().equals(existingOrder.getIsImported())) {
            // 更新易仓采购单明细
            purchaseOrderDetailService.updateOrderDetails(order, sbPoId, failResult);
        }
    }

    private void processNewOrder(OrdersSummaryDto order, List<String> failResult) {
        var newOrder = purchaseOrdersAssembler.orderDtotoDO(order);
        purchaseOrdersService.save(newOrder);
        var sbPoId = newOrder.getId();
        updateTrackingInfo(order, sbPoId);
        purchaseOrderDetailService.saveOrderDetails(order, sbPoId, order.getPoCode(), failResult);
    }

    private void updateOrderBasicInfo(OrdersSummaryDto order, String sbPoId) {
        var updatedOrder = purchaseOrdersAssembler.orderDtotoDO(order);
        updatedOrder.setId(sbPoId);
        purchaseOrdersService.updateById(updatedOrder);
    }

    private void updateTrackingInfo(OrdersSummaryDto order, String sbPoId) {
        purchaseOrderTrackService.updateTrackingInfo(order, sbPoId);
        purchaseOrderSystemTrackService.updateTrackingInfo(order, sbPoId);
    }

}
