package com.purchase.purchase_server.entity.bo;

import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * @Description 待发货数量统计
 * <AUTHOR>
 * @Date 2024/1/10 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ShipCountBO extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 无需发货数量
     */
    private Integer factoryRemainNum;
    /**
     * 需发货数量
     */
    private Integer shippingNum;
    /**
     * 未到发货期数量
     */
    private Integer notYetShippingPeriodNum;
}
