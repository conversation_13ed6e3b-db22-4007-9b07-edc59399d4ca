package com.crafts_mirror.utils.web.domain;

import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ResultDTO<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = -1L;

    /**
     * 响应状态码
     */
    private Integer status;
    /**
     * 响应信息
     */
    private String message;
    /**
     * 响应数据
     */
    private T data;

    public static <T> ResultDTO<T> success() {
        return restResult(null, ResponseCodeEnum.OK.getCode(), "ok");
    }

    public static <T> ResultDTO<T> success(String message) {
        return restResult(null, ResponseCodeEnum.OK.getCode(), message);
    }

    public static <T> ResultDTO<T> success(String message, T data) {
        return restResult(data, ResponseCodeEnum.OK.getCode(), message);
    }

    public static <T> ResultDTO<T> success(T data) {
        return restResult(data, ResponseCodeEnum.OK.getCode(), "ok");
    }


    public static <T> ResultDTO<T> error(String message) {
        return restResult(null, ResponseCodeEnum.ERROR.getCode(), message);
    }

    public static <T> ResultDTO<T> error(Integer status, String message) {
        return restResult(null, status, message);
    }

    public static <T> ResultDTO<T> error(ResponseCodeEnum responseCode, Throwable e) {
        return restResult(null, responseCode.getCode(), e.getMessage() != null ? e.getMessage() : "系统异常，请联系管理员！");
    }

    private static <T> ResultDTO<T> restResult(T data, int code, String msg) {
        ResultDTO<T> r = new ResultDTO<>();
        r.setStatus(code);
        r.setMessage(msg);
        r.setData(data);
        return r;
    }

}