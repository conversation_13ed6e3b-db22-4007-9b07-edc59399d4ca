package com.purchase.purchase_server.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.dataObject.TrialInventorySaleDestinationDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 目标日销mapper层
 * <AUTHOR>
 * @Date 2024/1/5 17:15
 **/
public interface TrialInventorySaleDestinationMapper extends MPJBaseMapper<TrialInventorySaleDestinationDO> {

    int deleteByShippingProjectIds(@Param("shippingProjectIds") List<String> shippingProjectIds);

}
