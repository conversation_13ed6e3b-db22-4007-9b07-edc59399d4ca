package com.purchase.purchase_server.entity.dataObject;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 */
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName(value = "cm_replenishment_trial_purchase_inventory")
public class ReplenishmentTrialPurchaseInventoryDO extends BaseEntity {
    /**
     * 理论补货量
     */
    private Integer advicePurchaseNum;
    /**
     * 目标仓
     */
    private String destinationWarehouse;

    /**
     * 建议采购日
     */
    private Date advicePurchaseDate;

    /**
     * 补货量区间
     */
    private String replenishmentRange;

    /**
     * 预计交货时间
     */
    private Date expectedFactoryFinishedDate;

    /**
     * 预计装柜时间
     */
    private Date expectedShippingStartDate;

    /**
     * 预计到仓时间
     */
    private Date expectedArrivingDate;

    /**
     * 箱数
     */
    private Integer packageNum;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 运营确认状态
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String operationConfirmedStatus;

    /**
     * 按虚拟sku展示采购计划的id
     */
    private String replenishmentVirtualPurchaseId;

    /**
     * 采纳状态(0-未采纳 1-已采纳)
     * @see com.purchase.purchase_server.enums.TrialPurchaseStatusEnum
     */
    private String trialStatus;

    /**
     * 是否修改过到货日期
     */
    private String isChangedArrivingDate;

    /**
     * 补货量类型 0-理论补货量，1-建议补货量, 2-运营确认量
     * @see com.purchase.purchase_server.enums.PurchaseNumTypeEnum
     */
    private String purchaseNumType;
}