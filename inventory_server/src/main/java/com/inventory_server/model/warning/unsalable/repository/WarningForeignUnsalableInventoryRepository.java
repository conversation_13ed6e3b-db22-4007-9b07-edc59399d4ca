package com.inventory_server.model.warning.unsalable.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.inventory_server.model.redundancy.entity.dos.InventoryForeignStoreDO;
import com.inventory_server.model.warning.unsalable.entity.dos.WarningForeignUnsalableInventoryDO;
import com.inventory_server.model.warning.unsalable.entity.dto.ForeignUnsalableDto;
import com.inventory_server.model.warning.unsalable.mapper.WarningForeignUnsalableInventoryMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description 工厂无计划库存持久层
 * <AUTHOR>
 * @Date 2025/4/17 11:24
 **/
@Service
public class WarningForeignUnsalableInventoryRepository extends ServiceImpl<WarningForeignUnsalableInventoryMapper, WarningForeignUnsalableInventoryDO> {

    public void deleteFactoryUnsalableByUnsalableId(String unsalableId) {
        baseMapper.delete(Wrappers.<WarningForeignUnsalableInventoryDO>lambdaQuery().eq(WarningForeignUnsalableInventoryDO::getUnsalableInventoryId, unsalableId));
    }

    public List<ForeignUnsalableDto> getByUnsalableId(String unsalableId) {
        return getByUnsalableIdList(Collections.singletonList(unsalableId));
    }

    public List<ForeignUnsalableDto> getByUnsalableIdList(List<String> unsalableIdList) {
        if (CollectionUtil.isEmpty(unsalableIdList)) {
            return new ArrayList<>();
        }
        return baseMapper.selectJoinList(ForeignUnsalableDto.class, new MPJLambdaWrapper<WarningForeignUnsalableInventoryDO>()
                .selectAs(WarningForeignUnsalableInventoryDO::getUnsalableInventoryId, ForeignUnsalableDto::getUnsalableId)
                .select(WarningForeignUnsalableInventoryDO::getUnsalableNum)
                .select(InventoryForeignStoreDO::getStartShippingDate)
                .select(InventoryForeignStoreDO::getEnableUsingDate)
                .select(InventoryForeignStoreDO::getWarehouse)
                .select(InventoryForeignStoreDO::getShipmentCode)
                .select(InventoryForeignStoreDO::getVirtualSku)

                .leftJoin(InventoryForeignStoreDO.class, InventoryForeignStoreDO::getId, WarningForeignUnsalableInventoryDO::getForeignStoreId)
                .in(WarningForeignUnsalableInventoryDO::getUnsalableInventoryId, unsalableIdList)
        );
    }
}
