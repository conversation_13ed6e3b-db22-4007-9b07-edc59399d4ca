package com.purchase.purchase_server.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.dataObject.ProductSnapshotDO;
import com.purchase.purchase_server.entity.form.QuerySnapDp;
import com.purchase.purchase_server.mapper.ProductSnapMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 快照产品repository层
 * <AUTHOR>
 * @Date 2024/1/11 20:13
 **/
@Service
public class ProductSnapRepositoryImpl extends ServiceImpl<ProductSnapMapper, ProductSnapshotDO> {
    public List<String> selectSnapIds(QuerySnapDp query){
        return baseMapper.selectJoinList(String.class, new MPJLambdaWrapper<ProductSnapshotDO>()
                .distinct()
                .select(ProductSnapshotDO::getId)
                .in(CollectionUtil.isNotEmpty(query.selfSkuList()), ProductSnapshotDO::getSelfSku, query.selfSkuList())
                .in(CollectionUtil.isNotEmpty(query.virtualSkuList()), ProductSnapshotDO::getVirtualSku, query.virtualSkuList())
                .apply(StrUtil.isNotBlank(query.productName()), "t.self_data->>'$.productName' like {0}", StrUtil.isBlank(query.productName()) ? query.productName() : "%" + query.productName().strip() + "%")
                .in(CollectionUtil.isNotEmpty(query.oldSkuList()), "t.virtual_data->>'$.oldSku'", query.oldSkuList())
                .in(CollectionUtil.isNotEmpty(query.productStatusList()), "t.virtual_data->>'$.productStatus'", query.productStatusList())
                .in(CollectionUtil.isNotEmpty(query.spuIdList()), "t.spu_data->>'$.spu'", query.spuIdList())
                .apply(StrUtil.isNotBlank(query.spuProductName()), "t.spu_data->>'$.spuProductName' like {0}", StrUtil.isBlank(query.spuProductName()) ? query.spuProductName() : "%" + query.spuProductName().strip() + "%")
                .apply(StrUtil.isNotBlank(query.factoryCode()), "t.factory_data->>'$.factoryCode' like {0}", StrUtil.isBlank(query.factoryCode()) ? query.factoryCode() : "%" + query.factoryCode().strip() + "%")
        );
    }

    public List<String> selectSnapIds(QuerySnapDp query, List<String> snapshotIdList){
        return baseMapper.selectJoinList(String.class, new MPJLambdaWrapper<ProductSnapshotDO>()
                .distinct()
                .select(ProductSnapshotDO::getId)
                .in(CollectionUtil.isNotEmpty(query.selfSkuList()), ProductSnapshotDO::getSelfSku, query.selfSkuList())
                .in(CollectionUtil.isNotEmpty(query.virtualSkuList()), ProductSnapshotDO::getVirtualSku, query.virtualSkuList())
                .apply(StrUtil.isNotBlank(query.productName()), "t.self_data->>'$.productName' like {0}", StrUtil.isBlank(query.productName()) ? query.productName() : "%" + query.productName().strip() + "%")
                .apply(StrUtil.isNotBlank(query.factoryCode()), "t.factory_data->>'$.factoryCode' like {0}", StrUtil.isBlank(query.factoryCode()) ? query.factoryCode() : "%" + query.factoryCode().strip() + "%")
                .in(CollectionUtil.isNotEmpty(query.oldSkuList()), "t.virtual_data->>'$.oldSku'", query.oldSkuList())
                .in(CollectionUtil.isNotEmpty(query.productStatusList()), "t.virtual_data->>'$.productStatus'", query.productStatusList())
                .in(CollectionUtil.isNotEmpty(query.spuIdList()), "t.spu_data->>'$.spu'", query.spuIdList())
                .apply(StrUtil.isNotBlank(query.spuProductName()), "t.spu_data->>'$.spuProductName' like {0}", StrUtil.isBlank(query.spuProductName()) ? query.spuProductName() : "%" + query.spuProductName().strip() + "%")
                .in(CollectionUtil.isNotEmpty(snapshotIdList), ProductSnapshotDO::getId, snapshotIdList)
        );
    }

    public List<ProductSnapshotDO> selectListByIdList(List<String> snapIdList) {
        return baseMapper.selectBatchIds(snapIdList);
    }
}
