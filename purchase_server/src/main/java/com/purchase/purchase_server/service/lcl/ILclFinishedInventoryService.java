package com.purchase.purchase_server.service.lcl;

import com.purchase.purchase_server.entity.dto.Lcl.LclConsolidationNonFinishedInventoryDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclEditRemainNumDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclTrialShippingInventoryDTO;
import com.purchase.purchase_server.entity.form.LclConsolidationForm;
import com.purchase.purchase_server.entity.form.LclFinishedDeleteForm;
import com.purchase.purchase_server.entity.form.LclShippingNumEditListNewForm;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cm_lcl_finished_inventory(拼柜装柜计划数据表)】的数据库操作Service
* @createDate 2024-12-04 16:47:07
*/
public interface ILclFinishedInventoryService {

    /**
     * 编辑发货数量
     */
    boolean updateLclShippingNum(LclShippingNumEditListNewForm form);

    /**
     * 删除装柜数据
     */
    boolean deleteLclFinished(LclFinishedDeleteForm form);

    /**
     * 获取未安排数量详情
     */
    List<LclTrialShippingInventoryDTO> detailRemainNum(LclConsolidationForm form);

    /**
     * 编辑-获取未安排数量详情
     */
    LclEditRemainNumDTO detailRemainNumForEditList(LclConsolidationForm form);

    List<LclConsolidationNonFinishedInventoryDTO> listByRecordId(String recordId);

}
