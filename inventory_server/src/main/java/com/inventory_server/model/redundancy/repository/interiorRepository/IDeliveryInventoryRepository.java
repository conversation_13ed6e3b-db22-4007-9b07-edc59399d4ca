package com.inventory_server.model.redundancy.repository.interiorRepository;

import com.inventory_server.model.redundancy.entity.form.NormalDeliveryInventoryForm;
import com.inventory_server.model.redundancy.entity.vo.TrialWatchBoardVo;

import java.util.List;

public interface IDeliveryInventoryRepository {

    void saveNormalDeliveryInventory(NormalDeliveryInventoryForm form);

    TrialWatchBoardVo getNormalDeliveryInventory(List<String> factoryFinishedList);
}
