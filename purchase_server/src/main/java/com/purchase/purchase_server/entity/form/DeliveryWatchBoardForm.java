package com.purchase.purchase_server.entity.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;

/**
 * @Description 发货试算看板
 * <AUTHOR>
 * @Date 2025/1/6 14:54
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryWatchBoardForm implements Serializable {
    @Serial
    private static final long serialVersionUID = 1546544534405L;

    private String shippingProjectId;

    @JsonFormat(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, timezone = "GMT+8")
    private LocalDate shippingStartDate;

    @JsonFormat(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, timezone = "GMT+8")
    private LocalDate shippingEndDate;
}
