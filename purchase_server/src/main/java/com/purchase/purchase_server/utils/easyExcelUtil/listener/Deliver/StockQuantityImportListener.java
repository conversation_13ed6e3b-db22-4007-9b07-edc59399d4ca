package com.purchase.purchase_server.utils.easyExcelUtil.listener.Deliver;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.excelObject.StockQuantityInfoExcel;
import com.purchase.purchase_server.entity.vo.UpgradeInfoInteriorVo;
import com.purchase.purchase_server.enums.IsOldStatusEnum;
import com.purchase.purchase_server.enums.StockQuantityImportHeadEnum;
import com.purchase.purchase_server.model.purchase.StockQuantityInfoDp;
import com.purchase.purchase_server.service.IProductSnapshotService;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.DeliveryManager;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.DeliveryMapManager;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;

/**
 * @Description 导入发货计划-在途
 * <AUTHOR>
 * @Date 2023/12/28 15:49
 **/
@Slf4j
public class StockQuantityImportListener extends AbstractPurchaseImportListener<StockQuantityInfoExcel> {

    private final Map<String, String> nameIdMap;

    protected final IProductSnapshotService productSnapshotService = ApplicationContextProvider.getBean(IProductSnapshotService.class);

    public StockQuantityImportListener(List<SenboWarehouseDto> senboWarehouseList) {
        super(senboWarehouseList);
        this.nameIdMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));
    }

    @Override
    public void invoke(StockQuantityInfoExcel product, AnalysisContext analysisContext) {
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }

        if (StrUtil.isBlank(product.getShipmentCode())) {
            throw new IllegalArgumentException("货件号不能为空");
        }

        if (product.getShipmentCode().length() > 30) {
            throw new IllegalArgumentException("货件号长度超过30");
        }

        DeliveryMapManager deliveryMapManager = DeliveryMapManager.getInstance();

        new StockQuantityInfoDp(product, nameIdMap);

        String virtualSku = product.getVirtualSku();
        VirtualProductDO virtualProductDO = Optional.ofNullable(deliveryMapManager.getSkuVirtualProductMap().get(virtualSku))
                .orElseGet(() -> Optional.ofNullable(deliveryMapManager.getOldSkuVirtualProductMap().get(virtualSku))
                        .orElseThrow(() -> new IllegalArgumentException("虚拟sku不存在")));
        // 获取或者保存快照
        String productSnapshotId = productSnapshotService.selecetAndSaveSnapList(virtualProductDO.getId(), "徐波");
        boolean isOldSku = virtualSku.equals(virtualProductDO.getOldSku());
        FactoryRemainInventoryDto factoryRemainInventoryDto = FactoryRemainInventoryDto.builder()
                .virtualSku(virtualSku)
                .shipmentCode(product.getShipmentCode())
                .warehouse(nameIdMap.get(product.getStoreHouse()))
                .enableUsingDate(DateUtil.parse(product.getInventoryTime(), YYYY_MM_DD_DATE_FORMAT_SLASH))
                .startShippingTime(DateUtil.parse(product.getStartShippingTime(), YYYY_MM_DD_DATE_FORMAT_SLASH))
                .storeNum(Double.valueOf(product.getStockQuantity()))
                .remarks(product.getRemarks())
                .productSnapshotId(productSnapshotId)
                .virtualSkuId(virtualProductDO.getId())
                .isOldStatus(isOldSku ? IsOldStatusEnum.OLD_SKU.getCode() : IsOldStatusEnum.VIRTUAL_SKU.getCode())
                .build();

        String resultSku = virtualProductDO.getVirtualSku();
        if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())){
            UpgradeInfoInteriorVo upgradeInfoInteriorVo = selectUpgradeInfo(virtualProductDO.getUpgradeId());

            if (ObjectUtil.isNotEmpty(upgradeInfoInteriorVo)){
                if (upgradeInfoInteriorVo.getOriginalSkuId().equals(virtualProductDO.getId())){
                    resultSku = upgradeInfoInteriorVo.getUpgradeSku();
                }
            }
        }

        String finalResultSku = resultSku;
        Optional.ofNullable(deliveryMapManager.getStockQuantityMap().get(finalResultSku))
                .ifPresentOrElse(
                        list -> list.stream()
                                .filter(item -> item.getWarehouse().equals(factoryRemainInventoryDto.getWarehouse())
                                        && item.getEnableUsingDate().equals(factoryRemainInventoryDto.getEnableUsingDate())
                                        && item.getShipmentCode().equals(factoryRemainInventoryDto.getShipmentCode())
                                        && item.getVirtualSku().equals(factoryRemainInventoryDto.getVirtualSku())
                                        && item.getStartShippingTime().equals(factoryRemainInventoryDto.getStartShippingTime())
                                        && Objects.equals(item.getRemarks(), factoryRemainInventoryDto.getRemarks())
                                )
                                .findFirst()
                                .ifPresentOrElse(
                                        item -> item.setStoreNum(item.getStoreNum() + factoryRemainInventoryDto.getStoreNum()),
                                        () -> list.add(factoryRemainInventoryDto)
                                ),
                        () -> {
                            ArrayList<FactoryRemainInventoryDto> newList = new ArrayList<>();
                            newList.add(factoryRemainInventoryDto);
                            deliveryMapManager.putStockQuantityMap(finalResultSku, newList);
                        }
                );
        DeliveryManager manager = DeliveryManager.getInstance();
        manager.incrementSuccessfulTimes();
    }

    /**
     * 这里会一行行的返回头
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        List<String> headList = StockQuantityImportHeadEnum.getCodeList();
        //删除headMap中的空值
        headMap.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty());
        List<String> headMapList = new ArrayList<>(headMap.values());
        //比较两个list集合值是否相等
        if (!headList.equals(headMapList)) {
            throw new RuntimeException("在途表头错误，请检查表头是否正确");
        }
    }
}
