package com.product.product_server.model.products;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * @Description 单品规格
 * <AUTHOR>
 * @Date 2023/12/6 11:30
 **/
public record SpecificationDp(Double singleLength, Double singleWidth, Double singleHeight, Double volume) {

    public SpecificationDp(Double singleLength, Double singleWidth, Double singleHeight) {
        this(singleLength, singleWidth, singleHeight, null);
    }

    public SpecificationDp(Double singleLength, Double singleWidth, Double singleHeight, Double volume) {
        this.singleLength = validNum(singleLength);
        this.singleWidth = validNum(singleWidth);
        this.singleHeight = validNum(singleHeight);
        this.volume = BigDecimal.valueOf(this.singleHeight * this.singleLength * this.singleWidth).setScale(1, RoundingMode.UP).doubleValue();
    }

    private Double validNum(Double num) {
        return BigDecimal.valueOf(Optional.ofNullable(num).orElse(0.0)).setScale(1, RoundingMode.UP).doubleValue();
    }

    public List<Double> sort() {
        return Stream.of(this.singleHeight, this.singleLength, this.singleWidth)
                .sorted(Comparator.comparing((Double a) -> a).reversed())
                .toList();
    }
}
