package com.product.product_server.repository.interiorRepository.impl;

import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.product.product_server.entity.dto.SenboWarehouseDto;
import com.product.product_server.entity.response.ResultDTO;
import com.product.product_server.entity.vo.SenboWarehouseVo;
import com.product.product_server.repository.interiorRepository.WarehouseRepository;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.Constants.AUTHORIZATION;
import static com.crafts_mirror.utils.constant.SystemConstant.SEN_BO_WAREHOUSE_WITHOUT_MT_INFO_GET_URL;

/**
 * @Description 获取森帛仓库信息
 * <AUTHOR>
 * @Date 2024/7/16 17:56
 **/
@Service
public class WarehouseRepositoryImpl implements WarehouseRepository {

    @Resource
    protected RestTemplate restTemplate;

    @Override
    public List<SenboWarehouseDto> getSenboWarehouseWithoutMTList() {
        String authorization = null;
        if(RequestContextHolder.getRequestAttributes() != null) {
            HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            authorization = Optional.of(request.getHeader(AUTHORIZATION)).orElseThrow(() -> new IllegalArgumentException("请先登录"));
        }
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, authorization);
        ResultDTO warehouseVo = restTemplateUtils.get(SEN_BO_WAREHOUSE_WITHOUT_MT_INFO_GET_URL, ResultDTO.class);
        List<SenboWarehouseDto> senboWarehouseList = JSON.to(SenboWarehouseVo.class, warehouseVo.getData()).getSenboWarehouseList();
        return senboWarehouseList.stream().filter(f -> !"8".equals(f.getSenboWarehouseId())).toList();
    }
}
