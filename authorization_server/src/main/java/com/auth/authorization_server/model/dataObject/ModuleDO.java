package com.auth.authorization_server.model.dataObject;

import com.auth.authorization_server.model.PhysicalBaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统权限模块表 cm_sys_module
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cm_sys_module")
public class ModuleDO implements Serializable {
    /**
     * 功能id
     */
    private String id;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 功能名称
     */
    private String functionName;

    /**
     * 备注
     */
    private String remark;
}
