package com.product.product_server.model.products;

import cn.hutool.core.util.StrUtil;

import java.util.Objects;

public record RemarksSaveDp(String id, String remarks) {

    public RemarksSaveDp(String id, String remarks) {
        this.id = validId(id);
        this.remarks = validAndTrimRemarks(remarks);
    }

    private String validAndTrimRemarks(String remarks) {
        if(StrUtil.isNotBlank(remarks) && remarks.length() > 100) {
            throw new RuntimeException("备注长度超出限制，请将其长度限制在100字符以内");
        }
        return StrUtil.isNotBlank(remarks) ? remarks.strip() : remarks;
    }

    private String validId(String selfProductId) {
        Objects.requireNonNull(selfProductId, "主键id不能为空");
        return selfProductId;
    }
}
