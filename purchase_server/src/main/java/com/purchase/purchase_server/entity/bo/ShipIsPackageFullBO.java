package com.purchase.purchase_server.entity.bo;

import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * @Description 仓库装货是否满箱
 * <AUTHOR>
 * @Date 2024/1/16 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ShipIsPackageFullBO extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 工厂交货表格主键
     */
    private String factory_finished_id;
    /**
     * 发货数量
     */
    private Integer shippingNum;
    /**
     * 箱子是否装满
     */
    private String isPackageFull;
}
