package com.inventory_server.model.redundancy.listener.template;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/8
 **/
public class WarehouseFilter {
    // 亚马逊库存概况
    public static List<String> filterForSheet1(List<String> warehouseNames) {
        return warehouseNames.stream()
                .filter(name -> !name.contains("FBA(AM)") && !name.contains("FBA(AC)") &&
                        !name.contains("FBA(AMEU)") && !name.contains("FBA(AK)") && !name.contains("中转仓"))
                .collect(Collectors.toList());
    }

    // 灯具库存概况
    public static List<String> filterForSheet4(List<String> warehouseNames) {
        return warehouseNames.stream()
                .filter(name -> !name.contains("FBA(AM)") && !name.contains("FBA(AC)") &&
                        !name.contains("FBA(AMEU)") && !name.contains("FBA(AK)") && !name.contains("中转仓"))
                .collect(Collectors.toList());
    }
}
