package com.purchase.purchase_server.config;

/**
 * <AUTHOR>
 * @date 2024/4/11
 **/

import com.crafts_mirror.utils.utils.SnowflakeIdWorker;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 雪花算法配置
 *
 * <AUTHOR>
 * @date 2024/4/11 16:44
 */
@Configuration
public class DistributedIdConfig {

    @Value("${snowflakeDatacenterId}")
    private long datacenterId;

    /**
     * 启动创建
     *
     * @param
     * <AUTHOR>
     * @date 2024/4/11 16:44
     */
    @Bean
    public SnowflakeIdWorker snowflakeIdWorker() {
        return new SnowflakeIdWorker(datacenterId);
    }
}
