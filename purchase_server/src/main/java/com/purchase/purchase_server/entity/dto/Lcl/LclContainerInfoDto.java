package com.purchase.purchase_server.entity.dto.Lcl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 货件详情vo类
 * <AUTHOR>
 * @Date 2025/5/27 13:59
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LclContainerInfoDto implements Serializable {

    private String containerInfoId;


    private String shipmentCode;
}
