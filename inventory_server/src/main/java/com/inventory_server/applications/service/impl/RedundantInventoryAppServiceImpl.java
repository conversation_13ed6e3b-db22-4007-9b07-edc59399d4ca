package com.inventory_server.applications.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.DateUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.inventory_server.applications.cqe.InventoryIdQuery;
import com.inventory_server.applications.cqe.InventoryIdSoldOutQuery;
import com.inventory_server.applications.cqe.InventoryInfoQuery;
import com.inventory_server.applications.cqe.LeadTimeCommand;
import com.inventory_server.applications.dto.*;
import com.inventory_server.applications.service.IRedundantInventoryAppService;
import com.inventory_server.applications.vo.response.ResultDTO;
import com.inventory_server.infrastructures.assembler.RedundantInventoryAssembler;
import com.inventory_server.infrastructures.entity.LogTrackNumDto;
import com.inventory_server.infrastructures.entity.form.FileMissionForm;
import com.inventory_server.infrastructures.exception.BusinessException;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.product.entity.vo.OperatorSearchVo;
import com.inventory_server.model.product.repository.interiorRepository.IProductCategoryRepository;
import com.inventory_server.model.redundancy.entity.dos.InventorySaleRulesDO;
import com.inventory_server.model.redundancy.entity.dos.InventorySoldOutDaysDO;
import com.inventory_server.model.redundancy.entity.dp.InventoryCalDp;
import com.inventory_server.model.redundancy.entity.dp.TargetSalesDp;
import com.inventory_server.model.redundancy.entity.dto.*;
import com.inventory_server.model.redundancy.entity.form.UrgentHeadShipDateForm;
import com.inventory_server.model.redundancy.entity.vo.DeliveryCalculationVo;
import com.inventory_server.model.redundancy.entity.vo.NormalDeliveryWatchBoardVo;
import com.inventory_server.model.redundancy.repository.redisRepository.IRedundantDateNumRepository;
import com.inventory_server.model.redundancy.service.IInventoryFactoryRedundantInfoService;
import com.inventory_server.model.redundancy.service.IInventoryForeignAndShippingRedundantInfoService;
import com.inventory_server.model.redundancy.service.IRedundantInventoryCalService;
import com.inventory_server.model.redundancy.service.IRedundantInventoryService;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.service.IWarehouseService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.*;
import static com.crafts_mirror.utils.constant.SystemConstant.FILE_SYSTEM_MISSION_CENTER_URL;
import static com.crafts_mirror.utils.utils.DateUtils.YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT;
import static java.math.RoundingMode.HALF_UP;

/**
 * @Description 应用服务，负责领域的组合、编排、转发、转换和传递
 * <AUTHOR>
 * @Date 2024/5/7 17:15
 **/
@Service
@Slf4j
public class RedundantInventoryAppServiceImpl implements IRedundantInventoryAppService {

    @Resource
    private IRedundantInventoryCalService redundantInventoryCalService;

    @Resource
    private IRedundantInventoryService redundantInventoryService;

    @Resource
    private IInventoryForeignAndShippingRedundantInfoService inventoryForeignRedundantInfoService;

    @Resource
    private IInventoryFactoryRedundantInfoService inventoryFactoryRedundantInfoService;

    @Resource
    private RedundantInventoryAssembler redundantInventoryAssembler;

    @Resource
    protected RestTemplate restTemplate;

    @Resource
    private IProductCategoryRepository productCategoryRepository;

    @Resource
    private IWarehouseService warehouseService;

    @Resource
    private IRedundantDateNumRepository redundantDateNumRepository;

    @Override
    public void calRedundantInventoryApp(InputStream file, byte[] fileBytes, String fileName) {
        // 解析excel
        ImportResultDto importResult = redundantInventoryService.importRedundantInventoryExcel(file, fileBytes, fileName);
        String importFileId = importResult.getImportFileId();
        List<InventoryCalDp> inventoryCalDps = importResult.getInventoryCalDpList();
        DateTime now = DateTime.now();

        int redundantDate = redundantDateNumRepository.getRedundantDateNum();
        int i = 0;
        // 达成率
        Map<String, AchievementRateDto> achievementRate = calAchievementRateMap(inventoryCalDps);
        List<List<InventoryCalDp>> partitionList = ListUtil.partition(inventoryCalDps, 100);
        for (List<InventoryCalDp> calDps : partitionList) {
            try {
                // 获取整个导入数据的发货计划（调用采购服务计算发货计划）
                var shippingResult = redundantInventoryService.getShippingCalculationResultWithRequirement(calDps,
                        true, true, redundantDate, now);

                List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
                Map<String, Integer> headShippingDateMap = senboWarehouseList.stream()
                        .collect(Collectors.toMap(s -> s.getSenboWarehouseId().toString(), SenboWarehouseDto::getHeadShippingDate));

                // 计算未发货的模拟日销表格
                var resultWithoutFactoryPlan = redundantInventoryService.getShippingCalculationResultWithRequirement(calDps,
                        false, true, redundantDate, now);

                RedundancyResultDto redundancyResultDto = new RedundancyResultDto();
                // 计算海外仓以及在途冗余
                redundantInventoryCalService.calForeignRedundantInventory(calDps, redundancyResultDto, resultWithoutFactoryPlan,
                        headShippingDateMap, redundantDate);

                // 计算工厂冗余库存
                redundantInventoryCalService.calFactoryRedundantInventory(calDps, shippingResult, redundancyResultDto, headShippingDateMap,
                        redundantDate);

                // 计算海外仓理论售罄时间
                Map<String, LocalDate> soldOutDateMap = redundantInventoryCalService.calTheoreticalSoldOutDate(resultWithoutFactoryPlan, calDps);

                // 计算售罄前断货天数
                List<DeliveryCalResultDto> deliveryResultList = resultWithoutFactoryPlan.getDeliveryCalResultList();
                var virtualSoldOutMap = redundantInventoryCalService.calAllVirtualSkuRealSoldOutDate(deliveryResultList, calDps, soldOutDateMap);
                Map<String, SalableDateDto> salableDateMap = redundantInventoryCalService.calAllVirtualSkuRealSoldOutDate(deliveryResultList);

                // 计算正常发货的模拟日销表格
                DeliveryCalculationVo normalDeliveryResult = redundantInventoryService.getNormalShippingCalculationResult(calDps, redundantDate);

                // 计算海外仓全链路理论售罄时间
                Map<String, LocalDate> fullLinkSoldOutDateMap = redundantInventoryCalService.calTheoreticalSoldOutDate(normalDeliveryResult, calDps);

                // 计算全链路售罄前断货天数
                List<DeliveryCalResultDto> fullLinkDeliveryResultList = normalDeliveryResult.getDeliveryCalResultList();
                var fullLinkVirtualSoldOutMap = redundantInventoryCalService.calAllVirtualSkuRealSoldOutDate(fullLinkDeliveryResultList, calDps, fullLinkSoldOutDateMap);
                Map<String, SalableDateDto> fullLinkSalableDateMap = redundantInventoryCalService.calAllVirtualSkuRealSoldOutDate(fullLinkDeliveryResultList);

                // 保存冗余库存
                redundantInventoryService.saveRedundancyInfo(importFileId, calDps, shippingResult, redundancyResultDto,
                        soldOutDateMap, virtualSoldOutMap, fullLinkSoldOutDateMap, fullLinkVirtualSoldOutMap, normalDeliveryResult,
                        headShippingDateMap, redundantDate, salableDateMap, fullLinkSalableDateMap, achievementRate);
            } catch (Exception e) {
                RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
                restTemplateUtil.post(FileMissionForm.builder()
                        .importStatus("失败")
                        .failedResultList(Collections.singletonList(String.format("未知异常：异常原因：%s", e.getMessage())))
                        .missionId(importFileId)
                        .finishDate(new Date())
                        .build(), ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL);
                log.warn("冗余库存计算异常，异常原因：{}", e.getMessage());
                throw new RuntimeException(e);
            } finally {
                log.warn("冗余库存一共{}组，已跑完{}组", partitionList.size(), ++i);
            }
        }

        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        restTemplateUtil.post(FileMissionForm.builder()
                .importStatus("试算成功")
                .missionId(importFileId)
                .type("冗余库存计划导入")
                .finishDate(new Date())
                .build(), ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL);
    }

    @Override
    public void exportTemplate(HttpServletResponse response) {
        redundantInventoryService.exportTemplate(response);
    }

    @Override
    public IPage<InventoryInfoDto> pageList(InventoryInfoQuery form) {
        return redundantInventoryService.pageList(form);
    }

    @Override
    public void export(InventoryInfoQuery form, HttpServletResponse response) {
        redundantInventoryService.export(form, response);
    }

    @Override
    public DetailInventoryRulesDto getDetailInventoryRules(String inventoryInfoId) {
        return redundantInventoryService.getDetailInventoryRules(inventoryInfoId);
    }

    @Override
    public DetailInventoryRulesDto getShippingCalculation(String inventoryInfoId) {
        return redundantInventoryService.getShippingCalculation(inventoryInfoId);
    }

    @Override
    public NormalDeliveryWatchBoardVo getNormalDeliveryWatchBoard(String inventoryInfoId) {
        return redundantInventoryService.getNormalDeliveryWatchBoard(inventoryInfoId);
    }

    @Override
    public DetailInventoryRulesDto getShippingCalculationWithRequirement(String inventoryInfoId) {
        return redundantInventoryService.getShippingCalculationWithoutFactoryPlan(inventoryInfoId);
    }

    @Override
    public NormalDeliveryWatchBoardVo getNormalShippingCalculation(String inventoryInfoId) {
        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
        return redundantInventoryService.getNormalShippingCalculation(inventoryInfoId, senboWarehouseList);
    }

    @Override
    public List<InventorySoldOutDaysDO> getBeforeSoldOutList(InventoryIdQuery infoId) {
        List<String> list = CollectionUtil.toList(infoId.getInventoryInfoId());
        return redundantInventoryService.getBeforeSoldOutList(list, infoId.getType()).get(infoId.getInventoryInfoId());
    }

    @Override
    public List<InventoryForeignRedundantInfoDto> getForeignRedundantInfo(InventoryIdQuery infoId) {
        var foreignRedundantList = inventoryForeignRedundantInfoService.getForeignRedundantList(CollectionUtil.toList(infoId.getInventoryInfoId()));
        return redundantInventoryAssembler.foreignRedundantBOToDto(foreignRedundantList);
    }

    @Override
    public List<InventoryForeignRedundantInfoDto> getForeignRedundantInfoByWatchBoardId(String watchBoardId) {
        var foreignRedundantList = inventoryForeignRedundantInfoService.getForeignRedundantListByWatchBoardId(watchBoardId);
        foreignRedundantList = foreignRedundantList.stream().filter(f -> f.getRedundantNum() > 0).collect(Collectors.toList());
        return redundantInventoryAssembler.foreignRedundantBOToDto(foreignRedundantList);

    }

    public List<InventoryShipRedundantInfoDto> getOnShippingRedundantInfo(InventoryIdQuery infoId) {
        List<String> list = CollectionUtil.toList(infoId.getInventoryInfoId());
        return inventoryForeignRedundantInfoService.getShippingRedundantList(list);
    }

    @Override
    public List<InventoryShipRedundantInfoDto> getOnShippingRedundantInfoByWatchBoardId(String watchBoardId) {
        return inventoryForeignRedundantInfoService.getOnShippingRedundantInfoByWatchBoardId(watchBoardId);
    }

    @Override
    public List<InventoryFactoryRedundantInfoDto> getFactoryRedundantInfo(InventoryIdQuery infoId) {
        return inventoryFactoryRedundantInfoService.getFactoryRedundantInfoList(CollectionUtil.toList(infoId.getInventoryInfoId()));
    }

    @Override
    public LeadTimeDto getLeadTime(InventoryIdQuery inventoryInfoId) {
        return redundantInventoryService.getLeadTime(inventoryInfoId.getInventoryInfoId());
    }

    @Override
    public Boolean updateUrgentProduceDays(LeadTimeCommand leadTimeCommand) {
        return redundantInventoryService.updateUrgentProduceDays(leadTimeCommand);
    }

    @Override
    public Boolean updateUrgentHeadShipDate(UrgentHeadShipDateForm urgentForm) {
        return redundantInventoryService.updateUrgentHeadShipDate(urgentForm);
    }

    @Override
    public String selectFileInfo(String filePath) {
        return redundantInventoryService.selectFileInfo(filePath);
    }

    @Override
    public InventoryFactoryAndForeignInfoDto getFactoryRedundantInfo(InventoryInfoQuery form) {
        return redundantInventoryService.getFactoryRedundantInfo(form.getSelfSkuIdList());
    }

    @Override
    public InventoryFactoryAndForeignInfoDto getFactoryRedundantInfoInterior(InventoryInfoQuery form) {
        return redundantInventoryService.getFactoryRedundantInfoInterior(form.getSelfSkuIdList());
    }

    @Override
    public Set<String> getSnapIds(InventoryInfoQuery infoQuery) {
        return redundantInventoryService.getSnapIds(infoQuery);
    }

    @Override
    public boolean hasRedundancyByVirtualSkuId(List<String> virtualSkuIds) {
        return redundantInventoryService.hasRedundancyByVirtualSkuId(virtualSkuIds);
    }

    /**
     * 删除库存信息
     *
     * @param infoIds 需要删除的库存ID
     * @return 结果
     */
    @Override
    public boolean deleteFactoryByInfoIds(List<String> infoIds) {
        return redundantInventoryService.deleteFactoryByInfoId(infoIds);
    }

    @Override
    public List<OperatorSearchVo> getOperator() {
        return redundantInventoryService.getOperator();
    }

    @Override
    public List<ProductCategoryDTO> category() {
        return productCategoryRepository.getProductCategories();
    }

    @Override
    public Map<String, LocalDate> calTheoreticalSoldOutDate(InventoryIdSoldOutQuery form) {
        List<DeliveryCalResultDto> deliveryCalResultList = new ArrayList<>();
        DeliveryCalResultDto deliveryCalResultDto = JSON.parseObject(form.getTrialCalReplenishmentDto(), DeliveryCalResultDto.class);
        deliveryCalResultList.add(deliveryCalResultDto);

        List<InventoryCalDp> inventoryCalDps = new ArrayList<>();
        Map targetSalesMap = JSON.parseObject(form.getDestinationSalesMap(), Map.class);

        TargetSalesDp targetSalesDp = new TargetSalesDp(deliveryCalResultDto.getVirtualSku(), new TreeMap<>(targetSalesMap));

        InventoryCalDp inventoryCalDp = new InventoryCalDp(null, null, targetSalesDp, null, null, null, null);
        inventoryCalDps.add(inventoryCalDp);
        return redundantInventoryCalService.calTheoreticalSoldOutDate(DeliveryCalculationVo.builder()
                .deliveryCalResultList(deliveryCalResultList)
                .build(), inventoryCalDps);
    }

    @Override
    public Map<String, List<SoldOutDateBeforeTheoryDto>> calAllVirtualSkuRealSoldOutDate(InventoryIdSoldOutQuery form) {
        List<DeliveryCalResultDto> deliveryCalResultList = new ArrayList<>();
        DeliveryCalResultDto deliveryCalResultDto = JSON.parseObject(form.getTrialCalReplenishmentDto(), DeliveryCalResultDto.class);
        deliveryCalResultList.add(deliveryCalResultDto);

        List<InventoryCalDp> inventoryCalDps = new ArrayList<>();
        Map destinationSalesMap = JSON.parseObject(form.getDestinationSalesMap(), Map.class);

        TreeMap<String, BigDecimal> targetSalesMap = new TreeMap<>(destinationSalesMap);
        TargetSalesDp targetSalesDp = new TargetSalesDp(deliveryCalResultDto.getVirtualSku(), targetSalesMap);

        InventoryCalDp inventoryCalDp = new InventoryCalDp(null, null, targetSalesDp, null, null, null, null);
        inventoryCalDps.add(inventoryCalDp);
        Map<String, String> soldOutDateMap = JSON.parseObject(form.getSoldOutDateMap(), Map.class);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_HYPHEN);
        Map<String, LocalDate> convertedMap = soldOutDateMap.entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> LocalDate.parse(entry.getValue(), formatter)
                ));

        return redundantInventoryCalService.calAllVirtualSkuRealSoldOutDate(deliveryCalResultList, inventoryCalDps, convertedMap);
    }

    @Override
    public List<RedundancySaleDestinationDto> getSaleDestination(InventoryInfoQuery form) {
        InventorySaleRulesDO saleDestinationDO = redundantInventoryService.getSaleDestination(form.getInventoryInfoId());

        if (ObjectUtil.isEmpty(saleDestinationDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "冗余数据不存在");
        }

        String saleDestination = saleDestinationDO.getSaleDestination();
        Map<String, BigDecimal> saleDestinationMap = JSONObject.parseObject(saleDestination, new TypeReference<>() {
        });
        List<RedundancySaleDestinationDto> result = new ArrayList<>();

        String saleDestinationYm = form.getSaleDestinationYm();
        // 获取指定年月的数据
        Map<String, BigDecimal> filteredMap = saleDestinationMap.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(saleDestinationYm))
                .collect(Collectors.toMap(
                        Map.Entry::getKey, // 将 2024/01/01 转换为 2024-01-01
                        Map.Entry::getValue));

        // 解析年月，计算该月的天数
        YearMonth ym = YearMonth.parse(saleDestinationYm, DateTimeFormatter.ofPattern(YYYY_MM_DATE_FORMAT_SLASH));
        int daysInMonth = ym.lengthOfMonth();

        // 为该月的每一天创建记录
        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate date = ym.atDay(day);
            String dateStr = LocalDateTimeUtil.format(date, YYYY_MM_DD_DATE_FORMAT_SLASH);
            // 如果日期存在于原始数据中，使用原始值，否则设为0
            BigDecimal value = filteredMap.getOrDefault(dateStr, new BigDecimal(0));
            result.add(new RedundancySaleDestinationDto(dateStr, value.doubleValue()));
        }
        // 按日期排序
        result.sort(Comparator.comparing(RedundancySaleDestinationDto::getSaleDestinationDate));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(content = "修改目标日销", operationType = "补货计划操作")
    public void updateSaleDestination(InventoryInfoQuery form, LogTrackNumDto logDto) {
        var newSaleDestinationMap = form.getSaleDestinationMap();
        if (CollectionUtil.isNotEmpty(newSaleDestinationMap)) {
            InventorySaleRulesDO saleDestinationDO = redundantInventoryService.getSaleDestination(form.getInventoryInfoId());
            if (ObjectUtil.isEmpty(saleDestinationDO)) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "冗余数据不存在");
            }

            String salesDestination = saleDestinationDO.getSaleDestination();
            Map<String, BigDecimal> saleDestinationMap = JSONObject.parseObject(salesDestination, new TypeReference<>() {
            });

            List<String> saleDestinationYmList = new ArrayList<>(newSaleDestinationMap.keySet());
            TreeMap<String, BigDecimal> filteredMap = saleDestinationMap.entrySet().stream()
                    .filter(entry -> saleDestinationYmList.stream()
                            .noneMatch(prefix -> entry.getKey().startsWith(prefix)))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (oldValue, newValue) -> newValue,
                            TreeMap::new
                    ));

            List<RedundancySaleDestinationDto> saleDestinationList = newSaleDestinationMap.values().stream()
                    .flatMap(List::stream)
                    .toList();
            for (var saleDestination : saleDestinationList) {
                String saleDestinationDate = DateUtil.format(DateUtil.parse(saleDestination.getSaleDestinationDate()), YYYY_MM_DD_DATE_FORMAT_SLASH);
                Double saleDestinationNum = saleDestination.getSaleDestinationNum();
                BigDecimal num = BigDecimal.valueOf(saleDestinationNum).setScale(3, RoundingMode.HALF_UP);

                if (num.compareTo(new BigDecimal(9999999)) > 0) {
                    throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "日销最多填写7位数");
                }
                filteredMap.put(DateUtil.format(DateUtil.parse(saleDestinationDate), YYYY_MM_DD_DATE_FORMAT_SLASH), BigDecimal.valueOf(saleDestinationNum));
            }

            String logStr = formatSaleDestinationLog(saleDestinationYmList);
            Map<String, String> logMap = new HashMap<>();
            logMap.put(form.getInventoryInfoId(), logStr);
            logDto.setLogMap(logMap);
            logDto.setAuthorization(SecurityUtils.getToken());
            redundantInventoryService.updateSaleDestination(InventorySaleRulesDO.builder().saleDestination(JSON.toJSONString(filteredMap)).build());
        }
    }

    private String formatSaleDestinationLog(List<String> saleDestinationYmList) {
        if (saleDestinationYmList == null || saleDestinationYmList.isEmpty()) {
            return "修改目标日销"; // 或者根据需要返回空字符串或抛出异常
        }

        // 1. 解析与分组 (使用 TreeMap 保证年份自动排序)
        Map<Integer, List<Integer>> yearMonthMap = new TreeMap<>();
        for (String ym : saleDestinationYmList) {
            try {
                String[] parts = ym.split("/");
                if (parts.length == 2) {
                    int year = Integer.parseInt(parts[0]);
                    int month = Integer.parseInt(parts[1]);
                    yearMonthMap.computeIfAbsent(year, k -> new ArrayList<>()).add(month);
                } else {
                    // 处理格式不正确的字符串，可以选择忽略或记录错误
                    System.err.println("Skipping invalid format: " + ym);
                }
            } catch (NumberFormatException e) {
                // 处理无法解析为数字的情况
                System.err.println("Skipping invalid number format: " + ym);
            }
        }

        // 2. 对每个年份的月份进行排序
        yearMonthMap.values().forEach(Collections::sort);

        // 3. 构建字符串
        StringBuilder logMessage = new StringBuilder("修改");
        List<String> yearParts = new ArrayList<>();

        for (Map.Entry<Integer, List<Integer>> entry : yearMonthMap.entrySet()) {
            int year = entry.getKey();
            List<Integer> months = entry.getValue();

            // 将月份列表转换为逗号分隔的字符串
            String monthsStr = months.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining("，")); // 使用中文逗号

            yearParts.add(year + "年" + monthsStr + "月");
        }

        logMessage.append(String.join("，", yearParts)); // 使用中文逗号连接不同年份的部分
        logMessage.append("目标日销");

        return logMessage.toString();
    }

    /**
     * 目标达成率
     *
     * @return 各虚拟sku对应的目标达成率
     */
    private Map<String, AchievementRateDto> calAchievementRateMap(List<InventoryCalDp> inventoryCalDps) {
        Map<String, ImportActualDestinationSalesDto> actualDailySalesMap = inventoryCalDps.stream()
                .filter(dp -> dp.actualDestinationSalesDto() != null && dp.virtualProduct() != null)
                .collect(Collectors.toMap(
                        dp -> dp.virtualProduct().getVirtualSku(),
                        InventoryCalDp::actualDestinationSalesDto,
                        (existing, replacement) -> replacement
                ));
        Map<String, TargetSalesDp> targetSalesMap = inventoryCalDps.stream()
                .filter(dp -> dp.targetSales() != null && dp.virtualProduct() != null)
                .collect(Collectors.toMap(
                        dp -> dp.virtualProduct().getVirtualSku(),
                        InventoryCalDp::targetSales,
                        (existing, replacement) -> replacement
                ));
        Map<String, Map<String, List<VirtualProductDO>>> virtualBySpuIdMap = inventoryCalDps.stream()
                .map(InventoryCalDp::virtualProduct)
                .collect(Collectors.groupingBy(VirtualProductDO::getSpuId,
                        // 第二个分组依据：channel
                        Collectors.groupingBy(VirtualProductDO::getChannel)
                ));

        Map<String, AchievementRateDto> achievementRate = new HashMap<>();
        DateTime createTime = DateUtil.beginOfDay(DateTime.now());
        LocalDate calDate = DateUtils.convertToLocalDate(createTime);
        for (Map.Entry<String, Map<String, List<VirtualProductDO>>> virtualEntry : virtualBySpuIdMap.entrySet()) {
            Map<String, List<VirtualProductDO>> virtualProducts = virtualEntry.getValue();
            virtualProducts.forEach((channel, virtualProductList) -> {
                BigDecimal actualDailySalesSum = BigDecimal.ZERO;
                BigDecimal targetSalesSum = BigDecimal.ZERO;
                Map<String, BigDecimal> actualDailySalesMapSum = new HashMap<>();
                Map<String, BigDecimal> targetSalesMapSum = new HashMap<>();

                for (VirtualProductDO virtualProductDO : virtualProductList) {
                    String virtualSku = virtualProductDO.getVirtualSku();
                    ImportActualDestinationSalesDto actualDailySalesDto = actualDailySalesMap.get(virtualSku);
                    BigDecimal actualDailySalesNum = new BigDecimal(actualDailySalesDto.getActualDailySales());
                    BigDecimal targetSalesNum = calCurrentMonthTargetSales(targetSalesMap.get(virtualSku).targetSalesMap(), calDate);

                    actualDailySalesSum = actualDailySalesSum.add(actualDailySalesNum);
                    targetSalesSum = targetSalesSum.add(targetSalesNum);

                    actualDailySalesMapSum.put(virtualSku, actualDailySalesNum);
                    targetSalesMapSum.put(virtualSku, targetSalesNum);
                }
                BigDecimal totalResult = targetSalesSum.compareTo(BigDecimal.ZERO) > 0 ?
                        actualDailySalesSum.divide(targetSalesSum, 3, HALF_UP) :
                        BigDecimal.ZERO;
                BigDecimal max = new BigDecimal("1");
                if (totalResult.compareTo(max) > 0) {
                    totalResult = max;
                }
                for (VirtualProductDO virtualProductDO : virtualProductList) {
                    String virtualSku = virtualProductDO.getVirtualSku();
                    BigDecimal actualDailySalesNum = actualDailySalesMapSum.get(virtualSku);
                    BigDecimal targetSalesNum = targetSalesMapSum.get(virtualSku);

                    BigDecimal subEntityRate = targetSalesNum.compareTo(BigDecimal.ZERO) > 0 ?
                            actualDailySalesNum.divide(targetSalesNum, 3, HALF_UP) :
                            BigDecimal.ZERO;
                    if (subEntityRate.compareTo(max) > 0) {
                        subEntityRate = max;
                    }
                    AchievementRateDto build = AchievementRateDto.builder()
                            .subEntityRate(subEntityRate)
                            .parentEntityRate(totalResult)
                            .targetSalesNum(targetSalesNum)
                            .build();
                    achievementRate.put(virtualSku, build);
                }
            });
        }
        return achievementRate;
    }

    private BigDecimal calCurrentMonthTargetSales(Map<String, BigDecimal> targetSalesMap, LocalDate calDate) {
        LocalDate thirtyDaysBefore = calDate.minusDays(30);
        if (CollectionUtil.isEmpty(targetSalesMap)) {
            return BigDecimal.ZERO;
        }

        BigDecimal thirtyTargetSales = calTotalTargetSales(targetSalesMap, thirtyDaysBefore, calDate);
        if (thirtyTargetSales.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal avgThirtyTargetSales = BigDecimal.valueOf(0.2).multiply(thirtyTargetSales).divide(BigDecimal.valueOf(30), 9, HALF_UP);

        LocalDate fourteenDaysBefore = calDate.minusDays(14);
        BigDecimal fourteenTargetSales = calTotalTargetSales(targetSalesMap, fourteenDaysBefore, calDate);
        BigDecimal avgFourteenTargetSales = BigDecimal.valueOf(0.3).multiply(fourteenTargetSales).divide(BigDecimal.valueOf(14), 9, HALF_UP);
        LocalDate sevenDaysBefore = calDate.minusDays(7);
        BigDecimal sevenTargetSales = calTotalTargetSales(targetSalesMap, sevenDaysBefore, calDate);
        BigDecimal avgSevenTargetSales = BigDecimal.valueOf(0.5).multiply(sevenTargetSales).divide(BigDecimal.valueOf(7), 9, HALF_UP);

        BigDecimal avgTargetSales = avgThirtyTargetSales.add(avgFourteenTargetSales).add(avgSevenTargetSales);
        if (avgFourteenTargetSales.compareTo(BigDecimal.ZERO) <= 0) {
            return avgTargetSales.divide(BigDecimal.valueOf(0.2), 3, HALF_UP);
        } else if (avgSevenTargetSales.compareTo(BigDecimal.ZERO) <= 0) {
            return avgTargetSales.divide(BigDecimal.valueOf(0.5), 3, HALF_UP);
        }
        return avgTargetSales.setScale(3, HALF_UP);
    }

    private BigDecimal calTotalTargetSales(Map<String, BigDecimal> targetSalesMap, LocalDate startDate, LocalDate endDate) {
        return targetSalesMap.entrySet().stream()
                .filter(entry -> !LocalDate.parse(entry.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT).isBefore(startDate) && LocalDate.parse(entry.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT).isBefore(endDate))
                .map(Map.Entry::getValue)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(3, HALF_UP);
    }
}
