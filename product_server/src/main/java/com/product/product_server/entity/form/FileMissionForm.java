package com.product.product_server.entity.form;

import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 文档中心保存数据表单
 * <AUTHOR>
 * @Date 2023/12/11 14:31
 **/
@Data
@Builder
public class FileMissionForm implements Serializable {
    @Serial
    private static final long serialVersionUID = -6439495840202433324L;

    private String missionId;

    private String fileName;

    private String filePath;

    private String type;

    private String importStatus;

    private String importResult;

    private List<String> failedResultList;

    private Date createDate;

    private Date finishDate;

    private Date updateDate;

    private String createBy;

    private String updateBy;
}
