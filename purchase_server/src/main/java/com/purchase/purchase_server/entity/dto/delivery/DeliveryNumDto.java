package com.purchase.purchase_server.entity.dto.delivery;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @Description 发货数量以及对应的发货计划和工厂计划
 * <AUTHOR>
 * @Date 2025/1/3 15:53
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryNumDto {
    private String shippingProjectId;

    private String factoryFinishedId;

    /**
     * 发货或者未发货数量
     */
    private Integer number;
}
