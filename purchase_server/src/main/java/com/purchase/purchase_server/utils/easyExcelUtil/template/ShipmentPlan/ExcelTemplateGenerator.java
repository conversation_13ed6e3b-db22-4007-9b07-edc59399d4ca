package com.purchase.purchase_server.utils.easyExcelUtil.template.ShipmentPlan;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.enums.shipment.ShipmentTypeEnum;
import com.purchase.purchase_server.utils.easyExcelUtil.CustomSheetWriteHandler;
import com.purchase.purchase_server.utils.easyExcelUtil.ExcelWidthStyleStrategy;
import com.purchase.purchase_server.utils.easyExcelUtil.template.WarehouseFilter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/10/8
 **/
public class ExcelTemplateGenerator {
    public void generateTemplate(ExcelWriter excelWriter, List<SenboWarehouseDto> warehouses) {
        List<String> warehouseNames = warehouses.stream().map(SenboWarehouseDto::getSenboWarehouse).collect(Collectors.toList());
        // 生成sheet
        generateSheet3(excelWriter, warehouseNames);
    }

    // 亚马逊库存概况
    private void generateSheet3(ExcelWriter excelWriter, List<String> warehouseNames) {
        List<List<String>> headList = new ArrayList<>();

        for (String column : ExcelConstants.Sheet.FIXED_COLUMNS) {
            headList.add(List.of(column));
        }
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").sheetNo(0).head(headList)
                .registerWriteHandler(new CustomSheetWriteHandler(warehouseNames, 1, "warehouseNames", 1))
                .registerWriteHandler(new CustomSheetWriteHandler(ShipmentTypeEnum.getDescList(), 10, "shipmentType", 2))
                .registerWriteHandler(new ExcelWidthStyleStrategy())
                .registerWriteHandler(createStyledWriteSheet()).build();
        excelWriter.write(new ArrayList<>(), writeSheet);
    }

    private HorizontalCellStyleStrategy createStyledWriteSheet() {
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();

        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);

        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setWrapped(false);
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }
}
