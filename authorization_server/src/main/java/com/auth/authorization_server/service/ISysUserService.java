package com.auth.authorization_server.service;

import com.auth.authorization_server.model.dataObject.UserDO;
import com.auth.authorization_server.req.UserParams;
import com.auth.authorization_server.domain.vo.UserVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.utils.common.entity.LoginVo;
import com.auth.authorization_server.domain.dto.UserDTO;

import java.util.List;
import java.util.Set;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface ISysUserService {

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    IPage<UserVO> selectAllocatedRoleList(UserParams user);

    /**
     * 根据角色ids查询已分配用户角色列表
     *
     * @param roleIds 角色id
     * @return 用户信息集合信息
     */
    List<UserDO> selectAllocatedListByRoleIds(Set<String> roleIds);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    List<UserVO> selectUnallocatedRoleList(UserParams user);

    /**
     * 根据条件分页查询已分配用户部门列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    IPage<UserVO> selectAllocatedDeptList(UserParams user);

    /**
     * 根据条件分页查询未分配用户部门列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    List<UserVO> selectUnallocatedDeptList(UserParams user);

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    IPage<UserVO> selectUserList(UserParams user);

    /**
     * 获取当前用户信息
     */
    LoginVo info(String username);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    UserDTO selectUserByUserName(String userName);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    UserDTO selectUserById(String userId);

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    void checkUserDataScope(String userId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean insertUser(UserDTO user) throws Exception;

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean checkUserNameUnique(UserDTO user);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean checkPhoneUnique(UserDTO user);


    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    void checkUserAllowed(UserDTO user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean updateUser(UserDTO user);

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean resetPwd(UserDTO user) throws Exception;

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean updateUserStatus(UserDTO user);

    /**
     * 用户授权角色
     *
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    void insertUserAuth(String userId, String[] roleIds);

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean updateUserProfile(UserDTO user);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    int resetUserPwd(String userName, String password);

    /**
     * 更新用户缓存
     *
     * @param userIds 用户id
     *
     */
    void updateCacheByUserIds(List<String> userIds);

    /**
     * 根据条件查询已用户信息
     *
     * @param userName 用户名称
     * @return 用户信息
     */
    List<UserVO> selectUserInfo(UserParams userName);
}
