package com.inventory_server.model.redundancy.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import com.inventory_server.model.redundancy.entity.dos.InventoryForeignStoreDO;
import com.inventory_server.model.redundancy.mapper.InventoryForeignStoreMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 海外仓库存持久层repository类
 * <AUTHOR>
 * @Date 2024/5/10 17:12
 **/
@Service
public class InventoryForeignStoreRepository extends ServiceImpl<InventoryForeignStoreMapper, InventoryForeignStoreDO> {

    public List<InventoryForeignStoreDO> getForeignStoreListByInfoIds(List<String> infoIds) {
        return baseMapper.selectList(new MPJLambdaWrapper<InventoryForeignStoreDO>()
                .in(InventoryForeignStoreDO::getInventoryInfoId, infoIds)
                .orderByAsc(InventoryForeignStoreDO::getEnableUsingDate)
                .orderByAsc(InventoryForeignStoreDO::getWarehouse)
        );
    }
    public void deleteForeignStoreInfoByInfoId(String inventoryInfoId) {
        List<InventoryForeignStoreDO> list = baseMapper.selectList(Wrappers.<InventoryForeignStoreDO>lambdaQuery()
                .eq(InventoryForeignStoreDO::getInventoryInfoId, inventoryInfoId));
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<String> idList = list.stream().map(PhysicalBaseEntity::getId).toList();
        baseMapper.deleteByIds(idList);
    }
}
