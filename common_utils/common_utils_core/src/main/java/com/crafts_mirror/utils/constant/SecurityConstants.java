package com.crafts_mirror.utils.constant;

/**
 * 权限相关通用常量
 * 
 * <AUTHOR>
 */
public class SecurityConstants
{
    /**
     * 用户ID字段
     */
    public static final String DETAILS_USER_ID = "id";

    /**
     * 用户名字段
     */
    public static final String DETAILS_USERNAME = "username";

    /**
     * 授权信息字段
     */
    public static final String AUTHORIZATION_HEADER = "authorization";

    /**
     * 请求来源
     */
    public static final String FROM_SOURCE = "from-source";

    /**
     * 内部请求
     */
    public static final String INNER = "inner";

    /**
     * 用户标识
     */
    public static final String USER_KEY = "user_key";

    /**
     * 登录用户
     */
    public static final String LOGIN_USER = "login_user";

    /**
     * 角色权限
     */
    public static final String ROLE_PERMISSION = "role_permission";

    /**
     * 数据权限类型-产品-产品管理
     */
    public static final String ROLE_DATA_PER_SELF = "100";

    /**
     * 数据权限类型-产品-虚拟SKU管理
     */
    public static final String ROLE_DATA_PER_VIR = "101";

    /**
     * 数据权限类型-采购-供应商管理
     */
    public static final String ROLE_DATA_PER_FACTORY = "200";

    /**
     * 数据权限类型-采购-发货
     */
    public static final String ROLE_DATA_PER_DELIVERY = "201";

    /**
     * 数据权限类型-采购-补货
     */
    public static final String ROLE_DATA_PER_REPLENISH = "202";

    /**
     * 数据权限类型-库存-库存管理
     */
    public static final String ROLE_DATA_PER_INVENTORY = "300";

    /**
     * 数据权限类型-库存-无计划库存
     */
    public static final String ROLE_DATA_PER_WARNING = "400";

    /**
     * 数据权限类型-库存-加急补货
     */
    public static final String ROLE_DATA_PER_URGENT_PURCHASE = "401";

    /**
     * 数据权限类型-销售-目标日销
     */
    public static final String ROLE_DATA_PER_TARGET_SALES = "500";
}
