ALTER TABLE `crafts_mirror`.`cm_replenishment_record`
    ADD COLUMN `advice_purchase_start_date` date NULL COMMENT '理论补货日（开始）' AFTER `data_source_id`;
ALTER TABLE `crafts_mirror`.`cm_replenishment_record`
    ADD COLUMN `advice_purchase_end_date` date NULL COMMENT '理论补货日（结束）' AFTER `advice_purchase_start_date`;

ALTER TABLE `crafts_mirror`.`cm_replenishment_trial_purchase_inventory`
    ADD COLUMN `operation_confirmed_status` char(1) NULL COMMENT '运营确认状态' AFTER `remarks`;

ALTER TABLE `crafts_mirror`.`cm_replenishment_virtual_sku_purchase`
    ADD COLUMN `operator_remark` varchar(500) DEFAULT NULL COMMENT '运营确认补充说明' AFTER `confirmed_reason`;

ALTER TABLE `crafts_mirror`.`cm_replenishment_virtual_sku_purchase`
    ADD COLUMN `expected_sold_out_date` date NULL COMMENT '预计售罄时间' AFTER `local_inventory`;

UPDATE `crafts_mirror`.`cm_replenishment_virtual_sku_purchase` SET `confirmed_reason` = NULL where `confirmed_reason` is not null ;

ALTER TABLE `crafts_mirror`.`cm_replenishment_project`
    ADD COLUMN `full_link_theoretical_sold_out_date` date NULL COMMENT '全链路海外仓理论售罄时间' AFTER `local_inventory`,
    ADD COLUMN `full_link_days_before_sold_out`      int  NULL COMMENT '全链路售罄前断货天数' AFTER `full_link_theoretical_sold_out_date`;


CREATE TABLE `cm_replenishment_sold_out_days`
(
    `id`                varchar(50) NOT NULL,
    `replenishment_project_id` varchar(50)   DEFAULT NULL,
    `days_begin`        date          DEFAULT NULL,
    `days_end`          date          DEFAULT NULL,
    `days_gap`          varchar(50)   DEFAULT NULL,
    `sold_out_days`     int           DEFAULT NULL,
    `lack_num`          double(10, 3) DEFAULT NULL,
    `create_date`       datetime      DEFAULT NULL,
    `update_date`       datetime      DEFAULT NULL,
    `create_by`         varchar(50)   DEFAULT NULL,
    `update_by`         varchar(50)   DEFAULT NULL,
    `type`              char(1)     NOT NULL COMMENT '0：售罄时间；1：全链路售罄时间',
    `status`            char(1)       DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    KEY `replenishment_project_id` (`replenishment_project_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci
  ROW_FORMAT = DYNAMIC;
