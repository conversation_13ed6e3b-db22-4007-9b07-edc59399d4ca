package com.purchase.purchase_server.utils.easyExcelUtil.listener.Replenishment;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.FactoryFinishedInventoryDto;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.excelObject.RepDeliveryPurchaseInfoExcel;
import com.purchase.purchase_server.entity.vo.UpgradeInfoInteriorVo;
import com.purchase.purchase_server.enums.RepDeliveryImportHeadEnum;
import com.purchase.purchase_server.model.purchase.RepDeliveryInfoDp;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentManager;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentMapManager;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;

/**
 * @Description 导入补货计划-计划
 * <AUTHOR>
 * @Date 2024/1/15 15:49
 **/
@Slf4j
public class RepDeliveryImportListener extends AbstractRepImportListener<RepDeliveryPurchaseInfoExcel> {

    public RepDeliveryImportListener(List<SenboWarehouseDto> senboWarehouseList) {
        super(senboWarehouseList);
    }

    @Override
    public void invoke(RepDeliveryPurchaseInfoExcel product, AnalysisContext analysisContext) {
//        log.warn("导入发货计划-------------导入excel进行中，成功解析excel");
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }
        ReplenishmentMapManager deliveryMapManager = ReplenishmentMapManager.getInstance();

        new RepDeliveryInfoDp(product);

        VirtualProductDO virtualProductDO = Optional.ofNullable(deliveryMapManager.getSkuVirtualProductMap().get(product.getVirtualSku()))
                .orElseGet(() -> Optional.ofNullable(deliveryMapManager.getOldSkuVirtualProductMap().get(product.getVirtualSku()))
                        .orElseThrow(() -> new IllegalArgumentException("虚拟sku不存在")));
        FactoryFinishedInventoryDto inventoryDto = FactoryFinishedInventoryDto.builder()
                .contractCode(product.getContractCode())
                .virtualSku(product.getVirtualSku())
                .factoryFinishedDate(DateUtil.parse(product.getDeliveryDate(), YYYY_MM_DD_DATE_FORMAT_SLASH))
                .shippingNum(Double.valueOf(product.getShipments())).remark(product.getRemark()).build();

        String resultSku = virtualProductDO.getVirtualSku();
        if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())){
            UpgradeInfoInteriorVo upgradeInfoInteriorVo = selectUpgradeInfo(virtualProductDO.getUpgradeId());

            if (ObjectUtil.isNotEmpty(upgradeInfoInteriorVo)){
                if (upgradeInfoInteriorVo.getOriginalSkuId().equals(virtualProductDO.getId())){
                    resultSku = upgradeInfoInteriorVo.getUpgradeSku();
                }
            }
        }
        String finalResultSku = resultSku;
        Optional.ofNullable(deliveryMapManager.getFactoryPlanMap().get(finalResultSku))
                .ifPresentOrElse(
                        list -> list.stream()
                                .filter(item -> item.checkIfTheSamePartFactoryOrder(inventoryDto))
                                .findFirst()
                                .ifPresentOrElse(
                                        item -> item.setShippingNum(item.getShippingNum() + inventoryDto.getShippingNum()),
                                        () -> list.add(inventoryDto)
                                ),
                        () -> {
                            ArrayList<FactoryFinishedInventoryDto> newList = new ArrayList<>();
                            newList.add(inventoryDto);
                            deliveryMapManager.putDeliveryMap(finalResultSku, newList);
                        }
                );
        ReplenishmentManager manager = ReplenishmentManager.getInstance();
        manager.incrementSuccessfulTimes();
    }

    /**
     * 这里会一行行的返回头
     *
     * @param headMap 表头map
     * @param context 上下文
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        List<String> headList = RepDeliveryImportHeadEnum.getCodeList();
        //删除headMap中的空值
        headMap.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty());
        List<String> headMapList = new ArrayList<>(headMap.values());

        //比较两个list集合值是否相等
        if (!headList.equals(headMapList)) {
            throw new RuntimeException("计划表头错误，请检查表头是否正确");
        }
    }
}
