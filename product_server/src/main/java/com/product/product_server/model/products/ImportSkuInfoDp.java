package com.product.product_server.model.products;

import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.utils.utils.PatternUtils;

/**
 * @Description Sku、老sku、spu、虚拟sku
 * <AUTHOR>
 * @Date 2023/12/8 11:11
 **/
public record ImportSkuInfoDp(String sku, String oldSku) {
    public ImportSkuInfoDp {
        validSkuAndOldSku(sku, oldSku);
    }

    private void validSkuAndOldSku(String sku, String oldSku) {
        if(StrUtil.isBlank(sku)) {
            throw new IllegalArgumentException("自定义sku为空");
        } if(sku.length() > 20) {
            throw new IllegalArgumentException("自定义sku长度超出限制");
        } else if (!PatternUtils.UPPERCASE_LOWERCASE_NUMBER_DOT_PATTERN.matcher(sku).matches()) {
            throw new IllegalArgumentException("自定义sku只能英文、数字和符号的组合");
        }

        if(StrUtil.isNotBlank(oldSku) && oldSku.length() > 20) {
            throw new IllegalArgumentException("老sku长度超出限制");
        } else if (StrUtil.isNotBlank(oldSku) && !PatternUtils.UPPERCASE_LOWERCASE_NUMBER_DOT_PATTERN.matcher(oldSku).matches()) {
            throw new IllegalArgumentException("老sku只能英文、数字和符号的组合");
        }
    }
}