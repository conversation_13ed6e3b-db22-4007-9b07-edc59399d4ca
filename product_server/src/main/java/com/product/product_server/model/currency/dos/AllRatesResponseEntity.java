package com.product.product_server.model.currency.dos;

import com.alibaba.fastjson2.annotation.JSONField;
import com.product.product_server.model.currency.dto.AllRatesResponseDto;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/4/9 22:10
 **/
@Data
public class AllRatesResponseEntity implements Serializable {
    private Integer code;

    private String msg;

    private AllRatesResponseDto data;

    @JSONField(name = "request_id")
    private String requestId;
}