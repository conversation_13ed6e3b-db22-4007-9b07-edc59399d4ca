package com.purchase.purchase_server.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.dataObject.FactoryFinishedInventoryDO;
import com.purchase.purchase_server.entity.dto.delivery.DeliveryNumDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FactoryFinishedInventoryMapper extends MPJBaseMapper<FactoryFinishedInventoryDO> {

    int deleteByShippingProjectIds(@Param("shippingProjectIds") List<String> shippingProjectIds,
                                   @Param("sourceType") String sourceType);

    List<DeliveryNumDto> getDeliveryNumInfo(@Param("projectList") List<String> projectList,
                                            @Param("shippingStartDate") String shippingStartDate,
                                            @Param("shippingEndDate") String shippingEndDate);

    List<DeliveryNumDto> getNotDeliveryNumInfo(@Param("projectList") List<String> projectList,
                                               @Param("shippingEndDate") String shippingEndDate);
}
