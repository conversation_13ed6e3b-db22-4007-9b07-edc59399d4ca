package com.purchase.purchase_server.entity.dto;

import com.purchase.purchase_server.entity.dataObject.FactoryFinishedInventoryDO;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/10/23 14:35
 **/
@Data
@Builder
public class NormalDeliveryInventorySaveDto {
    private Map<String, Integer> headShippingDays;

    private List<FactoryRemainInventoryDto> shippingInventoryList;

    private String virtualSku;

    private List<FactoryFinishedInventoryDO> factoryFinishedInventoryList;

    /**
     * 优先发货数据
     */
    private List<FactoryRemainInventoryDto> priorDeliveryList;
}
