package com.inventory_server.model.product.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * @Description 产品升级款关系表
 * <AUTHOR>
 * @Date 2023/12/15 20:47
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_virtual_upgrade_relation")
public class VirtualUpgradeRelationDO extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -451421694395675175L;

    /**
     * 被升级款商品ID
     */
    private String originalId;

    /**
     * 升级款商品ID
     */
    private String upgradeId;
}
