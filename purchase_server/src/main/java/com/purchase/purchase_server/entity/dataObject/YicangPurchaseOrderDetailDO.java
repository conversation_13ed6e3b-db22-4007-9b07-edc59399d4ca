package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.util.Date;

/**
 * 易仓采购单明细
 * @TableName cm_yicang_purchase_order_detail
 */
@TableName(value ="cm_yicang_purchase_order_detail")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class YicangPurchaseOrderDetailDO extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 3997082936951491121L;
    /**
     * 产品Id
     */
    private Integer productId;

    /**
     * 预期数量
     */
    private Integer qtyExpected;

    /**
     * (良品上架数量)付款数量
     */
    private Integer qtyPay;

    /**
     * 实收数量
     */
    private Integer qtyReceving;

    /**
     * 赠送数量
     */
    private Integer qtyFree;

    /**
     * 单价（不含税）
     */
    private Float unitPrice;

    /**
     * 单价（含税）
     */
    private Float contractPrice;

    /**
     * 总价
     */
    private Float totalPrice;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 自定义产品id（senbo）
     */
    private String sbSelfSkuId;

    /**
     * 自定义产品名称（senbo）
     */
    private String sbSelfSkuName;


    /**
     * 产品代码（自定义）
     */
    private String productSku;

    /**
     * 产品名称
     */
    private String productTitle;

    /**
     * 供应商品号
     */
    private String spSupplierSku;

    /**
     * 是否赠品
0否，
1是
     */
    private String isFree;

    /**
     * 产品采购备注
     */
    private String note;

    /**
     * 外部单号
     */
    private String popExternalNumber;

    /**
     * 头程占用数量
     */
    private Integer transferQty;

    /**
     * 税率
     */
    private Integer poTaxRate;

    /**
     * 首次到货时间
     */
    private Date firstReceiveTime;

    /**
     * 平台产品代码：如1688产品ID beta
     */
    private String popPlatformProduct;

    /**
     * 平台产品款式：如1688产品的specId
     */
    private String popPlatformSku;

    /**
     * 产品采购状态
     */
    private String poStatus;

    /**
     * 采购单表id
     */
    private String sbPoId;

    /**
     * 图片
     */
    private String image;

    /**
     * 产品经理
     */
    private String buyer;
}