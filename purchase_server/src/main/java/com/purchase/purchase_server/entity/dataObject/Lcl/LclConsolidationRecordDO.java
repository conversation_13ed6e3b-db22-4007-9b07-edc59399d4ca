package com.purchase.purchase_server.entity.dataObject.Lcl;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 拼柜装柜记录表
 */
@TableName(value ="cm_lcl_consolidation_record")
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclConsolidationRecordDO extends BaseEntity {
    @Serial
    private static final long serialVersionUID = -1068702576995694326L;
    /**
     * 发货记录id
     */
    private String shippingRecordId;

    /**
     * 发货记录名称
     */
    private String shippingRecordName;

    /**
     * 最早发货装柜时间
     */
    private Date shippingStartDate;

    /**
     * 最晚发货装柜时间
     */
    private Date shippingEndDate;

    /**
     * 清洗装柜开始时间
     */
    private Date lclStartDate;

    /**
     * 清洗装柜开始时间
     */
    private Date lclEndDate;
    /**
     * 排柜开始时间
     */
    private Date lclContainerStartDate;

    /**
     * 排柜结束时间
     */
    private Date lclContainerEndDate;



    /**
     * 状态（-2：作废；-1：已取消；0：草稿；1：数据整理中；2：待排柜；3：排柜中；4：已保存）
     * @see com.purchase.purchase_server.enums.lcl.LclStatusEnum
     */
    private String lclStatus;

    /**
     * 是否整理过
     * @see com.purchase.purchase_server.enums.lcl.LclConsolidationEnum
     */
    private String isLclConsolidation;

    /**
     * 最小体积阈值(立方米)
     */
    private BigDecimal minVolume;

    /**
     * 头程时间
     */
    private String headShippingDays;
}