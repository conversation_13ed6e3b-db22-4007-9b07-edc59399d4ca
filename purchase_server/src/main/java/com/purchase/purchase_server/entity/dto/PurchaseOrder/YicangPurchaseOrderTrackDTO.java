package com.purchase.purchase_server.entity.dto.PurchaseOrder;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 易仓采购单跟单状态表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YicangPurchaseOrderTrackDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3411196573099646278L;
    /**
     * 跟单状态中文名称
     */
    @JSONField(name = "name")
    private String name;

    /**
     * 跟单状态英文名称
     */
    @JSONField(name = "name_en")
    private String nameEn;

    /**
     * 易仓采购单id
     */
    private String ycPoId;
}