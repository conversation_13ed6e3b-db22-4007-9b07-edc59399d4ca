package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @Description 虚拟sku产品
 * <AUTHOR>
 * @Date 2024/1/6 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_virtual_product")
public class VirtualProductDO extends BaseEntity {
    private String selfProductSkuId;

    private String channel;

    private String virtualSku;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String oldSku;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String spuId;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remarks;

    /**
     * 子体类型
     * @see com.purchase.purchase_server.enums.VirtualSubTypeEnum
     */
    private Integer subType;

    /**
     * 产品状态
     * @see com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum
     */
    private Integer productStatus;

    /**
     * 产品类型
     * @see com.purchase.purchase_server.enums.VirtualProductTypeEnum
     */
    private Integer productType;

    /**
     * 升级款关系id
     */
    private String upgradeId;
    /**
     * 借货策略
     */
    private String borrowingStrategy;
}
