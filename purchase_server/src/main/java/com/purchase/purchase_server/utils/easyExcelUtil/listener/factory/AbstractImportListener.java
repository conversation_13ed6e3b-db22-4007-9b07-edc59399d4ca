package com.purchase.purchase_server.utils.easyExcelUtil.listener.factory;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.exception.FieldNotExistException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @Description 供应商导入处理父类
 * <AUTHOR>
 * @Date 2024/6/6 14:44
 **/
@Slf4j
public abstract class AbstractImportListener<T> extends AnalysisEventListener<T> {
    protected static final Integer TOTAL_ROW_NUMBER = 30000;

    public AbstractImportListener(String fileName, List<String> errorList) {
        this.fileName = fileName;
        this.errorList = errorList;
    }

    protected final String fileName;

    protected final List<String> errorList;

    /**
     * 验证器对象，用来验证数据是否符合规范
     */
    private final Validator validator;
    /**
     * 是否继续读取数据，默认为true，当读取到不符合规则的数据时，读取结束
     */
    private boolean isContinue = true;

    {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    /**
     * 停止读取数据行
     */
    public final void stop() {
        this.isContinue = false;
    }

    /**
     * 对数据行进行规则校验
     *
     * @param data    数据行
     * @param context 上下文
     * @return 校验结果集, 如果为空说明校验通过
     */
    public final boolean check(T data, AnalysisContext context) {
        ReadRowHolder readRowHolder = context.readRowHolder();
        Integer rowIndex = readRowHolder.getRowIndex();
        String sheetName = context.readSheetHolder().getSheetName();
        Set<ConstraintViolation<T>> validateResult = validator.validate(data);
        if (Objects.nonNull(validateResult) && !validateResult.isEmpty()) {
            for (ConstraintViolation<T> violation : validateResult) {
                errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                        sheetName, rowIndex + 1, violation.getMessage()));
            }
            return Boolean.FALSE;

        }
        return Boolean.TRUE;
    }

    @Override
    public final boolean hasNext(AnalysisContext context) {
        return isContinue;
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        switch (exception) {
            case ExcelDataConvertException excelException ->
                    errorList.add(String.format(" %s 第 %d 行，第 %d 列数据异常，请核对数据格式是否正确，该单元格是否多了前后空格",
                            context.readSheetHolder().getSheetName(), excelException.getRowIndex() + 1, excelException.getColumnIndex() + 1));
            case IllegalArgumentException illegalArgumentException ->
                    errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getSheetName(), context.readSheetHolder().getRowIndex() + 1, illegalArgumentException.getMessage()));
            case DataIntegrityViolationException dataIntegrityViolationException ->
                    errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getSheetName(), context.readSheetHolder().getRowIndex() + 1, dataIntegrityViolationException.getMessage()));
            case NullPointerException nullPointerException ->
                    errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getSheetName(), context.readSheetHolder().getRowIndex() + 1, nullPointerException.getMessage()));
            case FieldNotExistException fieldNotExistException ->
                    errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，请联系开发配置",
                            context.readSheetHolder().getSheetName(), context.readSheetHolder().getRowIndex() + 1, fieldNotExistException.getMessage()));

            case BusinessException businessException ->
                    errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getSheetName(), context.readSheetHolder().getRowIndex() + 1, businessException.getMessage()));

            case RuntimeException runtimeException -> {
                errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，该异常未被正常捕获，造成后续数据都无法继续导入，请联系开发人员，修复异常数据后再次导入",
                        context.readSheetHolder().getSheetName(), context.readSheetHolder().getRowIndex() + 1, runtimeException.getMessage()));
                doAfterAllAnalysed(context);
                throw new RuntimeException(runtimeException.getMessage());
            }
            default ->
                    errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getSheetName(), context.readSheetHolder().getRowIndex() + 1, exception.getMessage()));
        }
        log.warn("excel转实体类异常-{}，第 {} 行数据异常，异常原因：{}",
                context.readSheetHolder().getSheetName(), context.readSheetHolder().getRowIndex() + 1, exception.getMessage());
    }
}
