package com.purchase.purchase_server.entity.bo;


import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.util.Date;

/**
 * @TableName cm_replenishment_trial_purchase_inventory
 */
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ReplenishmentTrialPurchaseInventoryCopyBO extends BaseEntity {


    @Serial
    private static final long serialVersionUID = -732634632370407147L;
    /**
     * 理论补货量
     */
    private Integer advicePurchaseNum;
    /**
     * 建议补货量
     */
    private Integer actualReplenishmentNum;
    /**
     * 运营确认量
     */
    private Integer operationConfirmedNum;
    /**
     * 目标仓
     */
    private String destinationWarehouse;

    /**
     * 建议采购日
     */
    private Date advicePurchaseDate;

    /**
     * 预计交货时间
     */
    private Date expectedFactoryFinishedDate;

    /**
     * 预计装柜时间
     */
    private Date expectedShippingStartDate;

    /**
     * 预计到仓时间
     */
    private Date expectedArrivingDate;

    /**
     * 箱数
     */
    private Integer packageNum;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 按虚拟sku展示采购计划的id
     */
    private String replenishmentVirtualPurchaseId;

    /**
     * 采纳状态(0-未采纳 1-已采纳)
     * @see com.purchase.purchase_server.enums.TrialPurchaseStatusEnum
     */
    private String trialStatus;

    private Integer containerLoad;
}