package com.purchase.purchase_server.repository.httpRepository.impl;

import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.utils.common.entity.HttpRequestDetail;
import com.crafts_mirror.utils.constant.YiCangConstant;
import com.crafts_mirror.utils.utils.DingTalkUtils;
import com.crafts_mirror.utils.utils.HttpClientPool;
import com.crafts_mirror.utils.utils.YCRequestBody;
import com.google.common.util.concurrent.RateLimiter;
import com.purchase.purchase_server.entity.dataObject.YCResponseEntity;
import com.purchase.purchase_server.entity.dto.PurchaseOrdersSummaryBizContent;
import com.purchase.purchase_server.entity.form.PurchaseOrdersSummaryForm;
import com.purchase.purchase_server.repository.httpRepository.FetchPurchaseOrdersHttpRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.Collections;
import java.util.Objects;

import static com.crafts_mirror.utils.constant.DingTalkConstant.YC_INVENTORY_FETCH_RESULT_INFO_SECRET;
import static com.crafts_mirror.utils.constant.DingTalkConstant.YC_INVENTORY_FETCH_RESULT_INFO_TOKEN;

/**
 * @Description 从易仓获取采购单信息远程链接层
 * <AUTHOR>
 * @Date 2024/11/4 14:08
 **/
@Service
@Slf4j
public class FetchPurchaseOrdersRequestImpl implements FetchPurchaseOrdersHttpRepository {
    // 初始速率为1/3次每秒（等待3秒）
    private static final double INITIAL_RATE = 1.0/3.0;
    // 创建多个RateLimiter实例，分别对应不同等待时间
    private final RateLimiter firstRetryLimiter = RateLimiter.create(INITIAL_RATE);
    private final RateLimiter secondRetryLimiter = RateLimiter.create(INITIAL_RATE/2);  // 6秒
    private final RateLimiter thirdRetryLimiter = RateLimiter.create(INITIAL_RATE/4);   // 12秒

    @Value("${spring.profiles.active}")
    private String environment;

    @Override
    public PurchaseOrdersSummaryBizContent fetchAllPurchaseOrdersSummaryList(PurchaseOrdersSummaryForm form) {
        int maxRetries = 3;
        int retryCount = 0;
        while (retryCount <= maxRetries) {
            try {
                if (retryCount > 0) {
                    // 根据重试次数使用不同的RateLimiter
                    switch (retryCount) {
                        case 1 -> firstRetryLimiter.acquire();
                        case 2 -> secondRetryLimiter.acquire();
                        case 3 -> thirdRetryLimiter.acquire();
                    }
                }
                HttpRequestDetail<YCResponseEntity> requestDetail = buildHttpRequestDetail(form);
                HttpClientPool<YCResponseEntity> httpClientPool = new HttpClientPool<>(Collections.singletonList(requestDetail));
                YCResponseEntity entity = httpClientPool.sendRequest(requestDetail);

                if (Objects.equals(entity.getCode(), "200")) {
                    String bizContent = entity.getBizContent();
                    if (bizContent.equals("[]")){
                        return PurchaseOrdersSummaryBizContent.builder().total(0).build();
                    }
                    return JSON.parseObject(bizContent, PurchaseOrdersSummaryBizContent.class);
                }
                log.error("从易仓处获取采购单异常，第{}次重试：{}", retryCount + 1, entity.getMessage());
                retryCount++;

            } catch (Exception e) {
                log.error("从易仓处获取采购单发生异常，第{}次重试：{}", retryCount + 1, e.getMessage());
                retryCount++;

            }
        }
        if (environment.equals("prod")){
            DingTalkUtils.sendDingTalkTextMessage(YC_INVENTORY_FETCH_RESULT_INFO_TOKEN, YC_INVENTORY_FETCH_RESULT_INFO_SECRET, environment + ":从易仓处获取采购单异常，重试" + maxRetries + "次后仍然失败");
        }
        throw new RuntimeException("从易仓处获取采购单异常，重试" + maxRetries + "次后仍然失败");
    }

    private HttpRequestDetail<YCResponseEntity> buildHttpRequestDetail(PurchaseOrdersSummaryForm form) {
        YCRequestBody<PurchaseOrdersSummaryForm> ycRequestBody = new YCRequestBody<>("V1.0.0", "getPurchaseOrders", form)
                .createECRequestBody();
        String body = JSON.toJSONString(ycRequestBody);
        // 请求路径
        URI uri = URI.create(YiCangConstant.GET_WAREHOUSE_SUMMARY_URL);
        return new HttpRequestDetail<>(HttpRequestDetail.HttpMethod.POST, uri, body, YCResponseEntity.class);
    }
}
