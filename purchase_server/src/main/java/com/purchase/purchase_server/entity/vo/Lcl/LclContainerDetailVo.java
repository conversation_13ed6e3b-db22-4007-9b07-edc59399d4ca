package com.purchase.purchase_server.entity.vo.Lcl;

import com.purchase.purchase_server.entity.dto.Lcl.LclContainerDetailInfoDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/27 14:41
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LclContainerDetailVo implements Serializable {
    private List<LclContainerDetailInfoDto> detailList;
}
