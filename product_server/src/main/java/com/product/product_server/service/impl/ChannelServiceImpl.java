package com.product.product_server.service.impl;

import com.crafts_mirror.utils.common.entity.ChannelSearchDto;
import com.crafts_mirror.utils.common.entity.ChannelSearchVo;
import com.product.product_server.repository.interiorRepository.IChannelInteriorRepository;
import com.product.product_server.service.IChannelService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/6 11:11
 **/
@Service
public class ChannelServiceImpl implements IChannelService {

    @Resource
    private IChannelInteriorRepository channelInteriorRepository;

    @Override
    public ChannelSearchVo getAllNicheChannel() {
        ChannelSearchVo channelSearchVo = channelInteriorRepository.getAllChannel();
        channelSearchVo.setChannelSearchDtoList(channelSearchVo.getChannelSearchDtoList().stream()
                // 产品无法选择中转渠道
                .filter(f -> !f.getChannelName().equals("中转"))
                .toList());
        return channelSearchVo;
    }

    @Override
    public Map<String, String> getChannelIdNameMap() {
        ChannelSearchVo channelSearchVo = getAllNicheChannel();
        List<ChannelSearchDto> channelSearchList = channelSearchVo.getChannelSearchDtoList();
        return channelSearchList.stream().collect(Collectors.toMap(ChannelSearchDto::getChannelId, ChannelSearchDto::getChannelName));
    }

    @Override
    public Map<String, String> getChannelNameIdMap() {
        ChannelSearchVo channelSearchVo = getAllNicheChannel();
        List<ChannelSearchDto> channelSearchList = channelSearchVo.getChannelSearchDtoList();
        return channelSearchList.stream().collect(Collectors.toMap(c -> c.getChannelName().toUpperCase(Locale.ROOT), ChannelSearchDto::getChannelId));
    }
}
