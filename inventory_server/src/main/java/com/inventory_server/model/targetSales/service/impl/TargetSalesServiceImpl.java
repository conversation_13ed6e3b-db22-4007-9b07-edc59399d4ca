package com.inventory_server.model.targetSales.service.impl;

import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.dp.CalculationForm;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.inventory_server.model.targetSales.repository.redis.ITargetSalesRedisRepository;
import com.inventory_server.model.targetSales.service.ITargetSalesService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import static com.crafts_mirror.utils.constant.SystemConstant.PREPARE_VIRTUAL_SKU_TARGET_SALES;
import static com.crafts_mirror.utils.utils.DateUtils.YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT;

/**
 * <AUTHOR>
 * @Date 2025/3/17 17:44
 **/
@Service
@Slf4j
public class TargetSalesServiceImpl implements ITargetSalesService {

    @Resource
    private ITargetSalesRedisRepository targetSalesRedisRepository;

    @Resource
    private RestTemplate restTemplate;

    @Override
    public void prepareTargetSales(List<String>virtualIdList, LocalDate startDate){
        CalculationForm form = CalculationForm.builder().virtualSkuIdList(virtualIdList).startDate(startDate).build();
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO<Boolean> result = restTemplateUtils.post(form, ResultDTO.class, PREPARE_VIRTUAL_SKU_TARGET_SALES);
        if (result.getStatus() != 200 || !result.getData()) {
            log.error("准备目标日销失败：{}", result.getMessage());
            throw new IllegalArgumentException("获取目标日销失败，请联系研发人员处理");
        }
    }

    @Override
    public Map<String, BigDecimal> getTargetSales(String virtualId, LocalDate startDate) {
        Map<String, BigDecimal> targetSalesMap = targetSalesRedisRepository.getTargetSales(virtualId, startDate);
        implementTargetSalesMap(targetSalesMap, startDate);
        return targetSalesMap;
    }

    @Override
    public Map<String, BigDecimal> getTargetSales(String virtualId, LocalDate startDate, LocalDate endDate) {
        Map<String, BigDecimal> targetSalesMap = targetSalesRedisRepository.getTargetSales(virtualId, startDate);
        implementTargetSalesMap(targetSalesMap, startDate, endDate);
        return targetSalesMap;
    }

    private void implementTargetSalesMap(Map<String, BigDecimal> targetSalesMap, LocalDate startDate) {
        // 当天的6个月后的月底
        LocalDate sixMonthLater = LocalDate.now().plusMonths(7).withDayOfMonth(1);
        LocalDate calEndDate = targetSalesMap.keySet().stream()
                .map(m -> LocalDate.parse(m, YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT))
                .max(Comparator.comparing(m -> m))
                .orElse(sixMonthLater);
        calEndDate = calEndDate.isBefore(sixMonthLater) ? sixMonthLater : calEndDate;

        implementTargetSalesMap(targetSalesMap, startDate, calEndDate);
    }

    @Override
    public void implementTargetSalesMap(Map<String, BigDecimal> targetSalesMap, LocalDate startDate, LocalDate endDate) {
        LocalDate localDate = LocalDate.of(startDate.getYear(), startDate.getMonth(), startDate.getDayOfMonth());
        for(LocalDate calDate = localDate; calDate.isBefore(endDate); calDate = calDate.plusDays(1)) {
            String format = calDate.format(YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT);
            targetSalesMap.putIfAbsent(format, BigDecimal.ZERO);
        }
    }

}
