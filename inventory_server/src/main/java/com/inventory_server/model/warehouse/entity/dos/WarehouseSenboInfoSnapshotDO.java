package com.inventory_server.model.warehouse.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@TableName(value ="cm_warehouse_senbo_info_snapshot")
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseSenboInfoSnapshotDO extends BaseEntity {
    /**
     * 仓库信息
     */
    private String warehouseSenboInfo;
}