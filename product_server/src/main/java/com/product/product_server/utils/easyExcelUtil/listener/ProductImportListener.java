package com.product.product_server.utils.easyExcelUtil.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.enums.UserPostEnum;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.utils.SnowflakeIdWorker;
import com.crafts_mirror.utils.utils.StrUtils;
import com.product.product_server.entity.dataObject.FactoryInfoDO;
import com.product.product_server.entity.excelObject.SelfProductInfoExcel;
import com.product.product_server.entity.form.SpuInfoSaveForm;
import com.product.product_server.entity.response.ResultDTO;
import com.product.product_server.entity.vo.BuyerSearchVo;
import com.product.product_server.enums.PCSTypeEnum;
import com.product.product_server.exception.FieldNotExistException;
import com.product.product_server.model.products.*;
import com.product.product_server.model.virtualProduct.VirtualProductSkuStatusDp;
import com.product.product_server.repository.FactoryInfoRepositoryImpl;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SystemConstant.FILE_SYSTEM_PUT_OBJECT_BY_URL_URL;

/**
 * @Description 导入自定义产品监听类
 * <AUTHOR>
 * @Date 2023/12/12 15:49
 **/
@Slf4j
public class ProductImportListener extends AbstractProductImportListener<SelfProductInfoExcel> {
    private final FactoryInfoRepositoryImpl factoryInfoRepositoryImpl = ApplicationContextProvider.getBean(FactoryInfoRepositoryImpl.class);

    private final SnowflakeIdWorker snowflakeIdWorker = ApplicationContextProvider.getBean(SnowflakeIdWorker.class);

    private final Map<String, String> channelNameIdMap;

    public ProductImportListener(String fileName, String importExcelId, Map<String, String> channelNameIdMap) {
        super(fileName, importExcelId);
        this.channelNameIdMap = channelNameIdMap;
    }

    @Override
    public void invoke(SelfProductInfoExcel product, AnalysisContext analysisContext) {
        log.warn("导入自定义商品-------------导入excel进行中，成功解析excel");
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > 50000) {
            throw new RuntimeException("导入数据超过50000行，请缩小导入数据量");
        }
        Integer rowIndex = analysisContext.readSheetHolder().getRowIndex();
        String imageUrl = dealWithImageUrl(product.getImage(), rowIndex);

        // 设定sku以及老sku
        var importSkuInfoDp = new ImportSkuInfoDp(product.getSku(), product.getOldSku());

        // 设定供应商代号
        FactoryInfoDO factoryInfoDO = factoryInfoRepositoryImpl.getOne(Wrappers.<FactoryInfoDO>lambdaQuery().eq(FactoryInfoDO::getFactoryCode, product.getFactoryCode()).eq(FactoryInfoDO::getStatus, '0'));
        if (factoryInfoDO == null) {
            throw new IllegalArgumentException("供应商代码不存在");
        }
        product.setCurrency(factoryInfoDO.getCurrency());
        String category = product.getCategory();
        String categoryId = validCategoryExisted(category);
        product.setCategory(categoryId);
        // 判断运营
        String operator = product.getOperator();
        List<String> nickNameList = StrUtils.convertStringToList(operator);
        AtomicReference<String> operatorFlag = new AtomicReference<>();
        String operators = nickNameList.stream()
                .distinct()
                .map(nickName -> {
                    try {
                        List<BuyerSearchVo> userList = validNickNameExisted(nickName, UserPostEnum.OPERATIONS_MANAGER);
                        return CollectionUtil.isNotEmpty(userList) ? userList.getFirst().getUserName() : nickName;
                    } catch (Exception e) {
                        operatorFlag.set(e.getMessage());
                        return nickName;
                    }
                })
                .collect(Collectors.joining(","));

        // 校验渠道
        String channelName = product.getChannel();
        validChannelExisted(channelName, channelNameIdMap.keySet());
        String channelUpper = channelNameIdMap.get(Optional.ofNullable(channelName).orElse("").toUpperCase(Locale.ROOT));
        // 设定渠道、虚拟sku、spu
        var virtualSkuInfoDp = new ImportVirtualSkuInfoDp(channelUpper, product.getSpu(), product.getVirtualSku(), operators,
                new VirtualProductSkuStatusDp(false, product.getSubType(), product.getProductStatus(), product.getProductType()));

        if (StrUtil.isNotBlank(operatorFlag.get())){
            throw new FieldNotExistException(operatorFlag.get());
        }

        // 校验渠道和老sku的关系
        new OldSkuAndChannelDp(product.getOldSku(), channelName);

        // 设定外箱规格
        var specificationDp = new SpecificationDp(product.getCaseLength(),
                product.getCaseWidth(), product.getCaseHeight());
        // 校验采购人员
        List<BuyerSearchVo> userInteriorVOS = validNickNameExisted(product.getBuyer(), UserPostEnum.PRODUCT_MANAGER);
        product.setBuyer(userInteriorVOS.getFirst().getUserName());

        Objects.requireNonNull(product.getCurrency(), "币种不能为空");
        // 设定采购信息
        var purchaseInfoDp = new PurchaseInfoDp(
                new PurchaseDateDp(product.getPurchaseDate()),
                product.getBuyer(), product.getCurrency(),
                new PriceDp(product.getTaxes(), product.getPrice(), product.getPriceWithTaxes()),
                null);

        // 设置重量信息
        var caseWeightInfo = new CaseWeightInfoDp(product.getUnitOfWeight(),
                product.getSingleCaseGrossWeight(),
                product.getContainerLoad());
        // 校验spu信息
        SpuInfoSaveForm spuInfoSaveForm;
        if (StrUtil.isNotBlank(product.getSpu())) {
            spuInfoSaveForm = new SpuInfoSaveForm(null, product.getSpu(), product.getSpuProductName(),
                    null, new ArrayList<>());
        } else {
            spuInfoSaveForm = null;
        }

        if (StrUtil.isBlank(product.getProductName()) || product.getProductName().length() > 50) {
            throw new IllegalArgumentException("产品名称为空或长度超过50字符");
        }
        validRemarks(product.getSelfRemarks(), "自定义sku");
        validRemarks(product.getVirtualRemarks(), "虚拟sku");

        checkVirtualSkuAndOldSkuConsistency(product.getVirtualSku(), product.getOldSku());

        // 计算件的类型
        CalPCSTypeDp calPCSTypeDp = new CalPCSTypeDp(product.getProductName(), specificationDp, caseWeightInfo, null);
        PCSTypeEnum pcsTypeEnum = PCSTypeEnum.getByProductInfo(calPCSTypeDp);

        if (product.getCommodityInspection() == null) {
            throw new IllegalArgumentException("是否商检格式不正确");
        }

        // 保存自定义sku信息、虚拟sku信息，spu信息
        productImportService.saveSelfProductInfo(importSkuInfoDp, factoryInfoDO, virtualSkuInfoDp, specificationDp, purchaseInfoDp,
                caseWeightInfo, product, spuInfoSaveForm, imageUrl, pcsTypeEnum);

        successfulTimes++;
    }

    protected String dealWithImageUrl(String imageUrl, Integer rowIndex) {
        String url = "product/image/" + snowflakeIdWorker.nextId() + ".jpg";

        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, authorization);
        String path = String.format(FILE_SYSTEM_PUT_OBJECT_BY_URL_URL + "?networkUrl=%s&url=%s&expireTime=%s", imageUrl, url, "");
        ResultDTO<String> resultDTO = restTemplateUtils.get(path, ResultDTO.class);
        if (resultDTO == null || !Objects.equals(resultDTO.getStatus(), ResponseCodeEnum.OK.getCode())) {
            errorList.add(String.format("第%d行图片url获取图片数据异常，其余数据正常处理，图片数据不添加，请后续维护", rowIndex + 1));
            return null;
        }
        return resultDTO.getData();
    }

    private void validRemarks(String remarks, String skuType) {
        if (StrUtil.isNotBlank(remarks) && remarks.length() > 100) {
            throw new IllegalArgumentException(skuType + "备注超过长度限制");
        }
    }
}
