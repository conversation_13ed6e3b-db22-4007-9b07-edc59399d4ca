package com.purchase.purchase_server.repository.dataRepository.Lcl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclContainerInfoDO;
import com.purchase.purchase_server.entity.dto.Lcl.LclShipmentCategoryDto;
import com.purchase.purchase_server.entity.form.LclSearchPageForm;
import com.purchase.purchase_server.entity.vo.Lcl.LclContainerInfoPage;
import com.purchase.purchase_server.entity.vo.Lcl.LclContainerInfoVo;
import com.purchase.purchase_server.enums.lcl.LclContainerSortEnum;
import com.purchase.purchase_server.mapper.Lcl.LclContainerInfoMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/5/22
 **/
@Service
public class LclContainerInfoRepositoryImpl extends ServiceImpl<LclContainerInfoMapper, LclContainerInfoDO> {

    public LclContainerInfoPage<LclContainerInfoVo> getLclContainerInfoPage(LclSearchPageForm form, List<String> containerIdList) {
        LclContainerInfoPage<LclContainerInfoVo> page = new LclContainerInfoPage<>();
        page.setCurrent(form.getCurrent());
        page.setSize(form.getSize());

        MPJLambdaWrapper<LclContainerInfoDO> wrapper = new MPJLambdaWrapper<LclContainerInfoDO>()
                .selectAs(LclContainerInfoDO::getId, "containerId")
                .selectAll(LclContainerInfoDO.class)

                .like(StrUtil.isNotBlank(form.getShipmentCode()), LclContainerInfoDO::getShipmentCode, form.getShipmentCode())
                .eq(StrUtil.isNotBlank(form.getSourceType()), LclContainerInfoDO::getSourceType, form.getSourceType())
                .eq(LclContainerInfoDO::getLclRecordId, form.getRecordId())
                .in(CollectionUtil.isNotEmpty(containerIdList), LclContainerInfoDO::getId, containerIdList);

        String direction = StrUtil.isNotBlank(form.getDirection()) ? form.getDirection() : " ASC ";

        if (StrUtil.isNotBlank(form.getSort()) ) {
            wrapper.last(" ORDER BY " + LclContainerSortEnum.ofCode(form.getSort()).getCode() + " " + direction);
        } else {
            wrapper.last(defaultSortSql(direction));
        }
        return baseMapper.selectJoinPage(page, LclContainerInfoVo.class, wrapper);
    }

    private String defaultSortSql(String direction) {
        if (StrUtil.isBlank(direction)) {
            direction = " ASC";
        }
        return String.format(" ORDER BY CASE WHEN shipment_code LIKE '不到20方汇总%%' THEN 1 WHEN shipment_code LIKE '加急%%' THEN 2 " +
                " WHEN shipment_code LIKE '待生成的货件号%%' THEN 4 " +
                " ELSE 3 END %s, CAST( REGEXP_SUBSTR ( shipment_code, '\\\\d+$' ) AS UNSIGNED)%s ", direction, direction);
    }

    public int deleteByLclRecordId(String lclRecordId) {
        return baseMapper.deleteByLclRecordId(lclRecordId);
    }

    public List<LclShipmentCategoryDto> aggShipmentTypeByRecordId(String recordId) {
        return baseMapper.aggShipmentTypeByRecordId(recordId);
    }

    public List<LclContainerInfoDO> getAllContainerInfoByRecordId(String recordId) {
        if (StrUtil.isBlank(recordId)) {
            return new ArrayList<>();
        }
        return baseMapper.selectList(Wrappers.<LclContainerInfoDO>lambdaQuery()
                .eq(LclContainerInfoDO::getLclRecordId, recordId)
                .and(i -> i.likeRight(LclContainerInfoDO::getShipmentCode, "不到20方汇总")
                        .or().likeRight(LclContainerInfoDO::getShipmentCode, "待生成的货件号").or().likeRight(LclContainerInfoDO::getShipmentCode, "加急")
                )
                .last(defaultSortSql(null)));
    }

    public Set<String> selectAllShipmentCodeFromLclAndShipmentManagement() {
        return baseMapper.selectAllShipmentCodeFromLclAndShipmentManagement();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateShippingStartDate(LocalDate shippingStartDate, String containerId) {
        baseMapper.update(Wrappers.<LclContainerInfoDO>lambdaUpdate()
                .set(LclContainerInfoDO::getShippingStartDate, shippingStartDate)
                .eq(LclContainerInfoDO::getId, containerId)
        );
    }
}
