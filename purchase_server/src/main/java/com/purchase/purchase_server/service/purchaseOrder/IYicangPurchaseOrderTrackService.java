package com.purchase.purchase_server.service.purchaseOrder;

import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderTrackDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrdersSummaryDto;

/**
* <AUTHOR>
* @description 针对表【cm_yicang_purchase_order_track(易仓采购单跟单状态表)】的数据库操作Service
* @createDate 2024-11-01 14:30:43
*/
public interface IYicangPurchaseOrderTrackService {

    /**
     * 根据采购单表id删除
     * @param sbPoId 采购单表id
     */
    void removeBySbPoId(String sbPoId);

    /**
     * 保存易仓采购单跟单状态
     * @param trackDO 易仓采购单跟单状态
     */
    void save(YicangPurchaseOrderTrackDO trackDO);

    /**
     * 更新采购单跟单状态
     */
    void updateTrackingInfo(OrdersSummaryDto order, String sbPoId);
}
