package com.purchase.purchase_server.utils.easyExcelUtil;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * @author: 雪竹
 * @description: EasyExcel导出工具类
 * @dateTime: 2023/10/23 11:35
 **/
@Slf4j
public class EasyExcelExportUtils {

    private final HttpServletResponse response;

    @Getter
    private final ExcelWriter excelWriter;


    /**
    * @author: 毛豆
    * @description: 固定表头导出实体化工具类
    * @dateTime: 11:38 2024/1/3
    * @params: [response, fileName, sheetName, clazz]
    */
    public EasyExcelExportUtils(HttpServletResponse response, String fileName) {
        this.response = response;
        this.excelWriter = initExcelWriter(fileName);
    }

    public ExcelWriter initExcelWriter(String fileName) {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        try {
            return EasyExcel.write(response.getOutputStream())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
        } catch (IOException e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        }
    }
}
