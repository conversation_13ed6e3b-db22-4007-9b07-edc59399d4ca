package com.purchase.purchase_server.repository.dataRepository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.bo.ReplenishmentTrialPurchaseInventoryCopyBO;
import com.purchase.purchase_server.entity.dataObject.ProductSnapshotDO;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentTrialPurchaseInventoryCopyDO;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentVirtualSkuPurchaseDO;
import com.purchase.purchase_server.mapper.ReplenishmentTrialPurchaseInventoryCopyMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【cm_replenishment_trial_purchase_inventory】的数据库操作Service实现
 * @createDate 2024-01-17 10:45:51
 */
@Service
public class ReplenishmentTrialPurchaseInventoryCopyRepositoryImpl extends ServiceImpl<ReplenishmentTrialPurchaseInventoryCopyMapper, ReplenishmentTrialPurchaseInventoryCopyDO> {

    public IPage<ReplenishmentTrialPurchaseInventoryCopyBO> selectByPage(int size, int current){
        return baseMapper.selectJoinPage(new Page<>(current,size),ReplenishmentTrialPurchaseInventoryCopyBO.class,
                new MPJLambdaWrapper<ReplenishmentTrialPurchaseInventoryCopyDO>()
                        .selectAll(ReplenishmentTrialPurchaseInventoryCopyDO.class)
                        .select(ReplenishmentTrialPurchaseInventoryCopyDO::getId)
                        .select(ReplenishmentTrialPurchaseInventoryCopyDO::getCreateDate)
                        .select(ReplenishmentTrialPurchaseInventoryCopyDO::getCreateBy)
                        .select(ReplenishmentTrialPurchaseInventoryCopyDO::getUpdateDate)
                        .select(ReplenishmentTrialPurchaseInventoryCopyDO::getUpdateBy)

                        .select("t2.self_data ->> '$.containerLoad' as container_load")
                        .leftJoin(ReplenishmentVirtualSkuPurchaseDO.class, ReplenishmentVirtualSkuPurchaseDO::getId,ReplenishmentTrialPurchaseInventoryCopyDO::getReplenishmentVirtualPurchaseId)
                        .leftJoin(ProductSnapshotDO.class,ProductSnapshotDO::getId, ReplenishmentVirtualSkuPurchaseDO::getProductSnapshotId));
    }
}




