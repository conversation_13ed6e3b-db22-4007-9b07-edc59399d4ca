package com.inventory_server.model.warehouse.repository.httpRepository;

import com.crafts_mirror.utils.common.entity.HttpRequestDetail;
import com.crafts_mirror.utils.listener.HttpResponseListener;
import com.inventory_server.model.warehouse.entity.dos.YCResponseEntity;
import com.inventory_server.model.warehouse.entity.dto.WarehouseDetailDto;
import com.inventory_server.model.warehouse.entity.dto.WarehouseSummaryDto;

import java.util.List;

public interface FetchWarehouseHttpRepository {

    List<WarehouseSummaryDto> fetchAllWarehouseSummaryList(HttpRequestDetail<YCResponseEntity> requestDetail);

    void fetchWarehouseDetailList(List<HttpRequestDetail<YCResponseEntity>> requestDetailList,
                                                      HttpResponseListener<YCResponseEntity> httpResponseListener);
}
