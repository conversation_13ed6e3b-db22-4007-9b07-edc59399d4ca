package com.purchase.purchase_server.entity.dataObject;


import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * @Description 快照表
 * <AUTHOR>
 * @Date 2024/3/5 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_product_snapshot")
public class ProductSnapshotDO extends BaseEntity {
    /**
     * 自定义sku快照
     */
    private String selfData;
    /**
     * 虚拟sku快照
     */
    private String virtualData;
    /**
     * 工厂信息快照
     */
    private String factoryData;
    /**
     * spu快照
     */
    private String spuData;
    /**
     * 自定义skuId
     */
    private String selfSkuId;
    /**
     * 自定义sku
     */
    private String selfSku;
    /**
     * 虚拟skuId
     */
    private String virtualSkuId;

    /**
     * 升级款类型
     * 0-被升级款，1-升级款
     */
    private String upgradeType;

    /**
     * 升级款关联skuId
     */
    private String upgradeVirtualId;

    /**
     * 虚拟sku
     */
    private String virtualSku;
    /**
     * 产品经理
     */
    private String buyer;
    /**
     * 运营人员
     */
    private String operator;
}
